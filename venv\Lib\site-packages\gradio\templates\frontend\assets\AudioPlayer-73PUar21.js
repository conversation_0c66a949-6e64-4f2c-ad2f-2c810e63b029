/* empty css                                                        */import{M as Rt}from"./Music-CDm0RGMk.js";import"./index-B7J2Z2jS.js";import{f as ze}from"./utils-BsGrhMNe.js";import{P as Mt,T as St}from"./Trim-JQYgj7Jd.js";import{P as Dt}from"./Play-B0Q0U1Qz.js";import{U as Pt}from"./Undo-DCjBnnSO.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{E as Tt}from"./Empty-ZqppqzTN.js";import{r as st}from"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import{H as pe}from"./hls-CnVhpNcu.js";const{SvelteComponent:At,append:Wt,attr:F,detach:xt,init:Ot,insert:zt,noop:$e,safe_not_equal:Ht,svg_element:rt}=window.__gradio__svelte__internal;function Vt(r){let e,t;return{c(){e=rt("svg"),t=rt("path"),F(t,"stroke","currentColor"),F(t,"stroke-width","1.5"),F(t,"stroke-linecap","round"),F(t,"stroke-linejoin","round"),F(t,"d","M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z"),F(e,"xmlns","http://www.w3.org/2000/svg"),F(e,"width","24px"),F(e,"height","24px"),F(e,"fill","currentColor"),F(e,"stroke-width","1.5"),F(e,"viewBox","0 0 24 24"),F(e,"color","currentColor")},m(i,n){zt(i,e,n),Wt(e,t)},p:$e,i:$e,o:$e,d(i){i&&xt(e)}}}class $t extends At{constructor(e){super(),Ot(this,e,null,Vt,Ht,{})}}const{SvelteComponent:Nt,append:jt,attr:X,detach:qt,init:Bt,insert:Ut,noop:Ne,safe_not_equal:It,svg_element:ot}=window.__gradio__svelte__internal;function Ft(r){let e,t;return{c(){e=ot("svg"),t=ot("path"),X(t,"stroke","currentColor"),X(t,"stroke-width","1.5"),X(t,"stroke-linecap","round"),X(t,"stroke-linejoin","round"),X(t,"d","M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z"),X(e,"xmlns","http://www.w3.org/2000/svg"),X(e,"width","24px"),X(e,"height","24px"),X(e,"fill","currentColor"),X(e,"stroke-width","1.5"),X(e,"viewBox","0 0 24 24"),X(e,"color","currentColor")},m(i,n){Ut(i,e,n),jt(e,t)},p:Ne,i:Ne,o:Ne,d(i){i&&qt(e)}}}class Xt extends Nt{constructor(e){super(),Bt(this,e,null,Ft,It,{})}}const{SvelteComponent:Gt,append:We,attr:j,detach:Zt,init:Yt,insert:Kt,noop:je,safe_not_equal:Jt,svg_element:xe,text:Qt}=window.__gradio__svelte__internal;function ei(r){let e,t,i,n,s;return{c(){e=xe("svg"),t=xe("title"),i=Qt("Low volume"),n=xe("path"),s=xe("path"),j(n,"d","M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5"),j(n,"stroke-width","1.5"),j(n,"stroke-linecap","round"),j(n,"stroke-linejoin","round"),j(s,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),j(s,"stroke-width","1.5"),j(e,"width","100%"),j(e,"height","100%"),j(e,"viewBox","0 0 24 24"),j(e,"stroke-width","1.5"),j(e,"fill","none"),j(e,"xmlns","http://www.w3.org/2000/svg"),j(e,"stroke","currentColor"),j(e,"color","currentColor")},m(o,a){Kt(o,e,a),We(e,t),We(t,i),We(e,n),We(e,s)},p:je,i:je,o:je,d(o){o&&Zt(e)}}}class ti extends Gt{constructor(e){super(),Yt(this,e,null,ei,Jt,{})}}const{SvelteComponent:ii,append:we,attr:z,detach:ni,init:si,insert:ri,noop:qe,safe_not_equal:oi,svg_element:ye,text:li}=window.__gradio__svelte__internal;function ai(r){let e,t,i,n,s,o;return{c(){e=ye("svg"),t=ye("title"),i=li("High volume"),n=ye("path"),s=ye("path"),o=ye("path"),z(n,"d","M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z"),z(n,"stroke-width","1.5"),z(s,"d","M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5"),z(s,"stroke-width","1.5"),z(s,"stroke-linecap","round"),z(s,"stroke-linejoin","round"),z(o,"d","M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5"),z(o,"stroke-width","1.5"),z(o,"stroke-linecap","round"),z(o,"stroke-linejoin","round"),z(e,"width","100%"),z(e,"height","100%"),z(e,"viewBox","0 0 24 24"),z(e,"stroke-width","1.5"),z(e,"fill","none"),z(e,"stroke","currentColor"),z(e,"xmlns","http://www.w3.org/2000/svg"),z(e,"color","currentColor")},m(a,u){ri(a,e,u),we(e,t),we(t,i),we(e,n),we(e,s),we(e,o)},p:qe,i:qe,o:qe,d(a){a&&ni(e)}}}class ui extends ii{constructor(e){super(),si(this,e,null,ai,oi,{})}}const{SvelteComponent:di,append:ee,attr:O,detach:hi,init:ci,insert:fi,noop:Be,safe_not_equal:mi,svg_element:te,text:pi}=window.__gradio__svelte__internal;function gi(r){let e,t,i,n,s,o,a,u,d;return{c(){e=te("svg"),t=te("title"),i=pi("Muted volume"),n=te("g"),s=te("path"),o=te("path"),a=te("defs"),u=te("clipPath"),d=te("rect"),O(s,"d","M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14"),O(s,"stroke-width","1.5"),O(s,"stroke-linecap","round"),O(s,"stroke-linejoin","round"),O(o,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),O(o,"stroke-width","1.5"),O(n,"clip-path","url(#clip0_3173_16686)"),O(d,"width","24"),O(d,"height","24"),O(d,"fill","white"),O(u,"id","clip0_3173_16686"),O(e,"width","100%"),O(e,"height","100%"),O(e,"viewBox","0 0 24 24"),O(e,"stroke-width","1.5"),O(e,"fill","none"),O(e,"xmlns","http://www.w3.org/2000/svg"),O(e,"stroke","currentColor"),O(e,"color","currentColor")},m(l,h){fi(l,e,h),ee(e,t),ee(t,i),ee(e,n),ee(n,s),ee(n,o),ee(e,a),ee(a,u),ee(u,d)},p:Be,i:Be,o:Be,d(l){l&&hi(e)}}}class _i extends di{constructor(e){super(),ci(this,e,null,gi,mi,{})}}var vi=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{d(i.next(l))}catch(h){o(h)}}function u(l){try{d(i.throw(l))}catch(h){o(h)}}function d(l){l.done?s(l.value):n(l.value).then(a,u)}d((i=i.apply(r,e||[])).next())})};function bi(r,e){return vi(this,void 0,void 0,function*(){const t=new AudioContext({sampleRate:e});return t.decodeAudioData(r).finally(()=>t.close())})}function wi(r){const e=r[0];if(e.some(t=>t>1||t<-1)){const t=e.length;let i=0;for(let n=0;n<t;n++){const s=Math.abs(e[n]);s>i&&(i=s)}for(const n of r)for(let s=0;s<t;s++)n[s]/=i}return r}function yi(r,e){return typeof r[0]=="number"&&(r=[r]),wi(r),{duration:e,length:r[0].length,sampleRate:r[0].length/e,numberOfChannels:r.length,getChannelData:t=>r?.[t],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}const Ue={decode:bi,createBuffer:yi};var lt=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{d(i.next(l))}catch(h){o(h)}}function u(l){try{d(i.throw(l))}catch(h){o(h)}}function d(l){l.done?s(l.value):n(l.value).then(a,u)}d((i=i.apply(r,e||[])).next())})};function ki(r,e,t){var i,n;return lt(this,void 0,void 0,function*(){const s=yield fetch(r,t);{const o=(i=s.clone().body)===null||i===void 0?void 0:i.getReader(),a=Number((n=s.headers)===null||n===void 0?void 0:n.get("Content-Length"));let u=0;const d=(l,h)=>lt(this,void 0,void 0,function*(){if(l)return;u+=h?.length||0;const g=Math.round(u/a*100);return e(g),o?.read().then(({done:c,value:f})=>d(c,f))});o?.read().then(({done:l,value:h})=>d(l,h))}return s.blob()})}const Ci={fetchBlob:ki};class He{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class Ei extends He{constructor(e){super(),this.isExternalMedia=!1,e.media?(this.media=e.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),e.mediaControls&&(this.media.controls=!0),e.autoplay&&(this.media.autoplay=!0),e.playbackRate!=null&&this.onceMediaEvent("canplay",()=>{e.playbackRate!=null&&(this.media.playbackRate=e.playbackRate)})}onMediaEvent(e,t,i){return this.media.addEventListener(e,t,i),()=>this.media.removeEventListener(e,t)}onceMediaEvent(e,t){return this.onMediaEvent(e,t,{once:!0})}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const e=this.getSrc();e.startsWith("blob:")&&URL.revokeObjectURL(e)}setSrc(e,t){if(this.getSrc()===e)return;this.revokeSrc();const n=t instanceof Blob?URL.createObjectURL(t):e;this.media.src=n,this.media.load()}destroy(){this.media.pause(),!this.isExternalMedia&&(this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(e){this.media=e}play(){return this.media.play()}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(e){this.media.currentTime=e}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(e){this.media.volume=e}getMuted(){return this.media.muted}setMuted(e){this.media.muted=e}getPlaybackRate(){return this.media.playbackRate}setPlaybackRate(e,t){t!=null&&(this.media.preservesPitch=t),this.media.playbackRate=e}getMediaElement(){return this.media}setSinkId(e){return this.media.setSinkId(e)}}function Li(r,e,t,i,n=5){let s=()=>{};if(!r)return s;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),r.style.touchAction="none";let u=a.clientX,d=a.clientY,l=!1;const h=f=>{f.preventDefault(),f.stopPropagation();const w=f.clientX,R=f.clientY;if(l||Math.abs(w-u)>=n||Math.abs(R-d)>=n){const{left:E,top:p}=r.getBoundingClientRect();l||(l=!0,t?.(u-E,d-p)),e(w-u,R-d,w-E,R-p),u=w,d=R}},g=f=>{l&&(f.preventDefault(),f.stopPropagation())},c=()=>{r.style.touchAction="",l&&i?.(),s()};document.addEventListener("pointermove",h),document.addEventListener("pointerup",c),document.addEventListener("pointerleave",c),document.addEventListener("click",g,!0),s=()=>{document.removeEventListener("pointermove",h),document.removeEventListener("pointerup",c),document.removeEventListener("pointerleave",c),setTimeout(()=>{document.removeEventListener("click",g,!0)},10)}};return r.addEventListener("pointerdown",o),()=>{s(),r.removeEventListener("pointerdown",o)}}class Ve extends He{constructor(e,t){super(),this.timeouts=[],this.isScrolling=!1,this.audioData=null,this.resizeObserver=null,this.isDragging=!1,this.options=e;const i=this.parentFromOptionsContainer(e.container);this.parent=i;const[n,s]=this.initHtml();i.appendChild(n),this.container=n,this.scrollContainer=s.querySelector(".scroll"),this.wrapper=s.querySelector(".wrapper"),this.canvasWrapper=s.querySelector(".canvases"),this.progressWrapper=s.querySelector(".progress"),this.cursor=s.querySelector(".cursor"),t&&s.appendChild(t),this.initEvents()}parentFromOptionsContainer(e){let t;if(typeof e=="string"?t=document.querySelector(e):e instanceof HTMLElement&&(t=e),!t)throw new Error("Container not found");return t}initEvents(){const e=i=>{const n=this.wrapper.getBoundingClientRect(),s=i.clientX-n.left,o=i.clientX-n.left,a=s/n.width,u=o/n.height;return[a,u]};this.wrapper.addEventListener("click",i=>{const[n,s]=e(i);this.emit("click",n,s)}),this.wrapper.addEventListener("dblclick",i=>{const[n,s]=e(i);this.emit("dblclick",n,s)}),this.options.dragToSeek&&this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:i,scrollWidth:n,clientWidth:s}=this.scrollContainer,o=i/n,a=(i+s)/n;this.emit("scroll",o,a)});const t=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{t(()=>this.reRender())}),this.resizeObserver.observe(this.scrollContainer)}initDrag(){Li(this.wrapper,(e,t,i)=>{this.emit("drag",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))},()=>this.isDragging=!0,()=>this.isDragging=!1)}getHeight(){return this.options.height==null?128:isNaN(Number(this.options.height))?this.options.height==="auto"&&this.parent.clientHeight||128:Number(this.options.height)}initHtml(){const e=document.createElement("div"),t=e.attachShadow({mode:"open"});return t.innerHTML=`
      <style>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight()}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[e,t]}setOptions(e){if(this.options.container!==e.container){const t=this.parentFromOptionsContainer(e.container);t.appendChild(this.container),this.parent=t}e.dragToSeek&&!this.options.dragToSeek&&this.initDrag(),this.options=e,this.reRender()}getWrapper(){return this.wrapper}getScroll(){return this.scrollContainer.scrollLeft}destroy(){var e;this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect()}createDelay(e=10){const t={};return this.timeouts.push(t),i=>{t.timeout&&clearTimeout(t.timeout),t.timeout=setTimeout(i,e)}}convertColorValues(e){if(!Array.isArray(e))return e||"";if(e.length<2)return e[0]||"";const t=document.createElement("canvas"),n=t.getContext("2d").createLinearGradient(0,0,0,t.height),s=1/(e.length-1);return e.forEach((o,a)=>{const u=a*s;n.addColorStop(u,o)}),n}renderBarWaveform(e,t,i,n){const s=e[0],o=e[1]||e[0],a=s.length,{width:u,height:d}=i.canvas,l=d/2,h=window.devicePixelRatio||1,g=t.barWidth?t.barWidth*h:1,c=t.barGap?t.barGap*h:t.barWidth?g/2:0,f=t.barRadius||0,w=u/(g+c)/a,R=f&&"roundRect"in i?"roundRect":"rect";i.beginPath();let E=0,p=0,M=0;for(let y=0;y<=a;y++){const b=Math.round(y*w);if(b>E){const C=Math.round(p*l*n),L=Math.round(M*l*n),U=C+L||1;let W=l-C;t.barAlign==="top"?W=0:t.barAlign==="bottom"&&(W=d-U),i[R](E*(g+c),W,g,U,f),E=b,p=0,M=0}const S=Math.abs(s[y]||0),_=Math.abs(o[y]||0);S>p&&(p=S),_>M&&(M=_)}i.fill(),i.closePath()}renderLineWaveform(e,t,i,n){const s=o=>{const a=e[o]||e[0],u=a.length,{height:d}=i.canvas,l=d/2,h=i.canvas.width/u;i.moveTo(0,l);let g=0,c=0;for(let f=0;f<=u;f++){const w=Math.round(f*h);if(w>g){const E=Math.round(c*l*n)||1,p=l+E*(o===0?-1:1);i.lineTo(g,p),g=w,c=0}const R=Math.abs(a[f]||0);R>c&&(c=R)}i.lineTo(g,l)};i.beginPath(),s(0),s(1),i.fill(),i.closePath()}renderWaveform(e,t,i){if(i.fillStyle=this.convertColorValues(t.waveColor),t.renderFunction){t.renderFunction(e,i);return}let n=t.barHeight||1;if(t.normalize){const s=Array.from(e[0]).reduce((o,a)=>Math.max(o,Math.abs(a)),0);n=s?1/s:1}if(t.barWidth||t.barGap||t.barAlign){this.renderBarWaveform(e,t,i,n);return}this.renderLineWaveform(e,t,i,n)}renderSingleCanvas(e,t,i,n,s,o,a,u){const d=window.devicePixelRatio||1,l=document.createElement("canvas"),h=e[0].length;l.width=Math.round(i*(o-s)/h),l.height=n*d,l.style.width=`${Math.floor(l.width/d)}px`,l.style.height=`${n}px`,l.style.left=`${Math.floor(s*i/d/h)}px`,a.appendChild(l);const g=l.getContext("2d");if(this.renderWaveform(e.map(c=>c.slice(s,o)),t,g),l.width>0&&l.height>0){const c=l.cloneNode(),f=c.getContext("2d");f.drawImage(l,0,0),f.globalCompositeOperation="source-in",f.fillStyle=this.convertColorValues(t.progressColor),f.fillRect(0,0,l.width,l.height),u.appendChild(c)}}renderChannel(e,t,i){const n=document.createElement("div"),s=this.getHeight();n.style.height=`${s}px`,this.canvasWrapper.style.minHeight=`${s}px`,this.canvasWrapper.appendChild(n);const o=n.cloneNode();this.progressWrapper.appendChild(o);const{scrollLeft:a,scrollWidth:u,clientWidth:d}=this.scrollContainer,l=e[0].length,h=l/u;let g=Math.min(Ve.MAX_CANVAS_WIDTH,d);if(t.barWidth||t.barGap){const b=t.barWidth||.5,S=t.barGap||b/2,_=b+S;g%_!==0&&(g=Math.floor(g/_)*_)}const c=Math.floor(Math.abs(a)*h),f=Math.floor(c+g*h),w=f-c,R=(b,S)=>{this.renderSingleCanvas(e,t,i,s,Math.max(0,b),Math.min(S,l),n,o)},E=this.createDelay(),p=this.createDelay(),M=(b,S)=>{R(b,S),b>0&&E(()=>{M(b-w,S-w)})},y=(b,S)=>{R(b,S),S<l&&p(()=>{y(b+w,S+w)})};M(c,f),f<l&&y(f,f+w)}render(e){this.timeouts.forEach(a=>a.timeout&&clearTimeout(a.timeout)),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.wrapper.style.width="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const t=window.devicePixelRatio||1,i=this.scrollContainer.clientWidth,n=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrolling=n>i;const s=this.options.fillParent&&!this.isScrolling,o=(s?i:n)*t;if(this.wrapper.style.width=s?"100%":`${n}px`,this.scrollContainer.style.overflowX=this.isScrolling?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.options.splitChannels)for(let a=0;a<e.numberOfChannels;a++){const u=Object.assign(Object.assign({},this.options),this.options.splitChannels[a]);this.renderChannel([e.getChannelData(a)],u,o)}else{const a=[e.getChannelData(0)];e.numberOfChannels>1&&a.push(e.getChannelData(1)),this.renderChannel(a,this.options,o)}this.audioData=e,this.emit("render")}reRender(){if(!this.audioData)return;const e=this.progressWrapper.clientWidth;this.render(this.audioData);const t=this.progressWrapper.clientWidth;this.scrollContainer.scrollLeft+=t-e}zoom(e){this.options.minPxPerSec=e,this.reRender()}scrollIntoView(e,t=!1){const{clientWidth:i,scrollLeft:n,scrollWidth:s}=this.scrollContainer,o=s*e,a=i/2,u=t&&this.options.autoCenter&&!this.isDragging?a:i;if(o>n+u||o<n)if(this.options.autoCenter&&!this.isDragging){const d=a/20;o-(n+a)>=d&&o<n+i?this.scrollContainer.scrollLeft+=d:this.scrollContainer.scrollLeft=o-a}else this.isDragging?this.scrollContainer.scrollLeft=o<n?o-10:o-i+10:this.scrollContainer.scrollLeft=o;{const{scrollLeft:d}=this.scrollContainer,l=d/s,h=(d+i)/s;this.emit("scroll",l,h)}}renderProgress(e,t){if(isNaN(e))return;const i=e*100;this.canvasWrapper.style.clipPath=`polygon(${i}% 0, 100% 0, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.marginLeft=Math.round(i)===100?`-${this.options.cursorWidth}px`:"",this.isScrolling&&this.options.autoScroll&&this.scrollIntoView(e,t)}}Ve.MAX_CANVAS_WIDTH=4e3;class Ri extends He{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}var Ie=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{d(i.next(l))}catch(h){o(h)}}function u(l){try{d(i.throw(l))}catch(h){o(h)}}function d(l){l.done?s(l.value):n(l.value).then(a,u)}d((i=i.apply(r,e||[])).next())})};class Mi extends He{constructor(e=new AudioContext){super(),this.bufferNode=null,this.autoplay=!1,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.audioContext=e,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return Ie(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(e){this.currentSrc=e,fetch(e).then(t=>t.arrayBuffer()).then(t=>this.audioContext.decodeAudioData(t)).then(t=>{this.buffer=t,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play()})}_play(){var e;this.paused&&(this.paused=!1,(e=this.bufferNode)===null||e===void 0||e.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.bufferNode.buffer=this.buffer,this.bufferNode.connect(this.gainNode),this.playedDuration>=this.duration&&(this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,this.playedDuration),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))})}_pause(){var e;this.paused||(this.paused=!0,(e=this.bufferNode)===null||e===void 0||e.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime)}play(){return Ie(this,void 0,void 0,function*(){this._play(),this.emit("play")})}pause(){this._pause(),this.emit("pause")}setSinkId(e){return Ie(this,void 0,void 0,function*(){return this.audioContext.setSinkId(e)})}get playbackRate(){var e,t;return(t=(e=this.bufferNode)===null||e===void 0?void 0:e.playbackRate.value)!==null&&t!==void 0?t:1}set playbackRate(e){this.bufferNode&&(this.bufferNode.playbackRate.value=e)}get currentTime(){return this.paused?this.playedDuration:this.playedDuration+this.audioContext.currentTime-this.playStartTime}set currentTime(e){this.emit("seeking"),this.paused?this.playedDuration=e:(this._pause(),this.playedDuration=e,this._play()),this.emit("timeupdate")}get duration(){var e;return((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}get volume(){return this.gainNode.gain.value}set volume(e){this.gainNode.gain.value=e,this.emit("volumechange")}get muted(){return this._muted}set muted(e){this._muted!==e&&(this._muted=e,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}getGainNode(){return this.gainNode}}var ge=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{d(i.next(l))}catch(h){o(h)}}function u(l){try{d(i.throw(l))}catch(h){o(h)}}function d(l){l.done?s(l.value):n(l.value).then(a,u)}d((i=i.apply(r,e||[])).next())})};const Si={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class Ye extends Ei{static create(e){return new Ye(e)}constructor(e){const t=e.media||(e.backend==="WebAudio"?new Mi:void 0);super({media:t,mediaControls:e.mediaControls,autoplay:e.autoplay,playbackRate:e.audioRate}),this.plugins=[],this.decodedData=null,this.subscriptions=[],this.mediaSubscriptions=[],this.options=Object.assign({},Si,e),this.timer=new Ri;const i=t?void 0:this.getMediaElement();this.renderer=new Ve(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const n=this.options.url||this.getSrc();n?this.load(n,this.options.peaks,this.options.duration):this.options.peaks&&this.options.duration&&this.loadPredecoded()}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),!0),this.emit("timeupdate",e),this.emit("audioprocess",e)}))}initPlayerEvents(){this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),this.isPlaying()),this.emit("timeupdate",e)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop()}),this.onMediaEvent("emptied",()=>{this.timer.stop()}),this.onMediaEvent("ended",()=>{this.emit("finish")}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(e,t)=>{this.options.interact&&(this.seekTo(e),this.emit("interaction",e*this.getDuration()),this.emit("click",e,t))}),this.renderer.on("dblclick",(e,t)=>{this.emit("dblclick",e,t)}),this.renderer.on("scroll",(e,t)=>{const i=this.getDuration();this.emit("scroll",e*i,t*i)}),this.renderer.on("render",()=>{this.emit("redraw")}));{let e;this.subscriptions.push(this.renderer.on("drag",t=>{this.options.interact&&(this.renderer.renderProgress(t),clearTimeout(e),e=setTimeout(()=>{this.seekTo(t)},this.isPlaying()?0:200),this.emit("interaction",t*this.getDuration()),this.emit("drag",t))}))}}initPlugins(){var e;!((e=this.options.plugins)===null||e===void 0)&&e.length&&this.options.plugins.forEach(t=>{this.registerPlugin(t)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(e=>e()),this.mediaSubscriptions=[]}setOptions(e){this.options=Object.assign({},this.options,e),this.renderer.setOptions(this.options),e.audioRate&&this.setPlaybackRate(e.audioRate),e.mediaControls!=null&&(this.getMediaElement().controls=e.mediaControls)}registerPlugin(e){return e.init(this),this.plugins.push(e),this.subscriptions.push(e.once("destroy",()=>{this.plugins=this.plugins.filter(t=>t!==e)})),e}getWrapper(){return this.renderer.getWrapper()}getScroll(){return this.renderer.getScroll()}getActivePlugins(){return this.plugins}loadPredecoded(){return ge(this,void 0,void 0,function*(){this.options.peaks&&this.options.duration&&(this.decodedData=Ue.createBuffer(this.options.peaks,this.options.duration),yield Promise.resolve(),this.renderDecoded())})}renderDecoded(){return ge(this,void 0,void 0,function*(){this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData))})}loadAudio(e,t,i,n){return ge(this,void 0,void 0,function*(){if(this.emit("load",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,!t&&!i){const s=o=>this.emit("loading",o);t=yield Ci.fetchBlob(e,s,this.options.fetchParams)}if(this.setSrc(e,t),n=(yield Promise.resolve(n||this.getDuration()))||(yield new Promise(s=>{this.onceMediaEvent("loadedmetadata",()=>s(this.getDuration()))}))||(yield Promise.resolve(0)),i)this.decodedData=Ue.createBuffer(i,n);else if(t){const s=yield t.arrayBuffer();this.decodedData=yield Ue.decode(s,this.options.sampleRate)}this.renderDecoded(),this.emit("ready",this.getDuration())})}load(e,t,i){return ge(this,void 0,void 0,function*(){yield this.loadAudio(e,void 0,t,i)})}loadBlob(e,t,i){return ge(this,void 0,void 0,function*(){yield this.loadAudio("blob",e,t,i)})}zoom(e){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(e),this.emit("zoom",e)}getDecodedData(){return this.decodedData}exportPeaks({channels:e=2,maxLength:t=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const n=Math.min(e,this.decodedData.numberOfChannels),s=[];for(let o=0;o<n;o++){const a=this.decodedData.getChannelData(o),u=[],d=Math.round(a.length/t);for(let l=0;l<t;l++){const h=a.slice(l*d,(l+1)*d),g=Math.max(...h);u.push(Math.round(g*i)/i)}s.push(u)}return s}getDuration(){let e=super.getDuration()||0;return(e===0||e===1/0)&&this.decodedData&&(e=this.decodedData.duration),e}toggleInteraction(e){this.options.interact=e}seekTo(e){const t=this.getDuration()*e;this.setTime(t)}playPause(){return ge(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(e){this.setTime(this.getCurrentTime()+e)}empty(){this.load("",[[0]],.001)}setMediaElement(e){this.unsubscribePlayerEvents(),super.setMediaElement(e),this.initPlayerEvents()}destroy(){this.emit("destroy"),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}function Di(r){const e=r.numberOfChannels,t=r.length*e*2+44,i=new ArrayBuffer(t),n=new DataView(i);let s=0;const o=function(a,u,d){for(let l=0;l<d.length;l++)a.setUint8(u+l,d.charCodeAt(l))};o(n,s,"RIFF"),s+=4,n.setUint32(s,t-8,!0),s+=4,o(n,s,"WAVE"),s+=4,o(n,s,"fmt "),s+=4,n.setUint32(s,16,!0),s+=4,n.setUint16(s,1,!0),s+=2,n.setUint16(s,e,!0),s+=2,n.setUint32(s,r.sampleRate,!0),s+=4,n.setUint32(s,r.sampleRate*2*e,!0),s+=4,n.setUint16(s,e*2,!0),s+=2,n.setUint16(s,16,!0),s+=2,o(n,s,"data"),s+=4,n.setUint32(s,r.length*e*2,!0),s+=4;for(let a=0;a<r.length;a++)for(let u=0;u<e;u++){const d=Math.max(-1,Math.min(1,r.getChannelData(u)[a]));n.setInt16(s,d*32767,!0),s+=2}return new Uint8Array(i)}const Pi=async(r,e,t,i)=>{const n=new AudioContext({sampleRate:i||r.sampleRate}),s=r.numberOfChannels,o=i||r.sampleRate;let a=r.length,u=0;e&&t&&(u=Math.round(e*o),a=Math.round(t*o)-u);const d=n.createBuffer(s,a,o);for(let l=0;l<s;l++){const h=r.getChannelData(l),g=d.getChannelData(l);for(let c=0;c<a;c++)g[c]=h[u+c]}return Di(d)},at=(r,e)=>{r&&r.skip(e)},ve=(r,e)=>(e||(e=5),r/100*e||5);class Et{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class Ti extends Et{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}function Oe(r,e,t,i,n=5){let s=()=>{};if(!r)return s;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),r.style.touchAction="none";let u=a.clientX,d=a.clientY,l=!1;const h=f=>{f.preventDefault(),f.stopPropagation();const w=f.clientX,R=f.clientY;if(l||Math.abs(w-u)>=n||Math.abs(R-d)>=n){const{left:E,top:p}=r.getBoundingClientRect();l||(l=!0,t?.(u-E,d-p)),e(w-u,R-d,w-E,R-p),u=w,d=R}},g=f=>{l&&(f.preventDefault(),f.stopPropagation())},c=()=>{r.style.touchAction="",l&&i?.(),s()};document.addEventListener("pointermove",h),document.addEventListener("pointerup",c),document.addEventListener("pointerleave",c),document.addEventListener("click",g,!0),s=()=>{document.removeEventListener("pointermove",h),document.removeEventListener("pointerup",c),document.removeEventListener("pointerleave",c),setTimeout(()=>{document.removeEventListener("click",g,!0)},10)}};return r.addEventListener("pointerdown",o),()=>{s(),r.removeEventListener("pointerdown",o)}}class ut extends Et{constructor(e,t,i=0){var n,s,o,a,u,d,l;super(),this.totalDuration=t,this.numberOfChannels=i,this.minLength=0,this.maxLength=1/0,this.id=e.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(e.start),this.end=this.clampPosition((n=e.end)!==null&&n!==void 0?n:e.start),this.drag=(s=e.drag)===null||s===void 0||s,this.resize=(o=e.resize)===null||o===void 0||o,this.color=(a=e.color)!==null&&a!==void 0?a:"rgba(0, 0, 0, 0.1)",this.minLength=(u=e.minLength)!==null&&u!==void 0?u:this.minLength,this.maxLength=(d=e.maxLength)!==null&&d!==void 0?d:this.maxLength,this.channelIdx=(l=e.channelIdx)!==null&&l!==void 0?l:-1,this.element=this.initElement(),this.setContent(e.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(e){return Math.max(0,Math.min(this.totalDuration,e))}setPart(){const e=this.start===this.end;this.element.setAttribute("part",`${e?"marker":"region"} ${this.id}`)}addResizeHandles(e){const t=document.createElement("div");t.setAttribute("data-resize","left"),t.setAttribute("style",`
        position: absolute;
        z-index: 2;
        width: 6px;
        height: 100%;
        top: 0;
        left: 0;
        border-left: 2px solid rgba(0, 0, 0, 0.5);
        border-radius: 2px 0 0 2px;
        cursor: ew-resize;
        word-break: keep-all;
      `),t.setAttribute("part","region-handle region-handle-left");const i=t.cloneNode();i.setAttribute("data-resize","right"),i.style.left="",i.style.right="0",i.style.borderRight=i.style.borderLeft,i.style.borderLeft="",i.style.borderRadius="0 2px 2px 0",i.setAttribute("part","region-handle region-handle-right"),e.appendChild(t),e.appendChild(i),Oe(t,n=>this.onResize(n,"start"),()=>null,()=>this.onEndResizing(),1),Oe(i,n=>this.onResize(n,"end"),()=>null,()=>this.onEndResizing(),1)}removeResizeHandles(e){const t=e.querySelector('[data-resize="left"]'),i=e.querySelector('[data-resize="right"]');t&&e.removeChild(t),i&&e.removeChild(i)}initElement(){const e=document.createElement("div"),t=this.start===this.end;let i=0,n=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(n=100/this.numberOfChannels,i=n*this.channelIdx),e.setAttribute("style",`
      position: absolute;
      top: ${i}%;
      height: ${n}%;
      background-color: ${t?"none":this.color};
      border-left: ${t?"2px solid "+this.color:"none"};
      border-radius: 2px;
      box-sizing: border-box;
      transition: background-color 0.2s ease;
      cursor: ${this.drag?"grab":"default"};
      pointer-events: all;
    `),!t&&this.resize&&this.addResizeHandles(e),e}renderPosition(){const e=this.start/this.totalDuration,t=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*e+"%",this.element.style.right=100*t+"%"}initMouseEvents(){const{element:e}=this;e&&(e.addEventListener("click",t=>this.emit("click",t)),e.addEventListener("mouseenter",t=>this.emit("over",t)),e.addEventListener("mouseleave",t=>this.emit("leave",t)),e.addEventListener("dblclick",t=>this.emit("dblclick",t)),Oe(e,t=>this.onMove(t),()=>this.onStartMoving(),()=>this.onEndMoving()))}onStartMoving(){this.drag&&(this.element.style.cursor="grabbing")}onEndMoving(){this.drag&&(this.element.style.cursor="grab",this.emit("update-end"))}_onUpdate(e,t){if(!this.element.parentElement)return;const i=e/this.element.parentElement.clientWidth*this.totalDuration,n=t&&t!=="start"?this.start:this.start+i,s=t&&t!=="end"?this.end:this.end+i,o=s-n;n>=0&&s<=this.totalDuration&&n<=s&&o>=this.minLength&&o<=this.maxLength&&(this.start=n,this.end=s,this.renderPosition(),this.emit("update"))}onMove(e){this.drag&&this._onUpdate(e)}onResize(e,t){this.resize&&this._onUpdate(e,t)}onEndResizing(){this.resize&&this.emit("update-end")}_setTotalDuration(e){this.totalDuration=e,this.renderPosition()}play(){this.emit("play")}setContent(e){var t;if((t=this.content)===null||t===void 0||t.remove(),e){if(typeof e=="string"){this.content=document.createElement("div");const i=this.start===this.end;this.content.style.padding=`0.2em ${i?.2:.4}em`,this.content.textContent=e}else this.content=e;this.content.setAttribute("part","region-content"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(e){var t,i;if(e.color&&(this.color=e.color,this.element.style.backgroundColor=this.color),e.drag!==void 0&&(this.drag=e.drag,this.element.style.cursor=this.drag?"grab":"default"),e.start!==void 0||e.end!==void 0){const n=this.start===this.end;this.start=this.clampPosition((t=e.start)!==null&&t!==void 0?t:this.start),this.end=this.clampPosition((i=e.end)!==null&&i!==void 0?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(e.content&&this.setContent(e.content),e.id&&(this.id=e.id,this.setPart()),e.resize!==void 0&&e.resize!==this.resize){const n=this.start===this.end;this.resize=e.resize,this.resize&&!n?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit("remove"),this.element.remove(),this.element=null}}class Ke extends Ti{constructor(e){super(e),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(e){return new Ke(e)}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let e=[];this.subscriptions.push(this.wavesurfer.on("timeupdate",t=>{const i=this.regions.filter(n=>n.start<=t&&n.end>=t);i.forEach(n=>{e.includes(n)||this.emit("region-in",n)}),e.forEach(n=>{i.includes(n)||this.emit("region-out",n)}),e=i}))}initRegionsContainer(){const e=document.createElement("div");return e.setAttribute("style",`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      pointer-events: none;
    `),e}getRegions(){return this.regions}avoidOverlapping(e){if(!e.content)return;const t=e.content,i=t.getBoundingClientRect().left,n=e.element.scrollWidth,s=this.regions.filter(o=>{if(o===e||!o.content)return!1;const a=o.content.getBoundingClientRect().left,u=o.element.scrollWidth;return i<a+u&&a<i+n}).map(o=>{var a;return((a=o.content)===null||a===void 0?void 0:a.getBoundingClientRect().height)||0}).reduce((o,a)=>o+a,0);t.style.marginTop=`${s}px`}saveRegion(e){this.regionsContainer.appendChild(e.element),this.avoidOverlapping(e),this.regions.push(e);const t=[e.on("update-end",()=>{this.avoidOverlapping(e),this.emit("region-updated",e)}),e.on("play",()=>{var i,n;(i=this.wavesurfer)===null||i===void 0||i.play(),(n=this.wavesurfer)===null||n===void 0||n.setTime(e.start)}),e.on("click",i=>{this.emit("region-clicked",e,i)}),e.on("dblclick",i=>{this.emit("region-double-clicked",e,i)}),e.once("remove",()=>{t.forEach(i=>i()),this.regions=this.regions.filter(i=>i!==e)})];this.subscriptions.push(...t),this.emit("region-created",e)}addRegion(e){var t,i;if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const n=this.wavesurfer.getDuration(),s=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getDecodedData())===null||i===void 0?void 0:i.numberOfChannels,o=new ut(e,n,s);return n?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once("ready",a=>{o._setTotalDuration(a),this.saveRegion(o)})),o}enableDragSelection(e){var t,i;const n=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getWrapper())===null||i===void 0?void 0:i.querySelector("div");if(!n)return()=>{};let s=null,o=0;return Oe(n,(a,u,d)=>{s&&s._onUpdate(a,d>o?"end":"start")},a=>{var u,d;if(o=a,!this.wavesurfer)return;const l=this.wavesurfer.getDuration(),h=(d=(u=this.wavesurfer)===null||u===void 0?void 0:u.getDecodedData())===null||d===void 0?void 0:d.numberOfChannels,g=this.wavesurfer.getWrapper().clientWidth,c=a/g*l,f=(a+5)/g*l;s=new ut(Object.assign(Object.assign({},e),{start:c,end:f}),l,h),this.regionsContainer.appendChild(s.element)},()=>{s&&(this.saveRegion(s),s=null)})}clearRegions(){this.regions.forEach(e=>e.remove())}destroy(){this.clearRegions(),super.destroy()}}const{SvelteComponent:Ai,check_outros:Wi,create_component:Je,destroy_component:Qe,detach:xi,empty:Oi,flush:zi,group_outros:Hi,init:Vi,insert:$i,mount_component:et,safe_not_equal:Ni,transition_in:Se,transition_out:De}=window.__gradio__svelte__internal;function ji(r){let e,t;return e=new ui({}),{c(){Je(e.$$.fragment)},m(i,n){et(e,i,n),t=!0},i(i){t||(Se(e.$$.fragment,i),t=!0)},o(i){De(e.$$.fragment,i),t=!1},d(i){Qe(e,i)}}}function qi(r){let e,t;return e=new ti({}),{c(){Je(e.$$.fragment)},m(i,n){et(e,i,n),t=!0},i(i){t||(Se(e.$$.fragment,i),t=!0)},o(i){De(e.$$.fragment,i),t=!1},d(i){Qe(e,i)}}}function Bi(r){let e,t;return e=new _i({}),{c(){Je(e.$$.fragment)},m(i,n){et(e,i,n),t=!0},i(i){t||(Se(e.$$.fragment,i),t=!0)},o(i){De(e.$$.fragment,i),t=!1},d(i){Qe(e,i)}}}function Ui(r){let e,t,i,n;const s=[Bi,qi,ji],o=[];function a(u,d){return u[0]==0?0:u[0]<.5?1:u[0]>=.5?2:-1}return~(e=a(r))&&(t=o[e]=s[e](r)),{c(){t&&t.c(),i=Oi()},m(u,d){~e&&o[e].m(u,d),$i(u,i,d),n=!0},p(u,[d]){let l=e;e=a(u),e!==l&&(t&&(Hi(),De(o[l],1,1,()=>{o[l]=null}),Wi()),~e?(t=o[e],t||(t=o[e]=s[e](u),t.c()),Se(t,1),t.m(i.parentNode,i)):t=null)},i(u){n||(Se(t),n=!0)},o(u){De(t),n=!1},d(u){u&&xi(i),~e&&o[e].d(u)}}}function Ii(r,e,t){let{currentVolume:i}=e;return r.$$set=n=>{"currentVolume"in n&&t(0,i=n.currentVolume)},[i]}class Fi extends Ai{constructor(e){super(),Vi(this,e,Ii,Ui,Ni,{currentVolume:0})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),zi()}}const{SvelteComponent:Xi,attr:_e,binding_callbacks:Gi,detach:Zi,element:Yi,flush:Fe,init:Ki,insert:Ji,listen:dt,noop:ht,run_all:Qi,safe_not_equal:en}=window.__gradio__svelte__internal,{onMount:tn}=window.__gradio__svelte__internal;function nn(r){let e,t,i;return{c(){e=Yi("input"),_e(e,"id","volume"),_e(e,"class","volume-slider svelte-wuo8j5"),_e(e,"type","range"),_e(e,"min","0"),_e(e,"max","1"),_e(e,"step","0.01"),e.value=r[0]},m(n,s){Ji(n,e,s),r[4](e),t||(i=[dt(e,"focusout",r[5]),dt(e,"input",r[6])],t=!0)},p(n,[s]){s&1&&(e.value=n[0])},i:ht,o:ht,d(n){n&&Zi(e),r[4](null),t=!1,Qi(i)}}}function sn(r,e,t){let{currentVolume:i=1}=e,{show_volume_slider:n=!1}=e,{waveform:s}=e,o;tn(()=>{a()});const a=()=>{let h=o;h&&(h.style.background=`linear-gradient(to right, var(--color-accent) ${i*100}%, var(--neutral-400) ${i*100}%)`)};function u(h){Gi[h?"unshift":"push"](()=>{o=h,t(3,o)})}const d=()=>t(1,n=!1),l=h=>{h.target instanceof HTMLInputElement&&(t(0,i=parseFloat(h.target.value)),s?.setVolume(i))};return r.$$set=h=>{"currentVolume"in h&&t(0,i=h.currentVolume),"show_volume_slider"in h&&t(1,n=h.show_volume_slider),"waveform"in h&&t(2,s=h.waveform)},r.$$.update=()=>{r.$$.dirty&1&&a()},[i,n,s,o,u,d,l]}class rn extends Xi{constructor(e){super(),Ki(this,e,sn,nn,en,{currentVolume:0,show_volume_slider:1,waveform:2})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),Fe()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),Fe()}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),Fe()}}const{SvelteComponent:on,add_flush_callback:ct,append:V,attr:D,bind:ft,binding_callbacks:mt,check_outros:Ce,create_component:ne,destroy_component:se,detach:re,element:B,empty:ln,flush:N,group_outros:Ee,init:an,insert:oe,listen:J,mount_component:le,noop:Le,run_all:Lt,safe_not_equal:un,set_data:dn,set_style:pt,space:ie,text:gt,toggle_class:_t,transition_in:A,transition_out:H}=window.__gradio__svelte__internal;function vt(r){let e,t,i,n;function s(u){r[27](u)}function o(u){r[28](u)}let a={waveform:r[2]};return r[12]!==void 0&&(a.currentVolume=r[12]),r[1]!==void 0&&(a.show_volume_slider=r[1]),e=new rn({props:a}),mt.push(()=>ft(e,"currentVolume",s)),mt.push(()=>ft(e,"show_volume_slider",o)),{c(){ne(e.$$.fragment)},m(u,d){le(e,u,d),n=!0},p(u,d){const l={};d[0]&4&&(l.waveform=u[2]),!t&&d[0]&4096&&(t=!0,l.currentVolume=u[12],ct(()=>t=!1)),!i&&d[0]&2&&(i=!0,l.show_volume_slider=u[1],ct(()=>i=!1)),e.$set(l)},i(u){n||(A(e.$$.fragment,u),n=!0)},o(u){H(e.$$.fragment,u),n=!1},d(u){se(e,u)}}}function hn(r){let e,t;return e=new Dt({}),{c(){ne(e.$$.fragment)},m(i,n){le(e,i,n),t=!0},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){H(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function cn(r){let e,t;return e=new Mt({}),{c(){ne(e.$$.fragment)},m(i,n){le(e,i,n),t=!0},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){H(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function bt(r){let e,t,i,n,s,o=r[6]&&r[0]===""&&wt(r);const a=[mn,fn],u=[];function d(l,h){return l[0]===""?0:1}return t=d(r),i=u[t]=a[t](r),{c(){o&&o.c(),e=ie(),i.c(),n=ln()},m(l,h){o&&o.m(l,h),oe(l,e,h),u[t].m(l,h),oe(l,n,h),s=!0},p(l,h){l[6]&&l[0]===""?o?(o.p(l,h),h[0]&65&&A(o,1)):(o=wt(l),o.c(),A(o,1),o.m(e.parentNode,e)):o&&(Ee(),H(o,1,1,()=>{o=null}),Ce());let g=t;t=d(l),t===g?u[t].p(l,h):(Ee(),H(u[g],1,1,()=>{u[g]=null}),Ce(),i=u[t],i?i.p(l,h):(i=u[t]=a[t](l),i.c()),A(i,1),i.m(n.parentNode,n))},i(l){s||(A(o),A(i),s=!0)},o(l){H(o),H(i),s=!1},d(l){l&&(re(e),re(n)),o&&o.d(l),u[t].d(l)}}}function wt(r){let e,t,i,n,s;return t=new Pt({}),{c(){e=B("button"),ne(t.$$.fragment),D(e,"class","action icon svelte-ije4bl"),D(e,"aria-label","Reset audio")},m(o,a){oe(o,e,a),le(t,e,null),i=!0,n||(s=J(e,"click",r[33]),n=!0)},p:Le,i(o){i||(A(t.$$.fragment,o),i=!0)},o(o){H(t.$$.fragment,o),i=!1},d(o){o&&re(e),se(t),n=!1,s()}}}function fn(r){let e,t,i,n,s;return{c(){e=B("button"),e.textContent="Trim",t=ie(),i=B("button"),i.textContent="Cancel",D(e,"class","text-button svelte-ije4bl"),D(i,"class","text-button svelte-ije4bl")},m(o,a){oe(o,e,a),oe(o,t,a),oe(o,i,a),n||(s=[J(e,"click",r[14]),J(i,"click",r[16])],n=!0)},p:Le,i:Le,o:Le,d(o){o&&(re(e),re(t),re(i)),n=!1,Lt(s)}}}function mn(r){let e,t,i,n,s;return t=new St({}),{c(){e=B("button"),ne(t.$$.fragment),D(e,"class","action icon svelte-ije4bl"),D(e,"aria-label","Trim audio to selection")},m(o,a){oe(o,e,a),le(t,e,null),i=!0,n||(s=J(e,"click",r[16]),n=!0)},p:Le,i(o){i||(A(t.$$.fragment,o),i=!0)},o(o){H(t.$$.fragment,o),i=!1},d(o){o&&re(e),se(t),n=!1,s()}}}function pn(r){let e,t,i,n,s,o,a,u,d,l,h,g,c,f,w,R,E,p,M,y,b,S,_,C,L,U,W,$,ae,ue;n=new Fi({props:{currentVolume:r[12]}});let P=r[1]&&vt(r);w=new $t({});const ce=[cn,hn],Z=[];function fe(k,x){return k[5]?0:1}M=fe(r),y=Z[M]=ce[M](r),C=new Xt({});let T=r[10]&&r[7]&&bt(r);return{c(){e=B("div"),t=B("div"),i=B("button"),ne(n.$$.fragment),s=ie(),P&&P.c(),o=ie(),a=B("button"),u=B("span"),d=gt(r[11]),l=gt("x"),g=ie(),c=B("div"),f=B("button"),ne(w.$$.fragment),E=ie(),p=B("button"),y.c(),S=ie(),_=B("button"),ne(C.$$.fragment),U=ie(),W=B("div"),T&&T.c(),D(i,"class","action icon volume svelte-ije4bl"),D(i,"aria-label","Adjust volume"),pt(i,"color",r[1]?"var(--color-accent)":"var(--neutral-400)"),D(a,"class","playback icon svelte-ije4bl"),D(a,"aria-label",h=`Adjust playback speed to ${r[13][(r[13].indexOf(r[11])+1)%r[13].length]}x`),_t(a,"hidden",r[1]),D(t,"class","control-wrapper svelte-ije4bl"),D(f,"class","rewind icon svelte-ije4bl"),D(f,"aria-label",R=`Skip backwards by ${ve(r[3],r[9].skip_length)} seconds`),D(p,"class","play-pause-button icon svelte-ije4bl"),D(p,"aria-label",b=r[5]?r[4]("audio.pause"):r[4]("audio.play")),D(_,"class","skip icon svelte-ije4bl"),D(_,"aria-label",L="Skip forward by "+ve(r[3],r[9].skip_length)+" seconds"),D(c,"class","play-pause-wrapper svelte-ije4bl"),D(W,"class","settings-wrapper svelte-ije4bl"),D(e,"class","controls svelte-ije4bl"),D(e,"data-testid","waveform-controls")},m(k,x){oe(k,e,x),V(e,t),V(t,i),le(n,i,null),V(t,s),P&&P.m(t,null),V(t,o),V(t,a),V(a,u),V(u,d),V(u,l),V(e,g),V(e,c),V(c,f),le(w,f,null),V(c,E),V(c,p),Z[M].m(p,null),V(c,S),V(c,_),le(C,_,null),V(e,U),V(e,W),T&&T.m(W,null),$=!0,ae||(ue=[J(i,"click",r[26]),J(a,"click",r[29]),J(f,"click",r[30]),J(p,"click",r[31]),J(_,"click",r[32])],ae=!0)},p(k,x){const me={};x[0]&4096&&(me.currentVolume=k[12]),n.$set(me),x[0]&2&&pt(i,"color",k[1]?"var(--color-accent)":"var(--neutral-400)"),k[1]?P?(P.p(k,x),x[0]&2&&A(P,1)):(P=vt(k),P.c(),A(P,1),P.m(t,o)):P&&(Ee(),H(P,1,1,()=>{P=null}),Ce()),(!$||x[0]&2048)&&dn(d,k[11]),(!$||x[0]&2048&&h!==(h=`Adjust playback speed to ${k[13][(k[13].indexOf(k[11])+1)%k[13].length]}x`))&&D(a,"aria-label",h),(!$||x[0]&2)&&_t(a,"hidden",k[1]),(!$||x[0]&520&&R!==(R=`Skip backwards by ${ve(k[3],k[9].skip_length)} seconds`))&&D(f,"aria-label",R);let v=M;M=fe(k),M!==v&&(Ee(),H(Z[v],1,1,()=>{Z[v]=null}),Ce(),y=Z[M],y||(y=Z[M]=ce[M](k),y.c()),A(y,1),y.m(p,null)),(!$||x[0]&48&&b!==(b=k[5]?k[4]("audio.pause"):k[4]("audio.play")))&&D(p,"aria-label",b),(!$||x[0]&520&&L!==(L="Skip forward by "+ve(k[3],k[9].skip_length)+" seconds"))&&D(_,"aria-label",L),k[10]&&k[7]?T?(T.p(k,x),x[0]&1152&&A(T,1)):(T=bt(k),T.c(),A(T,1),T.m(W,null)):T&&(Ee(),H(T,1,1,()=>{T=null}),Ce())},i(k){$||(A(n.$$.fragment,k),A(P),A(w.$$.fragment,k),A(y),A(C.$$.fragment,k),A(T),$=!0)},o(k){H(n.$$.fragment,k),H(P),H(w.$$.fragment,k),H(y),H(C.$$.fragment,k),H(T),$=!1},d(k){k&&re(e),se(n),P&&P.d(),se(w),Z[M].d(),se(C),T&&T.d(),ae=!1,Lt(ue)}}}function gn(r,e,t){let{waveform:i}=e,{audio_duration:n}=e,{i18n:s}=e,{playing:o}=e,{show_redo:a=!1}=e,{interactive:u=!1}=e,{handle_trim_audio:d}=e,{mode:l=""}=e,{container:h}=e,{handle_reset_value:g}=e,{waveform_options:c={}}=e,{trim_region_settings:f={}}=e,{show_volume_slider:w=!1}=e,{editable:R=!0}=e,{trimDuration:E=0}=e,p=[.5,1,1.5,2],M=p[1],y=null,b=null,S,_,C="",L=1;const U=()=>{y&&(t(22,b=y?.addRegion({start:n/4,end:n/2,...f})),t(17,E=b.end-b.start))},W=()=>{if(i&&y&&b){const v=b.start,Q=b.end;d(v,Q),t(0,l=""),t(22,b=null)}},$=()=>{y?.getRegions().forEach(v=>{v.remove()}),y?.clearRegions()},ae=()=>{$(),l==="edit"?t(0,l=""):(t(0,l="edit"),U())},ue=(v,Q)=>{let de,m;b&&(v==="left"?Q==="ArrowLeft"?(de=b.start-.05,m=b.end):(de=b.start+.05,m=b.end):Q==="ArrowLeft"?(de=b.start,m=b.end-.05):(de=b.start,m=b.end+.05),b.setOptions({start:de,end:m}),t(17,E=b.end-b.start))},P=()=>t(1,w=!w);function ce(v){L=v,t(12,L)}function Z(v){w=v,t(1,w)}const fe=()=>{t(11,M=p[(p.indexOf(M)+1)%p.length]),i?.setPlaybackRate(M)},T=()=>i?.skip(ve(n,c.skip_length)*-1),k=()=>i?.playPause(),x=()=>i?.skip(ve(n,c.skip_length)),me=()=>{g(),$(),t(0,l="")};return r.$$set=v=>{"waveform"in v&&t(2,i=v.waveform),"audio_duration"in v&&t(3,n=v.audio_duration),"i18n"in v&&t(4,s=v.i18n),"playing"in v&&t(5,o=v.playing),"show_redo"in v&&t(6,a=v.show_redo),"interactive"in v&&t(7,u=v.interactive),"handle_trim_audio"in v&&t(18,d=v.handle_trim_audio),"mode"in v&&t(0,l=v.mode),"container"in v&&t(19,h=v.container),"handle_reset_value"in v&&t(8,g=v.handle_reset_value),"waveform_options"in v&&t(9,c=v.waveform_options),"trim_region_settings"in v&&t(20,f=v.trim_region_settings),"show_volume_slider"in v&&t(1,w=v.show_volume_slider),"editable"in v&&t(10,R=v.editable),"trimDuration"in v&&t(17,E=v.trimDuration)},r.$$.update=()=>{if(r.$$.dirty[0]&524292&&t(21,y=h&&i?i.registerPlugin(Ke.create()):null),r.$$.dirty[0]&2097152&&y?.on("region-out",v=>{v.play()}),r.$$.dirty[0]&2097152&&y?.on("region-updated",v=>{t(17,E=v.end-v.start)}),r.$$.dirty[0]&2097152&&y?.on("region-clicked",(v,Q)=>{Q.stopPropagation(),t(22,b=v),v.play()}),r.$$.dirty[0]&31981568&&b){const v=h.children[0].shadowRoot;t(24,_=v.querySelector('[data-resize="right"]')),t(23,S=v.querySelector('[data-resize="left"]')),S&&_&&(S.setAttribute("role","button"),_.setAttribute("role","button"),S?.setAttribute("aria-label","Drag to adjust start time"),_?.setAttribute("aria-label","Drag to adjust end time"),S?.setAttribute("tabindex","0"),_?.setAttribute("tabindex","0"),S.addEventListener("focus",()=>{y&&t(25,C="left")}),_.addEventListener("focus",()=>{y&&t(25,C="right")}))}r.$$.dirty[0]&35651584&&y&&window.addEventListener("keydown",v=>{v.key==="ArrowLeft"?ue(C,"ArrowLeft"):v.key==="ArrowRight"&&ue(C,"ArrowRight")})},[l,w,i,n,s,o,a,u,g,c,R,M,L,p,W,$,ae,E,d,h,f,y,b,S,_,C,P,ce,Z,fe,T,k,x,me]}class _n extends on{constructor(e){super(),an(this,e,gn,pn,un,{waveform:2,audio_duration:3,i18n:4,playing:5,show_redo:6,interactive:7,handle_trim_audio:18,mode:0,container:19,handle_reset_value:8,waveform_options:9,trim_region_settings:20,show_volume_slider:1,editable:10,trimDuration:17},null,[-1,-1])}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),N()}get audio_duration(){return this.$$.ctx[3]}set audio_duration(e){this.$$set({audio_duration:e}),N()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),N()}get playing(){return this.$$.ctx[5]}set playing(e){this.$$set({playing:e}),N()}get show_redo(){return this.$$.ctx[6]}set show_redo(e){this.$$set({show_redo:e}),N()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),N()}get handle_trim_audio(){return this.$$.ctx[18]}set handle_trim_audio(e){this.$$set({handle_trim_audio:e}),N()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),N()}get container(){return this.$$.ctx[19]}set container(e){this.$$set({container:e}),N()}get handle_reset_value(){return this.$$.ctx[8]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),N()}get waveform_options(){return this.$$.ctx[9]}set waveform_options(e){this.$$set({waveform_options:e}),N()}get trim_region_settings(){return this.$$.ctx[20]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),N()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),N()}get editable(){return this.$$.ctx[10]}set editable(e){this.$$set({editable:e}),N()}get trimDuration(){return this.$$.ctx[17]}set trimDuration(e){this.$$set({trimDuration:e}),N()}}const{SvelteComponent:vn,add_flush_callback:Xe,append:Y,attr:q,bind:Ge,binding_callbacks:he,bubble:bn,check_outros:wn,create_component:tt,destroy_component:it,detach:Re,element:K,empty:yn,flush:G,group_outros:kn,init:Cn,insert:Me,listen:Ze,mount_component:nt,run_all:En,safe_not_equal:Ln,set_data:Rn,set_style:yt,space:ke,text:Mn,toggle_class:kt,transition_in:Pe,transition_out:Te}=window.__gradio__svelte__internal,{onMount:Sn}=window.__gradio__svelte__internal,{createEventDispatcher:Dn}=window.__gradio__svelte__internal;function Pn(r){let e,t,i,n,s,o,a,u,d,l,h,g,c,f,w,R,E,p=r[0]==="edit"&&r[18]>0&&Ct(r);function M(_){r[32](_)}function y(_){r[33](_)}function b(_){r[34](_)}let S={container:r[10],waveform:r[11],playing:r[16],audio_duration:r[17],i18n:r[3],interactive:r[4],handle_trim_audio:r[21],show_redo:r[4],handle_reset_value:r[9],waveform_options:r[8],trim_region_settings:r[6],editable:r[5]};return r[0]!==void 0&&(S.mode=r[0]),r[18]!==void 0&&(S.trimDuration=r[18]),r[19]!==void 0&&(S.show_volume_slider=r[19]),g=new _n({props:S}),he.push(()=>Ge(g,"mode",M)),he.push(()=>Ge(g,"trimDuration",y)),he.push(()=>Ge(g,"show_volume_slider",b)),{c(){e=K("div"),t=K("div"),i=K("div"),n=ke(),s=K("div"),o=K("time"),o.textContent="0:00",a=ke(),u=K("div"),p&&p.c(),d=ke(),l=K("time"),l.textContent="0:00",h=ke(),tt(g.$$.fragment),q(i,"id","waveform"),q(i,"class","svelte-19usgod"),yt(i,"height",r[10]?null:"58px"),q(t,"class","waveform-container svelte-19usgod"),q(o,"id","time"),q(o,"class","svelte-19usgod"),q(l,"id","duration"),q(l,"class","svelte-19usgod"),q(s,"class","timestamps svelte-19usgod"),q(e,"class","component-wrapper svelte-19usgod"),q(e,"data-testid",R=r[2]?"waveform-"+r[2]:"unlabelled-audio")},m(_,C){Me(_,e,C),Y(e,t),Y(t,i),r[29](i),Y(e,n),Y(e,s),Y(s,o),r[30](o),Y(s,a),Y(s,u),p&&p.m(u,null),Y(u,d),Y(u,l),r[31](l),Y(e,h),nt(g,e,null),E=!0},p(_,C){C[0]&1024&&yt(i,"height",_[10]?null:"58px"),_[0]==="edit"&&_[18]>0?p?p.p(_,C):(p=Ct(_),p.c(),p.m(u,d)):p&&(p.d(1),p=null);const L={};C[0]&1024&&(L.container=_[10]),C[0]&2048&&(L.waveform=_[11]),C[0]&65536&&(L.playing=_[16]),C[0]&131072&&(L.audio_duration=_[17]),C[0]&8&&(L.i18n=_[3]),C[0]&16&&(L.interactive=_[4]),C[0]&16&&(L.show_redo=_[4]),C[0]&512&&(L.handle_reset_value=_[9]),C[0]&256&&(L.waveform_options=_[8]),C[0]&64&&(L.trim_region_settings=_[6]),C[0]&32&&(L.editable=_[5]),!c&&C[0]&1&&(c=!0,L.mode=_[0],Xe(()=>c=!1)),!f&&C[0]&262144&&(f=!0,L.trimDuration=_[18],Xe(()=>f=!1)),!w&&C[0]&524288&&(w=!0,L.show_volume_slider=_[19],Xe(()=>w=!1)),g.$set(L),(!E||C[0]&4&&R!==(R=_[2]?"waveform-"+_[2]:"unlabelled-audio"))&&q(e,"data-testid",R)},i(_){E||(Pe(g.$$.fragment,_),E=!0)},o(_){Te(g.$$.fragment,_),E=!1},d(_){_&&Re(e),r[29](null),r[30](null),p&&p.d(),r[31](null),it(g)}}}function Tn(r){let e,t;return e=new Tt({props:{size:"small",$$slots:{default:[An]},$$scope:{ctx:r}}}),{c(){tt(e.$$.fragment)},m(i,n){nt(e,i,n),t=!0},p(i,n){const s={};n[1]&256&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){t||(Pe(e.$$.fragment,i),t=!0)},o(i){Te(e.$$.fragment,i),t=!1},d(i){it(e,i)}}}function Ct(r){let e,t=ze(r[18])+"",i;return{c(){e=K("time"),i=Mn(t),q(e,"id","trim-duration"),q(e,"class","svelte-19usgod")},m(n,s){Me(n,e,s),Y(e,i)},p(n,s){s[0]&262144&&t!==(t=ze(n[18])+"")&&Rn(i,t)},d(n){n&&Re(e)}}}function An(r){let e,t;return e=new Rt({}),{c(){tt(e.$$.fragment)},m(i,n){nt(e,i,n),t=!0},i(i){t||(Pe(e.$$.fragment,i),t=!0)},o(i){Te(e.$$.fragment,i),t=!1},d(i){it(e,i)}}}function Wn(r){let e,t,i,n,s,o,a,u,d;const l=[Tn,Pn],h=[];function g(c,f){return c[1]===null?0:c[15]?1:-1}return~(n=g(r))&&(s=h[n]=l[n](r)),{c(){e=K("audio"),i=ke(),s&&s.c(),o=yn(),q(e,"class","standard-player svelte-19usgod"),e.controls=!0,e.autoplay=t=r[7].autoplay,kt(e,"hidden",r[15])},m(c,f){Me(c,e,f),r[26](e),Me(c,i,f),~n&&h[n].m(c,f),Me(c,o,f),a=!0,u||(d=[Ze(e,"load",r[25]),Ze(e,"ended",r[27]),Ze(e,"play",r[28])],u=!0)},p(c,f){(!a||f[0]&128&&t!==(t=c[7].autoplay))&&(e.autoplay=t),(!a||f[0]&32768)&&kt(e,"hidden",c[15]);let w=n;n=g(c),n===w?~n&&h[n].p(c,f):(s&&(kn(),Te(h[w],1,1,()=>{h[w]=null}),wn()),~n?(s=h[n],s?s.p(c,f):(s=h[n]=l[n](c),s.c()),Pe(s,1),s.m(o.parentNode,o)):s=null)},i(c){a||(Pe(s),a=!0)},o(c){Te(s),a=!1},d(c){c&&(Re(e),Re(i),Re(o)),r[26](null),~n&&h[n].d(c),u=!1,En(d)}}}function xn(r,e,t){let i,n,{value:s=null}=e,{label:o}=e,{i18n:a}=e,{dispatch_blob:u=()=>Promise.resolve()}=e,{interactive:d=!1}=e,{editable:l=!0}=e,{trim_region_settings:h={}}=e,{waveform_settings:g}=e,{waveform_options:c}=e,{mode:f=""}=e,{loop:w}=e,{handle_reset_value:R=()=>{}}=e,E,p,M=!1,y,b,S,_=0,C=!1,L,U=!1;const W=Dn(),$=()=>{t(11,p=Ye.create({container:E,...g})),st(s?.url).then(m=>{if(m&&p)return p.load(m)})},ae=async(m,I)=>{t(0,f="");const Ae=p?.getDecodedData();Ae&&await Pi(Ae,m,I,g.sampleRate).then(async be=>{await u([be],"change"),p?.destroy(),t(10,E.innerHTML="",E)}),W("edit")};async function ue(m){U=!1,await st(m).then(I=>{!I||s?.is_stream||(c.show_recording_waveform?p?.load(I):L&&t(14,L.src=I,L))})}function P(m){if(!(!m||!m.is_stream||!m.url))if(pe.isSupported()&&!U){const I=new pe({maxBufferLength:1,maxMaxBufferLength:1,lowLatencyMode:!0});I.loadSource(m.url),I.attachMedia(L),I.on(pe.Events.MANIFEST_PARSED,function(){g.autoplay&&L.play()}),I.on(pe.Events.ERROR,function(Ae,be){if(console.error("HLS error:",Ae,be),be.fatal)switch(be.type){case pe.ErrorTypes.NETWORK_ERROR:console.error("Fatal network error encountered, trying to recover"),I.startLoad();break;case pe.ErrorTypes.MEDIA_ERROR:console.error("Fatal media error encountered, trying to recover"),I.recoverMediaError();break;default:console.error("Fatal error, cannot recover"),I.destroy();break}}),U=!0}else U||(t(14,L.src=m.url,L),g.autoplay&&L.play(),U=!0)}Sn(()=>{window.addEventListener("keydown",m=>{!p||C||(m.key==="ArrowRight"&&f!=="edit"?at(p,.1):m.key==="ArrowLeft"&&f!=="edit"&&at(p,-.1))})});function ce(m){bn.call(this,r,m)}function Z(m){he[m?"unshift":"push"](()=>{L=m,t(14,L)})}const fe=()=>W("stop"),T=()=>W("play");function k(m){he[m?"unshift":"push"](()=>{E=m,t(10,E),t(15,n),t(11,p),t(8,c),t(1,s)})}function x(m){he[m?"unshift":"push"](()=>{y=m,t(12,y),t(11,p)})}function me(m){he[m?"unshift":"push"](()=>{b=m,t(13,b),t(11,p)})}function v(m){f=m,t(0,f)}function Q(m){_=m,t(18,_)}function de(m){C=m,t(19,C)}return r.$$set=m=>{"value"in m&&t(1,s=m.value),"label"in m&&t(2,o=m.label),"i18n"in m&&t(3,a=m.i18n),"dispatch_blob"in m&&t(22,u=m.dispatch_blob),"interactive"in m&&t(4,d=m.interactive),"editable"in m&&t(5,l=m.editable),"trim_region_settings"in m&&t(6,h=m.trim_region_settings),"waveform_settings"in m&&t(7,g=m.waveform_settings),"waveform_options"in m&&t(8,c=m.waveform_options),"mode"in m&&t(0,f=m.mode),"loop"in m&&t(23,w=m.loop),"handle_reset_value"in m&&t(9,R=m.handle_reset_value)},r.$$.update=()=>{r.$$.dirty[0]&2&&t(24,i=s?.url),r.$$.dirty[0]&258&&t(15,n=c.show_recording_waveform&&!s?.is_stream),r.$$.dirty[0]&35840&&n&&E!==void 0&&E!==null&&(p!==void 0&&p.destroy(),t(10,E.innerHTML="",E),$(),t(16,M=!1)),r.$$.dirty[0]&10240&&p?.on("decode",m=>{t(17,S=m),b&&t(13,b.textContent=ze(m),b)}),r.$$.dirty[0]&6144&&p?.on("timeupdate",m=>y&&t(12,y.textContent=ze(m),y)),r.$$.dirty[0]&2176&&p?.on("ready",()=>{g.autoplay?p?.play():p?.stop()}),r.$$.dirty[0]&8390656&&p?.on("finish",()=>{w?p?.play():(t(16,M=!1),W("stop"))}),r.$$.dirty[0]&2048&&p?.on("pause",()=>{t(16,M=!1),W("pause")}),r.$$.dirty[0]&2048&&p?.on("play",()=>{t(16,M=!0),W("play")}),r.$$.dirty[0]&2048&&p?.on("load",()=>{W("load")}),r.$$.dirty[0]&16777216&&i&&ue(i),r.$$.dirty[0]&16386&&L&&s?.is_stream&&P(s)},[f,s,o,a,d,l,h,g,c,R,E,p,y,b,L,n,M,S,_,C,W,ae,u,w,i,ce,Z,fe,T,k,x,me,v,Q,de]}class On extends vn{constructor(e){super(),Cn(this,e,xn,Wn,Ln,{value:1,label:2,i18n:3,dispatch_blob:22,interactive:4,editable:5,trim_region_settings:6,waveform_settings:7,waveform_options:8,mode:0,loop:23,handle_reset_value:9},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),G()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),G()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),G()}get dispatch_blob(){return this.$$.ctx[22]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),G()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),G()}get editable(){return this.$$.ctx[5]}set editable(e){this.$$set({editable:e}),G()}get trim_region_settings(){return this.$$.ctx[6]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),G()}get waveform_settings(){return this.$$.ctx[7]}set waveform_settings(e){this.$$set({waveform_settings:e}),G()}get waveform_options(){return this.$$.ctx[8]}set waveform_options(e){this.$$set({waveform_options:e}),G()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),G()}get loop(){return this.$$.ctx[23]}set loop(e){this.$$set({loop:e}),G()}get handle_reset_value(){return this.$$.ctx[9]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),G()}}const Gn=On;export{Gn as A,_n as W,Ye as a,Pi as p,at as s};
//# sourceMappingURL=AudioPlayer-73PUar21.js.map
