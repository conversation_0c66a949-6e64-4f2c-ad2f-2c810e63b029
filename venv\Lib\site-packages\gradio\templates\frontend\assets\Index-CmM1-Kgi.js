import{B as W}from"./Block-CJdXVpa7.js";import{B as X}from"./BlockTitle-Ct-h8ev5.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./index-B7J2Z2jS.js";import{S as Y}from"./index-B1FJGuzG.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import"./Info-IGMCDo7y.js";import"./MarkdownCode-CkSMBRHJ.js";import"./prism-python-MMh3z1bK.js";import"./svelte/svelte.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:Z,append:B,assign:y,attr:w,create_component:z,destroy_component:A,destroy_each:p,detach:J,element:j,ensure_array_like:H,flush:h,get_spread_object:x,get_spread_update:$,init:ee,insert:N,listen:I,mount_component:D,not_equal:te,run_all:le,set_data:M,space:q,text:P,toggle_class:T,transition_in:F,transition_out:G}=window.__gradio__svelte__internal;function K(t,e,l){const n=t.slice();return n[21]=e[l][0],n[22]=e[l][1],n[24]=l,n}function ie(t){let e;return{c(){e=P(t[9])},m(l,n){N(l,e,n)},p(l,n){n&512&&M(e,l[9])},d(l){l&&J(e)}}}function L(t){let e,l,n,a,c,k,g,r=t[21]+"",d,o,i,u;function b(){return t[18](t[22])}function v(...f){return t[19](t[24],t[22],...f)}function _(...f){return t[20](t[22],t[24],...f)}return{c(){e=j("label"),l=j("input"),k=q(),g=j("span"),d=P(r),o=q(),l.disabled=t[13],l.checked=n=t[0].includes(t[22]),w(l,"type","checkbox"),w(l,"name",a=t[22]?.toString()),w(l,"title",c=t[22]?.toString()),w(l,"class","svelte-1e02hys"),w(g,"class","ml-2 svelte-1e02hys"),w(e,"class","svelte-1e02hys"),T(e,"disabled",t[13]),T(e,"selected",t[0].includes(t[22]))},m(f,m){N(f,e,m),B(e,l),B(e,k),B(e,g),B(g,d),B(e,o),i||(u=[I(l,"change",b),I(l,"input",v),I(l,"keydown",_)],i=!0)},p(f,m){t=f,m&8192&&(l.disabled=t[13]),m&33&&n!==(n=t[0].includes(t[22]))&&(l.checked=n),m&32&&a!==(a=t[22]?.toString())&&w(l,"name",a),m&32&&c!==(c=t[22]?.toString())&&w(l,"title",c),m&32&&r!==(r=t[21]+"")&&M(d,r),m&8192&&T(e,"disabled",t[13]),m&33&&T(e,"selected",t[0].includes(t[22]))},d(f){f&&J(e),i=!1,le(u)}}}function se(t){let e,l,n,a,c,k;const g=[{autoscroll:t[1].autoscroll},{i18n:t[1].i18n},t[12]];let r={};for(let i=0;i<g.length;i+=1)r=y(r,g[i]);e=new Y({props:r}),e.$on("clear_status",t[17]),n=new X({props:{show_label:t[11],info:t[10],$$slots:{default:[ie]},$$scope:{ctx:t}}});let d=H(t[5]),o=[];for(let i=0;i<d.length;i+=1)o[i]=L(K(t,d,i));return{c(){z(e.$$.fragment),l=q(),z(n.$$.fragment),a=q(),c=j("div");for(let i=0;i<o.length;i+=1)o[i].c();w(c,"class","wrap svelte-1e02hys"),w(c,"data-testid","checkbox-group")},m(i,u){D(e,i,u),N(i,l,u),D(n,i,u),N(i,a,u),N(i,c,u);for(let b=0;b<o.length;b+=1)o[b]&&o[b].m(c,null);k=!0},p(i,u){const b=u&4098?$(g,[u&2&&{autoscroll:i[1].autoscroll},u&2&&{i18n:i[1].i18n},u&4096&&x(i[12])]):{};e.$set(b);const v={};if(u&2048&&(v.show_label=i[11]),u&1024&&(v.info=i[10]),u&33554944&&(v.$$scope={dirty:u,ctx:i}),n.$set(v),u&24611){d=H(i[5]);let _;for(_=0;_<d.length;_+=1){const f=K(i,d,_);o[_]?o[_].p(f,u):(o[_]=L(f),o[_].c(),o[_].m(c,null))}for(;_<o.length;_+=1)o[_].d(1);o.length=d.length}},i(i){k||(F(e.$$.fragment,i),F(n.$$.fragment,i),k=!0)},o(i){G(e.$$.fragment,i),G(n.$$.fragment,i),k=!1},d(i){i&&(J(l),J(a),J(c)),A(e,i),A(n,i),p(o,i)}}}function ne(t){let e,l;return e=new W({props:{visible:t[4],elem_id:t[2],elem_classes:t[3],type:"fieldset",container:t[6],scale:t[7],min_width:t[8],$$slots:{default:[se]},$$scope:{ctx:t}}}),{c(){z(e.$$.fragment)},m(n,a){D(e,n,a),l=!0},p(n,[a]){const c={};a&16&&(c.visible=n[4]),a&4&&(c.elem_id=n[2]),a&8&&(c.elem_classes=n[3]),a&64&&(c.container=n[6]),a&128&&(c.scale=n[7]),a&256&&(c.min_width=n[8]),a&33570339&&(c.$$scope={dirty:a,ctx:n}),e.$set(c)},i(n){l||(F(e.$$.fragment,n),l=!0)},o(n){G(e.$$.fragment,n),l=!1},d(n){A(e,n)}}}function ae(t,e,l){let n,{gradio:a}=e,{elem_id:c=""}=e,{elem_classes:k=[]}=e,{visible:g=!0}=e,{value:r=[]}=e,{choices:d}=e,{container:o=!0}=e,{scale:i=null}=e,{min_width:u=void 0}=e,{label:b=a.i18n("checkbox.checkbox_group")}=e,{info:v=void 0}=e,{show_label:_=!0}=e,{loading_status:f}=e,{interactive:m=!0}=e,{old_value:O=r.slice()}=e;function C(s){r.includes(s)?l(0,r=r.filter(S=>S!==s)):l(0,r=[...r,s]),a.dispatch("input")}const Q=()=>a.dispatch("clear_status",f),R=s=>C(s),U=(s,S,E)=>a.dispatch("select",{index:s,value:S,selected:E.currentTarget.checked}),V=(s,S,E)=>{E.key==="Enter"&&(C(s),a.dispatch("select",{index:S,value:s,selected:!r.includes(s)}))};return t.$$set=s=>{"gradio"in s&&l(1,a=s.gradio),"elem_id"in s&&l(2,c=s.elem_id),"elem_classes"in s&&l(3,k=s.elem_classes),"visible"in s&&l(4,g=s.visible),"value"in s&&l(0,r=s.value),"choices"in s&&l(5,d=s.choices),"container"in s&&l(6,o=s.container),"scale"in s&&l(7,i=s.scale),"min_width"in s&&l(8,u=s.min_width),"label"in s&&l(9,b=s.label),"info"in s&&l(10,v=s.info),"show_label"in s&&l(11,_=s.show_label),"loading_status"in s&&l(12,f=s.loading_status),"interactive"in s&&l(16,m=s.interactive),"old_value"in s&&l(15,O=s.old_value)},t.$$.update=()=>{t.$$.dirty&65536&&l(13,n=!m),t.$$.dirty&32771&&JSON.stringify(O)!==JSON.stringify(r)&&(l(15,O=r),a.dispatch("change"))},[r,a,c,k,g,d,o,i,u,b,v,_,f,n,C,O,m,Q,R,U,V]}class ve extends Z{constructor(e){super(),ee(this,e,ae,ne,te,{gradio:1,elem_id:2,elem_classes:3,visible:4,value:0,choices:5,container:6,scale:7,min_width:8,label:9,info:10,show_label:11,loading_status:12,interactive:16,old_value:15})}get gradio(){return this.$$.ctx[1]}set gradio(e){this.$$set({gradio:e}),h()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),h()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get choices(){return this.$$.ctx[5]}set choices(e){this.$$set({choices:e}),h()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[7]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[8]}set min_width(e){this.$$set({min_width:e}),h()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),h()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),h()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),h()}get loading_status(){return this.$$.ctx[12]}set loading_status(e){this.$$set({loading_status:e}),h()}get interactive(){return this.$$.ctx[16]}set interactive(e){this.$$set({interactive:e}),h()}get old_value(){return this.$$.ctx[15]}set old_value(e){this.$$set({old_value:e}),h()}}export{ve as default};
//# sourceMappingURL=Index-CmM1-Kgi.js.map
