{"version": 3, "file": "index-tFQomdd2.js", "sources": ["../../../../node_modules/.pnpm/dequal@2.0.2/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": ["has", "find", "iter", "tar", "key", "dequal", "foo", "bar", "ctor", "len", "tmp"], "mappings": "AAAA,IAAIA,EAAM,OAAO,UAAU,eAE3B,SAASC,EAAKC,EAAMC,EAAKC,EAAK,CAC7B,IAAKA,KAAOF,EAAK,OAChB,GAAIG,EAAOD,EAAKD,CAAG,EAAG,OAAOC,CAE/B,CAEO,SAASC,EAAOC,EAAKC,EAAK,CAChC,IAAIC,EAAMC,EAAKC,EACf,GAAIJ,IAAQC,EAAK,MAAO,GAExB,GAAID,GAAOC,IAAQC,EAAKF,EAAI,eAAiBC,EAAI,YAAa,CAC7D,GAAIC,IAAS,KAAM,OAAOF,EAAI,YAAcC,EAAI,UAChD,GAAIC,IAAS,OAAQ,OAAOF,EAAI,aAAeC,EAAI,WAEnD,GAAIC,IAAS,MAAO,CACnB,IAAKC,EAAIH,EAAI,UAAYC,EAAI,OAC5B,KAAOE,KAASJ,EAAOC,EAAIG,CAAG,EAAGF,EAAIE,CAAG,CAAC,GAAE,CAE5C,OAAOA,IAAQ,EACf,CAED,GAAID,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EACFC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,EAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACH,EAAI,IAAIG,CAAG,EAAG,MAAO,GAE3B,MAAO,EACP,CAED,GAAIF,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EAAI,CAAC,EACPC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,EAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACL,EAAOI,EAAI,CAAC,EAAGF,EAAI,IAAIG,CAAG,CAAC,EAC/B,MAAO,GAGT,MAAO,EACP,CAED,GAAIF,IAAS,YACZF,EAAM,IAAI,WAAWA,CAAG,EACxBC,EAAM,IAAI,WAAWA,CAAG,UACdC,IAAS,SAAU,CAC7B,IAAKC,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAI,QAAQG,CAAG,IAAMF,EAAI,QAAQE,CAAG,GAAE,CAEvD,OAAOA,IAAQ,EACf,CAED,GAAI,YAAY,OAAOH,CAAG,EAAG,CAC5B,IAAKG,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAIG,CAAG,IAAMF,EAAIE,CAAG,GAAE,CAEvC,OAAOA,IAAQ,EACf,CAED,GAAI,CAACD,GAAQ,OAAOF,GAAQ,SAAU,CACrCG,EAAM,EACN,IAAKD,KAAQF,EAEZ,GADIN,EAAI,KAAKM,EAAKE,CAAI,GAAK,EAAEC,GAAO,CAACT,EAAI,KAAKO,EAAKC,CAAI,GACnD,EAAEA,KAAQD,IAAQ,CAACF,EAAOC,EAAIE,CAAI,EAAGD,EAAIC,CAAI,CAAC,EAAG,MAAO,GAE7D,OAAO,OAAO,KAAKD,CAAG,EAAE,SAAWE,CACnC,CACD,CAED,OAAOH,IAAQA,GAAOC,IAAQA,CAC/B", "x_google_ignoreList": [0]}