import { c as create_ssr_component, o as onDestroy, v as validate_component, e as escape, d as add_attribute } from './ssr-C3HYbsxA.js';
import { m as mt, N as xt, $ as _t } from './2-DJbI4FWc.js';
export { default as BaseExample } from './Example-CmZTmcbg.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const T=(A,t)=>{if(A===null||A==="")return !0;const e=t?/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/:/^\d{4}-\d{2}-\d{2}$/,o=A.match(e)!==null,n=A.match(/^(?:\s*now\s*(?:-\s*\d+\s*[dmhs])?)?\s*$/)!==null;return o||n},P=(A,t)=>{if(!A||A===""){const o=new Date;return {selected_date:o,current_year:o.getFullYear(),current_month:o.getMonth(),selected_hour:o.getHours(),selected_minute:o.getMinutes(),selected_second:o.getSeconds(),is_pm:o.getHours()>=12}}try{let o=A;!t&&A.match(/^\d{4}-\d{2}-\d{2}$/)&&(o+=" 00:00:00");const n=new Date(o.replace(" ","T"));if(!isNaN(n.getTime()))return {selected_date:n,current_year:n.getFullYear(),current_month:n.getMonth(),selected_hour:n.getHours(),selected_minute:n.getMinutes(),selected_second:n.getSeconds(),is_pm:n.getHours()>=12}}catch{}const e=new Date;return {selected_date:e,current_year:e.getFullYear(),current_month:e.getMonth(),selected_hour:e.getHours(),selected_minute:e.getMinutes(),selected_second:e.getSeconds(),is_pm:e.getHours()>=12}};const Z={code:".label-content.svelte-ywg1ch{display:flex;justify-content:space-between;align-items:flex-start}button.svelte-ywg1ch{cursor:pointer;color:var(--body-text-color-subdued)}button.svelte-ywg1ch:hover{color:var(--body-text-color)}.svelte-ywg1ch::placeholder{color:var(--input-placeholder-color)}.timebox.svelte-ywg1ch{flex-grow:1;flex-shrink:1;display:flex;position:relative;background:var(--input-background-fill)}.timebox.svelte-ywg1ch svg{height:18px}.time.svelte-ywg1ch{padding:var(--input-padding);color:var(--body-text-color);font-weight:var(--input-text-weight);font-size:var(--input-text-size);line-height:var(--line-sm);outline:none;flex-grow:1;background:none;border:var(--input-border-width) solid var(--input-border-color);border-right:none;border-top-left-radius:var(--input-radius);border-bottom-left-radius:var(--input-radius);box-shadow:var(--input-shadow)}.time.svelte-ywg1ch:disabled{border-right:var(--input-border-width) solid var(--input-border-color);border-top-right-radius:var(--input-radius);border-bottom-right-radius:var(--input-radius)}.time.invalid.svelte-ywg1ch{color:var(--body-text-color-subdued)}.calendar.svelte-ywg1ch{display:inline-flex;justify-content:center;align-items:center;transition:var(--button-transition);box-shadow:var(--button-primary-shadow);text-align:center;background:var(--button-secondary-background-fill);color:var(--button-secondary-text-color);font-weight:var(--button-large-text-weight);font-size:var(--button-large-text-size);border-top-right-radius:var(--input-radius);border-bottom-right-radius:var(--input-radius);padding:var(--size-2);border:var(--input-border-width) solid var(--input-border-color)}.calendar.svelte-ywg1ch:hover{background:var(--button-secondary-background-fill-hover);box-shadow:var(--button-primary-shadow-hover)}.calendar.svelte-ywg1ch:active{box-shadow:var(--button-primary-shadow-active)}",map:'{"version":3,"file":"Index.svelte","sources":["Index.svelte"],"sourcesContent":["<script context=\\"module\\" lang=\\"ts\\">export { default as BaseExample } from \\"./Example.svelte\\";\\n<\/script>\\n\\n<script lang=\\"ts\\">import { Block, BlockTitle } from \\"@gradio/atoms\\";\\nimport { Calendar } from \\"@gradio/icons\\";\\nimport { onDestroy } from \\"svelte\\";\\nimport DateTimePicker from \\"./DateTimePicker.svelte\\";\\nimport { format_date, date_is_valid_format, parse_date_value } from \\"./utils\\";\\nexport let gradio;\\nexport let label = \\"Time\\";\\nexport let show_label = true;\\nexport let info = void 0;\\nexport let interactive;\\n$: disabled = !interactive;\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let value = \\"\\";\\nlet old_value = value;\\nexport let scale = null;\\nexport let min_width = void 0;\\nexport let include_time = true;\\nlet show_picker = false;\\nlet picker_ref;\\nlet input_ref;\\nlet calendar_button_ref;\\nlet picker_position = { top: 0, left: 0 };\\n$: if (value !== old_value) {\\n    old_value = value;\\n    entered_value = value;\\n    update_picker_from_value();\\n    gradio.dispatch(\\"change\\");\\n}\\nlet entered_value = value;\\n$: valid = date_is_valid_format(entered_value, include_time);\\nconst submit_values = () => {\\n    if (entered_value === value)\\n        return;\\n    if (!date_is_valid_format(entered_value, include_time))\\n        return;\\n    old_value = value = entered_value;\\n    gradio.dispatch(\\"change\\");\\n};\\nlet current_year = ( /* @__PURE__ */new Date()).getFullYear();\\nlet current_month = ( /* @__PURE__ */new Date()).getMonth();\\nlet selected_date = /* @__PURE__ */ new Date();\\nlet selected_hour = ( /* @__PURE__ */new Date()).getHours();\\nlet selected_minute = ( /* @__PURE__ */new Date()).getMinutes();\\nlet selected_second = ( /* @__PURE__ */new Date()).getSeconds();\\nlet is_pm = selected_hour >= 12;\\nconst update_picker_from_value = () => {\\n    const parsed = parse_date_value(entered_value, include_time);\\n    selected_date = parsed.selected_date;\\n    current_year = parsed.current_year;\\n    current_month = parsed.current_month;\\n    selected_hour = parsed.selected_hour;\\n    selected_minute = parsed.selected_minute;\\n    selected_second = parsed.selected_second;\\n    is_pm = parsed.is_pm;\\n};\\nconst calculate_picker_position = () => {\\n    if (calendar_button_ref) {\\n        const rect = calendar_button_ref.getBoundingClientRect();\\n        picker_position = {\\n            top: rect.bottom + 4,\\n            left: rect.right - 280\\n        };\\n    }\\n};\\nconst toggle_picker = (event) => {\\n    if (!disabled) {\\n        event.stopPropagation();\\n        show_picker = !show_picker;\\n        if (show_picker) {\\n            update_picker_from_value();\\n            calculate_picker_position();\\n            setTimeout(() => {\\n                if (typeof window !== \\"undefined\\") {\\n                    window.addEventListener(\\"click\\", handle_click_outside);\\n                    window.addEventListener(\\"scroll\\", handle_scroll, true);\\n                }\\n            }, 10);\\n        }\\n        else if (typeof window !== \\"undefined\\") {\\n            window.removeEventListener(\\"click\\", handle_click_outside);\\n            window.removeEventListener(\\"scroll\\", handle_scroll, true);\\n        }\\n    }\\n};\\nconst close_picker = () => {\\n    show_picker = false;\\n    if (typeof window !== \\"undefined\\") {\\n        window.removeEventListener(\\"click\\", handle_click_outside);\\n        window.removeEventListener(\\"scroll\\", handle_scroll, true);\\n    }\\n};\\nconst handle_click_outside = (event) => {\\n    if (show_picker && picker_ref && !picker_ref.contains(event.target) && calendar_button_ref && !calendar_button_ref.contains(event.target)) {\\n        close_picker();\\n    }\\n};\\nconst handle_scroll = () => {\\n    if (show_picker) {\\n        calculate_picker_position();\\n    }\\n};\\nconst handle_picker_update = (event) => {\\n    entered_value = event.detail.formatted;\\n    submit_values();\\n};\\nconst handle_picker_clear = () => {\\n    entered_value = \\"\\";\\n    value = \\"\\";\\n    close_picker();\\n    gradio.dispatch(\\"change\\");\\n};\\nonDestroy(() => {\\n    if (typeof window !== \\"undefined\\") {\\n        window.removeEventListener(\\"click\\", handle_click_outside);\\n        window.removeEventListener(\\"scroll\\", handle_scroll, true);\\n    }\\n});\\nupdate_picker_from_value();\\n<\/script>\\n\\n<Block\\n\\t{visible}\\n\\t{elem_id}\\n\\t{elem_classes}\\n\\t{scale}\\n\\t{min_width}\\n\\tallow_overflow={false}\\n\\tpadding={true}\\n>\\n\\t<div class=\\"label-content\\">\\n\\t\\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\\n\\t</div>\\n\\t<div class=\\"timebox\\">\\n\\t\\t<input\\n\\t\\t\\tbind:this={input_ref}\\n\\t\\t\\tclass=\\"time\\"\\n\\t\\t\\tbind:value={entered_value}\\n\\t\\t\\tclass:invalid={!valid}\\n\\t\\t\\ton:keydown={(evt) => {\\n\\t\\t\\t\\tif (evt.key === \\"Enter\\") {\\n\\t\\t\\t\\t\\tsubmit_values();\\n\\t\\t\\t\\t\\tgradio.dispatch(\\"submit\\");\\n\\t\\t\\t\\t}\\n\\t\\t\\t}}\\n\\t\\t\\ton:blur={submit_values}\\n\\t\\t\\t{disabled}\\n\\t\\t\\tplaceholder={include_time ? \\"YYYY-MM-DD HH:MM:SS\\" : \\"YYYY-MM-DD\\"}\\n\\t\\t/>\\n\\n\\t\\t{#if interactive}\\n\\t\\t\\t<button\\n\\t\\t\\t\\tbind:this={calendar_button_ref}\\n\\t\\t\\t\\tclass=\\"calendar\\"\\n\\t\\t\\t\\t{disabled}\\n\\t\\t\\t\\ton:click={toggle_picker}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<Calendar />\\n\\t\\t\\t</button>\\n\\t\\t{/if}\\n\\t</div>\\n\\n\\t{#if show_picker}\\n\\t\\t<div bind:this={picker_ref}>\\n\\t\\t\\t<DateTimePicker\\n\\t\\t\\t\\tbind:selected_date\\n\\t\\t\\t\\tbind:current_year\\n\\t\\t\\t\\tbind:current_month\\n\\t\\t\\t\\tbind:selected_hour\\n\\t\\t\\t\\tbind:selected_minute\\n\\t\\t\\t\\tbind:selected_second\\n\\t\\t\\t\\tbind:is_pm\\n\\t\\t\\t\\t{include_time}\\n\\t\\t\\t\\tposition={picker_position}\\n\\t\\t\\t\\ton:update={handle_picker_update}\\n\\t\\t\\t\\ton:clear={handle_picker_clear}\\n\\t\\t\\t\\ton:close={close_picker}\\n\\t\\t\\t/>\\n\\t\\t</div>\\n\\t{/if}\\n</Block>\\n\\n<style>\\n\\t.label-content {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: space-between;\\n\\t\\talign-items: flex-start;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tcursor: pointer;\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t}\\n\\n\\tbutton:hover {\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\n\\t::placeholder {\\n\\t\\tcolor: var(--input-placeholder-color);\\n\\t}\\n\\n\\t.timebox {\\n\\t\\tflex-grow: 1;\\n\\t\\tflex-shrink: 1;\\n\\t\\tdisplay: flex;\\n\\t\\tposition: relative;\\n\\t\\tbackground: var(--input-background-fill);\\n\\t}\\n\\n\\t.timebox :global(svg) {\\n\\t\\theight: 18px;\\n\\t}\\n\\n\\t.time {\\n\\t\\tpadding: var(--input-padding);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tfont-weight: var(--input-text-weight);\\n\\t\\tfont-size: var(--input-text-size);\\n\\t\\tline-height: var(--line-sm);\\n\\t\\toutline: none;\\n\\t\\tflex-grow: 1;\\n\\t\\tbackground: none;\\n\\t\\tborder: var(--input-border-width) solid var(--input-border-color);\\n\\t\\tborder-right: none;\\n\\t\\tborder-top-left-radius: var(--input-radius);\\n\\t\\tborder-bottom-left-radius: var(--input-radius);\\n\\t\\tbox-shadow: var(--input-shadow);\\n\\t}\\n\\n\\t.time:disabled {\\n\\t\\tborder-right: var(--input-border-width) solid var(--input-border-color);\\n\\t\\tborder-top-right-radius: var(--input-radius);\\n\\t\\tborder-bottom-right-radius: var(--input-radius);\\n\\t}\\n\\n\\t.time.invalid {\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t}\\n\\n\\t.calendar {\\n\\t\\tdisplay: inline-flex;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\ttransition: var(--button-transition);\\n\\t\\tbox-shadow: var(--button-primary-shadow);\\n\\t\\ttext-align: center;\\n\\t\\tbackground: var(--button-secondary-background-fill);\\n\\t\\tcolor: var(--button-secondary-text-color);\\n\\t\\tfont-weight: var(--button-large-text-weight);\\n\\t\\tfont-size: var(--button-large-text-size);\\n\\t\\tborder-top-right-radius: var(--input-radius);\\n\\t\\tborder-bottom-right-radius: var(--input-radius);\\n\\t\\tpadding: var(--size-2);\\n\\t\\tborder: var(--input-border-width) solid var(--input-border-color);\\n\\t}\\n\\n\\t.calendar:hover {\\n\\t\\tbackground: var(--button-secondary-background-fill-hover);\\n\\t\\tbox-shadow: var(--button-primary-shadow-hover);\\n\\t}\\n\\n\\t.calendar:active {\\n\\t\\tbox-shadow: var(--button-primary-shadow-active);\\n\\t}</style>\\n"],"names":[],"mappings":"AA2LC,4BAAe,CACd,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,UACd,CAEA,oBAAO,CACN,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,oBAAM,MAAO,CACZ,KAAK,CAAE,IAAI,iBAAiB,CAC7B,eAEA,aAAc,CACb,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,sBAAS,CACR,SAAS,CAAE,CAAC,CACZ,WAAW,CAAE,CAAC,CACd,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,IAAI,uBAAuB,CACxC,CAEA,sBAAQ,CAAS,GAAK,CACrB,MAAM,CAAE,IACT,CAEA,mBAAM,CACL,OAAO,CAAE,IAAI,eAAe,CAAC,CAC7B,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,WAAW,CAAE,IAAI,mBAAmB,CAAC,CACrC,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,SAAS,CAAE,CAAC,CACZ,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CACjE,YAAY,CAAE,IAAI,CAClB,sBAAsB,CAAE,IAAI,cAAc,CAAC,CAC3C,yBAAyB,CAAE,IAAI,cAAc,CAAC,CAC9C,UAAU,CAAE,IAAI,cAAc,CAC/B,CAEA,mBAAK,SAAU,CACd,YAAY,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CACvE,uBAAuB,CAAE,IAAI,cAAc,CAAC,CAC5C,0BAA0B,CAAE,IAAI,cAAc,CAC/C,CAEA,KAAK,sBAAS,CACb,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,uBAAU,CACT,OAAO,CAAE,WAAW,CACpB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,UAAU,CAAE,MAAM,CAClB,UAAU,CAAE,IAAI,kCAAkC,CAAC,CACnD,KAAK,CAAE,IAAI,6BAA6B,CAAC,CACzC,WAAW,CAAE,IAAI,0BAA0B,CAAC,CAC5C,SAAS,CAAE,IAAI,wBAAwB,CAAC,CACxC,uBAAuB,CAAE,IAAI,cAAc,CAAC,CAC5C,0BAA0B,CAAE,IAAI,cAAc,CAAC,CAC/C,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CACjE,CAEA,uBAAS,MAAO,CACf,UAAU,CAAE,IAAI,wCAAwC,CAAC,CACzD,UAAU,CAAE,IAAI,6BAA6B,CAC9C,CAEA,uBAAS,OAAQ,CAChB,UAAU,CAAE,IAAI,8BAA8B,CAC/C"}'},X=create_ssr_component((A,t,e,o)=>{let n,u,{gradio:i}=t,{label:l="Time"}=t,{show_label:s=!0}=t,{info:r=void 0}=t,{interactive:d}=t,{elem_id:v=""}=t,{elem_classes:C=[]}=t,{visible:m=!0}=t,{value:a=""}=t,y=a,{scale:b=null}=t,{min_width:g=void 0}=t,{include_time:h=!0}=t,M,z,f=a;const B=()=>{const c=P(f,h);c.selected_date,c.current_year,c.current_month,c.selected_hour,c.selected_minute,c.selected_second,c.is_pm;},U=c=>{},S=()=>{};onDestroy(()=>{typeof window<"u"&&(window.removeEventListener("click",U),window.removeEventListener("scroll",S,!0));}),B(),t.gradio===void 0&&e.gradio&&i!==void 0&&e.gradio(i),t.label===void 0&&e.label&&l!==void 0&&e.label(l),t.show_label===void 0&&e.show_label&&s!==void 0&&e.show_label(s),t.info===void 0&&e.info&&r!==void 0&&e.info(r),t.interactive===void 0&&e.interactive&&d!==void 0&&e.interactive(d),t.elem_id===void 0&&e.elem_id&&v!==void 0&&e.elem_id(v),t.elem_classes===void 0&&e.elem_classes&&C!==void 0&&e.elem_classes(C),t.visible===void 0&&e.visible&&m!==void 0&&e.visible(m),t.value===void 0&&e.value&&a!==void 0&&e.value(a),t.scale===void 0&&e.scale&&b!==void 0&&e.scale(b),t.min_width===void 0&&e.min_width&&g!==void 0&&e.min_width(g),t.include_time===void 0&&e.include_time&&h!==void 0&&e.include_time(h),A.css.add(Z);let E,w,O=A.head;do E=!0,A.head=O,n=!d,a!==y&&(y=a,f=a,B(),i.dispatch("change")),u=T(f,h),w=`${validate_component(mt,"Block").$$render(A,{visible:m,elem_id:v,elem_classes:C,scale:b,min_width:g,allow_overflow:!1,padding:!0},{},{default:()=>`<div class="label-content svelte-ywg1ch">${validate_component(xt,"BlockTitle").$$render(A,{show_label:s,info:r},{},{default:()=>`${escape(l)}`})}</div> <div class="timebox svelte-ywg1ch"><input class="${["time svelte-ywg1ch",u?"":"invalid"].join(" ").trim()}" ${n?"disabled":""}${add_attribute("placeholder",h?"YYYY-MM-DD HH:MM:SS":"YYYY-MM-DD",0)}${add_attribute("this",M,0)}${add_attribute("value",f,0)}> ${d?`<button class="calendar svelte-ywg1ch" ${n?"disabled":""}${add_attribute("this",z,0)}>${validate_component(_t,"Calendar").$$render(A,{},{},{})}</button>`:""}</div> `})}`;while(!E);return w});

export { X as default };
//# sourceMappingURL=Index54-DQyUaaDz.js.map
