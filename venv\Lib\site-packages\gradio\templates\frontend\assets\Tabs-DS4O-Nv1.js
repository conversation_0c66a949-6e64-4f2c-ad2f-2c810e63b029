import{w as ne}from"./index-B7J2Z2jS.js";const{SvelteComponent:Ie,append:X,attr:C,detach:Se,init:qe,insert:Ne,noop:Y,safe_not_equal:je,svg_element:P}=window.__gradio__svelte__internal;function Ee(t){let e,l,i,_;return{c(){e=P("svg"),l=P("circle"),i=P("circle"),_=P("circle"),C(l,"cx","2.5"),C(l,"cy","8"),C(l,"r","1.5"),C(l,"fill","currentColor"),C(i,"cx","8"),C(i,"cy","8"),C(i,"r","1.5"),C(i,"fill","currentColor"),C(_,"cx","13.5"),C(_,"cy","8"),C(_,"r","1.5"),C(_,"fill","currentColor"),C(e,"width","16"),C(e,"height","16"),C(e,"viewBox","0 0 16 16"),C(e,"fill","none"),C(e,"xmlns","http://www.w3.org/2000/svg")},m(r,a){Ne(r,e,a),X(e,l),X(e,i),X(e,_)},p:Y,i:Y,o:Y,d(r){r&&Se(e)}}}class Oe extends Ie{constructor(e){super(),qe(this,e,null,Ee,je,{})}}const{SvelteComponent:Re,append:T,attr:m,binding_callbacks:Z,check_outros:Ae,component_subscribe:se,create_component:De,create_slot:Me,destroy_component:Fe,destroy_each:y,detach:E,element:N,empty:$,ensure_array_like:A,flush:M,get_all_dirty_from_scope:Ge,get_slot_changes:He,group_outros:Je,init:Ke,insert:O,listen:H,mount_component:Le,run_all:Pe,safe_not_equal:Qe,set_data:x,set_store_value:F,set_style:oe,space:R,stop_propagation:Ue,text:ee,toggle_class:I,transition_in:G,transition_out:Q,update_slot_base:Ve}=window.__gradio__svelte__internal,{setContext:We,createEventDispatcher:Xe,tick:Ye,onMount:Ze}=window.__gradio__svelte__internal;function _e(t,e,l){const i=t.slice();return i[33]=e[l],i}function ae(t,e,l){const i=t.slice();return i[33]=e[l],i[37]=l,i}function fe(t,e,l){const i=t.slice();return i[33]=e[l],i[38]=e,i[37]=l,i}function ce(t){let e,l,i,_,r,a,f,d,u,c,v,z,j,b=A(t[3]),h=[];for(let s=0;s<b.length;s+=1)h[s]=ue(fe(t,b,s));let S=A(t[7]),g=[];for(let s=0;s<S.length;s+=1)g[s]=be(ae(t,S,s));d=new Oe({});let B=A(t[8]),w=[];for(let s=0;s<B.length;s+=1)w[s]=me(_e(t,B,s));return{c(){e=N("div"),l=N("div");for(let s=0;s<h.length;s+=1)h[s].c();i=R(),_=N("div");for(let s=0;s<g.length;s+=1)g[s].c();r=R(),a=N("span"),f=N("button"),De(d.$$.fragment),u=R(),c=N("div");for(let s=0;s<w.length;s+=1)w[s].c();m(l,"class","tab-container visually-hidden svelte-1tcem6n"),m(l,"aria-hidden","true"),m(_,"class","tab-container svelte-1tcem6n"),m(_,"role","tablist"),m(f,"class","svelte-1tcem6n"),I(f,"overflow-item-selected",t[12]),m(c,"class","overflow-dropdown svelte-1tcem6n"),I(c,"hide",!t[9]),m(a,"class","overflow-menu svelte-1tcem6n"),I(a,"hide",!t[11]||!t[8].some(ve)),m(e,"class","tab-wrapper svelte-1tcem6n")},m(s,k){O(s,e,k),T(e,l);for(let n=0;n<h.length;n+=1)h[n]&&h[n].m(l,null);T(e,i),T(e,_);for(let n=0;n<g.length;n+=1)g[n]&&g[n].m(_,null);t[28](_),T(e,r),T(e,a),T(a,f),Le(d,f,null),T(a,u),T(a,c);for(let n=0;n<w.length;n+=1)w[n]&&w[n].m(c,null);t[31](a),v=!0,z||(j=H(f,"click",Ue(t[29])),z=!0)},p(s,k){if(k[0]&40){b=A(s[3]);let n;for(n=0;n<b.length;n+=1){const q=fe(s,b,n);h[n]?h[n].p(q,k):(h[n]=ue(q),h[n].c(),h[n].m(l,null))}for(;n<h.length;n+=1)h[n].d(1);h.length=b.length}if(k[0]&393408){S=A(s[7]);let n;for(n=0;n<S.length;n+=1){const q=ae(s,S,n);g[n]?g[n].p(q,k):(g[n]=be(q),g[n].c(),g[n].m(_,null))}for(;n<g.length;n+=1)g[n].d(1);g.length=S.length}if((!v||k[0]&4096)&&I(f,"overflow-item-selected",s[12]),k[0]&262464){B=A(s[8]);let n;for(n=0;n<B.length;n+=1){const q=_e(s,B,n);w[n]?w[n].p(q,k):(w[n]=me(q),w[n].c(),w[n].m(c,null))}for(;n<w.length;n+=1)w[n].d(1);w.length=B.length}(!v||k[0]&512)&&I(c,"hide",!s[9]),(!v||k[0]&2304)&&I(a,"hide",!s[11]||!s[8].some(ve))},i(s){v||(G(d.$$.fragment,s),v=!0)},o(s){Q(d.$$.fragment,s),v=!1},d(s){s&&E(e),y(h,s),y(g,s),t[28](null),Fe(d),y(w,s),t[31](null),z=!1,j()}}}function re(t){let e,l=t[33]?.label+"",i,_,r=t[33];const a=()=>t[26](e,r),f=()=>t[26](null,r);return{c(){e=N("button"),i=ee(l),_=R(),m(e,"class","svelte-1tcem6n")},m(d,u){O(d,e,u),T(e,i),T(e,_),a()},p(d,u){t=d,u[0]&8&&l!==(l=t[33]?.label+"")&&x(i,l),r!==t[33]&&(f(),r=t[33],a())},d(d){d&&E(e),f()}}}function ue(t){let e,l=t[33]?.visible&&re(t);return{c(){l&&l.c(),e=$()},m(i,_){l&&l.m(i,_),O(i,e,_)},p(i,_){i[33]?.visible?l?l.p(i,_):(l=re(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&E(e),l&&l.d(i)}}}function de(t){let e,l=(t[33]?.label!==void 0?t[33]?.label:"Tab "+(t[37]+1))+"",i,_,r,a,f,d,u,c,v,z;function j(){return t[27](t[33],t[37])}return{c(){e=N("button"),i=ee(l),_=R(),m(e,"role","tab"),m(e,"aria-selected",r=t[33].id===t[6]),m(e,"aria-controls",a=t[33].elem_id),e.disabled=f=!t[33].interactive,m(e,"aria-disabled",d=!t[33].interactive),m(e,"id",u=t[33].elem_id?t[33].elem_id+"-button":null),m(e,"data-tab-id",c=t[33].id),m(e,"class","svelte-1tcem6n"),I(e,"selected",t[33].id===t[6])},m(b,h){O(b,e,h),T(e,i),T(e,_),v||(z=H(e,"click",j),v=!0)},p(b,h){t=b,h[0]&128&&l!==(l=(t[33]?.label!==void 0?t[33]?.label:"Tab "+(t[37]+1))+"")&&x(i,l),h[0]&192&&r!==(r=t[33].id===t[6])&&m(e,"aria-selected",r),h[0]&128&&a!==(a=t[33].elem_id)&&m(e,"aria-controls",a),h[0]&128&&f!==(f=!t[33].interactive)&&(e.disabled=f),h[0]&128&&d!==(d=!t[33].interactive)&&m(e,"aria-disabled",d),h[0]&128&&u!==(u=t[33].elem_id?t[33].elem_id+"-button":null)&&m(e,"id",u),h[0]&128&&c!==(c=t[33].id)&&m(e,"data-tab-id",c),h[0]&192&&I(e,"selected",t[33].id===t[6])},d(b){b&&E(e),v=!1,z()}}}function be(t){let e,l=t[33]?.visible&&de(t);return{c(){l&&l.c(),e=$()},m(i,_){l&&l.m(i,_),O(i,e,_)},p(i,_){i[33]?.visible?l?l.p(i,_):(l=de(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&E(e),l&&l.d(i)}}}function he(t){let e,l=t[33]?.label+"",i,_,r,a;function f(){return t[30](t[33])}return{c(){e=N("button"),i=ee(l),_=R(),m(e,"class","svelte-1tcem6n"),I(e,"selected",t[33]?.id===t[6])},m(d,u){O(d,e,u),T(e,i),T(e,_),r||(a=H(e,"click",f),r=!0)},p(d,u){t=d,u[0]&256&&l!==(l=t[33]?.label+"")&&x(i,l),u[0]&320&&I(e,"selected",t[33]?.id===t[6])},d(d){d&&E(e),r=!1,a()}}}function me(t){let e,l=t[33]?.visible&&he(t);return{c(){l&&l.c(),e=$()},m(i,_){l&&l.m(i,_),O(i,e,_)},p(i,_){i[33]?.visible?l?l.p(i,_):(l=he(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&E(e),l&&l.d(i)}}}function ye(t){let e,l,i,_,r,a,f=t[14]&&ce(t);const d=t[25].default,u=Me(d,t,t[24],null);return{c(){e=N("div"),f&&f.c(),l=R(),u&&u.c(),m(e,"class",i="tabs "+t[2].join(" ")+" svelte-1tcem6n"),m(e,"id",t[1]),I(e,"hide",!t[0]),oe(e,"flex-grow",t[13])},m(c,v){O(c,e,v),f&&f.m(e,null),T(e,l),u&&u.m(e,null),_=!0,r||(a=[H(window,"resize",t[20]),H(window,"click",t[19])],r=!0)},p(c,v){c[14]?f?(f.p(c,v),v[0]&16384&&G(f,1)):(f=ce(c),f.c(),G(f,1),f.m(e,l)):f&&(Je(),Q(f,1,1,()=>{f=null}),Ae()),u&&u.p&&(!_||v[0]&16777216)&&Ve(u,d,c,c[24],_?He(d,c[24],v,null):Ge(c[24]),null),(!_||v[0]&4&&i!==(i="tabs "+c[2].join(" ")+" svelte-1tcem6n"))&&m(e,"class",i),(!_||v[0]&2)&&m(e,"id",c[1]),(!_||v[0]&5)&&I(e,"hide",!c[0]),v[0]&8192&&oe(e,"flex-grow",c[13])},i(c){_||(G(f),G(u,c),_=!0)},o(c){Q(f),Q(u,c),_=!1},d(c){c&&E(e),f&&f.d(),u&&u.d(c),r=!1,Pe(a)}}}const $e={};function xe(t,e){const l={};return t.forEach(i=>{i&&(l[i.id]=e[i.id]?.getBoundingClientRect())}),l}const ve=t=>t?.visible;function el(t,e,l){let i,_,r,a,{$$slots:f={},$$scope:d}=e,{visible:u=!0}=e,{elem_id:c=""}=e,{elem_classes:v=[]}=e,{selected:z}=e,{initial_tabs:j}=e,b=[...j],h=[...j],S=[],g=!1,B,w;const s=ne(z||b[0]?.id||!1);se(t,s,o=>l(6,a=o));const k=ne(b.findIndex(o=>o?.id===z)||0);se(t,k,o=>l(23,r=o));const n=Xe();let q=!1,U=!1,J={};Ze(()=>{new IntersectionObserver(p=>{V()}).observe(w)}),We($e,{register_tab:(o,p)=>(l(3,b[p]=o,b),a===!1&&o.visible&&o.interactive&&(F(s,a=o.id,a),F(k,r=p,r)),p),unregister_tab:(o,p)=>{a===o.id&&F(s,a=b[0]?.id||!1,a),l(3,b[p]=null,b)},selected_tab:s,selected_tab_index:k});function K(o){const p=b.find(D=>D?.id===o);o!==void 0&&p&&p.interactive&&p.visible&&a!==p.id&&(l(21,z=o),F(s,a=o,a),F(k,r=b.findIndex(D=>D?.id===o),r),n("change"),l(9,g=!1))}function ge(o){g&&B&&!B.contains(o.target)&&l(9,g=!1)}async function V(){if(!w)return;await Ye();const o=w.getBoundingClientRect();let p=o.width;const D=xe(b,J);let W=0;const Be=o.left;for(let L=b.length-1;L>=0;L--){const te=b[L];if(!te)continue;const ie=D[te.id];if(ie&&ie.right-Be<p){W=L;break}}l(8,S=b.slice(W+1)),l(7,h=b.slice(0,W+1)),l(12,U=le(a)),l(11,q=S.length>0)}function le(o){return o===!1?!1:S.some(p=>p?.id===o)}function we(o,p){Z[o?"unshift":"push"](()=>{J[p.id]=o,l(5,J)})}const pe=(o,p)=>{o.id!==a&&(K(o.id),n("select",{value:o.label,index:p}))};function ke(o){Z[o?"unshift":"push"](()=>{w=o,l(4,w)})}const Ce=()=>l(9,g=!g),Te=o=>K(o?.id);function ze(o){Z[o?"unshift":"push"](()=>{B=o,l(10,B)})}return t.$$set=o=>{"visible"in o&&l(0,u=o.visible),"elem_id"in o&&l(1,c=o.elem_id),"elem_classes"in o&&l(2,v=o.elem_classes),"selected"in o&&l(21,z=o.selected),"initial_tabs"in o&&l(22,j=o.initial_tabs),"$$scope"in o&&l(24,d=o.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&8&&l(14,i=b.length>0),t.$$.dirty[0]&2097160&&z!==null&&K(z),t.$$.dirty[0]&56&&V(),t.$$.dirty[0]&64&&l(12,U=le(a)),t.$$.dirty[0]&8388616&&l(13,_=b[r>=0?r:0]?.scale)},[u,c,v,b,w,J,a,h,S,g,B,q,U,_,i,s,k,n,K,ge,V,z,j,r,d,f,we,pe,ke,Ce,Te,ze]}class ll extends Re{constructor(e){super(),Ke(this,e,el,ye,Qe,{visible:0,elem_id:1,elem_classes:2,selected:21,initial_tabs:22},null,[-1,-1])}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),M()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),M()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),M()}get selected(){return this.$$.ctx[21]}set selected(e){this.$$set({selected:e}),M()}get initial_tabs(){return this.$$.ctx[22]}set initial_tabs(e){this.$$set({initial_tabs:e}),M()}}const il=ll;export{il as T,$e as a};
//# sourceMappingURL=Tabs-DS4O-Nv1.js.map
