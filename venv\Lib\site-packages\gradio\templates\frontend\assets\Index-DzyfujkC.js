/* empty css                                                        */import{B as St}from"./BlockTitle-Ct-h8ev5.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{C as qt}from"./Clear-By3xiIwg.js";import{F as Mt}from"./File-BQ_9P3Ye.js";import{M as Tt}from"./SelectSource-CVJruJ8L.js";import{M as Dt}from"./Music-CDm0RGMk.js";import{S as Bt}from"./Send-DyoOovnk.js";import{S as Et}from"./Square-oAGqOwsh.js";import{V as Ht}from"./Video-fsmLZWjA.js";import"./index-B7J2Z2jS.js";import{a as Ft}from"./Upload-D4uXt6Nz.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import{I as Rt}from"./Image-CnqB5dbD.js";/* empty css                                                   */import{I as Vt}from"./InteractiveAudio-DBS9gPHc.js";import{B as At}from"./Block-CJdXVpa7.js";import{S as Pt}from"./index-B1FJGuzG.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import{default as vi}from"./Example-CiCdQyhW.js";import"./Info-IGMCDo7y.js";import"./MarkdownCode-CkSMBRHJ.js";import"./prism-python-MMh3z1bK.js";import"./svelte/svelte.js";/* empty css                                             */import"./file-url-DoxvUUVV.js";import"./ModifyUpload-5lgGIpMc.js";import"./IconButton-C_HS7fTi.js";import"./Download-DVtk-Jv3.js";import"./Edit-BpRIf5rU.js";import"./Undo-DCjBnnSO.js";import"./IconButtonWrapper--EIOWuEM.js";import"./DownloadLink-QIttOhoR.js";import"./BlockLabel-3KxTaaiM.js";import"./StreamingBar-JqJtcvLZ.js";import"./AudioPlayer-73PUar21.js";import"./utils-BsGrhMNe.js";import"./Trim-JQYgj7Jd.js";import"./Play-B0Q0U1Qz.js";import"./Empty-ZqppqzTN.js";import"./hls-CnVhpNcu.js";import"./Video-DtShVFLe.js";const{SvelteComponent:It,append:ke,attr:K,detach:Ut,init:jt,insert:Gt,noop:Ge,safe_not_equal:Lt,svg_element:he}=window.__gradio__svelte__internal;function Wt(t){let e,l,i,o,s;return{c(){e=he("svg"),l=he("g"),i=he("g"),o=he("g"),s=he("path"),K(l,"id","SVGRepo_bgCarrier"),K(l,"stroke-width","0"),K(i,"id","SVGRepo_tracerCarrier"),K(i,"stroke-linecap","round"),K(i,"stroke-linejoin","round"),K(s,"d","M1752.768 221.109C1532.646.986 1174.283.986 954.161 221.109l-838.588 838.588c-154.052 154.165-154.052 404.894 0 558.946 149.534 149.421 409.976 149.308 559.059 0l758.738-758.626c87.982-88.094 87.982-231.417 0-319.51-88.32-88.208-231.642-87.982-319.51 0l-638.796 638.908 79.85 79.849 638.795-638.908c43.934-43.821 115.539-43.934 159.812 0 43.934 44.047 43.934 115.877 0 159.812l-758.739 758.625c-110.23 110.118-289.355 110.005-399.36 0-110.118-110.117-110.005-289.242 0-399.247l838.588-838.588c175.963-175.962 462.382-176.188 638.909 0 176.075 176.188 176.075 462.833 0 638.908l-798.607 798.72 79.849 79.85 798.607-798.72c220.01-220.123 220.01-578.485 0-798.607"),K(s,"fill-rule","evenodd"),K(o,"id","SVGRepo_iconCarrier"),K(e,"fill","currentColor"),K(e,"width","100%"),K(e,"height","100%"),K(e,"viewBox","0 0 1920 1920"),K(e,"xmlns","http://www.w3.org/2000/svg")},m(h,f){Gt(h,e,f),ke(e,l),ke(e,i),ke(e,o),ke(o,s)},p:Ge,i:Ge,o:Ge,d(h){h&&Ut(e)}}}class Kt extends It{constructor(e){super(),jt(this,e,null,Wt,Lt,{})}}const{tick:Yt}=window.__gradio__svelte__internal;async function Le(t,e,l){if(await Yt(),e===l)return;const i=window.getComputedStyle(t),o=parseFloat(i.paddingTop),s=parseFloat(i.paddingBottom),h=parseFloat(i.lineHeight);let f=l===void 0?!1:o+s+h*l,a=o+s+e*h;t.style.height="1px";let d;f&&t.scrollHeight>f?d=f:t.scrollHeight<a?d=a:d=t.scrollHeight,t.style.height=`${d}px`}function Nt(t,e){if(e.lines===e.max_lines)return;t.style.overflowY="scroll";function l(i){Le(i.target,e.lines,e.max_lines)}if(t.addEventListener("input",l),!!e.text.trim())return Le(t,e.lines,e.max_lines),{destroy:()=>t.removeEventListener("input",l)}}const{SvelteComponent:Xt,action_destroyer:Jt,add_flush_callback:Je,append:Y,attr:w,bind:Oe,binding_callbacks:de,bubble:ve,check_outros:le,create_component:X,destroy_component:J,destroy_each:Ot,detach:O,element:N,ensure_array_like:Qe,flush:B,group_outros:ie,init:Qt,insert:Q,is_function:qe,listen:G,mount_component:Z,noop:oe,prevent_default:Zt,run_all:yt,safe_not_equal:xt,set_data:We,set_input_value:Ze,set_style:ye,space:te,text:Ke,toggle_class:y,transition_in:k,transition_out:S}=window.__gradio__svelte__internal,{onMount:$t,beforeUpdate:el,afterUpdate:tl,createEventDispatcher:ll,tick:xe}=window.__gradio__svelte__internal;function $e(t,e,l){const i=t.slice();return i[70]=e[l],i[72]=l,i}function il(t){let e;return{c(){e=Ke(t[7])},m(l,i){Q(l,e,i)},p(l,i){i[0]&128&&We(e,l[7])},d(l){l&&O(e)}}}function et(t){let e,l,i,o=Qe(t[0].files),s=[];for(let a=0;a<o.length;a+=1)s[a]=tt($e(t,o,a));const h=a=>S(s[a],1,1,()=>{s[a]=null});let f=t[28]&&lt();return{c(){e=N("div");for(let a=0;a<s.length;a+=1)s[a].c();l=te(),f&&f.c(),w(e,"class","thumbnails scroll-hide svelte-5gfv2q"),w(e,"aria-label","Uploaded files"),w(e,"data-testid","container_el"),ye(e,"display",t[0].files.length>0||t[28]?"flex":"none")},m(a,d){Q(a,e,d);for(let m=0;m<s.length;m+=1)s[m]&&s[m].m(e,null);Y(e,l),f&&f.m(e,null),i=!0},p(a,d){if(d[0]&65|d[1]&32){o=Qe(a[0].files);let m;for(m=0;m<o.length;m+=1){const z=$e(a,o,m);s[m]?(s[m].p(z,d),k(s[m],1)):(s[m]=tt(z),s[m].c(),k(s[m],1),s[m].m(e,l))}for(ie(),m=o.length;m<s.length;m+=1)h(m);le()}a[28]?f||(f=lt(),f.c(),f.m(e,null)):f&&(f.d(1),f=null),(!i||d[0]&268435457)&&ye(e,"display",a[0].files.length>0||a[28]?"flex":"none")},i(a){if(!i){for(let d=0;d<o.length;d+=1)k(s[d]);i=!0}},o(a){s=s.filter(Boolean);for(let d=0;d<s.length;d+=1)S(s[d]);i=!1},d(a){a&&O(e),Ot(s,a),f&&f.d()}}}function nl(t){let e,l;return e=new Mt({}),{c(){X(e.$$.fragment)},m(i,o){Z(e,i,o),l=!0},p:oe,i(i){l||(k(e.$$.fragment,i),l=!0)},o(i){S(e.$$.fragment,i),l=!1},d(i){J(e,i)}}}function sl(t){let e,l;return e=new Ht({}),{c(){X(e.$$.fragment)},m(i,o){Z(e,i,o),l=!0},p:oe,i(i){l||(k(e.$$.fragment,i),l=!0)},o(i){S(e.$$.fragment,i),l=!1},d(i){J(e,i)}}}function ol(t){let e,l;return e=new Dt({}),{c(){X(e.$$.fragment)},m(i,o){Z(e,i,o),l=!0},p:oe,i(i){l||(k(e.$$.fragment,i),l=!0)},o(i){S(e.$$.fragment,i),l=!1},d(i){J(e,i)}}}function al(t){let e,l;return e=new Rt({props:{src:t[70].url,title:null,alt:"",loading:"lazy",class:"thumbnail-image"}}),{c(){X(e.$$.fragment)},m(i,o){Z(e,i,o),l=!0},p(i,o){const s={};o[0]&1&&(s.src=i[70].url),e.$set(s)},i(i){l||(k(e.$$.fragment,i),l=!0)},o(i){S(e.$$.fragment,i),l=!1},d(i){J(e,i)}}}function tt(t){let e,l,i,o,s,h,f,a,d,m,z,_,v;o=new qt({});function r(...q){return t[51](t[72],...q)}const g=[al,ol,sl,nl],b=[];function W(q,A){return A[0]&1&&(h=null),A[0]&1&&(f=null),A[0]&1&&(a=null),h==null&&(h=!!(q[70].mime_type&&q[70].mime_type.includes("image"))),h?0:(f==null&&(f=!!(q[70].mime_type&&q[70].mime_type.includes("audio"))),f?1:(a==null&&(a=!!(q[70].mime_type&&q[70].mime_type.includes("video"))),a?2:3))}return d=W(t,[-1,-1,-1]),m=b[d]=g[d](t),{c(){e=N("span"),l=N("button"),i=N("button"),X(o.$$.fragment),s=te(),m.c(),w(i,"class","delete-button svelte-5gfv2q"),y(i,"disabled",t[6]),w(l,"class","thumbnail-item thumbnail-small svelte-5gfv2q"),w(e,"role","listitem"),w(e,"aria-label","File thumbnail")},m(q,A){Q(q,e,A),Y(e,l),Y(l,i),Z(o,i,null),Y(l,s),b[d].m(l,null),z=!0,_||(v=G(i,"click",r),_=!0)},p(q,A){t=q,(!z||A[0]&64)&&y(i,"disabled",t[6]);let P=d;d=W(t,A),d===P?b[d].p(t,A):(ie(),S(b[P],1,1,()=>{b[P]=null}),le(),m=b[d],m?m.p(t,A):(m=b[d]=g[d](t),m.c()),k(m,1),m.m(l,null))},i(q){z||(k(o.$$.fragment,q),k(m),z=!0)},o(q){S(o.$$.fragment,q),S(m),z=!1},d(q){q&&O(e),J(o),b[d].d(),_=!1,v()}}}function lt(t){let e;return{c(){e=N("div"),w(e,"class","loader svelte-5gfv2q"),w(e,"role","status"),w(e,"aria-label","Uploading")},m(l,i){Q(l,e,i)},d(l){l&&O(e)}}}function it(t){let e,l;return e=new Vt({props:{sources:["microphone"],class_name:"compact-audio",recording:ut,waveform_settings:t[22],waveform_options:t[23],i18n:t[4],active_source:t[2],upload:t[19],stream_handler:t[20],stream_every:1,editable:!0,label:t[7],root:t[16],loop:!1,show_label:!1,show_download_button:!1,dragging:!1}}),e.$on("change",t[52]),e.$on("clear",t[53]),e.$on("start_recording",t[54]),e.$on("pause_recording",t[55]),e.$on("stop_recording",t[56]),{c(){X(e.$$.fragment)},m(i,o){Z(e,i,o),l=!0},p(i,o){const s={};o[0]&4194304&&(s.waveform_settings=i[22]),o[0]&8388608&&(s.waveform_options=i[23]),o[0]&16&&(s.i18n=i[4]),o[0]&4&&(s.active_source=i[2]),o[0]&524288&&(s.upload=i[19]),o[0]&1048576&&(s.stream_handler=i[20]),o[0]&128&&(s.label=i[7]),o[0]&65536&&(s.root=i[16]),e.$set(s)},i(i){l||(k(e.$$.fragment,i),l=!0)},o(i){S(e.$$.fragment,i),l=!1},d(i){J(e,i)}}}function nt(t){let e,l,i,o,s,h,f,a,d;function m(v){t[58](v)}function z(v){t[59](v)}let _={file_count:t[21],filetype:t[17],root:t[16],max_file_size:t[18],show_progress:!1,disable_click:!0,hidden:!0,upload:t[19],stream_handler:t[20]};return t[1]!==void 0&&(_.dragging=t[1]),t[28]!==void 0&&(_.uploading=t[28]),e=new Ft({props:_}),t[57](e),de.push(()=>Oe(e,"dragging",m)),de.push(()=>Oe(e,"uploading",z)),e.$on("load",t[35]),e.$on("error",t[60]),h=new Kt({}),{c(){X(e.$$.fragment),o=te(),s=N("button"),X(h.$$.fragment),w(s,"data-testid","upload-button"),w(s,"class","upload-button svelte-5gfv2q"),s.disabled=t[6]},m(v,r){Z(e,v,r),Q(v,o,r),Q(v,s,r),Z(h,s,null),f=!0,a||(d=G(s,"click",function(){qe(t[6]?void 0:t[37])&&(t[6]?void 0:t[37]).apply(this,arguments)}),a=!0)},p(v,r){t=v;const g={};r[0]&2097152&&(g.file_count=t[21]),r[0]&131072&&(g.filetype=t[17]),r[0]&65536&&(g.root=t[16]),r[0]&262144&&(g.max_file_size=t[18]),r[0]&524288&&(g.upload=t[19]),r[0]&1048576&&(g.stream_handler=t[20]),!l&&r[0]&2&&(l=!0,g.dragging=t[1],Je(()=>l=!1)),!i&&r[0]&268435456&&(i=!0,g.uploading=t[28],Je(()=>i=!1)),e.$set(g),(!f||r[0]&64)&&(s.disabled=t[6])},i(v){f||(k(e.$$.fragment,v),k(h.$$.fragment,v),f=!0)},o(v){S(e.$$.fragment,v),S(h.$$.fragment,v),f=!1},d(v){v&&(O(o),O(s)),t[57](null),J(e,v),J(h),a=!1,d()}}}function st(t){let e,l,i,o,s;return l=new Tt({}),{c(){e=N("button"),X(l.$$.fragment),w(e,"data-testid","microphone-button"),w(e,"class","microphone-button svelte-5gfv2q"),e.disabled=t[6],y(e,"recording",ut)},m(h,f){Q(h,e,f),Z(l,e,null),i=!0,o||(s=G(e,"click",function(){qe(t[6]?void 0:t[61])&&(t[6]?void 0:t[61]).apply(this,arguments)}),o=!0)},p(h,f){t=h,(!i||f[0]&64)&&(e.disabled=t[6])},i(h){i||(k(l.$$.fragment,h),i=!0)},o(h){S(l.$$.fragment,h),i=!1},d(h){h&&O(e),J(l),o=!1,s()}}}function ot(t){let e,l,i,o,s,h;const f=[ul,rl],a=[];function d(m,z){return m[11]===!0?0:1}return l=d(t),i=a[l]=f[l](t),{c(){e=N("button"),i.c(),w(e,"class","submit-button svelte-5gfv2q"),e.disabled=t[6],y(e,"padded-button",t[11]!==!0)},m(m,z){Q(m,e,z),a[l].m(e,null),o=!0,s||(h=G(e,"click",function(){qe(t[6]?void 0:t[39])&&(t[6]?void 0:t[39]).apply(this,arguments)}),s=!0)},p(m,z){t=m;let _=l;l=d(t),l===_?a[l].p(t,z):(ie(),S(a[_],1,1,()=>{a[_]=null}),le(),i=a[l],i?i.p(t,z):(i=a[l]=f[l](t),i.c()),k(i,1),i.m(e,null)),(!o||z[0]&64)&&(e.disabled=t[6]),(!o||z[0]&2048)&&y(e,"padded-button",t[11]!==!0)},i(m){o||(k(i),o=!0)},o(m){S(i),o=!1},d(m){m&&O(e),a[l].d(),s=!1,h()}}}function rl(t){let e;return{c(){e=Ke(t[11])},m(l,i){Q(l,e,i)},p(l,i){i[0]&2048&&We(e,l[11])},i:oe,o:oe,d(l){l&&O(e)}}}function ul(t){let e,l;return e=new Bt({}),{c(){X(e.$$.fragment)},m(i,o){Z(e,i,o),l=!0},p:oe,i(i){l||(k(e.$$.fragment,i),l=!0)},o(i){S(e.$$.fragment,i),l=!1},d(i){J(e,i)}}}function at(t){let e,l,i,o,s,h;const f=[_l,fl],a=[];function d(m,z){return m[12]===!0?0:1}return l=d(t),i=a[l]=f[l](t),{c(){e=N("button"),i.c(),w(e,"class","stop-button svelte-5gfv2q"),y(e,"padded-button",t[12]!==!0)},m(m,z){Q(m,e,z),a[l].m(e,null),o=!0,s||(h=G(e,"click",t[38]),s=!0)},p(m,z){let _=l;l=d(m),l===_?a[l].p(m,z):(ie(),S(a[_],1,1,()=>{a[_]=null}),le(),i=a[l],i?i.p(m,z):(i=a[l]=f[l](m),i.c()),k(i,1),i.m(e,null)),(!o||z[0]&4096)&&y(e,"padded-button",m[12]!==!0)},i(m){o||(k(i),o=!0)},o(m){S(i),o=!1},d(m){m&&O(e),a[l].d(),s=!1,h()}}}function fl(t){let e;return{c(){e=Ke(t[12])},m(l,i){Q(l,e,i)},p(l,i){i[0]&4096&&We(e,l[12])},i:oe,o:oe,d(l){l&&O(e)}}}function _l(t){let e,l;return e=new Et({props:{fill:"none",stroke_width:2.5}}),{c(){X(e.$$.fragment)},m(i,o){Z(e,i,o),l=!0},p:oe,i(i){l||(k(e.$$.fragment,i),l=!0)},o(i){S(e.$$.fragment,i),l=!1},d(i){J(e,i)}}}function cl(t){let e,l,i,o,s=t[24]&&t[24].includes("microphone")&&t[2]==="microphone",h,f,a=t[24]&&t[24].includes("upload")&&!(t[21]==="single"&&t[0].files.length>0),d,m=t[24]&&t[24].includes("microphone"),z,_,v,r,g,b,W,q,A,P,x,$,ae,ne,H,se,re;l=new St({props:{show_label:t[9],info:t[8],rtl:t[13],$$slots:{default:[il]},$$scope:{ctx:t}}});let V=(t[0].files.length>0||t[28])&&et(t),T=s&&it(t),D=a&&nt(t),M=m&&st(t),C=t[11]&&ot(t),F=t[12]&&at(t);return{c(){e=N("div"),X(l.$$.fragment),i=te(),V&&V.c(),o=te(),T&&T.c(),h=te(),f=N("div"),D&&D.c(),d=te(),M&&M.c(),z=te(),_=N("textarea"),ae=te(),C&&C.c(),ne=te(),F&&F.c(),w(_,"data-testid","textbox"),w(_,"class","scroll-hide svelte-5gfv2q"),w(_,"dir",v=t[13]?"rtl":"ltr"),w(_,"placeholder",t[5]),w(_,"rows",t[3]),_.disabled=t[6],_.autofocus=t[14],w(_,"style",r=t[15]?"text-align: "+t[15]:""),w(_,"autocapitalize",g=t[25]?.autocapitalize),w(_,"autocorrect",b=t[25]?.autocorrect),w(_,"spellcheck",W=t[25]?.spellcheck),w(_,"autocomplete",q=t[25]?.autocomplete),w(_,"tabindex",A=t[25]?.tabindex),w(_,"enterkeyhint",P=t[25]?.enterkeyhint),w(_,"lang",x=t[25]?.lang),y(_,"no-label",!t[9]),w(f,"class","input-container svelte-5gfv2q"),w(e,"class","full-container svelte-5gfv2q"),w(e,"role","group"),w(e,"aria-label","Multimedia input field"),y(e,"dragging",t[1])},m(c,p){Q(c,e,p),Z(l,e,null),Y(e,i),V&&V.m(e,null),Y(e,o),T&&T.m(e,null),Y(e,h),Y(e,f),D&&D.m(f,null),Y(f,d),M&&M.m(f,null),Y(f,z),Y(f,_),Ze(_,t[0].text),t[63](_),Y(f,ae),C&&C.m(f,null),Y(f,ne),F&&F.m(f,null),t[64](e),H=!0,t[14]&&_.focus(),se||(re=[Jt($=Nt.call(null,_,{text:t[0].text,lines:t[3],max_lines:t[10]})),G(_,"input",t[62]),G(_,"keypress",t[33]),G(_,"blur",t[49]),G(_,"select",t[32]),G(_,"focus",t[50]),G(_,"scroll",t[34]),G(_,"paste",t[40]),G(e,"dragenter",t[41]),G(e,"dragleave",t[42]),G(e,"dragover",Zt(t[48])),G(e,"drop",t[43])],se=!0)},p(c,p){const U={};p[0]&512&&(U.show_label=c[9]),p[0]&256&&(U.info=c[8]),p[0]&8192&&(U.rtl=c[13]),p[0]&128|p[2]&2048&&(U.$$scope={dirty:p,ctx:c}),l.$set(U),c[0].files.length>0||c[28]?V?(V.p(c,p),p[0]&268435457&&k(V,1)):(V=et(c),V.c(),k(V,1),V.m(e,o)):V&&(ie(),S(V,1,1,()=>{V=null}),le()),p[0]&16777220&&(s=c[24]&&c[24].includes("microphone")&&c[2]==="microphone"),s?T?(T.p(c,p),p[0]&16777220&&k(T,1)):(T=it(c),T.c(),k(T,1),T.m(e,h)):T&&(ie(),S(T,1,1,()=>{T=null}),le()),p[0]&18874369&&(a=c[24]&&c[24].includes("upload")&&!(c[21]==="single"&&c[0].files.length>0)),a?D?(D.p(c,p),p[0]&18874369&&k(D,1)):(D=nt(c),D.c(),k(D,1),D.m(f,d)):D&&(ie(),S(D,1,1,()=>{D=null}),le()),p[0]&16777216&&(m=c[24]&&c[24].includes("microphone")),m?M?(M.p(c,p),p[0]&16777216&&k(M,1)):(M=st(c),M.c(),k(M,1),M.m(f,z)):M&&(ie(),S(M,1,1,()=>{M=null}),le()),(!H||p[0]&8192&&v!==(v=c[13]?"rtl":"ltr"))&&w(_,"dir",v),(!H||p[0]&32)&&w(_,"placeholder",c[5]),(!H||p[0]&8)&&w(_,"rows",c[3]),(!H||p[0]&64)&&(_.disabled=c[6]),(!H||p[0]&16384)&&(_.autofocus=c[14]),(!H||p[0]&32768&&r!==(r=c[15]?"text-align: "+c[15]:""))&&w(_,"style",r),(!H||p[0]&33554432&&g!==(g=c[25]?.autocapitalize))&&w(_,"autocapitalize",g),(!H||p[0]&33554432&&b!==(b=c[25]?.autocorrect))&&w(_,"autocorrect",b),(!H||p[0]&33554432&&W!==(W=c[25]?.spellcheck))&&w(_,"spellcheck",W),(!H||p[0]&33554432&&q!==(q=c[25]?.autocomplete))&&w(_,"autocomplete",q),(!H||p[0]&33554432&&A!==(A=c[25]?.tabindex))&&w(_,"tabindex",A),(!H||p[0]&33554432&&P!==(P=c[25]?.enterkeyhint))&&w(_,"enterkeyhint",P),(!H||p[0]&33554432&&x!==(x=c[25]?.lang))&&w(_,"lang",x),$&&qe($.update)&&p[0]&1033&&$.update.call(null,{text:c[0].text,lines:c[3],max_lines:c[10]}),p[0]&1&&Ze(_,c[0].text),(!H||p[0]&512)&&y(_,"no-label",!c[9]),c[11]?C?(C.p(c,p),p[0]&2048&&k(C,1)):(C=ot(c),C.c(),k(C,1),C.m(f,ne)):C&&(ie(),S(C,1,1,()=>{C=null}),le()),c[12]?F?(F.p(c,p),p[0]&4096&&k(F,1)):(F=at(c),F.c(),k(F,1),F.m(f,null)):F&&(ie(),S(F,1,1,()=>{F=null}),le()),(!H||p[0]&2)&&y(e,"dragging",c[1])},i(c){H||(k(l.$$.fragment,c),k(V),k(T),k(D),k(M),k(C),k(F),H=!0)},o(c){S(l.$$.fragment,c),S(V),S(T),S(D),S(M),S(C),S(F),H=!1},d(c){c&&O(e),J(l),V&&V.d(),T&&T.d(),D&&D.d(),M&&M.d(),t[63](null),C&&C.d(),F&&F.d(),t[64](null),se=!1,yt(re)}}}let ut=!1;function ml(t,e,l){let{value:i={text:"",files:[]}}=e,{value_is_output:o=!1}=e,{lines:s=1}=e,{i18n:h}=e,{placeholder:f="Type here..."}=e,{disabled:a=!1}=e,{label:d}=e,{info:m=void 0}=e,{show_label:z=!0}=e,{max_lines:_}=e,{submit_btn:v=null}=e,{stop_btn:r=null}=e,{rtl:g=!1}=e,{autofocus:b=!1}=e,{text_align:W=void 0}=e,{autoscroll:q=!0}=e,{root:A}=e,{file_types:P=null}=e,{max_file_size:x=null}=e,{upload:$}=e,{stream_handler:ae}=e,{file_count:ne="multiple"}=e,{max_plain_text_length:H=1e3}=e,{waveform_settings:se}=e,{waveform_options:re={show_recording_waveform:!0}}=e,{sources:V=["upload"]}=e,{active_source:T=null}=e,{html_attributes:D=null}=e,M,C,F,c=0,p=!1,{dragging:U=!1}=e,me=!1,ge=i?.text??"",ee=null,fe;const I=ll();el(()=>{F=C&&C.offsetHeight+C.scrollTop>C.scrollHeight-100});const Me=()=>{F&&q&&!p&&C.scrollTo(0,C.scrollHeight)};async function pe(){I("change",i),o||I("input")}$t(()=>{b&&C!==null&&C.focus()}),tl(()=>{F&&q&&Me(),l(44,o=!1)});function Te(n){const R=n.target,j=R.value,L=[R.selectionStart,R.selectionEnd];I("select",{value:j.substring(...L),index:L})}async function De(n){await xe(),n.key==="Enter"&&n.shiftKey&&s>1?(n.preventDefault(),I("submit")):n.key==="Enter"&&!n.shiftKey&&s===1&&_>=1&&(n.preventDefault(),I("submit"),l(2,T=null),ee&&(i.files.push(ee),l(0,i),l(29,ee=null)))}function Be(n){const R=n.target,j=R.scrollTop;j<c&&(p=!0),c=j;const L=R.scrollHeight-R.clientHeight;j>=L&&(p=!1)}async function Ee({detail:n}){if(pe(),Array.isArray(n)){for(let R of n)i.files.push(R);l(0,i)}else i.files.push(n),l(0,i);await xe(),I("change",i),I("upload",n)}function we(n,R){pe(),n.stopPropagation(),i.files.splice(R,1),l(0,i)}function He(){M.open_upload()}function Fe(){I("stop")}function Re(){I("submit"),l(2,T=null),ee&&(i.files.push(ee),l(0,i),l(29,ee=null))}async function Ve(n){if(!n.clipboardData)return;const R=n.clipboardData.items,j=n.clipboardData.getData("text");if(j&&j.length>H){n.preventDefault();const L=new window.File([j],"pasted_text.txt",{type:"text/plain",lastModified:Date.now()});M&&M.load_files([L]);return}for(let L in R){const ue=R[L];if(ue.kind==="file"&&ue.type.includes("image")){const _e=ue.getAsFile();_e&&M.load_files([_e])}}}function Ae(n){n.preventDefault(),l(1,U=!0)}function Pe(n){n.preventDefault();const R=fe.getBoundingClientRect(),{clientX:j,clientY:L}=n;(j<=R.left||j>=R.right||L<=R.top||L>=R.bottom)&&l(1,U=!1)}function Ie(n){if(n.preventDefault(),l(1,U=!1),n.dataTransfer&&n.dataTransfer.files){const R=Array.from(n.dataTransfer.files);if(P){const j=R.filter(ue=>P.some(_e=>_e.startsWith(".")?ue.name.toLowerCase().endsWith(_e.toLowerCase()):ue.type.match(new RegExp(_e.replace("*",".*"))))),L=R.length-j.length;L>0&&I("error",`${L} file(s) were rejected. Accepted formats: ${P.join(", ")}`),j.length>0&&M.load_files(j)}else M.load_files(R)}}function Ue(n){ve.call(this,t,n)}function je(n){ve.call(this,t,n)}function u(n){ve.call(this,t,n)}const ft=(n,R)=>we(R,n),_t=({detail:n})=>{n!==null&&l(29,ee=n)},ct=()=>{l(2,T=null)},mt=()=>I("start_recording"),gt=()=>I("pause_recording"),ht=()=>I("stop_recording");function dt(n){de[n?"unshift":"push"](()=>{M=n,l(27,M)})}function bt(n){U=n,l(1,U)}function pt(n){me=n,l(28,me)}function wt(n){ve.call(this,t,n)}const kt=()=>{l(2,T=T!=="microphone"?"microphone":null)};function vt(){i.text=this.value,l(0,i)}function zt(n){de[n?"unshift":"push"](()=>{C=n,l(26,C)})}function Ct(n){de[n?"unshift":"push"](()=>{fe=n,l(30,fe)})}return t.$$set=n=>{"value"in n&&l(0,i=n.value),"value_is_output"in n&&l(44,o=n.value_is_output),"lines"in n&&l(3,s=n.lines),"i18n"in n&&l(4,h=n.i18n),"placeholder"in n&&l(5,f=n.placeholder),"disabled"in n&&l(6,a=n.disabled),"label"in n&&l(7,d=n.label),"info"in n&&l(8,m=n.info),"show_label"in n&&l(9,z=n.show_label),"max_lines"in n&&l(10,_=n.max_lines),"submit_btn"in n&&l(11,v=n.submit_btn),"stop_btn"in n&&l(12,r=n.stop_btn),"rtl"in n&&l(13,g=n.rtl),"autofocus"in n&&l(14,b=n.autofocus),"text_align"in n&&l(15,W=n.text_align),"autoscroll"in n&&l(45,q=n.autoscroll),"root"in n&&l(16,A=n.root),"file_types"in n&&l(17,P=n.file_types),"max_file_size"in n&&l(18,x=n.max_file_size),"upload"in n&&l(19,$=n.upload),"stream_handler"in n&&l(20,ae=n.stream_handler),"file_count"in n&&l(21,ne=n.file_count),"max_plain_text_length"in n&&l(46,H=n.max_plain_text_length),"waveform_settings"in n&&l(22,se=n.waveform_settings),"waveform_options"in n&&l(23,re=n.waveform_options),"sources"in n&&l(24,V=n.sources),"active_source"in n&&l(2,T=n.active_source),"html_attributes"in n&&l(25,D=n.html_attributes),"dragging"in n&&l(1,U=n.dragging)},t.$$.update=()=>{t.$$.dirty[0]&2&&I("drag",U),t.$$.dirty[0]&1&&i===null&&l(0,i={text:"",files:[]}),t.$$.dirty[0]&1|t.$$.dirty[1]&65536&&ge!==i.text&&(I("change",i),l(47,ge=i.text)),t.$$.dirty[0]&67109897&&C&&s!==_&&Le(C,s,_)},[i,U,T,s,h,f,a,d,m,z,_,v,r,g,b,W,A,P,x,$,ae,ne,se,re,V,D,C,M,me,ee,fe,I,Te,De,Be,Ee,we,He,Fe,Re,Ve,Ae,Pe,Ie,o,q,H,ge,Ue,je,u,ft,_t,ct,mt,gt,ht,dt,bt,pt,wt,kt,vt,zt,Ct]}class gl extends Xt{constructor(e){super(),Qt(this,e,ml,cl,xt,{value:0,value_is_output:44,lines:3,i18n:4,placeholder:5,disabled:6,label:7,info:8,show_label:9,max_lines:10,submit_btn:11,stop_btn:12,rtl:13,autofocus:14,text_align:15,autoscroll:45,root:16,file_types:17,max_file_size:18,upload:19,stream_handler:20,file_count:21,max_plain_text_length:46,waveform_settings:22,waveform_options:23,sources:24,active_source:2,html_attributes:25,dragging:1},null,[-1,-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),B()}get value_is_output(){return this.$$.ctx[44]}set value_is_output(e){this.$$set({value_is_output:e}),B()}get lines(){return this.$$.ctx[3]}set lines(e){this.$$set({lines:e}),B()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),B()}get placeholder(){return this.$$.ctx[5]}set placeholder(e){this.$$set({placeholder:e}),B()}get disabled(){return this.$$.ctx[6]}set disabled(e){this.$$set({disabled:e}),B()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),B()}get info(){return this.$$.ctx[8]}set info(e){this.$$set({info:e}),B()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),B()}get max_lines(){return this.$$.ctx[10]}set max_lines(e){this.$$set({max_lines:e}),B()}get submit_btn(){return this.$$.ctx[11]}set submit_btn(e){this.$$set({submit_btn:e}),B()}get stop_btn(){return this.$$.ctx[12]}set stop_btn(e){this.$$set({stop_btn:e}),B()}get rtl(){return this.$$.ctx[13]}set rtl(e){this.$$set({rtl:e}),B()}get autofocus(){return this.$$.ctx[14]}set autofocus(e){this.$$set({autofocus:e}),B()}get text_align(){return this.$$.ctx[15]}set text_align(e){this.$$set({text_align:e}),B()}get autoscroll(){return this.$$.ctx[45]}set autoscroll(e){this.$$set({autoscroll:e}),B()}get root(){return this.$$.ctx[16]}set root(e){this.$$set({root:e}),B()}get file_types(){return this.$$.ctx[17]}set file_types(e){this.$$set({file_types:e}),B()}get max_file_size(){return this.$$.ctx[18]}set max_file_size(e){this.$$set({max_file_size:e}),B()}get upload(){return this.$$.ctx[19]}set upload(e){this.$$set({upload:e}),B()}get stream_handler(){return this.$$.ctx[20]}set stream_handler(e){this.$$set({stream_handler:e}),B()}get file_count(){return this.$$.ctx[21]}set file_count(e){this.$$set({file_count:e}),B()}get max_plain_text_length(){return this.$$.ctx[46]}set max_plain_text_length(e){this.$$set({max_plain_text_length:e}),B()}get waveform_settings(){return this.$$.ctx[22]}set waveform_settings(e){this.$$set({waveform_settings:e}),B()}get waveform_options(){return this.$$.ctx[23]}set waveform_options(e){this.$$set({waveform_options:e}),B()}get sources(){return this.$$.ctx[24]}set sources(e){this.$$set({sources:e}),B()}get active_source(){return this.$$.ctx[2]}set active_source(e){this.$$set({active_source:e}),B()}get html_attributes(){return this.$$.ctx[25]}set html_attributes(e){this.$$set({html_attributes:e}),B()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),B()}}const hl=gl,{SvelteComponent:dl,add_flush_callback:ze,assign:bl,bind:Ce,binding_callbacks:Se,check_outros:pl,create_component:Ye,destroy_component:Ne,detach:wl,flush:E,get_spread_object:kl,get_spread_update:vl,group_outros:zl,init:Cl,insert:Sl,mount_component:Xe,safe_not_equal:ql,space:Ml,transition_in:ce,transition_out:be}=window.__gradio__svelte__internal,{onMount:Tl}=window.__gradio__svelte__internal;function rt(t){let e,l;const i=[{autoscroll:t[2].autoscroll},{i18n:t[2].i18n},t[17]];let o={};for(let s=0;s<i.length;s+=1)o=bl(o,i[s]);return e=new Pt({props:o}),e.$on("clear_status",t[32]),{c(){Ye(e.$$.fragment)},m(s,h){Xe(e,s,h),l=!0},p(s,h){const f=h[0]&131076?vl(i,[h[0]&4&&{autoscroll:s[2].autoscroll},h[0]&4&&{i18n:s[2].i18n},h[0]&131072&&kl(s[17])]):{};e.$set(f)},i(s){l||(ce(e.$$.fragment,s),l=!0)},o(s){be(e.$$.fragment,s),l=!1},d(s){Ne(e,s)}}}function Dl(t){let e,l,i,o,s,h,f,a=t[17]&&rt(t);function d(r){t[35](r)}function m(r){t[36](r)}function z(r){t[37](r)}function _(r){t[38](r)}let v={file_types:t[6],root:t[23],label:t[9],info:t[10],show_label:t[11],lines:t[7],rtl:t[18],text_align:t[19],waveform_settings:t[30],i18n:t[2].i18n,max_lines:t[12]?t[12]:t[7]+1,placeholder:t[8],submit_btn:t[15],stop_btn:t[16],autofocus:t[20],autoscroll:t[21],file_count:t[24],sources:t[26],max_file_size:t[2].max_file_size,disabled:!t[22],upload:t[33],stream_handler:t[34],max_plain_text_length:t[25],html_attributes:t[27]};return t[0]!==void 0&&(v.value=t[0]),t[1]!==void 0&&(v.value_is_output=t[1]),t[28]!==void 0&&(v.dragging=t[28]),t[29]!==void 0&&(v.active_source=t[29]),l=new hl({props:v}),Se.push(()=>Ce(l,"value",d)),Se.push(()=>Ce(l,"value_is_output",m)),Se.push(()=>Ce(l,"dragging",z)),Se.push(()=>Ce(l,"active_source",_)),l.$on("change",t[39]),l.$on("input",t[40]),l.$on("submit",t[41]),l.$on("stop",t[42]),l.$on("blur",t[43]),l.$on("select",t[44]),l.$on("focus",t[45]),l.$on("error",t[46]),l.$on("start_recording",t[47]),l.$on("pause_recording",t[48]),l.$on("stop_recording",t[49]),l.$on("upload",t[50]),l.$on("clear",t[51]),{c(){a&&a.c(),e=Ml(),Ye(l.$$.fragment)},m(r,g){a&&a.m(r,g),Sl(r,e,g),Xe(l,r,g),f=!0},p(r,g){r[17]?a?(a.p(r,g),g[0]&131072&&ce(a,1)):(a=rt(r),a.c(),ce(a,1),a.m(e.parentNode,e)):a&&(zl(),be(a,1,1,()=>{a=null}),pl());const b={};g[0]&64&&(b.file_types=r[6]),g[0]&8388608&&(b.root=r[23]),g[0]&512&&(b.label=r[9]),g[0]&1024&&(b.info=r[10]),g[0]&2048&&(b.show_label=r[11]),g[0]&128&&(b.lines=r[7]),g[0]&262144&&(b.rtl=r[18]),g[0]&524288&&(b.text_align=r[19]),g[0]&1073741824&&(b.waveform_settings=r[30]),g[0]&4&&(b.i18n=r[2].i18n),g[0]&4224&&(b.max_lines=r[12]?r[12]:r[7]+1),g[0]&256&&(b.placeholder=r[8]),g[0]&32768&&(b.submit_btn=r[15]),g[0]&65536&&(b.stop_btn=r[16]),g[0]&1048576&&(b.autofocus=r[20]),g[0]&2097152&&(b.autoscroll=r[21]),g[0]&16777216&&(b.file_count=r[24]),g[0]&67108864&&(b.sources=r[26]),g[0]&4&&(b.max_file_size=r[2].max_file_size),g[0]&4194304&&(b.disabled=!r[22]),g[0]&4&&(b.upload=r[33]),g[0]&4&&(b.stream_handler=r[34]),g[0]&33554432&&(b.max_plain_text_length=r[25]),g[0]&134217728&&(b.html_attributes=r[27]),!i&&g[0]&1&&(i=!0,b.value=r[0],ze(()=>i=!1)),!o&&g[0]&2&&(o=!0,b.value_is_output=r[1],ze(()=>o=!1)),!s&&g[0]&268435456&&(s=!0,b.dragging=r[28],ze(()=>s=!1)),!h&&g[0]&536870912&&(h=!0,b.active_source=r[29],ze(()=>h=!1)),l.$set(b)},i(r){f||(ce(a),ce(l.$$.fragment,r),f=!0)},o(r){be(a),be(l.$$.fragment,r),f=!1},d(r){r&&wl(e),a&&a.d(r),Ne(l,r)}}}function Bl(t){let e,l;return e=new At({props:{visible:t[5],elem_id:t[3],elem_classes:[...t[4],"multimodal-textbox"],scale:t[13],min_width:t[14],allow_overflow:!1,padding:!1,border_mode:t[28]?"focus":"base",$$slots:{default:[Dl]},$$scope:{ctx:t}}}),{c(){Ye(e.$$.fragment)},m(i,o){Xe(e,i,o),l=!0},p(i,o){const s={};o[0]&32&&(s.visible=i[5]),o[0]&8&&(s.elem_id=i[3]),o[0]&16&&(s.elem_classes=[...i[4],"multimodal-textbox"]),o[0]&8192&&(s.scale=i[13]),o[0]&16384&&(s.min_width=i[14]),o[0]&268435456&&(s.border_mode=i[28]?"focus":"base"),o[0]&2147459015|o[1]&16777216&&(s.$$scope={dirty:o,ctx:i}),e.$set(s)},i(i){l||(ce(e.$$.fragment,i),l=!0)},o(i){be(e.$$.fragment,i),l=!1},d(i){Ne(e,i)}}}function El(t,e,l){let{gradio:i}=e,{elem_id:o=""}=e,{elem_classes:s=[]}=e,{visible:h=!0}=e,{value:f={text:"",files:[]}}=e,{file_types:a=null}=e,{lines:d}=e,{placeholder:m=""}=e,{label:z="MultimodalTextbox"}=e,{info:_=void 0}=e,{show_label:v}=e,{max_lines:r}=e,{scale:g=null}=e,{min_width:b=void 0}=e,{submit_btn:W=null}=e,{stop_btn:q=null}=e,{loading_status:A=void 0}=e,{value_is_output:P=!1}=e,{rtl:x=!1}=e,{text_align:$=void 0}=e,{autofocus:ae=!1}=e,{autoscroll:ne=!0}=e,{interactive:H}=e,{root:se}=e,{file_count:re}=e,{max_plain_text_length:V}=e,{sources:T=["upload"]}=e,{waveform_options:D={}}=e,{html_attributes:M=null}=e,C,F=null,c,p="darkorange";Tl(()=>{p=getComputedStyle(document?.documentElement).getPropertyValue("--color-accent"),me(),l(30,c.waveColor=D.waveform_color||"#9ca3af",c),l(30,c.progressColor=D.waveform_progress_color||p,c),l(30,c.mediaControls=D.show_controls,c),l(30,c.sampleRate=D.sample_rate||44100,c)});const U={color:D.trim_region_color,drag:!0,resize:!0};function me(){document.documentElement.style.setProperty("--trim-region-color",U.color||p)}const ge=()=>i.dispatch("clear_status",A),ee=(...u)=>i.client.upload(...u),fe=(...u)=>i.client.stream(...u);function I(u){f=u,l(0,f)}function Me(u){P=u,l(1,P)}function pe(u){C=u,l(28,C)}function Te(u){F=u,l(29,F)}const De=()=>i.dispatch("change",f),Be=()=>i.dispatch("input"),Ee=()=>i.dispatch("submit"),we=()=>i.dispatch("stop"),He=()=>i.dispatch("blur"),Fe=u=>i.dispatch("select",u.detail),Re=()=>i.dispatch("focus"),Ve=({detail:u})=>{i.dispatch("error",u)},Ae=()=>i.dispatch("start_recording"),Pe=()=>i.dispatch("pause_recording"),Ie=()=>i.dispatch("stop_recording"),Ue=u=>i.dispatch("upload",u.detail),je=()=>i.dispatch("clear");return t.$$set=u=>{"gradio"in u&&l(2,i=u.gradio),"elem_id"in u&&l(3,o=u.elem_id),"elem_classes"in u&&l(4,s=u.elem_classes),"visible"in u&&l(5,h=u.visible),"value"in u&&l(0,f=u.value),"file_types"in u&&l(6,a=u.file_types),"lines"in u&&l(7,d=u.lines),"placeholder"in u&&l(8,m=u.placeholder),"label"in u&&l(9,z=u.label),"info"in u&&l(10,_=u.info),"show_label"in u&&l(11,v=u.show_label),"max_lines"in u&&l(12,r=u.max_lines),"scale"in u&&l(13,g=u.scale),"min_width"in u&&l(14,b=u.min_width),"submit_btn"in u&&l(15,W=u.submit_btn),"stop_btn"in u&&l(16,q=u.stop_btn),"loading_status"in u&&l(17,A=u.loading_status),"value_is_output"in u&&l(1,P=u.value_is_output),"rtl"in u&&l(18,x=u.rtl),"text_align"in u&&l(19,$=u.text_align),"autofocus"in u&&l(20,ae=u.autofocus),"autoscroll"in u&&l(21,ne=u.autoscroll),"interactive"in u&&l(22,H=u.interactive),"root"in u&&l(23,se=u.root),"file_count"in u&&l(24,re=u.file_count),"max_plain_text_length"in u&&l(25,V=u.max_plain_text_length),"sources"in u&&l(26,T=u.sources),"waveform_options"in u&&l(31,D=u.waveform_options),"html_attributes"in u&&l(27,M=u.html_attributes)},l(30,c={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:!1,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20}),[f,P,i,o,s,h,a,d,m,z,_,v,r,g,b,W,q,A,x,$,ae,ne,H,se,re,V,T,M,C,F,c,D,ge,ee,fe,I,Me,pe,Te,De,Be,Ee,we,He,Fe,Re,Ve,Ae,Pe,Ie,Ue,je]}class pi extends dl{constructor(e){super(),Cl(this,e,El,Bl,ql,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,file_types:6,lines:7,placeholder:8,label:9,info:10,show_label:11,max_lines:12,scale:13,min_width:14,submit_btn:15,stop_btn:16,loading_status:17,value_is_output:1,rtl:18,text_align:19,autofocus:20,autoscroll:21,interactive:22,root:23,file_count:24,max_plain_text_length:25,sources:26,waveform_options:31,html_attributes:27},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),E()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),E()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),E()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),E()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get file_types(){return this.$$.ctx[6]}set file_types(e){this.$$set({file_types:e}),E()}get lines(){return this.$$.ctx[7]}set lines(e){this.$$set({lines:e}),E()}get placeholder(){return this.$$.ctx[8]}set placeholder(e){this.$$set({placeholder:e}),E()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),E()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),E()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),E()}get max_lines(){return this.$$.ctx[12]}set max_lines(e){this.$$set({max_lines:e}),E()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),E()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),E()}get submit_btn(){return this.$$.ctx[15]}set submit_btn(e){this.$$set({submit_btn:e}),E()}get stop_btn(){return this.$$.ctx[16]}set stop_btn(e){this.$$set({stop_btn:e}),E()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),E()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),E()}get rtl(){return this.$$.ctx[18]}set rtl(e){this.$$set({rtl:e}),E()}get text_align(){return this.$$.ctx[19]}set text_align(e){this.$$set({text_align:e}),E()}get autofocus(){return this.$$.ctx[20]}set autofocus(e){this.$$set({autofocus:e}),E()}get autoscroll(){return this.$$.ctx[21]}set autoscroll(e){this.$$set({autoscroll:e}),E()}get interactive(){return this.$$.ctx[22]}set interactive(e){this.$$set({interactive:e}),E()}get root(){return this.$$.ctx[23]}set root(e){this.$$set({root:e}),E()}get file_count(){return this.$$.ctx[24]}set file_count(e){this.$$set({file_count:e}),E()}get max_plain_text_length(){return this.$$.ctx[25]}set max_plain_text_length(e){this.$$set({max_plain_text_length:e}),E()}get sources(){return this.$$.ctx[26]}set sources(e){this.$$set({sources:e}),E()}get waveform_options(){return this.$$.ctx[31]}set waveform_options(e){this.$$set({waveform_options:e}),E()}get html_attributes(){return this.$$.ctx[27]}set html_attributes(e){this.$$set({html_attributes:e}),E()}}export{vi as BaseExample,hl as BaseMultimodalTextbox,pi as default};
//# sourceMappingURL=Index-DzyfujkC.js.map
