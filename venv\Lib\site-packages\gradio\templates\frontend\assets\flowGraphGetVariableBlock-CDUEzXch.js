import{F as r}from"./KHR_interactivity-DTxiAnOo.js";import{R as i}from"./declarationMapper-BZjsjg7g.js";import{R as l}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class t extends r{constructor(a){super(a),this.config=a,this.value=this.registerDataOutput("value",i,a.initialValue)}_updateOutputs(a){const e=this.config.variable;a.hasVariable(e)&&this.value.setValue(a.getVariable(e),a)}serialize(a){super.serialize(a),a.config.variable=this.config.variable}getClassName(){return"FlowGraphGetVariableBlock"}}l("FlowGraphGetVariableBlock",t);export{t as FlowGraphGetVariableBlock};
//# sourceMappingURL=flowGraphGetVariableBlock-CDUEzXch.js.map
