// qcolordialog.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QColorDialog : public QDialog
{
%TypeHeaderCode
#include <qcolordialog.h>
%End

public:
    enum ColorDialogOption /BaseType=Flag/
    {
        ShowAl<PERSON>Channel,
        <PERSON><PERSON><PERSON>ons,
        DontUseNativeDialog,
%If (Qt_6_7_0 -)
        NoEyeDropperButton,
%End
    };

    typedef QFlags<QColorDialog::ColorDialogOption> ColorDialogOptions;
    explicit QColorDialog(QWidget *parent /TransferThis/ = 0);
    QColorDialog(const QColor &initial, QWidget *parent /TransferThis/ = 0);
    virtual ~QColorDialog();
    static QColor getColor(const QColor &initial = Qt::white, QWidget *parent = 0, const QString &title = QString(), QColorDialog::ColorDialogOptions options = QColorDialog::ColorDialogOptions()) /ReleaseGIL/;
    static int customCount();
    static QColor customColor(int index);
    static void setCustomColor(int index, QColor color);
    static QColor standardColor(int index);
    static void setStandardColor(int index, QColor color);

public slots:
    virtual void open();

signals:
    void colorSelected(const QColor &color);
    void currentColorChanged(const QColor &color);

protected:
    virtual void changeEvent(QEvent *e);
    virtual void done(int result);

public:
    void setCurrentColor(const QColor &color);
    QColor currentColor() const;
    QColor selectedColor() const;
    void setOption(QColorDialog::ColorDialogOption option, bool on = true);
    bool testOption(QColorDialog::ColorDialogOption option) const;
    void setOptions(QColorDialog::ColorDialogOptions options);
    QColorDialog::ColorDialogOptions options() const;
    void open(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a0, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipCpp->open(receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    virtual void setVisible(bool visible);
};
