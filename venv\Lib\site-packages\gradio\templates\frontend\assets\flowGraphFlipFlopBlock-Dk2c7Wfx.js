import{f as a}from"./KHR_interactivity-DTxiAnOo.js";import{c as e}from"./declarationMapper-BZjsjg7g.js";import{R as l}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class o extends a{constructor(t){super(t),this.onOn=this._registerSignalOutput("onOn"),this.onOff=this._registerSignalOutput("onOff"),this.value=this.registerDataOutput("value",e)}_execute(t,r){let i=t._getExecutionVariable(this,"value",typeof this.config?.startValue=="boolean"?!this.config.startValue:!1);i=!i,t._setExecutionVariable(this,"value",i),this.value.setValue(i,t),i?this.onOn._activateSignal(t):this.onOff._activateSignal(t)}getClassName(){return"FlowGraphFlipFlopBlock"}}l("FlowGraphFlipFlopBlock",o);export{o as FlowGraphFlipFlopBlock};
//# sourceMappingURL=flowGraphFlipFlopBlock-Dk2c7Wfx.js.map
