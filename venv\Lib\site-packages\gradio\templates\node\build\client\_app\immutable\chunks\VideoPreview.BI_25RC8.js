import{SvelteComponent as he,init as ge,safe_not_equal as ce,svg_element as Be,claim_svg_element as Ne,children as B,detach as E,attr as k,insert_hydration as P,append_hydration as S,noop as ie,element as M,claim_element as R,onMount as Se,onD<PERSON><PERSON> as Ye,ensure_array_like as Ae,space as F,claim_space as G,set_style as ae,listen as J,update_keyed_each as Ze,destroy_block as xe,run_all as Le,get_svelte_dataset as Me,src_url_equal as Ee,create_component as A,claim_component as q,toggle_class as $,mount_component as U,transition_in as y,group_outros as re,transition_out as T,check_outros as oe,destroy_component as H,binding_callbacks as te,bind as ne,add_flush_callback as le,empty as ue,text as ke,claim_text as pe,set_data as Re,prevent_default as qe,stop_propagation as $e,createEventDispatcher as Ke,bubble as Z,afterUpdate as et,tick as tt}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{U as _e,I as Pe,A as nt,u as lt}from"./2.B2AoQPnG.js";import{B as it}from"./BlockLabel.BTSz9r5s.js";import{E as rt}from"./Empty.DwQ6nkN6.js";import{S as ot}from"./ShareButton.Be7APJkJ.js";import{D as at}from"./Download.CpfEFmFf.js";import{V as Je}from"./Video.CE2Y9LYL.js";import{I as ut}from"./IconButtonWrapper.D5aGR59h.js";import{D as st}from"./DownloadLink.D1g3Q1HV.js";import{T as ft,P as dt}from"./Trim.CWFkmJwA.js";import{P as _t}from"./Play.DJ4h2PVY.js";import{U as Qe}from"./Undo.LhwFM5M8.js";import{b as ct,t as mt,V as ht}from"./Video.SijWdeHX.js";/* empty css                                             */import{M as gt}from"./ModifyUpload.uW4g0eE0.js";function bt(l){let e,n;return{c(){e=Be("svg"),n=Be("path"),this.h()},l(t){e=Ne(t,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var i=B(e);n=Ne(i,"path",{d:!0}),B(n).forEach(E),i.forEach(E),this.h()},h(){k(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),k(e,"xmlns","http://www.w3.org/2000/svg"),k(e,"width","100%"),k(e,"height","100%"),k(e,"viewBox","0 0 24 24"),k(e,"fill","none"),k(e,"stroke","currentColor"),k(e,"stroke-width","1.5"),k(e,"stroke-linecap","round"),k(e,"stroke-linejoin","round")},m(t,i){P(t,e,i),S(e,n)},p:ie,i:ie,o:ie,d(t){t&&E(e)}}}class vt extends he{constructor(e){super(),ge(this,e,null,bt,ce,{})}}function Ue(l,e,n){const t=l.slice();return t[20]=e[n],t[22]=n,t}function wt(l){let e,n,t,i,o,r=[],a=new Map,c,g,f,v,d=Ae(l[1]);const u=_=>_[22];for(let _=0;_<d.length;_+=1){let m=Ue(l,d,_),V=u(m);a.set(V,r[_]=He(V,m))}return{c(){e=M("div"),n=M("button"),t=F(),i=M("div"),o=F();for(let _=0;_<r.length;_+=1)r[_].c();c=F(),g=M("button"),this.h()},l(_){e=R(_,"DIV",{id:!0,class:!0});var m=B(e);n=R(m,"BUTTON",{"aria-label":!0,class:!0,style:!0}),B(n).forEach(E),t=G(m),i=R(m,"DIV",{class:!0,style:!0}),B(i).forEach(E),o=G(m);for(let V=0;V<r.length;V+=1)r[V].l(m);c=G(m),g=R(m,"BUTTON",{"aria-label":!0,class:!0,style:!0}),B(g).forEach(E),m.forEach(E),this.h()},h(){k(n,"aria-label","start drag handle for trimming video"),k(n,"class","handle left svelte-10c4beq"),ae(n,"left",l[2]+"%"),k(i,"class","opaque-layer svelte-10c4beq"),ae(i,"left",l[2]+"%"),ae(i,"right",100-l[3]+"%"),k(g,"aria-label","end drag handle for trimming video"),k(g,"class","handle right svelte-10c4beq"),ae(g,"left",l[3]+"%"),k(e,"id","timeline"),k(e,"class","thumbnail-wrapper svelte-10c4beq")},m(_,m){P(_,e,m),S(e,n),S(e,t),S(e,i),S(e,o);for(let V=0;V<r.length;V+=1)r[V]&&r[V].m(e,null);S(e,c),S(e,g),f||(v=[J(n,"mousedown",l[10]),J(n,"blur",l[5]),J(n,"keydown",l[11]),J(g,"mousedown",l[12]),J(g,"blur",l[5]),J(g,"keydown",l[13])],f=!0)},p(_,m){m&4&&ae(n,"left",_[2]+"%"),m&4&&ae(i,"left",_[2]+"%"),m&8&&ae(i,"right",100-_[3]+"%"),m&2&&(d=Ae(_[1]),r=Ze(r,m,u,1,_,d,a,e,xe,He,c,Ue)),m&8&&ae(g,"left",_[3]+"%")},d(_){_&&E(e);for(let m=0;m<r.length;m+=1)r[m].d();f=!1,Le(v)}}}function kt(l){let e,n='<span aria-label="loading timeline" class="loader svelte-10c4beq"></span>';return{c(){e=M("div"),e.innerHTML=n,this.h()},l(t){e=R(t,"DIV",{class:!0,"data-svelte-h":!0}),Me(e)!=="svelte-13yzice"&&(e.innerHTML=n),this.h()},h(){k(e,"class","load-wrap svelte-10c4beq")},m(t,i){P(t,e,i)},p:ie,d(t){t&&E(e)}}}function He(l,e){let n,t,i;return{key:l,first:null,c(){n=M("img"),this.h()},l(o){n=R(o,"IMG",{src:!0,alt:!0,draggable:!0,class:!0}),this.h()},h(){Ee(n.src,t=e[20])||k(n,"src",t),k(n,"alt",i=`frame-${e[22]}`),k(n,"draggable","false"),k(n,"class","svelte-10c4beq"),this.first=n},m(o,r){P(o,n,r)},p(o,r){e=o,r&2&&!Ee(n.src,t=e[20])&&k(n,"src",t),r&2&&i!==(i=`frame-${e[22]}`)&&k(n,"alt",i)},d(o){o&&E(n)}}}function pt(l){let e;function n(o,r){return o[0]?kt:wt}let t=n(l),i=t(l);return{c(){e=M("div"),i.c(),this.h()},l(o){e=R(o,"DIV",{class:!0});var r=B(e);i.l(r),r.forEach(E),this.h()},h(){k(e,"class","container svelte-10c4beq")},m(o,r){P(o,e,r),i.m(e,null)},p(o,[r]){t===(t=n(o))&&i?i.p(o,r):(i.d(1),i=t(o),i&&(i.c(),i.m(e,null)))},i:ie,o:ie,d(o){o&&E(e),i.d()}}}let Ie=10;function Et(l,e,n){let{videoElement:t}=e,{trimmedDuration:i}=e,{dragStart:o}=e,{dragEnd:r}=e,{loadingTimeline:a}=e,c=[],g,f=0,v=100,d=null;const u=b=>{d=b},_=()=>{d=null},m=(b,L)=>{if(d){const I=document.getElementById("timeline");if(!I)return;const h=I.getBoundingClientRect();let N=(b.clientX-h.left)/h.width*100;if(L?N=d==="left"?f+L:v+L:N=(b.clientX-h.left)/h.width*100,N=Math.max(0,Math.min(N,100)),d==="left"){n(2,f=Math.min(N,v));const z=f/100*g;n(6,t.currentTime=z,t),n(8,o=z)}else if(d==="right"){n(3,v=Math.max(N,f));const z=v/100*g;n(6,t.currentTime=z,t),n(9,r=z)}const x=f/100*g,p=v/100*g;n(7,i=p-x),n(2,f),n(3,v)}},V=b=>{if(d){const L=1/g*100;b.key==="ArrowLeft"?m({clientX:0},-L):b.key==="ArrowRight"&&m({clientX:0},L)}},X=()=>{const b=document.createElement("canvas"),L=b.getContext("2d");if(!L)return;b.width=t.videoWidth,b.height=t.videoHeight,L.drawImage(t,0,0,b.width,b.height);const I=b.toDataURL("image/jpeg",.7);n(1,c=[...c,I])};Se(()=>{const b=()=>{g=t.duration;const L=g/Ie;let I=0;const h=()=>{X(),I++,I<Ie?n(6,t.currentTime+=L,t):t.removeEventListener("seeked",h)};t.addEventListener("seeked",h),n(6,t.currentTime=0,t)};t.readyState>=1?b():t.addEventListener("loadedmetadata",b)}),Ye(()=>{window.removeEventListener("mousemove",m),window.removeEventListener("mouseup",_),window.removeEventListener("keydown",V)}),Se(()=>{window.addEventListener("mousemove",m),window.addEventListener("mouseup",_),window.addEventListener("keydown",V)});const W=()=>u("left"),j=b=>{(b.key==="ArrowLeft"||b.key=="ArrowRight")&&u("left")},Q=()=>u("right"),O=b=>{(b.key==="ArrowLeft"||b.key=="ArrowRight")&&u("right")};return l.$$set=b=>{"videoElement"in b&&n(6,t=b.videoElement),"trimmedDuration"in b&&n(7,i=b.trimmedDuration),"dragStart"in b&&n(8,o=b.dragStart),"dragEnd"in b&&n(9,r=b.dragEnd),"loadingTimeline"in b&&n(0,a=b.loadingTimeline)},l.$$.update=()=>{l.$$.dirty&2&&n(0,a=c.length!==Ie)},[a,c,f,v,u,_,t,i,o,r,W,j,Q,O]}class yt extends he{constructor(e){super(),ge(this,e,Et,pt,ce,{videoElement:6,trimmedDuration:7,dragStart:8,dragEnd:9,loadingTimeline:0})}}function Oe(l){let e,n,t,i,o,r,a;function c(u){l[18](u)}function g(u){l[19](u)}function f(u){l[20](u)}function v(u){l[21](u)}let d={videoElement:l[2]};return l[14]!==void 0&&(d.dragStart=l[14]),l[15]!==void 0&&(d.dragEnd=l[15]),l[12]!==void 0&&(d.trimmedDuration=l[12]),l[16]!==void 0&&(d.loadingTimeline=l[16]),n=new yt({props:d}),te.push(()=>ne(n,"dragStart",c)),te.push(()=>ne(n,"dragEnd",g)),te.push(()=>ne(n,"trimmedDuration",f)),te.push(()=>ne(n,"loadingTimeline",v)),{c(){e=M("div"),A(n.$$.fragment),this.h()},l(u){e=R(u,"DIV",{class:!0});var _=B(e);q(n.$$.fragment,_),_.forEach(E),this.h()},h(){k(e,"class","timeline-wrapper svelte-7yrr5f")},m(u,_){P(u,e,_),U(n,e,null),a=!0},p(u,_){const m={};_&4&&(m.videoElement=u[2]),!t&&_&16384&&(t=!0,m.dragStart=u[14],le(()=>t=!1)),!i&&_&32768&&(i=!0,m.dragEnd=u[15],le(()=>i=!1)),!o&&_&4096&&(o=!0,m.trimmedDuration=u[12],le(()=>o=!1)),!r&&_&65536&&(r=!0,m.loadingTimeline=u[16],le(()=>r=!1)),n.$set(m)},i(u){a||(y(n.$$.fragment,u),a=!0)},o(u){T(n.$$.fragment,u),a=!1},d(u){u&&E(e),H(n)}}}function Tt(l){let e;return{c(){e=M("div"),this.h()},l(n){e=R(n,"DIV",{class:!0}),B(e).forEach(E),this.h()},h(){k(e,"class","svelte-7yrr5f")},m(n,t){P(n,e,t)},p:ie,d(n){n&&E(e)}}}function Vt(l){let e,n=_e(l[12])+"",t,i,o,r,a="Trim",c,g,f="Cancel",v,d;return{c(){e=M("time"),t=ke(n),i=F(),o=M("div"),r=M("button"),r.textContent=a,c=F(),g=M("button"),g.textContent=f,this.h()},l(u){e=R(u,"TIME",{"aria-label":!0,class:!0});var _=B(e);t=pe(_,n),_.forEach(E),i=G(u),o=R(u,"DIV",{class:!0});var m=B(o);r=R(m,"BUTTON",{class:!0,"data-svelte-h":!0}),Me(r)!=="svelte-18nzick"&&(r.textContent=a),c=G(m),g=R(m,"BUTTON",{class:!0,"data-svelte-h":!0}),Me(g)!=="svelte-1mj98i4"&&(g.textContent=f),m.forEach(E),this.h()},h(){k(e,"aria-label","duration of selected region in seconds"),k(e,"class","svelte-7yrr5f"),$(e,"hidden",l[16]),k(r,"class","text-button svelte-7yrr5f"),$(r,"hidden",l[16]),k(g,"class","text-button svelte-7yrr5f"),$(g,"hidden",l[16]),k(o,"class","edit-buttons svelte-7yrr5f")},m(u,_){P(u,e,_),S(e,t),P(u,i,_),P(u,o,_),S(o,r),S(o,c),S(o,g),v||(d=[J(r,"click",l[22]),J(g,"click",l[17])],v=!0)},p(u,_){_&4096&&n!==(n=_e(u[12])+"")&&Re(t,n),_&65536&&$(e,"hidden",u[16]),_&65536&&$(r,"hidden",u[16]),_&65536&&$(g,"hidden",u[16])},d(u){u&&(E(e),E(i),E(o)),v=!1,Le(d)}}}function Xe(l){let e,n;return e=new Pe({props:{Icon:Qe,label:"Reset video to initial value",disabled:l[1]||!l[11]}}),e.$on("click",l[23]),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},p(t,i){const o={};i&2050&&(o.disabled=t[1]||!t[11]),e.$set(o)},i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function je(l){let e,n;return e=new Pe({props:{Icon:ft,label:"Trim video to selection",disabled:l[1]}}),e.$on("click",l[17]),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},p(t,i){const o={};i&2&&(o.disabled=t[1]),e.$set(o)},i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function Dt(l){let e,n,t,i=l[3]&&l[0]===""&&Xe(l),o=l[4]&&l[0]===""&&je(l);return{c(){i&&i.c(),e=F(),o&&o.c(),n=ue()},l(r){i&&i.l(r),e=G(r),o&&o.l(r),n=ue()},m(r,a){i&&i.m(r,a),P(r,e,a),o&&o.m(r,a),P(r,n,a),t=!0},p(r,a){r[3]&&r[0]===""?i?(i.p(r,a),a&9&&y(i,1)):(i=Xe(r),i.c(),y(i,1),i.m(e.parentNode,e)):i&&(re(),T(i,1,1,()=>{i=null}),oe()),r[4]&&r[0]===""?o?(o.p(r,a),a&17&&y(o,1)):(o=je(r),o.c(),y(o,1),o.m(n.parentNode,n)):o&&(re(),T(o,1,1,()=>{o=null}),oe())},i(r){t||(y(i),y(o),t=!0)},o(r){T(i),T(o),t=!1},d(r){r&&(E(e),E(n)),i&&i.d(r),o&&o.d(r)}}}function It(l){var v;let e,n,t,i,o,r,a=l[0]==="edit"&&Oe(l);function c(d,u){return d[0]==="edit"&&d[12]!==null?Vt:Tt}let g=c(l),f=g(l);return o=new gt({props:{i18n:l[7],download:l[9]?(v=l[8])==null?void 0:v.url:null,$$slots:{default:[Dt]},$$scope:{ctx:l}}}),o.$on("clear",l[24]),{c(){e=M("div"),a&&a.c(),n=F(),t=M("div"),f.c(),i=F(),A(o.$$.fragment),this.h()},l(d){e=R(d,"DIV",{class:!0});var u=B(e);a&&a.l(u),n=G(u),t=R(u,"DIV",{class:!0,"data-testid":!0});var _=B(t);f.l(_),_.forEach(E),u.forEach(E),i=G(d),q(o.$$.fragment,d),this.h()},h(){k(t,"class","controls svelte-7yrr5f"),k(t,"data-testid","waveform-controls"),k(e,"class","container svelte-7yrr5f"),$(e,"hidden",l[0]!=="edit")},m(d,u){P(d,e,u),a&&a.m(e,null),S(e,n),S(e,t),f.m(t,null),P(d,i,u),U(o,d,u),r=!0},p(d,[u]){var m;d[0]==="edit"?a?(a.p(d,u),u&1&&y(a,1)):(a=Oe(d),a.c(),y(a,1),a.m(e,n)):a&&(re(),T(a,1,1,()=>{a=null}),oe()),g===(g=c(d))&&f?f.p(d,u):(f.d(1),f=g(d),f&&(f.c(),f.m(t,null))),(!r||u&1)&&$(e,"hidden",d[0]!=="edit");const _={};u&128&&(_.i18n=d[7]),u&768&&(_.download=d[9]?(m=d[8])==null?void 0:m.url:null),u&33556539&&(_.$$scope={dirty:u,ctx:d}),o.$set(_)},i(d){r||(y(a),y(o.$$.fragment,d),r=!0)},o(d){T(a),T(o.$$.fragment,d),r=!1},d(d){d&&(E(e),E(i)),a&&a.d(),f.d(),H(o,d)}}}function St(l,e,n){let{videoElement:t}=e,{showRedo:i=!1}=e,{interactive:o=!0}=e,{mode:r=""}=e,{handle_reset_value:a}=e,{handle_trim_video:c}=e,{processingVideo:g=!1}=e,{i18n:f}=e,{value:v=null}=e,{show_download_button:d=!1}=e,{handle_clear:u=()=>{}}=e,{has_change_history:_=!1}=e,m;Se(async()=>{n(13,m=await ct())});let V=null,X=0,W=0,j=!1;const Q=()=>{r==="edit"?(n(0,r=""),n(12,V=t.duration)):n(0,r="edit")};function O(p){X=p,n(14,X)}function b(p){W=p,n(15,W)}function L(p){V=p,n(12,V),n(0,r),n(2,t)}function I(p){j=p,n(16,j)}const h=()=>{n(0,r=""),n(1,g=!0),mt(m,X,W,t).then(p=>{c(p)}).then(()=>{n(1,g=!1)})},N=()=>{a(),n(0,r="")},x=()=>u();return l.$$set=p=>{"videoElement"in p&&n(2,t=p.videoElement),"showRedo"in p&&n(3,i=p.showRedo),"interactive"in p&&n(4,o=p.interactive),"mode"in p&&n(0,r=p.mode),"handle_reset_value"in p&&n(5,a=p.handle_reset_value),"handle_trim_video"in p&&n(6,c=p.handle_trim_video),"processingVideo"in p&&n(1,g=p.processingVideo),"i18n"in p&&n(7,f=p.i18n),"value"in p&&n(8,v=p.value),"show_download_button"in p&&n(9,d=p.show_download_button),"handle_clear"in p&&n(10,u=p.handle_clear),"has_change_history"in p&&n(11,_=p.has_change_history)},l.$$.update=()=>{l.$$.dirty&4101&&r==="edit"&&V===null&&t&&n(12,V=t.duration)},[r,g,t,i,o,a,c,f,v,d,u,_,V,m,X,W,j,Q,O,b,L,I,h,N,x]}class Mt extends he{constructor(e){super(),ge(this,e,St,It,ce,{videoElement:2,showRedo:3,interactive:4,mode:0,handle_reset_value:5,handle_trim_video:6,processingVideo:1,i18n:7,value:8,show_download_button:9,handle_clear:10,has_change_history:11})}}function Rt(l){let e,n;return{c(){e=M("track"),this.h()},l(t){e=R(t,"TRACK",{kind:!0,src:!0}),this.h()},h(){k(e,"kind","captions"),Ee(e.src,n=l[1])||k(e,"src",n),e.default=!0},m(t,i){P(t,e,i)},p(t,i){i[0]&2&&!Ee(e.src,n=t[1])&&k(e,"src",n)},d(t){t&&E(e)}}}function Lt(l){let e,n;return e=new dt({}),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function Pt(l){let e,n;return e=new _t({}),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function Ct(l){let e,n;return e=new Qe({}),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function ze(l){let e,n,t;function i(r){l[38](r)}let o={videoElement:l[17],showRedo:!0,handle_trim_video:l[23],handle_reset_value:l[7],value:l[11],i18n:l[9],show_download_button:l[10],handle_clear:l[12],has_change_history:l[13]};return l[18]!==void 0&&(o.processingVideo=l[18]),e=new Mt({props:o}),te.push(()=>ne(e,"processingVideo",i)),{c(){A(e.$$.fragment)},l(r){q(e.$$.fragment,r)},m(r,a){U(e,r,a),t=!0},p(r,a){const c={};a[0]&131072&&(c.videoElement=r[17]),a[0]&128&&(c.handle_reset_value=r[7]),a[0]&2048&&(c.value=r[11]),a[0]&512&&(c.i18n=r[9]),a[0]&1024&&(c.show_download_button=r[10]),a[0]&4096&&(c.handle_clear=r[12]),a[0]&8192&&(c.has_change_history=r[13]),!n&&a[0]&262144&&(n=!0,c.processingVideo=r[18],le(()=>n=!1)),e.$set(c)},i(r){t||(y(e.$$.fragment,r),t=!0)},o(r){T(e.$$.fragment,r),t=!1},d(r){H(e,r)}}}function Bt(l){let e,n,t,i,o,r,a,c,g,f,v,d,u,_,m,V=_e(l[14])+"",X,W,j=_e(l[15])+"",Q,O,b,L,I,h,N,x,p,z,me,be;function ye(w){l[28](w)}function Te(w){l[29](w)}function Ve(w){l[30](w)}function De(w){l[31](w)}let se={src:l[0],preload:"auto",autoplay:l[3],loop:l[4],is_stream:l[8],"data-testid":`${l[5]}-player`,processingVideo:l[18],$$slots:{default:[Rt]},$$scope:{ctx:l}};l[14]!==void 0&&(se.currentTime=l[14]),l[15]!==void 0&&(se.duration=l[15]),l[16]!==void 0&&(se.paused=l[16]),l[17]!==void 0&&(se.node=l[17]),t=new ht({props:se}),te.push(()=>ne(t,"currentTime",ye)),te.push(()=>ne(t,"duration",Te)),te.push(()=>ne(t,"paused",Ve)),te.push(()=>ne(t,"node",De)),t.$on("click",l[20]),t.$on("play",l[32]),t.$on("pause",l[33]),t.$on("error",l[34]),t.$on("ended",l[22]),t.$on("loadstart",l[35]),t.$on("loadeddata",l[36]),t.$on("loadedmetadata",l[37]);const ve=[Ct,Pt,Lt],ee=[];function we(w,D){return w[14]===w[15]?0:w[16]?1:2}d=we(l),u=ee[d]=ve[d](l),N=new vt({});let C=l[6]&&ze(l);return{c(){e=M("div"),n=M("div"),A(t.$$.fragment),c=F(),g=M("div"),f=M("div"),v=M("span"),u.c(),_=F(),m=M("span"),X=ke(V),W=ke(" / "),Q=ke(j),O=F(),b=M("progress"),I=F(),h=M("div"),A(N.$$.fragment),x=F(),C&&C.c(),p=ue(),this.h()},l(w){e=R(w,"DIV",{class:!0});var D=B(e);n=R(D,"DIV",{class:!0});var s=B(n);q(t.$$.fragment,s),s.forEach(E),c=G(D),g=R(D,"DIV",{class:!0});var Y=B(g);f=R(Y,"DIV",{class:!0});var K=B(f);v=R(K,"SPAN",{role:!0,tabindex:!0,class:!0,"aria-label":!0});var de=B(v);u.l(de),de.forEach(E),_=G(K),m=R(K,"SPAN",{class:!0});var fe=B(m);X=pe(fe,V),W=pe(fe," / "),Q=pe(fe,j),fe.forEach(E),O=G(K),b=R(K,"PROGRESS",{class:!0}),B(b).forEach(E),I=G(K),h=R(K,"DIV",{role:!0,tabindex:!0,class:!0,"aria-label":!0});var Ce=B(h);q(N.$$.fragment,Ce),Ce.forEach(E),K.forEach(E),Y.forEach(E),D.forEach(E),x=G(w),C&&C.l(w),p=ue(),this.h()},h(){k(n,"class","mirror-wrap svelte-euo1cw"),$(n,"mirror",l[2]),k(v,"role","button"),k(v,"tabindex","0"),k(v,"class","icon svelte-euo1cw"),k(v,"aria-label","play-pause-replay-button"),k(m,"class","time svelte-euo1cw"),b.value=L=l[14]/l[15]||0,k(b,"class","svelte-euo1cw"),k(h,"role","button"),k(h,"tabindex","0"),k(h,"class","icon svelte-euo1cw"),k(h,"aria-label","full-screen"),k(f,"class","inner svelte-euo1cw"),k(g,"class","controls svelte-euo1cw"),k(e,"class","wrap svelte-euo1cw")},m(w,D){P(w,e,D),S(e,n),U(t,n,null),S(e,c),S(e,g),S(g,f),S(f,v),ee[d].m(v,null),S(f,_),S(f,m),S(m,X),S(m,W),S(m,Q),S(f,O),S(f,b),S(f,I),S(f,h),U(N,h,null),P(w,x,D),C&&C.m(w,D),P(w,p,D),z=!0,me||(be=[J(v,"click",l[20]),J(v,"keydown",l[20]),J(b,"mousemove",l[19]),J(b,"touchmove",qe(l[19])),J(b,"click",$e(qe(l[21]))),J(h,"click",l[24]),J(h,"keypress",l[24])],me=!0)},p(w,D){const s={};D[0]&1&&(s.src=w[0]),D[0]&8&&(s.autoplay=w[3]),D[0]&16&&(s.loop=w[4]),D[0]&256&&(s.is_stream=w[8]),D[0]&32&&(s["data-testid"]=`${w[5]}-player`),D[0]&262144&&(s.processingVideo=w[18]),D[0]&2|D[1]&512&&(s.$$scope={dirty:D,ctx:w}),!i&&D[0]&16384&&(i=!0,s.currentTime=w[14],le(()=>i=!1)),!o&&D[0]&32768&&(o=!0,s.duration=w[15],le(()=>o=!1)),!r&&D[0]&65536&&(r=!0,s.paused=w[16],le(()=>r=!1)),!a&&D[0]&131072&&(a=!0,s.node=w[17],le(()=>a=!1)),t.$set(s),(!z||D[0]&4)&&$(n,"mirror",w[2]);let Y=d;d=we(w),d!==Y&&(re(),T(ee[Y],1,1,()=>{ee[Y]=null}),oe(),u=ee[d],u||(u=ee[d]=ve[d](w),u.c()),y(u,1),u.m(v,null)),(!z||D[0]&16384)&&V!==(V=_e(w[14])+"")&&Re(X,V),(!z||D[0]&32768)&&j!==(j=_e(w[15])+"")&&Re(Q,j),(!z||D[0]&49152&&L!==(L=w[14]/w[15]||0))&&(b.value=L),w[6]?C?(C.p(w,D),D[0]&64&&y(C,1)):(C=ze(w),C.c(),y(C,1),C.m(p.parentNode,p)):C&&(re(),T(C,1,1,()=>{C=null}),oe())},i(w){z||(y(t.$$.fragment,w),y(u),y(N.$$.fragment,w),y(C),z=!0)},o(w){T(t.$$.fragment,w),T(u),T(N.$$.fragment,w),T(C),z=!1},d(w){w&&(E(e),E(x),E(p)),H(t),ee[d].d(),H(N),C&&C.d(w),me=!1,Le(be)}}}function Nt(l,e,n){let{root:t=""}=e,{src:i}=e,{subtitle:o=null}=e,{mirror:r}=e,{autoplay:a}=e,{loop:c}=e,{label:g="test"}=e,{interactive:f=!1}=e,{handle_change:v=()=>{}}=e,{handle_reset_value:d=()=>{}}=e,{upload:u}=e,{is_stream:_}=e,{i18n:m}=e,{show_download_button:V=!1}=e,{value:X=null}=e,{handle_clear:W=()=>{}}=e,{has_change_history:j=!1}=e;const Q=Ke();let O=0,b,L=!0,I,h=!1;function N(s){if(!b)return;if(s.type==="click"){p(s);return}if(s.type!=="touchmove"&&!(s.buttons&1))return;const Y=s.type==="touchmove"?s.touches[0].clientX:s.clientX,{left:K,right:de}=s.currentTarget.getBoundingClientRect();n(14,O=b*(Y-K)/(de-K))}async function x(){document.fullscreenElement!=I&&(I.currentTime>0&&!I.paused&&!I.ended&&I.readyState>I.HAVE_CURRENT_DATA?I.pause():await I.play())}function p(s){const{left:Y,right:K}=s.currentTarget.getBoundingClientRect();n(14,O=b*(s.clientX-Y)/(K-Y))}function z(){Q("stop"),Q("end")}const me=async s=>{var fe;let Y=new File([s],"video.mp4");const K=await nt([Y]);let de=(fe=await u(K,t))==null?void 0:fe.filter(Boolean)[0];v(de)};function be(){I.requestFullscreen()}function ye(s){O=s,n(14,O)}function Te(s){b=s,n(15,b)}function Ve(s){L=s,n(16,L)}function De(s){I=s,n(17,I)}function se(s){Z.call(this,l,s)}function ve(s){Z.call(this,l,s)}function ee(s){Z.call(this,l,s)}function we(s){Z.call(this,l,s)}function C(s){Z.call(this,l,s)}function w(s){Z.call(this,l,s)}function D(s){h=s,n(18,h)}return l.$$set=s=>{"root"in s&&n(25,t=s.root),"src"in s&&n(0,i=s.src),"subtitle"in s&&n(1,o=s.subtitle),"mirror"in s&&n(2,r=s.mirror),"autoplay"in s&&n(3,a=s.autoplay),"loop"in s&&n(4,c=s.loop),"label"in s&&n(5,g=s.label),"interactive"in s&&n(6,f=s.interactive),"handle_change"in s&&n(26,v=s.handle_change),"handle_reset_value"in s&&n(7,d=s.handle_reset_value),"upload"in s&&n(27,u=s.upload),"is_stream"in s&&n(8,_=s.is_stream),"i18n"in s&&n(9,m=s.i18n),"show_download_button"in s&&n(10,V=s.show_download_button),"value"in s&&n(11,X=s.value),"handle_clear"in s&&n(12,W=s.handle_clear),"has_change_history"in s&&n(13,j=s.has_change_history)},l.$$.update=()=>{l.$$.dirty[0]&16384&&n(14,O=O||0),l.$$.dirty[0]&32768&&n(15,b=b||0)},[i,o,r,a,c,g,f,d,_,m,V,X,W,j,O,b,L,I,h,N,x,p,z,me,be,t,v,u,ye,Te,Ve,De,se,ve,ee,we,C,w,D]}class At extends he{constructor(e){super(),ge(this,e,Nt,Bt,ce,{root:25,src:0,subtitle:1,mirror:2,autoplay:3,loop:4,label:5,interactive:6,handle_change:26,handle_reset_value:7,upload:27,is_stream:8,i18n:9,show_download_button:10,value:11,handle_clear:12,has_change_history:13},null,[-1,-1])}}const qt=At;function Ut(l){let e=l[0].url,n,t,i,o,r=Fe(l);return i=new ut({props:{display_top_corner:l[10],$$slots:{default:[Xt]},$$scope:{ctx:l}}}),{c(){r.c(),n=F(),t=M("div"),A(i.$$.fragment),this.h()},l(a){r.l(a),n=G(a),t=R(a,"DIV",{"data-testid":!0});var c=B(t);q(i.$$.fragment,c),c.forEach(E),this.h()},h(){k(t,"data-testid","download-div")},m(a,c){r.m(a,c),P(a,n,c),P(a,t,c),U(i,t,null),o=!0},p(a,c){c&1&&ce(e,e=a[0].url)?(re(),T(r,1,1,ie),oe(),r=Fe(a),r.c(),y(r,1),r.m(n.parentNode,n)):r.p(a,c);const g={};c&1024&&(g.display_top_corner=a[10]),c&4194657&&(g.$$scope={dirty:c,ctx:a}),i.$set(g)},i(a){o||(y(r),y(i.$$.fragment,a),o=!0)},o(a){T(r),T(i.$$.fragment,a),o=!1},d(a){a&&(E(n),E(t)),r.d(a),H(i)}}}function Ht(l){let e,n;return e=new rt({props:{unpadded_box:!0,size:"large",$$slots:{default:[jt]},$$scope:{ctx:l}}}),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},p(t,i){const o={};i&4194304&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function Fe(l){var t;let e,n;return e=new qt({props:{src:l[0].url,subtitle:(t=l[1])==null?void 0:t.url,is_stream:l[0].is_stream,autoplay:l[4],mirror:!1,label:l[2],loop:l[7],interactive:!1,upload:l[9],i18n:l[8]}}),e.$on("play",l[12]),e.$on("pause",l[13]),e.$on("stop",l[14]),e.$on("end",l[15]),e.$on("loadedmetadata",l[16]),{c(){A(e.$$.fragment)},l(i){q(e.$$.fragment,i)},m(i,o){U(e,i,o),n=!0},p(i,o){var a;const r={};o&1&&(r.src=i[0].url),o&2&&(r.subtitle=(a=i[1])==null?void 0:a.url),o&1&&(r.is_stream=i[0].is_stream),o&16&&(r.autoplay=i[4]),o&4&&(r.label=i[2]),o&128&&(r.loop=i[7]),o&512&&(r.upload=i[9]),o&256&&(r.i18n=i[8]),e.$set(r)},i(i){n||(y(e.$$.fragment,i),n=!0)},o(i){T(e.$$.fragment,i),n=!1},d(i){H(e,i)}}}function Ge(l){var t;let e,n;return e=new st({props:{href:l[0].is_stream?(t=l[0].url)==null?void 0:t.replace("playlist.m3u8","playlist-file"):l[0].url,download:l[0].orig_name||l[0].path,$$slots:{default:[Ot]},$$scope:{ctx:l}}}),{c(){A(e.$$.fragment)},l(i){q(e.$$.fragment,i)},m(i,o){U(e,i,o),n=!0},p(i,o){var a;const r={};o&1&&(r.href=i[0].is_stream?(a=i[0].url)==null?void 0:a.replace("playlist.m3u8","playlist-file"):i[0].url),o&1&&(r.download=i[0].orig_name||i[0].path),o&4194304&&(r.$$scope={dirty:o,ctx:i}),e.$set(r)},i(i){n||(y(e.$$.fragment,i),n=!0)},o(i){T(e.$$.fragment,i),n=!1},d(i){H(e,i)}}}function Ot(l){let e,n;return e=new Pe({props:{Icon:at,label:"Download"}}),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},p:ie,i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function We(l){let e,n;return e=new ot({props:{i18n:l[8],value:l[0],formatter:l[17]}}),e.$on("error",l[18]),e.$on("share",l[19]),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},p(t,i){const o={};i&256&&(o.i18n=t[8]),i&1&&(o.value=t[0]),e.$set(o)},i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function Xt(l){let e,n,t,i=l[6]&&Ge(l),o=l[5]&&We(l);return{c(){i&&i.c(),e=F(),o&&o.c(),n=ue()},l(r){i&&i.l(r),e=G(r),o&&o.l(r),n=ue()},m(r,a){i&&i.m(r,a),P(r,e,a),o&&o.m(r,a),P(r,n,a),t=!0},p(r,a){r[6]?i?(i.p(r,a),a&64&&y(i,1)):(i=Ge(r),i.c(),y(i,1),i.m(e.parentNode,e)):i&&(re(),T(i,1,1,()=>{i=null}),oe()),r[5]?o?(o.p(r,a),a&32&&y(o,1)):(o=We(r),o.c(),y(o,1),o.m(n.parentNode,n)):o&&(re(),T(o,1,1,()=>{o=null}),oe())},i(r){t||(y(i),y(o),t=!0)},o(r){T(i),T(o),t=!1},d(r){r&&(E(e),E(n)),i&&i.d(r),o&&o.d(r)}}}function jt(l){let e,n;return e=new Je({}),{c(){A(e.$$.fragment)},l(t){q(e.$$.fragment,t)},m(t,i){U(e,t,i),n=!0},i(t){n||(y(e.$$.fragment,t),n=!0)},o(t){T(e.$$.fragment,t),n=!1},d(t){H(e,t)}}}function zt(l){let e,n,t,i,o,r;e=new it({props:{show_label:l[3],Icon:Je,label:l[2]||"Video"}});const a=[Ht,Ut],c=[];function g(f,v){return!f[0]||f[0].url===void 0?0:1}return t=g(l),i=c[t]=a[t](l),{c(){A(e.$$.fragment),n=F(),i.c(),o=ue()},l(f){q(e.$$.fragment,f),n=G(f),i.l(f),o=ue()},m(f,v){U(e,f,v),P(f,n,v),c[t].m(f,v),P(f,o,v),r=!0},p(f,[v]){const d={};v&8&&(d.show_label=f[3]),v&4&&(d.label=f[2]||"Video"),e.$set(d);let u=t;t=g(f),t===u?c[t].p(f,v):(re(),T(c[u],1,1,()=>{c[u]=null}),oe(),i=c[t],i?i.p(f,v):(i=c[t]=a[t](f),i.c()),y(i,1),i.m(o.parentNode,o))},i(f){r||(y(e.$$.fragment,f),y(i),r=!0)},o(f){T(e.$$.fragment,f),T(i),r=!1},d(f){f&&(E(n),E(o)),H(e,f),c[t].d(f)}}}function Ft(l,e,n){let{value:t=null}=e,{subtitle:i=null}=e,{label:o=void 0}=e,{show_label:r=!0}=e,{autoplay:a}=e,{show_share_button:c=!0}=e,{show_download_button:g=!0}=e,{loop:f}=e,{i18n:v}=e,{upload:d}=e,{display_icon_button_wrapper_top_corner:u=!1}=e,_=null,m=null;const V=Ke();et(async()=>{t!==_&&i!==m&&m!==null&&(_=t,n(0,t=null),await tt(),n(0,t=_)),_=t,m=i});function X(h){Z.call(this,l,h)}function W(h){Z.call(this,l,h)}function j(h){Z.call(this,l,h)}function Q(h){Z.call(this,l,h)}const O=()=>{V("load")},b=async h=>h?await lt(h.data):"";function L(h){Z.call(this,l,h)}function I(h){Z.call(this,l,h)}return l.$$set=h=>{"value"in h&&n(0,t=h.value),"subtitle"in h&&n(1,i=h.subtitle),"label"in h&&n(2,o=h.label),"show_label"in h&&n(3,r=h.show_label),"autoplay"in h&&n(4,a=h.autoplay),"show_share_button"in h&&n(5,c=h.show_share_button),"show_download_button"in h&&n(6,g=h.show_download_button),"loop"in h&&n(7,f=h.loop),"i18n"in h&&n(8,v=h.i18n),"upload"in h&&n(9,d=h.upload),"display_icon_button_wrapper_top_corner"in h&&n(10,u=h.display_icon_button_wrapper_top_corner)},l.$$.update=()=>{l.$$.dirty&1&&t&&V("change",t)},[t,i,o,r,a,c,g,f,v,d,u,V,X,W,j,Q,O,b,L,I]}class Gt extends he{constructor(e){super(),ge(this,e,Ft,zt,ce,{value:0,subtitle:1,label:2,show_label:3,autoplay:4,show_share_button:5,show_download_button:6,loop:7,i18n:8,upload:9,display_icon_button_wrapper_top_corner:10})}}const sn=Object.freeze(Object.defineProperty({__proto__:null,default:Gt},Symbol.toStringTag,{value:"Module"}));export{qt as P,Gt as V,sn as a};
//# sourceMappingURL=VideoPreview.BI_25RC8.js.map
