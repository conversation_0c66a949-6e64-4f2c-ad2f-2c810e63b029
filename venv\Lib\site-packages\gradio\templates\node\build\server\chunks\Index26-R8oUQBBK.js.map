{"version": 3, "file": "Index26-R8oUQBBK.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index26.js"], "sourcesContent": ["import{create_ssr_component as P,add_styles as tt,validate_component as i,escape as Y,add_attribute as q}from\"svelte/internal\";import nt from\"./ImagePreview.js\";import{createEventDispatcher as N,onMount as it,onDestroy as ot,afterUpdate as lt}from\"svelte\";import{W as rt,t as At,f as ct,u as st,v as dt,w as vt,B as mt,c as et,I as ut,F as Ct,a as _t,h as ft,o as pt,b as H,U as V,E as ht}from\"./FullscreenButton.js\";import{n as gt}from\"./client.js\";import{a as wt,S as J}from\"./StreamingBar.js\";import\"svelte/transition\";import{U as bt}from\"./ModifyUpload.js\";import{I as It}from\"./Image.js\";import{default as qt}from\"./Example10.js\";const yt={code:\"button.svelte-qbrfs{cursor:pointer;width:var(--size-full)}.wrap.svelte-qbrfs{display:flex;flex-direction:column;justify-content:center;align-items:center;min-height:var(--size-60);color:var(--block-label-text-color);height:100%;padding-top:var(--size-3)}.icon-wrap.svelte-qbrfs{width:30px;margin-bottom:var(--spacing-lg)}@media(min-width: 768px){.wrap.svelte-qbrfs{font-size:var(--text-lg)}}\",map:'{\"version\":3,\"file\":\"WebcamPermissions.svelte\",\"sources\":[\"WebcamPermissions.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Webcam } from \\\\\"@gradio/icons\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nconst dispatch = createEventDispatcher();\\\\n<\\/script>\\\\n\\\\n<button style:height=\\\\\"100%\\\\\" on:click={() => dispatch(\\\\\"click\\\\\")}>\\\\n\\\\t<div class=\\\\\"wrap\\\\\">\\\\n\\\\t\\\\t<span class=\\\\\"icon-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t<Webcam />\\\\n\\\\t\\\\t</span>\\\\n\\\\t\\\\t{\\\\\"Click to Access Webcam\\\\\"}\\\\n\\\\t</div>\\\\n</button>\\\\n\\\\n<style>\\\\n\\\\tbutton {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmin-height: var(--size-60);\\\\n\\\\t\\\\tcolor: var(--block-label-text-color);\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tpadding-top: var(--size-3);\\\\n\\\\t}\\\\n\\\\n\\\\t.icon-wrap {\\\\n\\\\t\\\\twidth: 30px;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 768px) {\\\\n\\\\t\\\\t.wrap {\\\\n\\\\t\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAeC,mBAAO,CACN,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,kBAAM,CACL,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,KAAK,CAAE,IAAI,wBAAwB,CAAC,CACpC,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,uBAAW,CACV,KAAK,CAAE,IAAI,CACX,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,kBAAM,CACL,SAAS,CAAE,IAAI,SAAS,CACzB,CACD\"}'},Et=P((a,e,t,K)=>(N(),a.css.add(yt),`<button class=\"svelte-qbrfs\"${tt({height:\"100%\"})}><div class=\"wrap svelte-qbrfs\"><span class=\"icon-wrap svelte-qbrfs\">${i(rt,\"Webcam\").$$render(a,{},{},{})}</span> ${Y(\"Click to Access Webcam\")}</div> </button>`)),xt={code:\".wrap.svelte-10cpz3p.svelte-10cpz3p{position:relative;width:var(--size-full);height:var(--size-full)}.hide.svelte-10cpz3p.svelte-10cpz3p{display:none}video.svelte-10cpz3p.svelte-10cpz3p{width:var(--size-full);height:var(--size-full);object-fit:contain}.button-wrap.svelte-10cpz3p.svelte-10cpz3p{position:absolute;background-color:var(--block-background-fill);border:1px solid var(--border-color-primary);border-radius:var(--radius-xl);padding:var(--size-1-5);display:flex;bottom:var(--size-2);left:50%;transform:translate(-50%, 0);box-shadow:var(--shadow-drop-lg);border-radius:var(--radius-xl);line-height:var(--size-3);color:var(--button-secondary-text-color)}.icon-with-text.svelte-10cpz3p.svelte-10cpz3p{width:var(--size-20);align-items:center;margin:0 var(--spacing-xl);display:flex;justify-content:space-evenly}@media(min-width: 768px){button.svelte-10cpz3p.svelte-10cpz3p{bottom:var(--size-4)}}@media(min-width: 1280px){button.svelte-10cpz3p.svelte-10cpz3p{bottom:var(--size-8)}}.icon.svelte-10cpz3p.svelte-10cpz3p{width:18px;height:18px;display:flex;justify-content:space-between;align-items:center}.color-primary.svelte-10cpz3p.svelte-10cpz3p{fill:var(--primary-600);stroke:var(--primary-600);color:var(--primary-600)}.flip.svelte-10cpz3p.svelte-10cpz3p{transform:scaleX(-1)}.select-wrap.svelte-10cpz3p.svelte-10cpz3p{-webkit-appearance:none;-moz-appearance:none;appearance:none;color:var(--button-secondary-text-color);background-color:transparent;width:95%;font-size:var(--text-md);position:absolute;bottom:var(--size-2);background-color:var(--block-background-fill);box-shadow:var(--shadow-drop-lg);border-radius:var(--radius-xl);z-index:var(--layer-top);border:1px solid var(--border-color-primary);text-align:left;line-height:var(--size-4);white-space:nowrap;text-overflow:ellipsis;left:50%;transform:translate(-50%, 0);max-width:var(--size-52)}.select-wrap.svelte-10cpz3p>option.svelte-10cpz3p{padding:0.25rem 0.5rem;border-bottom:1px solid var(--border-color-accent);padding-right:var(--size-8);text-overflow:ellipsis;overflow:hidden}.select-wrap.svelte-10cpz3p>option.svelte-10cpz3p:hover{background-color:var(--color-accent)}.select-wrap.svelte-10cpz3p>option.svelte-10cpz3p:last-child{border:none}.inset-icon.svelte-10cpz3p.svelte-10cpz3p{position:absolute;top:5px;right:-6.5px;width:var(--size-10);height:var(--size-5);opacity:0.8}@media(min-width: 768px){.wrap.svelte-10cpz3p.svelte-10cpz3p{font-size:var(--text-lg)}}\",map:'{\"version\":3,\"file\":\"Webcam.svelte\",\"sources\":[\"Webcam.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, onDestroy, onMount } from \\\\\"svelte\\\\\";\\\\nimport { Camera, Circle, Square, DropdownArrow, Spinner } from \\\\\"@gradio/icons\\\\\";\\\\nimport { StreamingBar } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport { prepare_files } from \\\\\"@gradio/client\\\\\";\\\\nimport WebcamPermissions from \\\\\"./WebcamPermissions.svelte\\\\\";\\\\nimport { fade } from \\\\\"svelte/transition\\\\\";\\\\nimport { get_devices, get_video_stream, set_available_devices } from \\\\\"./stream_utils\\\\\";\\\\nlet video_source;\\\\nlet available_video_devices = [];\\\\nlet selected_device = null;\\\\nlet time_limit = null;\\\\nlet stream_state = \\\\\"closed\\\\\";\\\\nexport const modify_stream = (state) => {\\\\n    if (state === \\\\\"closed\\\\\") {\\\\n        time_limit = null;\\\\n        stream_state = \\\\\"closed\\\\\";\\\\n        value = null;\\\\n    }\\\\n    else if (state === \\\\\"waiting\\\\\") {\\\\n        stream_state = \\\\\"waiting\\\\\";\\\\n    }\\\\n    else {\\\\n        stream_state = \\\\\"open\\\\\";\\\\n    }\\\\n};\\\\nexport const set_time_limit = (time) => {\\\\n    if (recording)\\\\n        time_limit = time;\\\\n};\\\\nlet canvas;\\\\nexport let streaming = false;\\\\nexport let pending = false;\\\\nexport let root = \\\\\"\\\\\";\\\\nexport let stream_every = 1;\\\\nexport let mode = \\\\\"image\\\\\";\\\\nexport let mirror_webcam;\\\\nexport let include_audio;\\\\nexport let webcam_constraints = null;\\\\nexport let i18n;\\\\nexport let upload;\\\\nexport let value = null;\\\\nconst dispatch = createEventDispatcher();\\\\nonMount(() => {\\\\n    canvas = document.createElement(\\\\\"canvas\\\\\");\\\\n    if (streaming && mode === \\\\\"image\\\\\") {\\\\n        window.setInterval(() => {\\\\n            if (video_source && !pending) {\\\\n                take_picture();\\\\n            }\\\\n        }, stream_every * 1e3);\\\\n    }\\\\n});\\\\nconst handle_device_change = async (event) => {\\\\n    const target = event.target;\\\\n    const device_id = target.value;\\\\n    await get_video_stream(include_audio, video_source, webcam_constraints, device_id).then(async (local_stream) => {\\\\n        stream = local_stream;\\\\n        selected_device = available_video_devices.find((device) => device.deviceId === device_id) || null;\\\\n        options_open = false;\\\\n    });\\\\n};\\\\nasync function access_webcam() {\\\\n    try {\\\\n        get_video_stream(include_audio, video_source, webcam_constraints).then(async (local_stream) => {\\\\n            webcam_accessed = true;\\\\n            available_video_devices = await get_devices();\\\\n            stream = local_stream;\\\\n        }).then(() => set_available_devices(available_video_devices)).then((devices) => {\\\\n            available_video_devices = devices;\\\\n            const used_devices = stream.getTracks().map((track) => track.getSettings()?.deviceId)[0];\\\\n            selected_device = used_devices ? devices.find((device) => device.deviceId === used_devices) || available_video_devices[0] : available_video_devices[0];\\\\n        });\\\\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"image.no_webcam_support\\\\\"));\\\\n        }\\\\n    }\\\\n    catch (err) {\\\\n        if (err instanceof DOMException && err.name == \\\\\"NotAllowedError\\\\\") {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"image.allow_webcam_access\\\\\"));\\\\n        }\\\\n        else {\\\\n            throw err;\\\\n        }\\\\n    }\\\\n}\\\\nfunction take_picture() {\\\\n    var context = canvas.getContext(\\\\\"2d\\\\\");\\\\n    if ((!streaming || streaming && recording) && video_source.videoWidth && video_source.videoHeight) {\\\\n        canvas.width = video_source.videoWidth;\\\\n        canvas.height = video_source.videoHeight;\\\\n        context.drawImage(video_source, 0, 0, video_source.videoWidth, video_source.videoHeight);\\\\n        if (mirror_webcam) {\\\\n            context.scale(-1, 1);\\\\n            context.drawImage(video_source, -video_source.videoWidth, 0);\\\\n        }\\\\n        if (streaming && (!recording || stream_state === \\\\\"waiting\\\\\")) {\\\\n            return;\\\\n        }\\\\n        if (streaming) {\\\\n            const image_data = canvas.toDataURL(\\\\\"image/jpeg\\\\\");\\\\n            dispatch(\\\\\"stream\\\\\", image_data);\\\\n            return;\\\\n        }\\\\n        canvas.toBlob((blob) => {\\\\n            dispatch(streaming ? \\\\\"stream\\\\\" : \\\\\"capture\\\\\", blob);\\\\n        }, `image/${streaming ? \\\\\"jpeg\\\\\" : \\\\\"png\\\\\"}`, 0.8);\\\\n    }\\\\n}\\\\nlet recording = false;\\\\nlet recorded_blobs = [];\\\\nlet stream;\\\\nlet mimeType;\\\\nlet media_recorder;\\\\nfunction take_recording() {\\\\n    if (recording) {\\\\n        media_recorder.stop();\\\\n        let video_blob = new Blob(recorded_blobs, { type: mimeType });\\\\n        let ReaderObj = new FileReader();\\\\n        ReaderObj.onload = async function (e) {\\\\n            if (e.target) {\\\\n                let _video_blob = new File([video_blob], \\\\\"sample.\\\\\" + mimeType.substring(6));\\\\n                const val = await prepare_files([_video_blob]);\\\\n                let val_ = ((await upload(val, root))?.filter(Boolean))[0];\\\\n                dispatch(\\\\\"capture\\\\\", val_);\\\\n                dispatch(\\\\\"stop_recording\\\\\");\\\\n            }\\\\n        };\\\\n        ReaderObj.readAsDataURL(video_blob);\\\\n    }\\\\n    else if (typeof MediaRecorder !== \\\\\"undefined\\\\\") {\\\\n        dispatch(\\\\\"start_recording\\\\\");\\\\n        recorded_blobs = [];\\\\n        let validMimeTypes = [\\\\\"video/webm\\\\\", \\\\\"video/mp4\\\\\"];\\\\n        for (let validMimeType of validMimeTypes) {\\\\n            if (MediaRecorder.isTypeSupported(validMimeType)) {\\\\n                mimeType = validMimeType;\\\\n                break;\\\\n            }\\\\n        }\\\\n        if (mimeType === null) {\\\\n            console.error(\\\\\"No supported MediaRecorder mimeType\\\\\");\\\\n            return;\\\\n        }\\\\n        media_recorder = new MediaRecorder(stream, {\\\\n            mimeType\\\\n        });\\\\n        media_recorder.addEventListener(\\\\\"dataavailable\\\\\", function (e) {\\\\n            recorded_blobs.push(e.data);\\\\n        });\\\\n        media_recorder.start(200);\\\\n    }\\\\n    recording = !recording;\\\\n}\\\\nlet webcam_accessed = false;\\\\nfunction record_video_or_photo({ destroy } = {}) {\\\\n    if (mode === \\\\\"image\\\\\" && streaming) {\\\\n        recording = !recording;\\\\n    }\\\\n    if (!destroy) {\\\\n        if (mode === \\\\\"image\\\\\") {\\\\n            take_picture();\\\\n        }\\\\n        else {\\\\n            take_recording();\\\\n        }\\\\n    }\\\\n    if (!recording && stream) {\\\\n        dispatch(\\\\\"close_stream\\\\\");\\\\n        stream.getTracks().forEach((track) => track.stop());\\\\n        video_source.srcObject = null;\\\\n        webcam_accessed = false;\\\\n        window.setTimeout(() => {\\\\n            value = null;\\\\n        }, 500);\\\\n        value = null;\\\\n    }\\\\n}\\\\nlet options_open = false;\\\\nexport function click_outside(node, cb) {\\\\n    const handle_click = (event) => {\\\\n        if (node && !node.contains(event.target) && !event.defaultPrevented) {\\\\n            cb(event);\\\\n        }\\\\n    };\\\\n    document.addEventListener(\\\\\"click\\\\\", handle_click, true);\\\\n    return {\\\\n        destroy() {\\\\n            document.removeEventListener(\\\\\"click\\\\\", handle_click, true);\\\\n        }\\\\n    };\\\\n}\\\\nfunction handle_click_outside(event) {\\\\n    event.preventDefault();\\\\n    event.stopPropagation();\\\\n    options_open = false;\\\\n}\\\\nonDestroy(() => {\\\\n    if (typeof window === \\\\\"undefined\\\\\")\\\\n        return;\\\\n    record_video_or_photo({ destroy: true });\\\\n    stream?.getTracks().forEach((track) => track.stop());\\\\n});\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"wrap\\\\\">\\\\n\\\\t<StreamingBar {time_limit} />\\\\n\\\\t<!-- svelte-ignore a11y-media-has-caption -->\\\\n\\\\t<!-- need to suppress for video streaming https://github.com/sveltejs/svelte/issues/5967 -->\\\\n\\\\t<video\\\\n\\\\t\\\\tbind:this={video_source}\\\\n\\\\t\\\\tclass:flip={mirror_webcam}\\\\n\\\\t\\\\tclass:hide={!webcam_accessed || (webcam_accessed && !!value)}\\\\n\\\\t/>\\\\n\\\\t<!-- svelte-ignore a11y-missing-attribute -->\\\\n\\\\t<img\\\\n\\\\t\\\\tsrc={value?.url}\\\\n\\\\t\\\\tclass:hide={!webcam_accessed || (webcam_accessed && !value)}\\\\n\\\\t/>\\\\n\\\\t{#if !webcam_accessed}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tin:fade={{ delay: 100, duration: 200 }}\\\\n\\\\t\\\\t\\\\ttitle=\\\\\"grant webcam access\\\\\"\\\\n\\\\t\\\\t\\\\tstyle=\\\\\"height: 100%\\\\\"\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<WebcamPermissions on:click={async () => access_webcam()} />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<div class=\\\\\"button-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\ton:click={() => record_video_or_photo()}\\\\n\\\\t\\\\t\\\\t\\\\taria-label={mode === \\\\\"image\\\\\" ? \\\\\"capture photo\\\\\" : \\\\\"start recording\\\\\"}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if mode === \\\\\"video\\\\\" || streaming}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if streaming && stream_state === \\\\\"waiting\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon-with-text\\\\\" style=\\\\\"width:var(--size-24);\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon color-primary\\\\\" title=\\\\\"spinner\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Spinner />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.waiting\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else if (streaming && stream_state === \\\\\"open\\\\\") || (!streaming && recording)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon-with-text\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon color-primary\\\\\" title=\\\\\"stop recording\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Square />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.stop\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon-with-text\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon color-primary\\\\\" title=\\\\\"start recording\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Circle />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.record\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon\\\\\" title=\\\\\"capture photo\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Camera />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{#if !recording}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => (options_open = true)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"select input source\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<DropdownArrow />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t\\\\t{#if options_open && selected_device}\\\\n\\\\t\\\\t\\\\t<select\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"select-wrap\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"select source\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tuse:click_outside={handle_click_outside}\\\\n\\\\t\\\\t\\\\t\\\\ton:change={handle_device_change}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"inset-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click|stopPropagation={() => (options_open = false)}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<DropdownArrow />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t{#if available_video_devices.length === 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<option value=\\\\\"\\\\\">{i18n(\\\\\"common.no_devices\\\\\")}</option>\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each available_video_devices as device}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<option\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue={device.deviceId}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tselected={selected_device.deviceId === device.deviceId}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{device.label}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</option>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</select>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.wrap {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.hide {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\tvideo {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}\\\\n\\\\n\\\\t.button-wrap {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tpadding: var(--size-1-5);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tbottom: var(--size-2);\\\\n\\\\t\\\\tleft: 50%;\\\\n\\\\t\\\\ttransform: translate(-50%, 0);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tline-height: var(--size-3);\\\\n\\\\t\\\\tcolor: var(--button-secondary-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.icon-with-text {\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-evenly;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 768px) {\\\\n\\\\t\\\\tbutton {\\\\n\\\\t\\\\t\\\\tbottom: var(--size-4);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 1280px) {\\\\n\\\\t\\\\tbutton {\\\\n\\\\t\\\\t\\\\tbottom: var(--size-8);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.icon {\\\\n\\\\t\\\\twidth: 18px;\\\\n\\\\t\\\\theight: 18px;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.color-primary {\\\\n\\\\t\\\\tfill: var(--primary-600);\\\\n\\\\t\\\\tstroke: var(--primary-600);\\\\n\\\\t\\\\tcolor: var(--primary-600);\\\\n\\\\t}\\\\n\\\\n\\\\t.flip {\\\\n\\\\t\\\\ttransform: scaleX(-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.select-wrap {\\\\n\\\\t\\\\t-webkit-appearance: none;\\\\n\\\\t\\\\t-moz-appearance: none;\\\\n\\\\t\\\\tappearance: none;\\\\n\\\\t\\\\tcolor: var(--button-secondary-text-color);\\\\n\\\\t\\\\tbackground-color: transparent;\\\\n\\\\t\\\\twidth: 95%;\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: var(--size-2);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tz-index: var(--layer-top);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t\\\\tline-height: var(--size-4);\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\tleft: 50%;\\\\n\\\\t\\\\ttransform: translate(-50%, 0);\\\\n\\\\t\\\\tmax-width: var(--size-52);\\\\n\\\\t}\\\\n\\\\n\\\\t.select-wrap > option {\\\\n\\\\t\\\\tpadding: 0.25rem 0.5rem;\\\\n\\\\t\\\\tborder-bottom: 1px solid var(--border-color-accent);\\\\n\\\\t\\\\tpadding-right: var(--size-8);\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.select-wrap > option:hover {\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.select-wrap > option:last-child {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.inset-icon {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 5px;\\\\n\\\\t\\\\tright: -6.5px;\\\\n\\\\t\\\\twidth: var(--size-10);\\\\n\\\\t\\\\theight: var(--size-5);\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 768px) {\\\\n\\\\t\\\\t.wrap {\\\\n\\\\t\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8SC,mCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CAEA,mCAAM,CACL,OAAO,CAAE,IACV,CAEA,mCAAM,CACL,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,OACb,CAEA,0CAAa,CACZ,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,UAAU,CAAC,CACxB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAC7B,UAAU,CAAE,IAAI,gBAAgB,CAAC,CACjC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,KAAK,CAAE,IAAI,6BAA6B,CACzC,CAEA,6CAAgB,CACf,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,YAClB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,oCAAO,CACN,MAAM,CAAE,IAAI,QAAQ,CACrB,CACD,CAEA,MAAO,YAAY,MAAM,CAAE,CAC1B,oCAAO,CACN,MAAM,CAAE,IAAI,QAAQ,CACrB,CACD,CAEA,mCAAM,CACL,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MACd,CAEA,4CAAe,CACd,IAAI,CAAE,IAAI,aAAa,CAAC,CACxB,MAAM,CAAE,IAAI,aAAa,CAAC,CAC1B,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,mCAAM,CACL,SAAS,CAAE,OAAO,EAAE,CACrB,CAEA,0CAAa,CACZ,kBAAkB,CAAE,IAAI,CACxB,eAAe,CAAE,IAAI,CACrB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,6BAA6B,CAAC,CACzC,gBAAgB,CAAE,WAAW,CAC7B,KAAK,CAAE,GAAG,CACV,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,UAAU,CAAE,IAAI,gBAAgB,CAAC,CACjC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,WAAW,CAAC,CACzB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,UAAU,CAAE,IAAI,CAChB,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,WAAW,CAAE,MAAM,CACnB,aAAa,CAAE,QAAQ,CACvB,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAC7B,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,2BAAY,CAAG,qBAAO,CACrB,OAAO,CAAE,OAAO,CAAC,MAAM,CACvB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB,CAAC,CACnD,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,aAAa,CAAE,QAAQ,CACvB,QAAQ,CAAE,MACX,CAEA,2BAAY,CAAG,qBAAM,MAAO,CAC3B,gBAAgB,CAAE,IAAI,cAAc,CACrC,CAEA,2BAAY,CAAG,qBAAM,WAAY,CAChC,MAAM,CAAE,IACT,CAEA,yCAAY,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,GAAG,CACR,KAAK,CAAE,MAAM,CACb,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,GACV,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,mCAAM,CACL,SAAS,CAAE,IAAI,SAAS,CACzB,CACD\"}'};function $(a,e){const t=K=>{a&&!a.contains(K.target)&&!K.defaultPrevented&&e(K)};return document.addEventListener(\"click\",t,!0),{destroy(){document.removeEventListener(\"click\",t,!0)}}}const Bt=P((a,e,t,K)=>{let m,o=null,g=\"closed\";const Q=n=>{n===\"closed\"?(o=null,g=\"closed\",u=null):n===\"waiting\"?g=\"waiting\":g=\"open\"},C=n=>{r&&(o=n)};let s,{streaming:l=!1}=e,{pending:y=!1}=e,{root:E=\"\"}=e,{stream_every:_=1}=e,{mode:f=\"image\"}=e,{mirror_webcam:w}=e,{include_audio:x}=e,{webcam_constraints:U=null}=e,{i18n:b}=e,{upload:I}=e,{value:u=null}=e;const h=N();it(()=>{s=document.createElement(\"canvas\"),l&&f===\"image\"&&window.setInterval(()=>{},_*1e3)});function O(){var n=s.getContext(\"2d\");if((!l||l&&r)&&m.videoWidth&&m.videoHeight){if(s.width=m.videoWidth,s.height=m.videoHeight,n.drawImage(m,0,0,m.videoWidth,m.videoHeight),w&&(n.scale(-1,1),n.drawImage(m,-m.videoWidth,0)),l&&(!r||g===\"waiting\"))return;if(l){const c=s.toDataURL(\"image/jpeg\");h(\"stream\",c);return}s.toBlob(c=>{h(l?\"stream\":\"capture\",c)},`image/${l?\"jpeg\":\"png\"}`,.8)}}let r=!1,d=[],B,z,M;function W(){if(r){M.stop();let n=new Blob(d,{type:z}),c=new FileReader;c.onload=async function(D){if(D.target){let v=new File([n],\"sample.\"+z.substring(6));const L=await gt([v]);let j=(await I(L,E))?.filter(Boolean)[0];h(\"capture\",j),h(\"stop_recording\")}},c.readAsDataURL(n)}else if(typeof MediaRecorder<\"u\"){h(\"start_recording\"),d=[];let n=[\"video/webm\",\"video/mp4\"];for(let c of n)if(MediaRecorder.isTypeSupported(c)){z=c;break}if(z===null){console.error(\"No supported MediaRecorder mimeType\");return}M=new MediaRecorder(B,{mimeType:z}),M.addEventListener(\"dataavailable\",function(c){d.push(c.data)}),M.start(200)}r=!r}let k=!1;function T({destroy:n}={}){f===\"image\"&&l&&(r=!r),n||(f===\"image\"?O():W()),!r&&B&&(h(\"close_stream\"),B.getTracks().forEach(c=>c.stop()),m.srcObject=null,k=!1,window.setTimeout(()=>{u=null},500),u=null)}return ot(()=>{typeof window>\"u\"||T({destroy:!0})}),e.modify_stream===void 0&&t.modify_stream&&Q!==void 0&&t.modify_stream(Q),e.set_time_limit===void 0&&t.set_time_limit&&C!==void 0&&t.set_time_limit(C),e.streaming===void 0&&t.streaming&&l!==void 0&&t.streaming(l),e.pending===void 0&&t.pending&&y!==void 0&&t.pending(y),e.root===void 0&&t.root&&E!==void 0&&t.root(E),e.stream_every===void 0&&t.stream_every&&_!==void 0&&t.stream_every(_),e.mode===void 0&&t.mode&&f!==void 0&&t.mode(f),e.mirror_webcam===void 0&&t.mirror_webcam&&w!==void 0&&t.mirror_webcam(w),e.include_audio===void 0&&t.include_audio&&x!==void 0&&t.include_audio(x),e.webcam_constraints===void 0&&t.webcam_constraints&&U!==void 0&&t.webcam_constraints(U),e.i18n===void 0&&t.i18n&&b!==void 0&&t.i18n(b),e.upload===void 0&&t.upload&&I!==void 0&&t.upload(I),e.value===void 0&&t.value&&u!==void 0&&t.value(u),e.click_outside===void 0&&t.click_outside&&$!==void 0&&t.click_outside($),a.css.add(xt),`<div class=\"wrap svelte-10cpz3p\">${i(wt,\"StreamingBar\").$$render(a,{time_limit:o},{},{})}   <video class=\"${[\"svelte-10cpz3p\",(w?\"flip\":\"\")+\" \"+(!k||k&&u?\"hide\":\"\")].join(\" \").trim()}\"${q(\"this\",m,0)}></video>  <img${q(\"src\",u?.url,0)} class=\"${[\"svelte-10cpz3p\",!k||k&&!u?\"hide\":\"\"].join(\" \").trim()}\"> ${k?`<div class=\"button-wrap svelte-10cpz3p\"><button${q(\"aria-label\",f===\"image\"?\"capture photo\":\"start recording\",0)} class=\"svelte-10cpz3p\">${f===\"video\"||l?`${l&&g===\"waiting\"?`<div class=\"icon-with-text svelte-10cpz3p\" style=\"width:var(--size-24);\"><div class=\"icon color-primary svelte-10cpz3p\" title=\"spinner\">${i(At,\"Spinner\").$$render(a,{},{},{})}</div> ${Y(b(\"audio.waiting\"))}</div>`:`${l&&g===\"open\"||!l&&r?`<div class=\"icon-with-text svelte-10cpz3p\"><div class=\"icon color-primary svelte-10cpz3p\" title=\"stop recording\">${i(ct,\"Square\").$$render(a,{},{},{})}</div> ${Y(b(\"audio.stop\"))}</div>`:`<div class=\"icon-with-text svelte-10cpz3p\"><div class=\"icon color-primary svelte-10cpz3p\" title=\"start recording\">${i(st,\"Circle\").$$render(a,{},{},{})}</div> ${Y(b(\"audio.record\"))}</div>`}`}`:`<div class=\"icon svelte-10cpz3p\" title=\"capture photo\">${i(dt,\"Camera\").$$render(a,{},{},{})}</div>`}</button> ${r?\"\":`<button class=\"icon svelte-10cpz3p\" aria-label=\"select input source\">${i(vt,\"DropdownArrow\").$$render(a,{},{},{})}</button>`}</div> `:`<div title=\"grant webcam access\" style=\"height: 100%\">${i(Et,\"WebcamPermissions\").$$render(a,{},{},{})}</div>`} </div>`}),zt=Bt,Mt={code:\".image-frame.svelte-1hdlew6 img{width:var(--size-full);height:var(--size-full);object-fit:scale-down}.upload-container.svelte-1hdlew6{display:flex;align-items:center;justify-content:center;height:100%;flex-shrink:1;max-height:100%}.reduced-height.svelte-1hdlew6{height:calc(100% - var(--size-10))}.image-container.svelte-1hdlew6{display:flex;height:100%;flex-direction:column;justify-content:center;align-items:center;max-height:100%}.selectable.svelte-1hdlew6{cursor:crosshair}.image-frame.svelte-1hdlew6{object-fit:cover;width:100%;height:100%}\",map:'{\"version\":3,\"file\":\"ImageUploader.svelte\",\"sources\":[\"ImageUploader.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, tick } from \\\\\"svelte\\\\\";\\\\nimport { BlockLabel, IconButtonWrapper, IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Clear, Image as ImageIcon } from \\\\\"@gradio/icons\\\\\";\\\\nimport { FullscreenButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport {} from \\\\\"@gradio/utils\\\\\";\\\\nimport { get_coordinates_of_clicked_image } from \\\\\"./utils\\\\\";\\\\nimport Webcam from \\\\\"./Webcam.svelte\\\\\";\\\\nimport { Upload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { FileData } from \\\\\"@gradio/client\\\\\";\\\\nimport { SelectSource } from \\\\\"@gradio/atoms\\\\\";\\\\nimport Image from \\\\\"./Image.svelte\\\\\";\\\\nexport let value = null;\\\\nexport let label = void 0;\\\\nexport let show_label;\\\\nexport let sources = [\\\\\"upload\\\\\", \\\\\"clipboard\\\\\", \\\\\"webcam\\\\\"];\\\\nexport let streaming = false;\\\\nexport let pending = false;\\\\nexport let webcam_options;\\\\nexport let selectable = false;\\\\nexport let root;\\\\nexport let i18n;\\\\nexport let max_file_size = null;\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nexport let stream_every;\\\\nexport let modify_stream;\\\\nexport let set_time_limit;\\\\nexport let show_fullscreen_button = true;\\\\nlet upload_input;\\\\nexport let uploading = false;\\\\nexport let active_source = null;\\\\nexport let fullscreen = false;\\\\nasync function handle_upload({ detail }) {\\\\n    if (!streaming) {\\\\n        if (detail.path?.toLowerCase().endsWith(\\\\\".svg\\\\\") && detail.url) {\\\\n            const response = await fetch(detail.url);\\\\n            const svgContent = await response.text();\\\\n            value = {\\\\n                ...detail,\\\\n                url: `data:image/svg+xml,${encodeURIComponent(svgContent)}`\\\\n            };\\\\n        }\\\\n        else {\\\\n            value = detail;\\\\n        }\\\\n        await tick();\\\\n        dispatch(\\\\\"upload\\\\\");\\\\n    }\\\\n}\\\\nfunction handle_clear() {\\\\n    value = null;\\\\n    dispatch(\\\\\"clear\\\\\");\\\\n    dispatch(\\\\\"change\\\\\", null);\\\\n}\\\\nasync function handle_save(img_blob, event) {\\\\n    if (event === \\\\\"stream\\\\\") {\\\\n        dispatch(\\\\\"stream\\\\\", {\\\\n            value: { url: img_blob },\\\\n            is_value_data: true\\\\n        });\\\\n        return;\\\\n    }\\\\n    pending = true;\\\\n    const f = await upload_input.load_files([\\\\n        new File([img_blob], `image/${streaming ? \\\\\"jpeg\\\\\" : \\\\\"png\\\\\"}`)\\\\n    ]);\\\\n    if (event === \\\\\"change\\\\\" || event === \\\\\"upload\\\\\") {\\\\n        value = f?.[0] || null;\\\\n        await tick();\\\\n        dispatch(\\\\\"change\\\\\");\\\\n    }\\\\n    pending = false;\\\\n}\\\\n$: active_streaming = streaming && active_source === \\\\\"webcam\\\\\";\\\\n$: if (uploading && !active_streaming)\\\\n    value = null;\\\\nconst dispatch = createEventDispatcher();\\\\nexport let dragging = false;\\\\n$: dispatch(\\\\\"drag\\\\\", dragging);\\\\nfunction handle_click(evt) {\\\\n    let coordinates = get_coordinates_of_clicked_image(evt);\\\\n    if (coordinates) {\\\\n        dispatch(\\\\\"select\\\\\", { index: coordinates, value: null });\\\\n    }\\\\n}\\\\n$: if (!active_source && sources) {\\\\n    active_source = sources[0];\\\\n}\\\\nasync function handle_select_source(source) {\\\\n    switch (source) {\\\\n        case \\\\\"clipboard\\\\\":\\\\n            upload_input.paste_clipboard();\\\\n            break;\\\\n        default:\\\\n            break;\\\\n    }\\\\n}\\\\nlet image_container;\\\\nfunction on_drag_over(evt) {\\\\n    evt.preventDefault();\\\\n    evt.stopPropagation();\\\\n    if (evt.dataTransfer) {\\\\n        evt.dataTransfer.dropEffect = \\\\\"copy\\\\\";\\\\n    }\\\\n    dragging = true;\\\\n}\\\\nasync function on_drop(evt) {\\\\n    evt.preventDefault();\\\\n    evt.stopPropagation();\\\\n    dragging = false;\\\\n    if (value) {\\\\n        handle_clear();\\\\n        await tick();\\\\n    }\\\\n    active_source = \\\\\"upload\\\\\";\\\\n    await tick();\\\\n    upload_input.load_files_from_drop(evt);\\\\n}\\\\n<\\/script>\\\\n\\\\n<BlockLabel {show_label} Icon={ImageIcon} label={label || \\\\\"Image\\\\\"} />\\\\n\\\\n<div data-testid=\\\\\"image\\\\\" class=\\\\\"image-container\\\\\" bind:this={image_container}>\\\\n\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t{#if value?.url && !active_streaming}\\\\n\\\\t\\\\t\\\\t{#if show_fullscreen_button}\\\\n\\\\t\\\\t\\\\t\\\\t<FullscreenButton {fullscreen} on:fullscreen />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\tIcon={Clear}\\\\n\\\\t\\\\t\\\\t\\\\tlabel=\\\\\"Remove Image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:click={(event) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tvalue = null;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"clear\\\\\");\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tevent.stopPropagation();\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</IconButtonWrapper>\\\\n\\\\t<!-- svelte-ignore a11y-no-static-element-interactions -->\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"upload-container\\\\\"\\\\n\\\\t\\\\tclass:reduced-height={sources.length > 1}\\\\n\\\\t\\\\tstyle:width={value ? \\\\\"auto\\\\\" : \\\\\"100%\\\\\"}\\\\n\\\\t\\\\ton:dragover={on_drag_over}\\\\n\\\\t\\\\ton:drop={on_drop}\\\\n\\\\t>\\\\n\\\\t\\\\t<Upload\\\\n\\\\t\\\\t\\\\thidden={value !== null || active_source === \\\\\"webcam\\\\\"}\\\\n\\\\t\\\\t\\\\tbind:this={upload_input}\\\\n\\\\t\\\\t\\\\tbind:uploading\\\\n\\\\t\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\t\\\\tfiletype={active_source === \\\\\"clipboard\\\\\" ? \\\\\"clipboard\\\\\" : \\\\\"image/*\\\\\"}\\\\n\\\\t\\\\t\\\\ton:load={handle_upload}\\\\n\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t{max_file_size}\\\\n\\\\t\\\\t\\\\tdisable_click={!sources.includes(\\\\\"upload\\\\\") || value !== null}\\\\n\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\taria_label={i18n(\\\\\"image.drop_to_upload\\\\\")}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#if value === null}\\\\n\\\\t\\\\t\\\\t\\\\t<slot />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</Upload>\\\\n\\\\t\\\\t{#if active_source === \\\\\"webcam\\\\\" && (streaming || (!streaming && !value))}\\\\n\\\\t\\\\t\\\\t<Webcam\\\\n\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\ton:capture={(e) => handle_save(e.detail, \\\\\"change\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\ton:stream={(e) => handle_save(e.detail, \\\\\"stream\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t\\\\ton:drag\\\\n\\\\t\\\\t\\\\t\\\\ton:upload={(e) => handle_save(e.detail, \\\\\"upload\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\ton:close_stream\\\\n\\\\t\\\\t\\\\t\\\\tmirror_webcam={webcam_options.mirror}\\\\n\\\\t\\\\t\\\\t\\\\t{stream_every}\\\\n\\\\t\\\\t\\\\t\\\\t{streaming}\\\\n\\\\t\\\\t\\\\t\\\\tmode=\\\\\"image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tinclude_audio={false}\\\\n\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\tbind:modify_stream\\\\n\\\\t\\\\t\\\\t\\\\tbind:set_time_limit\\\\n\\\\t\\\\t\\\\t\\\\twebcam_constraints={webcam_options.constraints}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{:else if value !== null && !streaming}\\\\n\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-click-events-have-key-events-->\\\\n\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-no-static-element-interactions-->\\\\n\\\\t\\\\t\\\\t<div class:selectable class=\\\\\"image-frame\\\\\" on:click={handle_click}>\\\\n\\\\t\\\\t\\\\t\\\\t<Image src={value.url} alt={value.alt_text} />\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n\\\\t{#if sources.length > 1 || sources.includes(\\\\\"clipboard\\\\\")}\\\\n\\\\t\\\\t<SelectSource\\\\n\\\\t\\\\t\\\\t{sources}\\\\n\\\\t\\\\t\\\\tbind:active_source\\\\n\\\\t\\\\t\\\\t{handle_clear}\\\\n\\\\t\\\\t\\\\thandle_select={handle_select_source}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.image-frame :global(img) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: scale-down;\\\\n\\\\t}\\\\n\\\\n\\\\t.upload-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-shrink: 1;\\\\n\\\\t\\\\tmax-height: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.reduced-height {\\\\n\\\\t\\\\theight: calc(100% - var(--size-10));\\\\n\\\\t}\\\\n\\\\n\\\\t.image-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmax-height: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: crosshair;\\\\n\\\\t}\\\\n\\\\n\\\\t.image-frame {\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8MC,2BAAY,CAAS,GAAK,CACzB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,UACb,CAEA,gCAAkB,CACjB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CAEvB,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,UAAU,CAAE,IACb,CAEA,8BAAgB,CACf,MAAM,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CACnC,CAEA,+BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IACb,CAEA,0BAAY,CACX,MAAM,CAAE,SACT,CAEA,2BAAa,CACZ,UAAU,CAAE,KAAK,CACjB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT\"}'},kt=P((a,e,t,K)=>{let m,{value:o=null}=e,{label:g=void 0}=e,{show_label:Q}=e,{sources:C=[\"upload\",\"clipboard\",\"webcam\"]}=e,{streaming:s=!1}=e,{pending:l=!1}=e,{webcam_options:y}=e,{selectable:E=!1}=e,{root:_}=e,{i18n:f}=e,{max_file_size:w=null}=e,{upload:x}=e,{stream_handler:U}=e,{stream_every:b}=e,{modify_stream:I}=e,{set_time_limit:u}=e,{show_fullscreen_button:h=!0}=e,O,{uploading:r=!1}=e,{active_source:d=null}=e,{fullscreen:B=!1}=e;function z(){o=null,M(\"clear\"),M(\"change\",null)}const M=N();let{dragging:W=!1}=e;async function k(v){switch(v){case\"clipboard\":O.paste_clipboard();break}}let T;e.value===void 0&&t.value&&o!==void 0&&t.value(o),e.label===void 0&&t.label&&g!==void 0&&t.label(g),e.show_label===void 0&&t.show_label&&Q!==void 0&&t.show_label(Q),e.sources===void 0&&t.sources&&C!==void 0&&t.sources(C),e.streaming===void 0&&t.streaming&&s!==void 0&&t.streaming(s),e.pending===void 0&&t.pending&&l!==void 0&&t.pending(l),e.webcam_options===void 0&&t.webcam_options&&y!==void 0&&t.webcam_options(y),e.selectable===void 0&&t.selectable&&E!==void 0&&t.selectable(E),e.root===void 0&&t.root&&_!==void 0&&t.root(_),e.i18n===void 0&&t.i18n&&f!==void 0&&t.i18n(f),e.max_file_size===void 0&&t.max_file_size&&w!==void 0&&t.max_file_size(w),e.upload===void 0&&t.upload&&x!==void 0&&t.upload(x),e.stream_handler===void 0&&t.stream_handler&&U!==void 0&&t.stream_handler(U),e.stream_every===void 0&&t.stream_every&&b!==void 0&&t.stream_every(b),e.modify_stream===void 0&&t.modify_stream&&I!==void 0&&t.modify_stream(I),e.set_time_limit===void 0&&t.set_time_limit&&u!==void 0&&t.set_time_limit(u),e.show_fullscreen_button===void 0&&t.show_fullscreen_button&&h!==void 0&&t.show_fullscreen_button(h),e.uploading===void 0&&t.uploading&&r!==void 0&&t.uploading(r),e.active_source===void 0&&t.active_source&&d!==void 0&&t.active_source(d),e.fullscreen===void 0&&t.fullscreen&&B!==void 0&&t.fullscreen(B),e.dragging===void 0&&t.dragging&&W!==void 0&&t.dragging(W),a.css.add(Mt);let n,c,D=a.head;do n=!0,a.head=D,!d&&C&&(d=C[0]),m=s&&d===\"webcam\",r&&!m&&(o=null),M(\"drag\",W),c=`${i(mt,\"BlockLabel\").$$render(a,{show_label:Q,Icon:et,label:g||\"Image\"},{},{})} <div data-testid=\"image\" class=\"image-container svelte-1hdlew6\"${q(\"this\",T,0)}>${i(ut,\"IconButtonWrapper\").$$render(a,{},{},{default:()=>`${o?.url&&!m?`${h?`${i(Ct,\"FullscreenButton\").$$render(a,{fullscreen:B},{},{})}`:\"\"} ${i(_t,\"IconButton\").$$render(a,{Icon:ft,label:\"Remove Image\"},{},{})}`:\"\"}`})}  <div class=\"${[\"upload-container svelte-1hdlew6\",C.length>1?\"reduced-height\":\"\"].join(\" \").trim()}\"${tt({width:o?\"auto\":\"100%\"})}>${i(bt,\"Upload\").$$render(a,{hidden:o!==null||d===\"webcam\",filetype:d===\"clipboard\"?\"clipboard\":\"image/*\",root:_,max_file_size:w,disable_click:!C.includes(\"upload\")||o!==null,upload:x,stream_handler:U,aria_label:f(\"image.drop_to_upload\"),this:O,uploading:r,dragging:W},{this:v=>{O=v,n=!1},uploading:v=>{r=v,n=!1},dragging:v=>{W=v,n=!1}},{default:()=>`${o===null?`${K.default?K.default({}):\"\"}`:\"\"}`})} ${d===\"webcam\"&&(s||!s&&!o)?`${i(zt,\"Webcam\").$$render(a,{root:_,value:o,mirror_webcam:y.mirror,stream_every:b,streaming:s,mode:\"image\",include_audio:!1,i18n:f,upload:x,webcam_constraints:y.constraints,modify_stream:I,set_time_limit:u},{modify_stream:v=>{I=v,n=!1},set_time_limit:v=>{u=v,n=!1}},{})}`:`${o!==null&&!s?`  <div class=\"${[\"image-frame svelte-1hdlew6\",E?\"selectable\":\"\"].join(\" \").trim()}\">${i(It,\"Image\").$$render(a,{src:o.url,alt:o.alt_text},{},{})}</div>`:\"\"}`}</div> ${C.length>1||C.includes(\"clipboard\")?`${i(pt,\"SelectSource\").$$render(a,{sources:C,handle_clear:z,handle_select:k,active_source:d},{active_source:v=>{d=v,n=!1}},{})}`:\"\"} </div>`;while(!n);return c}),Wt=kt,Rt=P((a,e,t,K)=>{let m=\"closed\",o=()=>{};function g(A){m=A,o(A)}const Q=()=>m;let{set_time_limit:C}=e,{value_is_output:s=!1}=e,{elem_id:l=\"\"}=e,{elem_classes:y=[]}=e,{visible:E=!0}=e,{value:_=null}=e,f=null,{label:w}=e,{show_label:x}=e,{show_download_button:U}=e,{root:b}=e,{height:I}=e,{width:u}=e,{stream_every:h}=e,{_selectable:O=!1}=e,{container:r=!0}=e,{scale:d=null}=e,{min_width:B=void 0}=e,{loading_status:z}=e,{show_share_button:M=!1}=e,{sources:W=[\"upload\",\"clipboard\",\"webcam\"]}=e,{interactive:k}=e,{streaming:T}=e,{pending:n}=e,{placeholder:c=void 0}=e,{show_fullscreen_button:D}=e,{input_ready:v}=e,{webcam_options:L}=e,j=!1,F=!1,{gradio:p}=e;lt(()=>{s=!1});let G,R=null,X;e.modify_stream_state===void 0&&t.modify_stream_state&&g!==void 0&&t.modify_stream_state(g),e.get_stream_state===void 0&&t.get_stream_state&&Q!==void 0&&t.get_stream_state(Q),e.set_time_limit===void 0&&t.set_time_limit&&C!==void 0&&t.set_time_limit(C),e.value_is_output===void 0&&t.value_is_output&&s!==void 0&&t.value_is_output(s),e.elem_id===void 0&&t.elem_id&&l!==void 0&&t.elem_id(l),e.elem_classes===void 0&&t.elem_classes&&y!==void 0&&t.elem_classes(y),e.visible===void 0&&t.visible&&E!==void 0&&t.visible(E),e.value===void 0&&t.value&&_!==void 0&&t.value(_),e.label===void 0&&t.label&&w!==void 0&&t.label(w),e.show_label===void 0&&t.show_label&&x!==void 0&&t.show_label(x),e.show_download_button===void 0&&t.show_download_button&&U!==void 0&&t.show_download_button(U),e.root===void 0&&t.root&&b!==void 0&&t.root(b),e.height===void 0&&t.height&&I!==void 0&&t.height(I),e.width===void 0&&t.width&&u!==void 0&&t.width(u),e.stream_every===void 0&&t.stream_every&&h!==void 0&&t.stream_every(h),e._selectable===void 0&&t._selectable&&O!==void 0&&t._selectable(O),e.container===void 0&&t.container&&r!==void 0&&t.container(r),e.scale===void 0&&t.scale&&d!==void 0&&t.scale(d),e.min_width===void 0&&t.min_width&&B!==void 0&&t.min_width(B),e.loading_status===void 0&&t.loading_status&&z!==void 0&&t.loading_status(z),e.show_share_button===void 0&&t.show_share_button&&M!==void 0&&t.show_share_button(M),e.sources===void 0&&t.sources&&W!==void 0&&t.sources(W),e.interactive===void 0&&t.interactive&&k!==void 0&&t.interactive(k),e.streaming===void 0&&t.streaming&&T!==void 0&&t.streaming(T),e.pending===void 0&&t.pending&&n!==void 0&&t.pending(n),e.placeholder===void 0&&t.placeholder&&c!==void 0&&t.placeholder(c),e.show_fullscreen_button===void 0&&t.show_fullscreen_button&&D!==void 0&&t.show_fullscreen_button(D),e.input_ready===void 0&&t.input_ready&&v!==void 0&&t.input_ready(v),e.webcam_options===void 0&&t.webcam_options&&L!==void 0&&t.webcam_options(L),e.gradio===void 0&&t.gradio&&p!==void 0&&t.gradio(p);let S,Z,at=a.head;do S=!0,a.head=at,v=!F,JSON.stringify(_)!==JSON.stringify(f)&&(f=_,p.dispatch(\"change\"),s||p.dispatch(\"input\")),Z=`   ${k?`${i(H,\"Block\").$$render(a,{visible:E,variant:_===null?\"dashed\":\"solid\",border_mode:G?\"focus\":\"base\",padding:!1,elem_id:l,elem_classes:y,height:I||void 0,width:u,allow_overflow:!1,container:r,scale:d,min_width:B,fullscreen:j},{fullscreen:A=>{j=A,S=!1}},{default:()=>`${i(J,\"StatusTracker\").$$render(a,Object.assign({},{autoscroll:p.autoscroll},{i18n:p.i18n},z),{},{})} ${i(Wt,\"ImageUploader\").$$render(a,{selectable:O,root:b,sources:W,fullscreen:j,label:w,show_label:x,pending:n,streaming:T,webcam_options:L,stream_every:h,max_file_size:p.max_file_size,i18n:p.i18n,upload:(...A)=>p.client.upload(...A),stream_handler:p.client?.stream,this:X,uploading:F,active_source:R,value:_,dragging:G,modify_stream:o,set_time_limit:C},{this:A=>{X=A,S=!1},uploading:A=>{F=A,S=!1},active_source:A=>{R=A,S=!1},value:A=>{_=A,S=!1},dragging:A=>{G=A,S=!1},modify_stream:A=>{o=A,S=!1},set_time_limit:A=>{C=A,S=!1}},{default:()=>`${R===\"upload\"||!R?`${i(V,\"UploadText\").$$render(a,{i18n:p.i18n,type:\"image\",placeholder:c},{},{})}`:`${R===\"clipboard\"?`${i(V,\"UploadText\").$$render(a,{i18n:p.i18n,type:\"clipboard\",mode:\"short\"},{},{})}`:`${i(ht,\"Empty\").$$render(a,{unpadded_box:!0,size:\"large\"},{},{default:()=>`${i(et,\"Image\").$$render(a,{},{},{})}`})}`}`}`})}`})}`:`${i(H,\"Block\").$$render(a,{visible:E,variant:\"solid\",border_mode:G?\"focus\":\"base\",padding:!1,elem_id:l,elem_classes:y,height:I||void 0,width:u,allow_overflow:!1,container:r,scale:d,min_width:B,fullscreen:j},{fullscreen:A=>{j=A,S=!1}},{default:()=>`${i(J,\"StatusTracker\").$$render(a,Object.assign({},{autoscroll:p.autoscroll},{i18n:p.i18n},z),{},{})} ${i(nt,\"StaticImage\").$$render(a,{fullscreen:j,value:_,label:w,show_label:x,show_download_button:U,selectable:O,show_share_button:M,i18n:p.i18n,show_fullscreen_button:D},{},{})}`})}`}`;while(!S);return Z});export{qt as BaseExample,It as BaseImage,Wt as BaseImageUploader,nt as BaseStaticImage,zt as Webcam,Rt as default};\n//# sourceMappingURL=Index26.js.map\n"], "names": ["P", "N", "tt", "i", "rt", "Y", "gt", "ot", "wt", "q", "At", "ct", "st", "dt", "vt", "b", "mt", "et", "ut", "Ct", "_t", "ft", "bt", "It", "pt", "O", "H", "J", "V", "ht", "nt"], "mappings": ";;;;;;;;;;;AAA2nB,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,yYAAyY,CAAC,GAAG,CAAC,ogDAAogD,CAAC,CAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIC,qBAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,4BAA4B,EAAEC,UAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,qEAAqE,EAAEC,kBAAC,CAACC,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAEC,MAAC,CAAC,wBAAwB,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,w4EAAw4E,CAAC,GAAG,CAAC,mxgBAAmxgB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAM,MAAC,EAAE,CAACL,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACC,qBAAC,EAAE,CAA+F,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAMK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAC,CAAC,KAAK,GAAG,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,OAAOC,SAAE,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,iCAAiC,EAAEJ,kBAAC,CAACK,EAAE,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEC,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAEA,aAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,+CAA+C,EAAEA,aAAC,CAAC,YAAY,CAAC,CAAC,GAAG,OAAO,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,wIAAwI,EAAEN,kBAAC,CAACO,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAEL,MAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iHAAiH,EAAEF,kBAAC,CAACQ,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAEN,MAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,kHAAkH,EAAEF,kBAAC,CAACS,IAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAEP,MAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uDAAuD,EAAEF,kBAAC,CAACU,IAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,qEAAqE,EAAEV,kBAAC,CAACW,EAAE,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,sDAAsD,EAAEX,kBAAC,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,oiBAAoiB,CAAC,GAAG,CAAC,ykQAAykQ,CAAC,CAAC,EAAE,CAACH,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAACe,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAC,CAAC,MAAM,CAAC,CAACd,qBAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAEc,GAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sBAAsB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEZ,kBAAC,CAACa,EAAE,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAACC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,gEAAgE,EAAER,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,kBAAC,CAACe,EAAE,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEf,kBAAC,CAACgB,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEhB,kBAAC,CAACiB,CAAE,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,iCAAiC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEnB,UAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEC,kBAAC,CAACmB,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEnB,kBAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAACY,GAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,4BAA4B,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAEZ,kBAAC,CAACoB,CAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAEpB,kBAAC,CAACqB,EAAE,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACxB,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAACyB,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAgB,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,mBAAmB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAEA,GAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sBAAsB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAEtB,kBAAC,CAACuB,EAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAEvB,kBAAC,CAACwB,EAAC,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAExB,kBAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAACsB,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEtB,kBAAC,CAACyB,EAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,EAAEzB,kBAAC,CAACyB,EAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzB,kBAAC,CAAC0B,IAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE1B,kBAAC,CAACc,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEd,kBAAC,CAACuB,EAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAEvB,kBAAC,CAACwB,EAAC,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAExB,kBAAC,CAAC2B,CAAE,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU,CAACL,GAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;;;;"}