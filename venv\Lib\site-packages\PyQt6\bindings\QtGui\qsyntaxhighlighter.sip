// qsyntaxhighlighter.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSyntaxHighlighter : public QObject
{
%TypeHeaderCode
#include <qsyntaxhighlighter.h>
%End

public:
    explicit QSyntaxHighlighter(QTextDocument *parent /TransferThis/);
    explicit QSyntaxHighlighter(QObject *parent /TransferThis/);
    virtual ~QSyntaxHighlighter();
    void setDocument(QTextDocument *doc /KeepReference/);
    QTextDocument *document() const;

public slots:
    void rehighlight();
    void rehighlightBlock(const QTextBlock &block);

protected:
    virtual void highlightBlock(const QString &text) = 0;
    void setFormat(int start, int count, const QTextCharFormat &format);
    void setFormat(int start, int count, const QColor &color);
    void setFormat(int start, int count, const QFont &font);
    QTextCharFormat format(int pos) const;
    int previousBlockState() const;
    int currentBlockState() const;
    void setCurrentBlockState(int newState);
    void setCurrentBlockUserData(QTextBlockUserData *data /GetWrapper/);
%MethodCode
        // Ownership of the user data is with the document not the syntax highlighter.
        
        typedef PyObject *(*helper_func)(QObject *, const sipTypeDef *);
        
        static helper_func helper = 0;
        
        if (!helper)
        {
            helper = (helper_func)sipImportSymbol("qtgui_wrap_ancestors");
            Q_ASSERT(helper);
        }
        
        QTextDocument *td = sipCpp->document();
        
        if (td)
        {
            PyObject *py_td = helper(td, sipType_QTextDocument);
        
            if (!py_td)
            {
                sipIsErr = 1;
            }
            else
            {
                sipTransferTo(a0Wrapper, py_td);
                Py_DECREF(py_td);
            }
        }
        
        #if defined(SIP_PROTECTED_IS_PUBLIC)
        sipCpp->setCurrentBlockUserData(a0);
        #else
        sipCpp->sipProtect_setCurrentBlockUserData(a0);
        #endif
%End

    QTextBlockUserData *currentBlockUserData() const;
    QTextBlock currentBlock() const;
};
