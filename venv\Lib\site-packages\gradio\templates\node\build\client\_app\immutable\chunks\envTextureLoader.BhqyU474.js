import{G as t,U as d,a as p}from"./environmentTextureTools.DZJEvUsQ.js";class h{constructor(){this.supportCascades=!1}loadCubeData(i,a,c,n,e){if(Array.isArray(i))return;const s=t(i);if(s){a.width=s.width,a.height=s.width;try{d(a,s),p(a,i,s).then(()=>{a.isReady=!0,a.onLoadedObservable.notifyObservers(a),a.onLoadedObservable.clear(),n&&n()},l=>{e==null||e("Can not upload environment levels",l)})}catch(l){e==null||e("Can not upload environment file",l)}}else e&&e("Can not parse the environment file",null)}loadData(){throw".env not supported in 2d."}}export{h as _ENVTextureLoader};
//# sourceMappingURL=envTextureLoader.BhqyU474.js.map
