import{SvelteComponent as g,init as x,safe_not_equal as w,svg_element as a,claim_svg_element as p,children as d,detach as i,attr as t,set_style as s,insert_hydration as C,append_hydration as c,noop as m}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function f(v){let e,r,l,n;return{c(){e=a("svg"),r=a("g"),l=a("path"),n=a("path"),this.h()},l(o){e=p(o,"svg",{width:!0,height:!0,viewBox:!0,version:!0,xmlns:!0,"xmlns:xlink":!0,"xml:space":!0,style:!0});var h=d(e);r=p(h,"g",{});var u=d(r);l=p(u,"path",{d:!0}),d(l).forEach(i),n=p(u,"path",{d:!0}),d(n).forEach(i),u.forEach(i),h.forEach(i),this.h()},h(){t(l,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),t(n,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 5 5"),t(e,"version","1.1"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),t(e,"xml:space","preserve"),s(e,"fill","currentColor"),s(e,"fill-rule","evenodd"),s(e,"clip-rule","evenodd"),s(e,"stroke-linejoin","round"),s(e,"stroke-miterlimit","2")},m(o,h){C(o,e,h),c(e,r),c(r,l),c(r,n)},p:m,i:m,o:m,d(o){o&&i(e)}}}class k extends g{constructor(e){super(),x(this,e,null,f,w,{})}}export{k as C};
//# sourceMappingURL=clear.D_TEXRKD.js.map
