import{V as f,aq as b,C as _,T as y,an as p,ao as L}from"./index-Dpxo-yl_.js";import{a as A}from"./objectModelMapping-BR4RdEzn.js";import{ArrayItem as l,GLTFLoader as u}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";const n="EXT_lights_ies";class w{constructor(i){this.name=n,this._loader=i,this.enabled=this._loader.isExtensionUsed(n)}dispose(){this._loader=null,delete this._lights}onLoading(){const i=this._loader.gltf.extensions;if(i&&i[this.name]){const r=i[this.name];this._lights=r.lights,l.Assign(this._lights)}}loadNodeAsync(i,r,c){return u.LoadExtensionAsync(i,r,this.name,async(h,a)=>{this._loader._allMaterialsDirtyRequired=!0;let e,t;const m=await this._loader.loadNodeAsync(i,r,s=>{t=l.Get(h,this._lights,a.light);const g=t.name||s.name;this._loader.babylonScene._blockEntityCollection=!!this._loader._assetContainer,e=new A(g,f.Zero(),f.Backward(),0,1,this._loader.babylonScene),e.angle=Math.PI/2,e.innerAngle=0,e._parentContainer=this._loader._assetContainer,this._loader.babylonScene._blockEntityCollection=!1,t._babylonLight=e,e.falloffType=b.FALLOFF_GLTF,e.diffuse=a.color?_.FromArray(a.color):_.White(),e.intensity=a.multiplier||1,e.range=Number.MAX_VALUE,e.parent=s,this._loader._babylonLights.push(e),u.AddPointerMetadata(e,h),c(s)});let o;if(t.uri)o=await this._loader.loadUriAsync(i,t,t.uri);else{const s=l.Get(`${i}/bufferView`,this._loader.gltf.bufferViews,t.bufferView);o=await this._loader.loadBufferViewAsync(`/bufferViews/${s.index}`,s)}return e.iesProfileTexture=new y(name+"_iesProfile",this._loader.babylonScene,!0,!1,void 0,null,null,o,!0,void 0,void 0,void 0,void 0,".ies"),m})}}p(n);L(n,!0,d=>new w(d));export{w as EXT_lights_ies};
//# sourceMappingURL=EXT_lights_ies-1RUPRq1O.js.map
