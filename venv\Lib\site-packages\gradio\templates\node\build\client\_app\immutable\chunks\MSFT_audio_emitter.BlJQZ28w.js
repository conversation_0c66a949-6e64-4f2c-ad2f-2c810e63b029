import{b5 as M,R as v,A as n,O as w,V as g,ak as S,h as m,b,b7 as x,p as P,b2 as f,M as N,b0 as R,an as O,ao as I}from"./index.BoI39RQH.js";import{ArrayItem as y,GLTFLoader as C}from"./glTFLoader.BetPWe9U.js";import"./audioEngine.7qgWwRWJ.js";class k{constructor(e,t,i){this.frame=e,this.action=t,this.onlyOnce=i,this.isDone=!1}_clone(){return new k(this.frame,this.action,this.onlyOnce)}}class A{get loop(){return this._loop}set loop(e){e!==this._loop&&(this._loop=e,this.updateOptions({loop:e}))}get currentTime(){var e;if(this._htmlAudioElement)return this._htmlAudioElement.currentTime;if((e=n.audioEngine)!=null&&e.audioContext&&(this.isPlaying||this.isPaused)){const t=this.isPaused?0:n.audioEngine.audioContext.currentTime-this._startTime;return this._currentTime+t}return 0}get spatialSound(){return this._spatialSound}set spatialSound(e){if(e==this._spatialSound)return;const t=this.isPlaying;this.pause(),e?(this._spatialSound=e,this._updateSpatialParameters()):this._disableSpatialSound(),t&&this.play()}constructor(e,t,i,o=null,s){var r;if(this.autoplay=!1,this._loop=!1,this.useCustomAttenuation=!1,this.isPlaying=!1,this.isPaused=!1,this.refDistance=1,this.rolloffFactor=1,this.maxDistance=100,this.distanceModel="linear",this.metadata=null,this.onEndedObservable=new w,this._spatialSound=!1,this._panningModel="equalpower",this._playbackRate=1,this._streaming=!1,this._startTime=0,this._currentTime=0,this._position=g.Zero(),this._localDirection=new g(1,0,0),this._volume=1,this._isReadyToPlay=!1,this._isDirectional=!1,this._coneInnerAngle=360,this._coneOuterAngle=360,this._coneOuterGain=0,this._isOutputConnected=!1,this._urlType="Unknown",this.name=e,i=i||S.LastCreatedScene,!!i)if(this._scene=i,A._SceneComponentInitialization(i),this._readyToPlayCallback=o,this._customAttenuationFunction=(l,u,d,c,h)=>u<d?l*(1-u/d):0,s&&(this.autoplay=s.autoplay||!1,this._loop=s.loop||!1,s.volume!==void 0&&(this._volume=s.volume),this._spatialSound=s.spatialSound??!1,this.maxDistance=s.maxDistance??100,this.useCustomAttenuation=s.useCustomAttenuation??!1,this.rolloffFactor=s.rolloffFactor||1,this.refDistance=s.refDistance||1,this.distanceModel=s.distanceModel||"linear",this._playbackRate=s.playbackRate||1,this._streaming=s.streaming??!1,this._length=s.length,this._offset=s.offset),(r=n.audioEngine)!=null&&r.canUseWebAudio&&n.audioEngine.audioContext){this._soundGain=n.audioEngine.audioContext.createGain(),this._soundGain.gain.value=this._volume,this._inputAudioNode=this._soundGain,this._outputAudioNode=this._soundGain,this._spatialSound&&this._createSpatialParameters(),this._scene.mainSoundTrack.addSound(this);let l=!0;if(t)try{typeof t=="string"?(this._urlType="String",this._url=t):t instanceof ArrayBuffer?this._urlType="ArrayBuffer":t instanceof HTMLMediaElement?this._urlType="MediaElement":t instanceof MediaStream?this._urlType="MediaStream":t instanceof AudioBuffer?this._urlType="AudioBuffer":Array.isArray(t)&&(this._urlType="Array");let u=[],d=!1;switch(this._urlType){case"MediaElement":this._streaming=!0,this._isReadyToPlay=!0,this._streamingSource=n.audioEngine.audioContext.createMediaElementSource(t),this.autoplay&&this.play(0,this._offset,this._length),this._readyToPlayCallback&&this._readyToPlayCallback();break;case"MediaStream":this._streaming=!0,this._isReadyToPlay=!0,this._streamingSource=n.audioEngine.audioContext.createMediaStreamSource(t),this.autoplay&&this.play(0,this._offset,this._length),this._readyToPlayCallback&&this._readyToPlayCallback();break;case"ArrayBuffer":t.byteLength>0&&(d=!0,this._soundLoaded(t));break;case"AudioBuffer":this._audioBufferLoaded(t);break;case"String":u.push(t);case"Array":u.length===0&&(u=t);for(let c=0;c<u.length;c++){const h=u[c];if(d=s&&s.skipCodecCheck||h.indexOf(".mp3",h.length-4)!==-1&&n.audioEngine.isMP3supported||h.indexOf(".ogg",h.length-4)!==-1&&n.audioEngine.isOGGsupported||h.indexOf(".wav",h.length-4)!==-1||h.indexOf(".m4a",h.length-4)!==-1||h.indexOf(".mp4",h.length-4)!==-1||h.indexOf("blob:")!==-1,d){this._streaming?(this._htmlAudioElement=new Audio(h),this._htmlAudioElement.controls=!1,this._htmlAudioElement.loop=this.loop,b.SetCorsBehavior(h,this._htmlAudioElement),this._htmlAudioElement.preload="auto",this._htmlAudioElement.addEventListener("canplaythrough",()=>{this._isReadyToPlay=!0,this.autoplay&&this.play(0,this._offset,this._length),this._readyToPlayCallback&&this._readyToPlayCallback()},{once:!0}),document.body.appendChild(this._htmlAudioElement),this._htmlAudioElement.load()):this._scene._loadFile(h,p=>{this._soundLoaded(p)},void 0,!0,!0,p=>{p&&m.Error("XHR "+p.status+" error on: "+h+"."),m.Error("Sound creation aborted."),this._scene.mainSoundTrack.removeSound(this)});break}}break;default:l=!1;break}l?d||(this._isReadyToPlay=!0,this._readyToPlayCallback&&setTimeout(()=>{this._readyToPlayCallback&&this._readyToPlayCallback()},1e3)):m.Error("Parameter must be a URL to the sound, an Array of URLs (.mp3 & .ogg) or an ArrayBuffer of the sound.")}catch{m.Error("Unexpected error. Sound creation aborted."),this._scene.mainSoundTrack.removeSound(this)}}else this._scene.mainSoundTrack.addSound(this),n.audioEngine&&!n.audioEngine.WarnedWebAudioUnsupported&&(m.Error("Web Audio is not supported by your browser."),n.audioEngine.WarnedWebAudioUnsupported=!0),this._readyToPlayCallback&&setTimeout(()=>{this._readyToPlayCallback&&this._readyToPlayCallback()},1e3)}dispose(){var e;(e=n.audioEngine)!=null&&e.canUseWebAudio&&(this.isPlaying&&this.stop(),this._isReadyToPlay=!1,this.soundTrackId===-1?this._scene.mainSoundTrack.removeSound(this):this._scene.soundTracks&&this._scene.soundTracks[this.soundTrackId].removeSound(this),this._soundGain&&(this._soundGain.disconnect(),this._soundGain=null),this._soundPanner&&(this._soundPanner.disconnect(),this._soundPanner=null),this._soundSource&&(this._soundSource.disconnect(),this._soundSource=null),this._audioBuffer=null,this._htmlAudioElement&&(this._htmlAudioElement.pause(),this._htmlAudioElement.src="",document.body.removeChild(this._htmlAudioElement),this._htmlAudioElement=null),this._streamingSource&&(this._streamingSource.disconnect(),this._streamingSource=null),this._connectedTransformNode&&this._registerFunc&&(this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc),this._connectedTransformNode=null),this._clearTimeoutsAndObservers())}isReady(){return this._isReadyToPlay}getClassName(){return"Sound"}_audioBufferLoaded(e){var t;(t=n.audioEngine)!=null&&t.audioContext&&(this._audioBuffer=e,this._isReadyToPlay=!0,this.autoplay&&this.play(0,this._offset,this._length),this._readyToPlayCallback&&this._readyToPlayCallback())}_soundLoaded(e){var t;(t=n.audioEngine)!=null&&t.audioContext&&n.audioEngine.audioContext.decodeAudioData(e,i=>{this._audioBufferLoaded(i)},i=>{m.Error("Error while decoding audio data for: "+this.name+" / Error: "+i)})}setAudioBuffer(e){var t;(t=n.audioEngine)!=null&&t.canUseWebAudio&&(this._audioBuffer=e,this._isReadyToPlay=!0)}updateOptions(e){e&&(this.loop=e.loop??this.loop,this.maxDistance=e.maxDistance??this.maxDistance,this.useCustomAttenuation=e.useCustomAttenuation??this.useCustomAttenuation,this.rolloffFactor=e.rolloffFactor??this.rolloffFactor,this.refDistance=e.refDistance??this.refDistance,this.distanceModel=e.distanceModel??this.distanceModel,this._playbackRate=e.playbackRate??this._playbackRate,this._length=e.length??void 0,this.spatialSound=e.spatialSound??this._spatialSound,this._setOffset(e.offset??void 0),this.setVolume(e.volume??this._volume),this._updateSpatialParameters(),this.isPlaying&&(this._streaming&&this._htmlAudioElement?(this._htmlAudioElement.playbackRate=this._playbackRate,this._htmlAudioElement.loop!==this.loop&&(this._htmlAudioElement.loop=this.loop)):this._soundSource&&(this._soundSource.playbackRate.value=this._playbackRate,this._soundSource.loop!==this.loop&&(this._soundSource.loop=this.loop),this._offset!==void 0&&this._soundSource.loopStart!==this._offset&&(this._soundSource.loopStart=this._offset),this._length!==void 0&&this._length!==this._soundSource.loopEnd&&(this._soundSource.loopEnd=(this._offset|0)+this._length))))}_createSpatialParameters(){var e;(e=n.audioEngine)!=null&&e.canUseWebAudio&&n.audioEngine.audioContext&&(this._scene.headphone&&(this._panningModel="HRTF"),this._soundPanner=this._soundPanner??n.audioEngine.audioContext.createPanner(),this._soundPanner&&this._outputAudioNode&&(this._updateSpatialParameters(),this._soundPanner.connect(this._outputAudioNode),this._inputAudioNode=this._soundPanner))}_disableSpatialSound(){var e;this._spatialSound&&(this._inputAudioNode=this._soundGain,(e=this._soundPanner)==null||e.disconnect(),this._soundPanner=null,this._spatialSound=!1)}_updateSpatialParameters(){this._spatialSound&&(this._soundPanner?this.useCustomAttenuation?(this._soundPanner.distanceModel="linear",this._soundPanner.maxDistance=Number.MAX_VALUE,this._soundPanner.refDistance=1,this._soundPanner.rolloffFactor=1,this._soundPanner.panningModel=this._panningModel):(this._soundPanner.distanceModel=this.distanceModel,this._soundPanner.maxDistance=this.maxDistance,this._soundPanner.refDistance=this.refDistance,this._soundPanner.rolloffFactor=this.rolloffFactor,this._soundPanner.panningModel=this._panningModel):this._createSpatialParameters())}switchPanningModelToHRTF(){this._panningModel="HRTF",this._switchPanningModel()}switchPanningModelToEqualPower(){this._panningModel="equalpower",this._switchPanningModel()}_switchPanningModel(){var e;(e=n.audioEngine)!=null&&e.canUseWebAudio&&this._spatialSound&&this._soundPanner&&(this._soundPanner.panningModel=this._panningModel)}connectToSoundTrackAudioNode(e){var t;(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._outputAudioNode&&(this._isOutputConnected&&this._outputAudioNode.disconnect(),this._outputAudioNode.connect(e),this._isOutputConnected=!0)}setDirectionalCone(e,t,i){if(t<e){m.Error("setDirectionalCone(): outer angle of the cone must be superior or equal to the inner angle.");return}this._coneInnerAngle=e,this._coneOuterAngle=t,this._coneOuterGain=i,this._isDirectional=!0,this.isPlaying&&this.loop&&(this.stop(),this.play(0,this._offset,this._length))}get directionalConeInnerAngle(){return this._coneInnerAngle}set directionalConeInnerAngle(e){var t;if(e!=this._coneInnerAngle){if(this._coneOuterAngle<e){m.Error("directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.");return}this._coneInnerAngle=e,(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._spatialSound&&this._soundPanner&&(this._soundPanner.coneInnerAngle=this._coneInnerAngle)}}get directionalConeOuterAngle(){return this._coneOuterAngle}set directionalConeOuterAngle(e){var t;if(e!=this._coneOuterAngle){if(e<this._coneInnerAngle){m.Error("directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.");return}this._coneOuterAngle=e,(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._spatialSound&&this._soundPanner&&(this._soundPanner.coneOuterAngle=this._coneOuterAngle)}}setPosition(e){var t;e.equals(this._position)||(this._position.copyFrom(e),(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._spatialSound&&this._soundPanner&&!isNaN(this._position.x)&&!isNaN(this._position.y)&&!isNaN(this._position.z)&&(this._soundPanner.positionX.value=this._position.x,this._soundPanner.positionY.value=this._position.y,this._soundPanner.positionZ.value=this._position.z))}setLocalDirectionToMesh(e){var t;this._localDirection=e,(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._connectedTransformNode&&this.isPlaying&&this._updateDirection()}_updateDirection(){if(!this._connectedTransformNode||!this._soundPanner)return;const e=this._connectedTransformNode.getWorldMatrix(),t=g.TransformNormal(this._localDirection,e);t.normalize(),this._soundPanner.orientationX.value=t.x,this._soundPanner.orientationY.value=t.y,this._soundPanner.orientationZ.value=t.z}updateDistanceFromListener(){var e;if((e=n.audioEngine)!=null&&e.canUseWebAudio&&this._connectedTransformNode&&this.useCustomAttenuation&&this._soundGain&&this._scene.activeCamera){const t=this._scene.audioListenerPositionProvider?this._connectedTransformNode.position.subtract(this._scene.audioListenerPositionProvider()).length():this._connectedTransformNode.getDistanceToCamera(this._scene.activeCamera);this._soundGain.gain.value=this._customAttenuationFunction(this._volume,t,this.maxDistance,this.refDistance,this.rolloffFactor)}}setAttenuationFunction(e){this._customAttenuationFunction=e}play(e,t,i){var o,s,r,l;if(this._isReadyToPlay&&this._scene.audioEnabled&&((o=n.audioEngine)!=null&&o.audioContext))try{this._clearTimeoutsAndObservers();let u=e?((s=n.audioEngine)==null?void 0:s.audioContext.currentTime)+e:(r=n.audioEngine)==null?void 0:r.audioContext.currentTime;if((!this._soundSource||!this._streamingSource)&&this._spatialSound&&this._soundPanner&&(!isNaN(this._position.x)&&!isNaN(this._position.y)&&!isNaN(this._position.z)&&(this._soundPanner.positionX.value=this._position.x,this._soundPanner.positionY.value=this._position.y,this._soundPanner.positionZ.value=this._position.z),this._isDirectional&&(this._soundPanner.coneInnerAngle=this._coneInnerAngle,this._soundPanner.coneOuterAngle=this._coneOuterAngle,this._soundPanner.coneOuterGain=this._coneOuterGain,this._connectedTransformNode?this._updateDirection():this._soundPanner.setOrientation(this._localDirection.x,this._localDirection.y,this._localDirection.z))),this._streaming){if(!this._streamingSource&&this._htmlAudioElement&&(this._streamingSource=n.audioEngine.audioContext.createMediaElementSource(this._htmlAudioElement),this._htmlAudioElement.onended=()=>{this._onended()},this._htmlAudioElement.playbackRate=this._playbackRate),this._streamingSource&&(this._streamingSource.disconnect(),this._inputAudioNode&&this._streamingSource.connect(this._inputAudioNode)),this._htmlAudioElement){const d=()=>{var c,h;if((c=n.audioEngine)!=null&&c.unlocked){if(!this._htmlAudioElement)return;this._htmlAudioElement.currentTime=t??0;const p=this._htmlAudioElement.play();p!==void 0&&p.catch(()=>{var T,D;(T=n.audioEngine)==null||T.lock(),(this.loop||this.autoplay)&&(this._audioUnlockedObserver=(D=n.audioEngine)==null?void 0:D.onAudioUnlockedObservable.addOnce(()=>{d()}))})}else(this.loop||this.autoplay)&&(this._audioUnlockedObserver=(h=n.audioEngine)==null?void 0:h.onAudioUnlockedObservable.addOnce(()=>{d()}))};d()}}else{const d=()=>{var c,h,p;if((c=n.audioEngine)!=null&&c.audioContext){if(i=i||this._length,t!==void 0&&this._setOffset(t),this._soundSource){const T=this._soundSource;T.onended=()=>{T.disconnect()}}if(this._soundSource=(h=n.audioEngine)==null?void 0:h.audioContext.createBufferSource(),this._soundSource&&this._inputAudioNode){this._soundSource.buffer=this._audioBuffer,this._soundSource.connect(this._inputAudioNode),this._soundSource.loop=this.loop,t!==void 0&&(this._soundSource.loopStart=t),i!==void 0&&(this._soundSource.loopEnd=(t|0)+i),this._soundSource.playbackRate.value=this._playbackRate,this._soundSource.onended=()=>{this._onended()},u=e?((p=n.audioEngine)==null?void 0:p.audioContext.currentTime)+e:n.audioEngine.audioContext.currentTime;const T=((this.isPaused?this.currentTime:0)+(this._offset??0))%this._soundSource.buffer.duration;this._soundSource.start(u,T,this.loop?void 0:i)}}};((l=n.audioEngine)==null?void 0:l.audioContext.state)==="suspended"?this._tryToPlayTimeout=setTimeout(()=>{var c;((c=n.audioEngine)==null?void 0:c.audioContext.state)==="suspended"?(n.audioEngine.lock(),(this.loop||this.autoplay)&&(this._audioUnlockedObserver=n.audioEngine.onAudioUnlockedObservable.addOnce(()=>{d()}))):d()},500):d()}this._startTime=u,this.isPlaying=!0,this.isPaused=!1}catch(u){m.Error("Error while trying to play audio: "+this.name+", "+u.message)}}_onended(){this.isPlaying=!1,this._startTime=0,this._currentTime=0,this.onended&&this.onended(),this.onEndedObservable.notifyObservers(this)}stop(e){var t,i;if(this.isPlaying)if(this._clearTimeoutsAndObservers(),this._streaming)this._htmlAudioElement?(this._htmlAudioElement.pause(),this._htmlAudioElement.currentTime>0&&(this._htmlAudioElement.currentTime=0)):(t=this._streamingSource)==null||t.disconnect(),this.isPlaying=!1;else if((i=n.audioEngine)!=null&&i.audioContext&&this._soundSource){const o=e?n.audioEngine.audioContext.currentTime+e:void 0;this._soundSource.onended=()=>{this.isPlaying=!1,this.isPaused=!1,this._startTime=0,this._currentTime=0,this._soundSource&&(this._soundSource.onended=()=>{}),this._onended()},this._soundSource.stop(o)}else this.isPlaying=!1;else this.isPaused&&(this.isPaused=!1,this._startTime=0,this._currentTime=0)}pause(){var e,t;this.isPlaying&&(this._clearTimeoutsAndObservers(),this._streaming?(this._htmlAudioElement?this._htmlAudioElement.pause():(e=this._streamingSource)==null||e.disconnect(),this.isPlaying=!1,this.isPaused=!0):(t=n.audioEngine)!=null&&t.audioContext&&this._soundSource&&(this._soundSource.onended=()=>{},this._soundSource.stop(),this.isPlaying=!1,this.isPaused=!0,this._currentTime+=n.audioEngine.audioContext.currentTime-this._startTime))}setVolume(e,t){var i;(i=n.audioEngine)!=null&&i.canUseWebAudio&&this._soundGain&&(t&&n.audioEngine.audioContext?(this._soundGain.gain.cancelScheduledValues(n.audioEngine.audioContext.currentTime),this._soundGain.gain.setValueAtTime(this._soundGain.gain.value,n.audioEngine.audioContext.currentTime),this._soundGain.gain.linearRampToValueAtTime(e,n.audioEngine.audioContext.currentTime+t)):this._soundGain.gain.value=e),this._volume=e}setPlaybackRate(e){this._playbackRate=e,this.isPlaying&&(this._streaming&&this._htmlAudioElement?this._htmlAudioElement.playbackRate=this._playbackRate:this._soundSource&&(this._soundSource.playbackRate.value=this._playbackRate))}getPlaybackRate(){return this._playbackRate}getVolume(){return this._volume}attachToMesh(e){this._connectedTransformNode&&this._registerFunc&&(this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc),this._registerFunc=null),this._connectedTransformNode=e,this._spatialSound||(this._spatialSound=!0,this._createSpatialParameters(),this.isPlaying&&this.loop&&(this.stop(),this.play(0,this._offset,this._length))),this._onRegisterAfterWorldMatrixUpdate(this._connectedTransformNode),this._registerFunc=t=>this._onRegisterAfterWorldMatrixUpdate(t),this._connectedTransformNode.registerAfterWorldMatrixUpdate(this._registerFunc)}detachFromMesh(){this._connectedTransformNode&&this._registerFunc&&(this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc),this._registerFunc=null,this._connectedTransformNode=null)}_onRegisterAfterWorldMatrixUpdate(e){var t;if(!e.getBoundingInfo)this.setPosition(e.absolutePosition);else{const o=e.getBoundingInfo();this.setPosition(o.boundingSphere.centerWorld)}(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._isDirectional&&this.isPlaying&&this._updateDirection()}clone(){if(this._streaming)return null;{const e=()=>{x(()=>this._isReadyToPlay,()=>{i._audioBuffer=this.getAudioBuffer(),i._isReadyToPlay=!0,i.autoplay&&i.play(0,this._offset,this._length)},void 0,300)},t={autoplay:this.autoplay,loop:this.loop,volume:this._volume,spatialSound:this._spatialSound,maxDistance:this.maxDistance,useCustomAttenuation:this.useCustomAttenuation,rolloffFactor:this.rolloffFactor,refDistance:this.refDistance,distanceModel:this.distanceModel},i=new A(this.name+"_cloned",new ArrayBuffer(0),this._scene,null,t);return this.useCustomAttenuation&&i.setAttenuationFunction(this._customAttenuationFunction),i.setPosition(this._position),i.setPlaybackRate(this._playbackRate),e(),i}}getAudioBuffer(){return this._audioBuffer}getSoundSource(){return this._soundSource}getSoundGain(){return this._soundGain}serialize(){const e={name:this.name,url:this._url,autoplay:this.autoplay,loop:this.loop,volume:this._volume,spatialSound:this._spatialSound,maxDistance:this.maxDistance,rolloffFactor:this.rolloffFactor,refDistance:this.refDistance,distanceModel:this.distanceModel,playbackRate:this._playbackRate,panningModel:this._panningModel,soundTrackId:this.soundTrackId,metadata:this.metadata};return this._spatialSound&&(this._connectedTransformNode&&(e.connectedMeshId=this._connectedTransformNode.id),e.position=this._position.asArray(),e.refDistance=this.refDistance,e.distanceModel=this.distanceModel,e.isDirectional=this._isDirectional,e.localDirectionToMesh=this._localDirection.asArray(),e.coneInnerAngle=this._coneInnerAngle,e.coneOuterAngle=this._coneOuterAngle,e.coneOuterGain=this._coneOuterGain),e}static Parse(e,t,i,o){const s=e.name;let r;e.url?r=i+e.url:r=i+s;const l={autoplay:e.autoplay,loop:e.loop,volume:e.volume,spatialSound:e.spatialSound,maxDistance:e.maxDistance,rolloffFactor:e.rolloffFactor,refDistance:e.refDistance,distanceModel:e.distanceModel,playbackRate:e.playbackRate};let u;if(!o)u=new A(s,r,t,()=>{t.removePendingData(u)},l),t.addPendingData(u);else{const d=()=>{x(()=>o._isReadyToPlay,()=>{u._audioBuffer=o.getAudioBuffer(),u._isReadyToPlay=!0,u.autoplay&&u.play(0,u._offset,u._length)},void 0,300)};u=new A(s,new ArrayBuffer(0),t,null,l),d()}if(e.position){const d=g.FromArray(e.position);u.setPosition(d)}if(e.isDirectional&&(u.setDirectionalCone(e.coneInnerAngle||360,e.coneOuterAngle||360,e.coneOuterGain||0),e.localDirectionToMesh)){const d=g.FromArray(e.localDirectionToMesh);u.setLocalDirectionToMesh(d)}if(e.connectedMeshId){const d=t.getMeshById(e.connectedMeshId);d&&u.attachToMesh(d)}return e.metadata&&(u.metadata=e.metadata),u}_setOffset(e){this._offset!==e&&(this.isPaused&&(this.stop(),this.isPaused=!1),this._offset=e)}_clearTimeoutsAndObservers(){var e;this._tryToPlayTimeout&&(clearTimeout(this._tryToPlayTimeout),this._tryToPlayTimeout=null),this._audioUnlockedObserver&&((e=n.audioEngine)==null||e.onAudioUnlockedObservable.remove(this._audioUnlockedObserver),this._audioUnlockedObserver=null)}}A._SceneComponentInitialization=a=>{throw M("AudioSceneComponent")};v("BABYLON.Sound",A);class F{constructor(e,t,i){if(this.loop=!1,this._coneInnerAngle=360,this._coneOuterAngle=360,this._volume=1,this.isPlaying=!1,this.isPaused=!1,this._sounds=[],this._weights=[],t.length!==i.length)throw new Error("Sounds length does not equal weights length");this.loop=e,this._weights=i;let o=0;for(const r of i)o+=r;const s=o>0?1/o:0;for(let r=0;r<this._weights.length;r++)this._weights[r]*=s;this._sounds=t;for(const r of this._sounds)r.onEndedObservable.add(()=>{this._onended()})}get directionalConeInnerAngle(){return this._coneInnerAngle}set directionalConeInnerAngle(e){if(e!==this._coneInnerAngle){if(this._coneOuterAngle<e){m.Error("directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.");return}this._coneInnerAngle=e;for(const t of this._sounds)t.directionalConeInnerAngle=e}}get directionalConeOuterAngle(){return this._coneOuterAngle}set directionalConeOuterAngle(e){if(e!==this._coneOuterAngle){if(e<this._coneInnerAngle){m.Error("directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.");return}this._coneOuterAngle=e;for(const t of this._sounds)t.directionalConeOuterAngle=e}}get volume(){return this._volume}set volume(e){if(e!==this._volume)for(const t of this._sounds)t.setVolume(e)}_onended(){this._currentIndex!==void 0&&(this._sounds[this._currentIndex].autoplay=!1),this.loop&&this.isPlaying?this.play():this.isPlaying=!1}pause(){this.isPlaying&&(this.isPaused=!0,this._currentIndex!==void 0&&this._sounds[this._currentIndex].pause())}stop(){this.isPlaying=!1,this._currentIndex!==void 0&&this._sounds[this._currentIndex].stop()}play(e){if(!this.isPaused){this.stop();const i=Math.random();let o=0;for(let s=0;s<this._weights.length;s++)if(o+=this._weights[s],i<=o){this._currentIndex=s;break}}const t=this._sounds[this._currentIndex??0];t.isReady()?t.play(0,this.isPaused?void 0:e):t.autoplay=!0,this.isPlaying=!0,this.isPaused=!1}}class U{constructor(e,t={}){this.id=-1,this._isInitialized=!1,e=e||S.LastCreatedScene,e&&(this._scene=e,this.soundCollection=[],this._options=t,!this._options.mainTrack&&this._scene.soundTracks&&(this._scene.soundTracks.push(this),this.id=this._scene.soundTracks.length-1))}_initializeSoundTrackAudioGraph(){var e;(e=n.audioEngine)!=null&&e.canUseWebAudio&&n.audioEngine.audioContext&&(this._outputAudioNode=n.audioEngine.audioContext.createGain(),this._outputAudioNode.connect(n.audioEngine.masterGain),this._options&&this._options.volume&&(this._outputAudioNode.gain.value=this._options.volume),this._isInitialized=!0)}dispose(){if(n.audioEngine&&n.audioEngine.canUseWebAudio){for(this._connectedAnalyser&&this._connectedAnalyser.stopDebugCanvas();this.soundCollection.length;)this.soundCollection[0].dispose();this._outputAudioNode&&this._outputAudioNode.disconnect(),this._outputAudioNode=null}}addSound(e){var t;this._isInitialized||this._initializeSoundTrackAudioGraph(),(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._outputAudioNode&&e.connectToSoundTrackAudioNode(this._outputAudioNode),e.soundTrackId!==void 0&&(e.soundTrackId===-1?this._scene.mainSoundTrack.removeSound(e):this._scene.soundTracks&&this._scene.soundTracks[e.soundTrackId].removeSound(e)),this.soundCollection.push(e),e.soundTrackId=this.id}removeSound(e){const t=this.soundCollection.indexOf(e);t!==-1&&this.soundCollection.splice(t,1)}setVolume(e){var t;(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._outputAudioNode&&(this._outputAudioNode.gain.value=e)}switchPanningModelToHRTF(){var e;if((e=n.audioEngine)!=null&&e.canUseWebAudio)for(let t=0;t<this.soundCollection.length;t++)this.soundCollection[t].switchPanningModelToHRTF()}switchPanningModelToEqualPower(){var e;if((e=n.audioEngine)!=null&&e.canUseWebAudio)for(let t=0;t<this.soundCollection.length;t++)this.soundCollection[t].switchPanningModelToEqualPower()}connectToAnalyser(e){var t;this._connectedAnalyser&&this._connectedAnalyser.stopDebugCanvas(),this._connectedAnalyser=e,(t=n.audioEngine)!=null&&t.canUseWebAudio&&this._outputAudioNode&&(this._outputAudioNode.disconnect(),this._connectedAnalyser.connectAudioNodes(this._outputAudioNode,n.audioEngine.masterGain))}}Object.defineProperty(P.prototype,"mainSoundTrack",{get:function(){let a=this._getComponent(f.NAME_AUDIO);return a||(a=new _(this),this._addComponent(a)),this._mainSoundTrack||(this._mainSoundTrack=new U(this,{mainTrack:!0})),this._mainSoundTrack},enumerable:!0,configurable:!0});P.prototype.getSoundByName=function(a){let e;for(e=0;e<this.mainSoundTrack.soundCollection.length;e++)if(this.mainSoundTrack.soundCollection[e].name===a)return this.mainSoundTrack.soundCollection[e];if(this.soundTracks){for(let t=0;t<this.soundTracks.length;t++)for(e=0;e<this.soundTracks[t].soundCollection.length;e++)if(this.soundTracks[t].soundCollection[e].name===a)return this.soundTracks[t].soundCollection[e]}return null};Object.defineProperty(P.prototype,"audioEnabled",{get:function(){let a=this._getComponent(f.NAME_AUDIO);return a||(a=new _(this),this._addComponent(a)),a.audioEnabled},set:function(a){let e=this._getComponent(f.NAME_AUDIO);e||(e=new _(this),this._addComponent(e)),a?e.enableAudio():e.disableAudio()},enumerable:!0,configurable:!0});Object.defineProperty(P.prototype,"headphone",{get:function(){let a=this._getComponent(f.NAME_AUDIO);return a||(a=new _(this),this._addComponent(a)),a.headphone},set:function(a){let e=this._getComponent(f.NAME_AUDIO);e||(e=new _(this),this._addComponent(e)),a?e.switchAudioModeForHeadphones():e.switchAudioModeForNormalSpeakers()},enumerable:!0,configurable:!0});Object.defineProperty(P.prototype,"audioListenerPositionProvider",{get:function(){let a=this._getComponent(f.NAME_AUDIO);return a||(a=new _(this),this._addComponent(a)),a.audioListenerPositionProvider},set:function(a){let e=this._getComponent(f.NAME_AUDIO);if(e||(e=new _(this),this._addComponent(e)),a&&typeof a!="function")throw new Error("The value passed to [Scene.audioListenerPositionProvider] must be a function that returns a Vector3");e.audioListenerPositionProvider=a},enumerable:!0,configurable:!0});Object.defineProperty(P.prototype,"audioListenerRotationProvider",{get:function(){let a=this._getComponent(f.NAME_AUDIO);return a||(a=new _(this),this._addComponent(a)),a.audioListenerRotationProvider},set:function(a){let e=this._getComponent(f.NAME_AUDIO);if(e||(e=new _(this),this._addComponent(e)),a&&typeof a!="function")throw new Error("The value passed to [Scene.audioListenerRotationProvider] must be a function that returns a Vector3");e.audioListenerRotationProvider=a},enumerable:!0,configurable:!0});Object.defineProperty(P.prototype,"audioPositioningRefreshRate",{get:function(){let a=this._getComponent(f.NAME_AUDIO);return a||(a=new _(this),this._addComponent(a)),a.audioPositioningRefreshRate},set:function(a){let e=this._getComponent(f.NAME_AUDIO);e||(e=new _(this),this._addComponent(e)),e.audioPositioningRefreshRate=a},enumerable:!0,configurable:!0});class _{get audioEnabled(){return this._audioEnabled}get headphone(){return this._headphone}constructor(e){this.name=f.NAME_AUDIO,this._audioEnabled=!0,this._headphone=!1,this.audioPositioningRefreshRate=500,this.audioListenerPositionProvider=null,this.audioListenerRotationProvider=null,this._cachedCameraDirection=new g,this._cachedCameraPosition=new g,this._lastCheck=0,this._invertMatrixTemp=new N,this._cameraDirectionTemp=new g,e=e||S.LastCreatedScene,e&&(this.scene=e,e.soundTracks=[],e.sounds=[])}register(){this.scene._afterRenderStage.registerStep(f.STEP_AFTERRENDER_AUDIO,this,this._afterRender)}rebuild(){}serialize(e){if(e.sounds=[],this.scene.soundTracks)for(let t=0;t<this.scene.soundTracks.length;t++){const i=this.scene.soundTracks[t];for(let o=0;o<i.soundCollection.length;o++)e.sounds.push(i.soundCollection[o].serialize())}}addFromContainer(e){e.sounds&&e.sounds.forEach(t=>{t.play(),t.autoplay=!0,this.scene.mainSoundTrack.addSound(t)})}removeFromContainer(e,t=!1){e.sounds&&e.sounds.forEach(i=>{i.stop(),i.autoplay=!1,this.scene.mainSoundTrack.removeSound(i),t&&i.dispose()})}dispose(){const e=this.scene;if(e._mainSoundTrack&&e.mainSoundTrack.dispose(),e.soundTracks)for(let t=0;t<e.soundTracks.length;t++)e.soundTracks[t].dispose()}disableAudio(){const e=this.scene;this._audioEnabled=!1,n.audioEngine&&n.audioEngine.audioContext&&n.audioEngine.audioContext.suspend();let t;for(t=0;t<e.mainSoundTrack.soundCollection.length;t++)e.mainSoundTrack.soundCollection[t].pause();if(e.soundTracks)for(t=0;t<e.soundTracks.length;t++)for(let i=0;i<e.soundTracks[t].soundCollection.length;i++)e.soundTracks[t].soundCollection[i].pause()}enableAudio(){const e=this.scene;this._audioEnabled=!0,n.audioEngine&&n.audioEngine.audioContext&&n.audioEngine.audioContext.resume();let t;for(t=0;t<e.mainSoundTrack.soundCollection.length;t++)e.mainSoundTrack.soundCollection[t].isPaused&&e.mainSoundTrack.soundCollection[t].play();if(e.soundTracks)for(t=0;t<e.soundTracks.length;t++)for(let i=0;i<e.soundTracks[t].soundCollection.length;i++)e.soundTracks[t].soundCollection[i].isPaused&&e.soundTracks[t].soundCollection[i].play()}switchAudioModeForHeadphones(){const e=this.scene;if(this._headphone=!0,e.mainSoundTrack.switchPanningModelToHRTF(),e.soundTracks)for(let t=0;t<e.soundTracks.length;t++)e.soundTracks[t].switchPanningModelToHRTF()}switchAudioModeForNormalSpeakers(){const e=this.scene;if(this._headphone=!1,e.mainSoundTrack.switchPanningModelToEqualPower(),e.soundTracks)for(let t=0;t<e.soundTracks.length;t++)e.soundTracks[t].switchPanningModelToEqualPower()}_afterRender(){const e=R.Now;if(this._lastCheck&&e-this._lastCheck<this.audioPositioningRefreshRate)return;this._lastCheck=e;const t=this.scene;if(!this._audioEnabled||!t._mainSoundTrack||!t.soundTracks||t._mainSoundTrack.soundCollection.length===0&&t.soundTracks.length===1)return;const i=n.audioEngine;if(i&&i.audioContext){let o=t.activeCamera;if(t.activeCameras&&t.activeCameras.length>0&&(o=t.activeCameras[0]),this.audioListenerPositionProvider){const r=this.audioListenerPositionProvider();i.audioContext.listener.setPosition(r.x||0,r.y||0,r.z||0)}else o?this._cachedCameraPosition.equals(o.globalPosition)||(this._cachedCameraPosition.copyFrom(o.globalPosition),i.audioContext.listener.setPosition(o.globalPosition.x,o.globalPosition.y,o.globalPosition.z)):i.audioContext.listener.setPosition(0,0,0);if(this.audioListenerRotationProvider){const r=this.audioListenerRotationProvider();i.audioContext.listener.setOrientation(r.x||0,r.y||0,r.z||0,0,1,0)}else o?(o.rigCameras&&o.rigCameras.length>0&&(o=o.rigCameras[0]),o.getViewMatrix().invertToRef(this._invertMatrixTemp),g.TransformNormalToRef(_._CameraDirection,this._invertMatrixTemp,this._cameraDirectionTemp),this._cameraDirectionTemp.normalize(),!isNaN(this._cameraDirectionTemp.x)&&!isNaN(this._cameraDirectionTemp.y)&&!isNaN(this._cameraDirectionTemp.z)&&(this._cachedCameraDirection.equals(this._cameraDirectionTemp)||(this._cachedCameraDirection.copyFrom(this._cameraDirectionTemp),i.audioContext.listener.setOrientation(this._cameraDirectionTemp.x,this._cameraDirectionTemp.y,this._cameraDirectionTemp.z,0,1,0)))):i.audioContext.listener.setOrientation(0,0,0,0,1,0);let s;for(s=0;s<t.mainSoundTrack.soundCollection.length;s++){const r=t.mainSoundTrack.soundCollection[s];r.useCustomAttenuation&&r.updateDistanceFromListener()}if(t.soundTracks)for(s=0;s<t.soundTracks.length;s++)for(let r=0;r<t.soundTracks[s].soundCollection.length;r++){const l=t.soundTracks[s].soundCollection[r];l.useCustomAttenuation&&l.updateDistanceFromListener()}}}}_._CameraDirection=new g(0,0,-1);A._SceneComponentInitialization=a=>{let e=a._getComponent(f.NAME_AUDIO);e||(e=new _(a),a._addComponent(e))};const E="MSFT_audio_emitter";class L{constructor(e){this.name=E,this._loader=e,this.enabled=this._loader.isExtensionUsed(E)}dispose(){this._loader=null,this._clips=null,this._emitters=null}onLoading(){const e=this._loader.gltf.extensions;if(e&&e[this.name]){const t=e[this.name];this._clips=t.clips,this._emitters=t.emitters,y.Assign(this._clips),y.Assign(this._emitters)}}loadSceneAsync(e,t){return C.LoadExtensionAsync(e,t,this.name,(i,o)=>{const s=new Array;s.push(this._loader.loadSceneAsync(e,t));for(const r of o.emitters){const l=y.Get(`${i}/emitters`,this._emitters,r);if(l.refDistance!=null||l.maxDistance!=null||l.rolloffFactor!=null||l.distanceModel!=null||l.innerAngle!=null||l.outerAngle!=null)throw new Error(`${i}: Direction or Distance properties are not allowed on emitters attached to a scene`);s.push(this._loadEmitterAsync(`${i}/emitters/${l.index}`,l))}return Promise.all(s).then(()=>{})})}loadNodeAsync(e,t,i){return C.LoadExtensionAsync(e,t,this.name,(o,s)=>{const r=new Array;return this._loader.loadNodeAsync(o,t,l=>{for(const u of s.emitters){const d=y.Get(`${o}/emitters`,this._emitters,u);r.push(this._loadEmitterAsync(`${o}/emitters/${d.index}`,d).then(()=>{for(const c of d._babylonSounds)c.attachToMesh(l),(d.innerAngle!=null||d.outerAngle!=null)&&(c.setLocalDirectionToMesh(g.Forward()),c.setDirectionalCone(2*b.ToDegrees(d.innerAngle==null?Math.PI:d.innerAngle),2*b.ToDegrees(d.outerAngle==null?Math.PI:d.outerAngle),0))}))}i(l)}).then(l=>Promise.all(r).then(()=>l))})}loadAnimationAsync(e,t){return C.LoadExtensionAsync(e,t,this.name,(i,o)=>this._loader.loadAnimationAsync(e,t).then(s=>{const r=new Array;y.Assign(o.events);for(const l of o.events)r.push(this._loadAnimationEventAsync(`${i}/events/${l.index}`,e,t,l,s));return Promise.all(r).then(()=>s)}))}_loadClipAsync(e,t){if(t._objectURL)return t._objectURL;let i;if(t.uri)i=this._loader.loadUriAsync(e,t,t.uri);else{const o=y.Get(`${e}/bufferView`,this._loader.gltf.bufferViews,t.bufferView);i=this._loader.loadBufferViewAsync(`/bufferViews/${o.index}`,o)}return t._objectURL=i.then(o=>URL.createObjectURL(new Blob([o],{type:t.mimeType}))),t._objectURL}_loadEmitterAsync(e,t){if(t._babylonSounds=t._babylonSounds||[],!t._babylonData){const i=new Array,o=t.name||`emitter${t.index}`,s={loop:!1,autoplay:!1,volume:t.volume==null?1:t.volume};for(let l=0;l<t.clips.length;l++){const u=`/extensions/${this.name}/clips`,d=y.Get(u,this._clips,t.clips[l].clip);i.push(this._loadClipAsync(`${u}/${t.clips[l].clip}`,d).then(c=>{const h=t._babylonSounds[l]=new A(o,c,this._loader.babylonScene,null,s);h.refDistance=t.refDistance||1,h.maxDistance=t.maxDistance||256,h.rolloffFactor=t.rolloffFactor||1,h.distanceModel=t.distanceModel||"exponential"}))}const r=Promise.all(i).then(()=>{const l=t.clips.map(d=>d.weight||1),u=new F(t.loop||!1,t._babylonSounds,l);t.innerAngle&&(u.directionalConeInnerAngle=2*b.ToDegrees(t.innerAngle)),t.outerAngle&&(u.directionalConeOuterAngle=2*b.ToDegrees(t.outerAngle)),t.volume&&(u.volume=t.volume),t._babylonData.sound=u});t._babylonData={loaded:r}}return t._babylonData.loaded}_getEventAction(e,t,i,o,s){switch(i){case"play":return r=>{const l=(s||0)+(r-o);t.play(l)};case"stop":return()=>{t.stop()};case"pause":return()=>{t.pause()};default:throw new Error(`${e}: Unsupported action ${i}`)}}_loadAnimationEventAsync(e,t,i,o,s){if(s.targetedAnimations.length==0)return Promise.resolve();const r=s.targetedAnimations[0],l=o.emitter,u=y.Get(`/extensions/${this.name}/emitters`,this._emitters,l);return this._loadEmitterAsync(e,u).then(()=>{const d=u._babylonData.sound;if(d){const c=new k(o.time,this._getEventAction(e,d,o.action,o.time,o.startOffset));r.animation.addEvent(c),s.onAnimationGroupEndObservable.add(()=>{d.stop()}),s.onAnimationGroupPauseObservable.add(()=>{d.pause()})}})}}O(E);I(E,!0,a=>new L(a));export{L as MSFT_audio_emitter};
//# sourceMappingURL=MSFT_audio_emitter.BlJQZ28w.js.map
