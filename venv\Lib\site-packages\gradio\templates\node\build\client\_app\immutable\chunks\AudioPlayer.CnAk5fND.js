import{SvelteComponent as le,init as ae,safe_not_equal as ue,svg_element as F,claim_svg_element as q,children as T,detach as y,attr as h,insert_hydration as X,append_hydration as E,noop as I,text as we,claim_text as ye,empty as ke,group_outros as fe,transition_out as z,check_outros as me,transition_in as O,create_component as Y,claim_component as K,mount_component as J,destroy_component as Q,element as H,claim_element as x,listen as Z,run_all as Re,onMount as Xe,binding_callbacks as oe,space as ee,claim_space as te,set_style as Me,toggle_class as De,set_data as Ge,bind as Ce,add_flush_callback as Ee,get_svelte_dataset as Te,createEventDispatcher as Ye,bubble as Ke}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{x as Be,U as Se}from"./2.B2AoQPnG.js";import{M as Je}from"./Music.BVFRDHso.js";import{P as Qe,T as $e}from"./Trim.CWFkmJwA.js";import{P as et}from"./Play.DJ4h2PVY.js";import{U as tt}from"./Undo.LhwFM5M8.js";import{E as it}from"./Empty.DwQ6nkN6.js";import{H as ve}from"./hls.C_AVPmGC.js";function nt(s){let e,t;return{c(){e=F("svg"),t=F("path"),this.h()},l(i){e=q(i,"svg",{xmlns:!0,width:!0,height:!0,fill:!0,"stroke-width":!0,viewBox:!0,color:!0});var n=T(e);t=q(n,"path",{stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,d:!0}),T(t).forEach(y),n.forEach(y),this.h()},h(){h(t,"stroke","currentColor"),h(t,"stroke-width","1.5"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round"),h(t,"d","M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"width","24px"),h(e,"height","24px"),h(e,"fill","currentColor"),h(e,"stroke-width","1.5"),h(e,"viewBox","0 0 24 24"),h(e,"color","currentColor")},m(i,n){X(i,e,n),E(e,t)},p:I,i:I,o:I,d(i){i&&y(e)}}}class rt extends le{constructor(e){super(),ae(this,e,null,nt,ue,{})}}function st(s){let e,t;return{c(){e=F("svg"),t=F("path"),this.h()},l(i){e=q(i,"svg",{xmlns:!0,width:!0,height:!0,fill:!0,"stroke-width":!0,viewBox:!0,color:!0});var n=T(e);t=q(n,"path",{stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,d:!0}),T(t).forEach(y),n.forEach(y),this.h()},h(){h(t,"stroke","currentColor"),h(t,"stroke-width","1.5"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round"),h(t,"d","M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"width","24px"),h(e,"height","24px"),h(e,"fill","currentColor"),h(e,"stroke-width","1.5"),h(e,"viewBox","0 0 24 24"),h(e,"color","currentColor")},m(i,n){X(i,e,n),E(e,t)},p:I,i:I,o:I,d(i){i&&y(e)}}}class ot extends le{constructor(e){super(),ae(this,e,null,st,ue,{})}}function lt(s){let e,t,i,n,r;return{c(){e=F("svg"),t=F("title"),i=we("Low volume"),n=F("path"),r=F("path"),this.h()},l(o){e=q(o,"svg",{width:!0,height:!0,viewBox:!0,"stroke-width":!0,fill:!0,xmlns:!0,stroke:!0,color:!0});var a=T(e);t=q(a,"title",{});var u=T(t);i=ye(u,"Low volume"),u.forEach(y),n=q(a,"path",{d:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),T(n).forEach(y),r=q(a,"path",{d:!0,"stroke-width":!0}),T(r).forEach(y),a.forEach(y),this.h()},h(){h(n,"d","M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5"),h(n,"stroke-width","1.5"),h(n,"stroke-linecap","round"),h(n,"stroke-linejoin","round"),h(r,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),h(r,"stroke-width","1.5"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 24 24"),h(e,"stroke-width","1.5"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"stroke","currentColor"),h(e,"color","currentColor")},m(o,a){X(o,e,a),E(e,t),E(t,i),E(e,n),E(e,r)},p:I,i:I,o:I,d(o){o&&y(e)}}}class at extends le{constructor(e){super(),ae(this,e,null,lt,ue,{})}}function ut(s){let e,t,i,n,r,o;return{c(){e=F("svg"),t=F("title"),i=we("High volume"),n=F("path"),r=F("path"),o=F("path"),this.h()},l(a){e=q(a,"svg",{width:!0,height:!0,viewBox:!0,"stroke-width":!0,fill:!0,stroke:!0,xmlns:!0,color:!0});var u=T(e);t=q(u,"title",{});var d=T(t);i=ye(d,"High volume"),d.forEach(y),n=q(u,"path",{d:!0,"stroke-width":!0}),T(n).forEach(y),r=q(u,"path",{d:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),T(r).forEach(y),o=q(u,"path",{d:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),T(o).forEach(y),u.forEach(y),this.h()},h(){h(n,"d","M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z"),h(n,"stroke-width","1.5"),h(r,"d","M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5"),h(r,"stroke-width","1.5"),h(r,"stroke-linecap","round"),h(r,"stroke-linejoin","round"),h(o,"d","M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5"),h(o,"stroke-width","1.5"),h(o,"stroke-linecap","round"),h(o,"stroke-linejoin","round"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 24 24"),h(e,"stroke-width","1.5"),h(e,"fill","none"),h(e,"stroke","currentColor"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"color","currentColor")},m(a,u){X(a,e,u),E(e,t),E(t,i),E(e,n),E(e,r),E(e,o)},p:I,i:I,o:I,d(a){a&&y(e)}}}class dt extends le{constructor(e){super(),ae(this,e,null,ut,ue,{})}}function ht(s){let e,t,i,n,r,o,a,u,d;return{c(){e=F("svg"),t=F("title"),i=we("Muted volume"),n=F("g"),r=F("path"),o=F("path"),a=F("defs"),u=F("clipPath"),d=F("rect"),this.h()},l(l){e=q(l,"svg",{width:!0,height:!0,viewBox:!0,"stroke-width":!0,fill:!0,xmlns:!0,stroke:!0,color:!0});var c=T(e);t=q(c,"title",{});var w=T(t);i=ye(w,"Muted volume"),w.forEach(y),n=q(c,"g",{"clip-path":!0});var f=T(n);r=q(f,"path",{d:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),T(r).forEach(y),o=q(f,"path",{d:!0,"stroke-width":!0}),T(o).forEach(y),f.forEach(y),a=q(c,"defs",{});var p=T(a);u=q(p,"clipPath",{id:!0});var C=T(u);d=q(C,"rect",{width:!0,height:!0,fill:!0}),T(d).forEach(y),C.forEach(y),p.forEach(y),c.forEach(y),this.h()},h(){h(r,"d","M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14"),h(r,"stroke-width","1.5"),h(r,"stroke-linecap","round"),h(r,"stroke-linejoin","round"),h(o,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),h(o,"stroke-width","1.5"),h(n,"clip-path","url(#clip0_3173_16686)"),h(d,"width","24"),h(d,"height","24"),h(d,"fill","white"),h(u,"id","clip0_3173_16686"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 24 24"),h(e,"stroke-width","1.5"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"stroke","currentColor"),h(e,"color","currentColor")},m(l,c){X(l,e,c),E(e,t),E(t,i),E(e,n),E(n,r),E(n,o),E(e,a),E(a,u),E(u,d)},p:I,i:I,o:I,d(l){l&&y(e)}}}class ct extends le{constructor(e){super(),ae(this,e,null,ht,ue,{})}}var ft=function(s,e,t,i){function n(r){return r instanceof t?r:new t(function(o){o(r)})}return new(t||(t=Promise))(function(r,o){function a(l){try{d(i.next(l))}catch(c){o(c)}}function u(l){try{d(i.throw(l))}catch(c){o(c)}}function d(l){l.done?r(l.value):n(l.value).then(a,u)}d((i=i.apply(s,e||[])).next())})};function mt(s,e){return ft(this,void 0,void 0,function*(){const t=new AudioContext({sampleRate:e});return t.decodeAudioData(s).finally(()=>t.close())})}function pt(s){const e=s[0];if(e.some(t=>t>1||t<-1)){const t=e.length;let i=0;for(let n=0;n<t;n++){const r=Math.abs(e[n]);r>i&&(i=r)}for(const n of s)for(let r=0;r<t;r++)n[r]/=i}return s}function gt(s,e){return typeof s[0]=="number"&&(s=[s]),pt(s),{duration:e,length:s[0].length,sampleRate:s[0].length/e,numberOfChannels:s.length,getChannelData:t=>s==null?void 0:s[t],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}const We={decode:mt,createBuffer:gt};var je=function(s,e,t,i){function n(r){return r instanceof t?r:new t(function(o){o(r)})}return new(t||(t=Promise))(function(r,o){function a(l){try{d(i.next(l))}catch(c){o(c)}}function u(l){try{d(i.throw(l))}catch(c){o(c)}}function d(l){l.done?r(l.value):n(l.value).then(a,u)}d((i=i.apply(s,e||[])).next())})};function vt(s,e,t){var i,n;return je(this,void 0,void 0,function*(){const r=yield fetch(s,t);{const o=(i=r.clone().body)===null||i===void 0?void 0:i.getReader(),a=Number((n=r.headers)===null||n===void 0?void 0:n.get("Content-Length"));let u=0;const d=(l,c)=>je(this,void 0,void 0,function*(){if(l)return;u+=(c==null?void 0:c.length)||0;const w=Math.round(u/a*100);return e(w),o==null?void 0:o.read().then(({done:f,value:p})=>d(f,p))});o==null||o.read().then(({done:l,value:c})=>d(l,c))}return r.blob()})}const _t={fetchBlob:vt};class Pe{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i!=null&&i.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class bt extends Pe{constructor(e){super(),this.isExternalMedia=!1,e.media?(this.media=e.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),e.mediaControls&&(this.media.controls=!0),e.autoplay&&(this.media.autoplay=!0),e.playbackRate!=null&&this.onceMediaEvent("canplay",()=>{e.playbackRate!=null&&(this.media.playbackRate=e.playbackRate)})}onMediaEvent(e,t,i){return this.media.addEventListener(e,t,i),()=>this.media.removeEventListener(e,t)}onceMediaEvent(e,t){return this.onMediaEvent(e,t,{once:!0})}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const e=this.getSrc();e.startsWith("blob:")&&URL.revokeObjectURL(e)}setSrc(e,t){if(this.getSrc()===e)return;this.revokeSrc();const n=t instanceof Blob?URL.createObjectURL(t):e;this.media.src=n,this.media.load()}destroy(){this.media.pause(),!this.isExternalMedia&&(this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(e){this.media=e}play(){return this.media.play()}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(e){this.media.currentTime=e}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(e){this.media.volume=e}getMuted(){return this.media.muted}setMuted(e){this.media.muted=e}getPlaybackRate(){return this.media.playbackRate}setPlaybackRate(e,t){t!=null&&(this.media.preservesPitch=t),this.media.playbackRate=e}getMediaElement(){return this.media}setSinkId(e){return this.media.setSinkId(e)}}function wt(s,e,t,i,n=5){let r=()=>{};if(!s)return r;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),s.style.touchAction="none";let u=a.clientX,d=a.clientY,l=!1;const c=p=>{p.preventDefault(),p.stopPropagation();const C=p.clientX,W=p.clientY;if(l||Math.abs(C-u)>=n||Math.abs(W-d)>=n){const{left:S,top:m}=s.getBoundingClientRect();l||(l=!0,t==null||t(u-S,d-m)),e(C-u,W-d,C-S,W-m),u=C,d=W}},w=p=>{l&&(p.preventDefault(),p.stopPropagation())},f=()=>{s.style.touchAction="",l&&(i==null||i()),r()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",f),document.addEventListener("pointerleave",f),document.addEventListener("click",w,!0),r=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",f),document.removeEventListener("pointerleave",f),setTimeout(()=>{document.removeEventListener("click",w,!0)},10)}};return s.addEventListener("pointerdown",o),()=>{r(),s.removeEventListener("pointerdown",o)}}class Ae extends Pe{constructor(e,t){super(),this.timeouts=[],this.isScrolling=!1,this.audioData=null,this.resizeObserver=null,this.isDragging=!1,this.options=e;const i=this.parentFromOptionsContainer(e.container);this.parent=i;const[n,r]=this.initHtml();i.appendChild(n),this.container=n,this.scrollContainer=r.querySelector(".scroll"),this.wrapper=r.querySelector(".wrapper"),this.canvasWrapper=r.querySelector(".canvases"),this.progressWrapper=r.querySelector(".progress"),this.cursor=r.querySelector(".cursor"),t&&r.appendChild(t),this.initEvents()}parentFromOptionsContainer(e){let t;if(typeof e=="string"?t=document.querySelector(e):e instanceof HTMLElement&&(t=e),!t)throw new Error("Container not found");return t}initEvents(){const e=i=>{const n=this.wrapper.getBoundingClientRect(),r=i.clientX-n.left,o=i.clientX-n.left,a=r/n.width,u=o/n.height;return[a,u]};this.wrapper.addEventListener("click",i=>{const[n,r]=e(i);this.emit("click",n,r)}),this.wrapper.addEventListener("dblclick",i=>{const[n,r]=e(i);this.emit("dblclick",n,r)}),this.options.dragToSeek&&this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:i,scrollWidth:n,clientWidth:r}=this.scrollContainer,o=i/n,a=(i+r)/n;this.emit("scroll",o,a)});const t=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{t(()=>this.reRender())}),this.resizeObserver.observe(this.scrollContainer)}initDrag(){wt(this.wrapper,(e,t,i)=>{this.emit("drag",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))},()=>this.isDragging=!0,()=>this.isDragging=!1)}getHeight(){return this.options.height==null?128:isNaN(Number(this.options.height))?this.options.height==="auto"&&this.parent.clientHeight||128:Number(this.options.height)}initHtml(){const e=document.createElement("div"),t=e.attachShadow({mode:"open"});return t.innerHTML=`
      <style>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight()}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[e,t]}setOptions(e){if(this.options.container!==e.container){const t=this.parentFromOptionsContainer(e.container);t.appendChild(this.container),this.parent=t}e.dragToSeek&&!this.options.dragToSeek&&this.initDrag(),this.options=e,this.reRender()}getWrapper(){return this.wrapper}getScroll(){return this.scrollContainer.scrollLeft}destroy(){var e;this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect()}createDelay(e=10){const t={};return this.timeouts.push(t),i=>{t.timeout&&clearTimeout(t.timeout),t.timeout=setTimeout(i,e)}}convertColorValues(e){if(!Array.isArray(e))return e||"";if(e.length<2)return e[0]||"";const t=document.createElement("canvas"),n=t.getContext("2d").createLinearGradient(0,0,0,t.height),r=1/(e.length-1);return e.forEach((o,a)=>{const u=a*r;n.addColorStop(u,o)}),n}renderBarWaveform(e,t,i,n){const r=e[0],o=e[1]||e[0],a=r.length,{width:u,height:d}=i.canvas,l=d/2,c=window.devicePixelRatio||1,w=t.barWidth?t.barWidth*c:1,f=t.barGap?t.barGap*c:t.barWidth?w/2:0,p=t.barRadius||0,C=u/(w+f)/a,W=p&&"roundRect"in i?"roundRect":"rect";i.beginPath();let S=0,m=0,R=0;for(let v=0;v<=a;v++){const k=Math.round(v*C);if(k>S){const N=Math.round(m*l*n),b=Math.round(R*l*n),M=N+b||1;let D=l-N;t.barAlign==="top"?D=0:t.barAlign==="bottom"&&(D=d-M),i[W](S*(w+f),D,w,M,p),S=k,m=0,R=0}const P=Math.abs(r[v]||0),A=Math.abs(o[v]||0);P>m&&(m=P),A>R&&(R=A)}i.fill(),i.closePath()}renderLineWaveform(e,t,i,n){const r=o=>{const a=e[o]||e[0],u=a.length,{height:d}=i.canvas,l=d/2,c=i.canvas.width/u;i.moveTo(0,l);let w=0,f=0;for(let p=0;p<=u;p++){const C=Math.round(p*c);if(C>w){const S=Math.round(f*l*n)||1,m=l+S*(o===0?-1:1);i.lineTo(w,m),w=C,f=0}const W=Math.abs(a[p]||0);W>f&&(f=W)}i.lineTo(w,l)};i.beginPath(),r(0),r(1),i.fill(),i.closePath()}renderWaveform(e,t,i){if(i.fillStyle=this.convertColorValues(t.waveColor),t.renderFunction){t.renderFunction(e,i);return}let n=t.barHeight||1;if(t.normalize){const r=Array.from(e[0]).reduce((o,a)=>Math.max(o,Math.abs(a)),0);n=r?1/r:1}if(t.barWidth||t.barGap||t.barAlign){this.renderBarWaveform(e,t,i,n);return}this.renderLineWaveform(e,t,i,n)}renderSingleCanvas(e,t,i,n,r,o,a,u){const d=window.devicePixelRatio||1,l=document.createElement("canvas"),c=e[0].length;l.width=Math.round(i*(o-r)/c),l.height=n*d,l.style.width=`${Math.floor(l.width/d)}px`,l.style.height=`${n}px`,l.style.left=`${Math.floor(r*i/d/c)}px`,a.appendChild(l);const w=l.getContext("2d");if(this.renderWaveform(e.map(f=>f.slice(r,o)),t,w),l.width>0&&l.height>0){const f=l.cloneNode(),p=f.getContext("2d");p.drawImage(l,0,0),p.globalCompositeOperation="source-in",p.fillStyle=this.convertColorValues(t.progressColor),p.fillRect(0,0,l.width,l.height),u.appendChild(f)}}renderChannel(e,t,i){const n=document.createElement("div"),r=this.getHeight();n.style.height=`${r}px`,this.canvasWrapper.style.minHeight=`${r}px`,this.canvasWrapper.appendChild(n);const o=n.cloneNode();this.progressWrapper.appendChild(o);const{scrollLeft:a,scrollWidth:u,clientWidth:d}=this.scrollContainer,l=e[0].length,c=l/u;let w=Math.min(Ae.MAX_CANVAS_WIDTH,d);if(t.barWidth||t.barGap){const k=t.barWidth||.5,P=t.barGap||k/2,A=k+P;w%A!==0&&(w=Math.floor(w/A)*A)}const f=Math.floor(Math.abs(a)*c),p=Math.floor(f+w*c),C=p-f,W=(k,P)=>{this.renderSingleCanvas(e,t,i,r,Math.max(0,k),Math.min(P,l),n,o)},S=this.createDelay(),m=this.createDelay(),R=(k,P)=>{W(k,P),k>0&&S(()=>{R(k-C,P-C)})},v=(k,P)=>{W(k,P),P<l&&m(()=>{v(k+C,P+C)})};R(f,p),p<l&&v(p,p+C)}render(e){this.timeouts.forEach(a=>a.timeout&&clearTimeout(a.timeout)),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.wrapper.style.width="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const t=window.devicePixelRatio||1,i=this.scrollContainer.clientWidth,n=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrolling=n>i;const r=this.options.fillParent&&!this.isScrolling,o=(r?i:n)*t;if(this.wrapper.style.width=r?"100%":`${n}px`,this.scrollContainer.style.overflowX=this.isScrolling?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.options.splitChannels)for(let a=0;a<e.numberOfChannels;a++){const u=Object.assign(Object.assign({},this.options),this.options.splitChannels[a]);this.renderChannel([e.getChannelData(a)],u,o)}else{const a=[e.getChannelData(0)];e.numberOfChannels>1&&a.push(e.getChannelData(1)),this.renderChannel(a,this.options,o)}this.audioData=e,this.emit("render")}reRender(){if(!this.audioData)return;const e=this.progressWrapper.clientWidth;this.render(this.audioData);const t=this.progressWrapper.clientWidth;this.scrollContainer.scrollLeft+=t-e}zoom(e){this.options.minPxPerSec=e,this.reRender()}scrollIntoView(e,t=!1){const{clientWidth:i,scrollLeft:n,scrollWidth:r}=this.scrollContainer,o=r*e,a=i/2,u=t&&this.options.autoCenter&&!this.isDragging?a:i;if(o>n+u||o<n)if(this.options.autoCenter&&!this.isDragging){const d=a/20;o-(n+a)>=d&&o<n+i?this.scrollContainer.scrollLeft+=d:this.scrollContainer.scrollLeft=o-a}else this.isDragging?this.scrollContainer.scrollLeft=o<n?o-10:o-i+10:this.scrollContainer.scrollLeft=o;{const{scrollLeft:d}=this.scrollContainer,l=d/r,c=(d+i)/r;this.emit("scroll",l,c)}}renderProgress(e,t){if(isNaN(e))return;const i=e*100;this.canvasWrapper.style.clipPath=`polygon(${i}% 0, 100% 0, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.marginLeft=Math.round(i)===100?`-${this.options.cursorWidth}px`:"",this.isScrolling&&this.options.autoScroll&&this.scrollIntoView(e,t)}}Ae.MAX_CANVAS_WIDTH=4e3;class yt extends Pe{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}var Oe=function(s,e,t,i){function n(r){return r instanceof t?r:new t(function(o){o(r)})}return new(t||(t=Promise))(function(r,o){function a(l){try{d(i.next(l))}catch(c){o(c)}}function u(l){try{d(i.throw(l))}catch(c){o(c)}}function d(l){l.done?r(l.value):n(l.value).then(a,u)}d((i=i.apply(s,e||[])).next())})};class kt extends Pe{constructor(e=new AudioContext){super(),this.bufferNode=null,this.autoplay=!1,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.audioContext=e,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return Oe(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(e){this.currentSrc=e,fetch(e).then(t=>t.arrayBuffer()).then(t=>this.audioContext.decodeAudioData(t)).then(t=>{this.buffer=t,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play()})}_play(){var e;this.paused&&(this.paused=!1,(e=this.bufferNode)===null||e===void 0||e.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.bufferNode.buffer=this.buffer,this.bufferNode.connect(this.gainNode),this.playedDuration>=this.duration&&(this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,this.playedDuration),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))})}_pause(){var e;this.paused||(this.paused=!0,(e=this.bufferNode)===null||e===void 0||e.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime)}play(){return Oe(this,void 0,void 0,function*(){this._play(),this.emit("play")})}pause(){this._pause(),this.emit("pause")}setSinkId(e){return Oe(this,void 0,void 0,function*(){return this.audioContext.setSinkId(e)})}get playbackRate(){var e,t;return(t=(e=this.bufferNode)===null||e===void 0?void 0:e.playbackRate.value)!==null&&t!==void 0?t:1}set playbackRate(e){this.bufferNode&&(this.bufferNode.playbackRate.value=e)}get currentTime(){return this.paused?this.playedDuration:this.playedDuration+this.audioContext.currentTime-this.playStartTime}set currentTime(e){this.emit("seeking"),this.paused?this.playedDuration=e:(this._pause(),this.playedDuration=e,this._play()),this.emit("timeupdate")}get duration(){var e;return((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}get volume(){return this.gainNode.gain.value}set volume(e){this.gainNode.gain.value=e,this.emit("volumechange")}get muted(){return this._muted}set muted(e){this._muted!==e&&(this._muted=e,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}getGainNode(){return this.gainNode}}var _e=function(s,e,t,i){function n(r){return r instanceof t?r:new t(function(o){o(r)})}return new(t||(t=Promise))(function(r,o){function a(l){try{d(i.next(l))}catch(c){o(c)}}function u(l){try{d(i.throw(l))}catch(c){o(c)}}function d(l){l.done?r(l.value):n(l.value).then(a,u)}d((i=i.apply(s,e||[])).next())})};const Ct={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class ze extends bt{static create(e){return new ze(e)}constructor(e){const t=e.media||(e.backend==="WebAudio"?new kt:void 0);super({media:t,mediaControls:e.mediaControls,autoplay:e.autoplay,playbackRate:e.audioRate}),this.plugins=[],this.decodedData=null,this.subscriptions=[],this.mediaSubscriptions=[],this.options=Object.assign({},Ct,e),this.timer=new yt;const i=t?void 0:this.getMediaElement();this.renderer=new Ae(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const n=this.options.url||this.getSrc();n?this.load(n,this.options.peaks,this.options.duration):this.options.peaks&&this.options.duration&&this.loadPredecoded()}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),!0),this.emit("timeupdate",e),this.emit("audioprocess",e)}))}initPlayerEvents(){this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),this.isPlaying()),this.emit("timeupdate",e)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop()}),this.onMediaEvent("emptied",()=>{this.timer.stop()}),this.onMediaEvent("ended",()=>{this.emit("finish")}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(e,t)=>{this.options.interact&&(this.seekTo(e),this.emit("interaction",e*this.getDuration()),this.emit("click",e,t))}),this.renderer.on("dblclick",(e,t)=>{this.emit("dblclick",e,t)}),this.renderer.on("scroll",(e,t)=>{const i=this.getDuration();this.emit("scroll",e*i,t*i)}),this.renderer.on("render",()=>{this.emit("redraw")}));{let e;this.subscriptions.push(this.renderer.on("drag",t=>{this.options.interact&&(this.renderer.renderProgress(t),clearTimeout(e),e=setTimeout(()=>{this.seekTo(t)},this.isPlaying()?0:200),this.emit("interaction",t*this.getDuration()),this.emit("drag",t))}))}}initPlugins(){var e;!((e=this.options.plugins)===null||e===void 0)&&e.length&&this.options.plugins.forEach(t=>{this.registerPlugin(t)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(e=>e()),this.mediaSubscriptions=[]}setOptions(e){this.options=Object.assign({},this.options,e),this.renderer.setOptions(this.options),e.audioRate&&this.setPlaybackRate(e.audioRate),e.mediaControls!=null&&(this.getMediaElement().controls=e.mediaControls)}registerPlugin(e){return e.init(this),this.plugins.push(e),this.subscriptions.push(e.once("destroy",()=>{this.plugins=this.plugins.filter(t=>t!==e)})),e}getWrapper(){return this.renderer.getWrapper()}getScroll(){return this.renderer.getScroll()}getActivePlugins(){return this.plugins}loadPredecoded(){return _e(this,void 0,void 0,function*(){this.options.peaks&&this.options.duration&&(this.decodedData=We.createBuffer(this.options.peaks,this.options.duration),yield Promise.resolve(),this.renderDecoded())})}renderDecoded(){return _e(this,void 0,void 0,function*(){this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData))})}loadAudio(e,t,i,n){return _e(this,void 0,void 0,function*(){if(this.emit("load",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,!t&&!i){const r=o=>this.emit("loading",o);t=yield _t.fetchBlob(e,r,this.options.fetchParams)}if(this.setSrc(e,t),n=(yield Promise.resolve(n||this.getDuration()))||(yield new Promise(r=>{this.onceMediaEvent("loadedmetadata",()=>r(this.getDuration()))}))||(yield Promise.resolve(0)),i)this.decodedData=We.createBuffer(i,n);else if(t){const r=yield t.arrayBuffer();this.decodedData=yield We.decode(r,this.options.sampleRate)}this.renderDecoded(),this.emit("ready",this.getDuration())})}load(e,t,i){return _e(this,void 0,void 0,function*(){yield this.loadAudio(e,void 0,t,i)})}loadBlob(e,t,i){return _e(this,void 0,void 0,function*(){yield this.loadAudio("blob",e,t,i)})}zoom(e){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(e),this.emit("zoom",e)}getDecodedData(){return this.decodedData}exportPeaks({channels:e=2,maxLength:t=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const n=Math.min(e,this.decodedData.numberOfChannels),r=[];for(let o=0;o<n;o++){const a=this.decodedData.getChannelData(o),u=[],d=Math.round(a.length/t);for(let l=0;l<t;l++){const c=a.slice(l*d,(l+1)*d),w=Math.max(...c);u.push(Math.round(w*i)/i)}r.push(u)}return r}getDuration(){let e=super.getDuration()||0;return(e===0||e===1/0)&&this.decodedData&&(e=this.decodedData.duration),e}toggleInteraction(e){this.options.interact=e}seekTo(e){const t=this.getDuration()*e;this.setTime(t)}playPause(){return _e(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(e){this.setTime(this.getCurrentTime()+e)}empty(){this.load("",[[0]],.001)}setMediaElement(e){this.unsubscribePlayerEvents(),super.setMediaElement(e),this.initPlayerEvents()}destroy(){this.emit("destroy"),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}function Et(s){const e=s.numberOfChannels,t=s.length*e*2+44,i=new ArrayBuffer(t),n=new DataView(i);let r=0;const o=function(a,u,d){for(let l=0;l<d.length;l++)a.setUint8(u+l,d.charCodeAt(l))};o(n,r,"RIFF"),r+=4,n.setUint32(r,t-8,!0),r+=4,o(n,r,"WAVE"),r+=4,o(n,r,"fmt "),r+=4,n.setUint32(r,16,!0),r+=4,n.setUint16(r,1,!0),r+=2,n.setUint16(r,e,!0),r+=2,n.setUint32(r,s.sampleRate,!0),r+=4,n.setUint32(r,s.sampleRate*2*e,!0),r+=4,n.setUint16(r,e*2,!0),r+=2,n.setUint16(r,16,!0),r+=2,o(n,r,"data"),r+=4,n.setUint32(r,s.length*e*2,!0),r+=4;for(let a=0;a<s.length;a++)for(let u=0;u<e;u++){const d=Math.max(-1,Math.min(1,s.getChannelData(u)[a]));n.setInt16(r,d*32767,!0),r+=2}return new Uint8Array(i)}const Lt=async(s,e,t,i)=>{const n=new AudioContext({sampleRate:i||s.sampleRate}),r=s.numberOfChannels,o=i||s.sampleRate;let a=s.length,u=0;e&&t&&(u=Math.round(e*o),a=Math.round(t*o)-u);const d=n.createBuffer(r,a,o);for(let l=0;l<r;l++){const c=s.getChannelData(l),w=d.getChannelData(l);for(let f=0;f<a;f++)w[f]=c[u+f]}return Et(d)},He=(s,e)=>{s&&s.skip(e)},be=(s,e)=>(e||(e=5),s/100*e||5);class Ze{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i==null?void 0:i.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class Mt extends Ze{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}function Le(s,e,t,i,n=5){let r=()=>{};if(!s)return r;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),s.style.touchAction="none";let u=a.clientX,d=a.clientY,l=!1;const c=p=>{p.preventDefault(),p.stopPropagation();const C=p.clientX,W=p.clientY;if(l||Math.abs(C-u)>=n||Math.abs(W-d)>=n){const{left:S,top:m}=s.getBoundingClientRect();l||(l=!0,t==null||t(u-S,d-m)),e(C-u,W-d,C-S,W-m),u=C,d=W}},w=p=>{l&&(p.preventDefault(),p.stopPropagation())},f=()=>{s.style.touchAction="",l&&(i==null||i()),r()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",f),document.addEventListener("pointerleave",f),document.addEventListener("click",w,!0),r=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",f),document.removeEventListener("pointerleave",f),setTimeout(()=>{document.removeEventListener("click",w,!0)},10)}};return s.addEventListener("pointerdown",o),()=>{r(),s.removeEventListener("pointerdown",o)}}class xe extends Ze{constructor(e,t,i=0){var n,r,o,a,u,d,l;super(),this.totalDuration=t,this.numberOfChannels=i,this.minLength=0,this.maxLength=1/0,this.id=e.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(e.start),this.end=this.clampPosition((n=e.end)!==null&&n!==void 0?n:e.start),this.drag=(r=e.drag)===null||r===void 0||r,this.resize=(o=e.resize)===null||o===void 0||o,this.color=(a=e.color)!==null&&a!==void 0?a:"rgba(0, 0, 0, 0.1)",this.minLength=(u=e.minLength)!==null&&u!==void 0?u:this.minLength,this.maxLength=(d=e.maxLength)!==null&&d!==void 0?d:this.maxLength,this.channelIdx=(l=e.channelIdx)!==null&&l!==void 0?l:-1,this.element=this.initElement(),this.setContent(e.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(e){return Math.max(0,Math.min(this.totalDuration,e))}setPart(){const e=this.start===this.end;this.element.setAttribute("part",`${e?"marker":"region"} ${this.id}`)}addResizeHandles(e){const t=document.createElement("div");t.setAttribute("data-resize","left"),t.setAttribute("style",`
        position: absolute;
        z-index: 2;
        width: 6px;
        height: 100%;
        top: 0;
        left: 0;
        border-left: 2px solid rgba(0, 0, 0, 0.5);
        border-radius: 2px 0 0 2px;
        cursor: ew-resize;
        word-break: keep-all;
      `),t.setAttribute("part","region-handle region-handle-left");const i=t.cloneNode();i.setAttribute("data-resize","right"),i.style.left="",i.style.right="0",i.style.borderRight=i.style.borderLeft,i.style.borderLeft="",i.style.borderRadius="0 2px 2px 0",i.setAttribute("part","region-handle region-handle-right"),e.appendChild(t),e.appendChild(i),Le(t,n=>this.onResize(n,"start"),()=>null,()=>this.onEndResizing(),1),Le(i,n=>this.onResize(n,"end"),()=>null,()=>this.onEndResizing(),1)}removeResizeHandles(e){const t=e.querySelector('[data-resize="left"]'),i=e.querySelector('[data-resize="right"]');t&&e.removeChild(t),i&&e.removeChild(i)}initElement(){const e=document.createElement("div"),t=this.start===this.end;let i=0,n=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(n=100/this.numberOfChannels,i=n*this.channelIdx),e.setAttribute("style",`
      position: absolute;
      top: ${i}%;
      height: ${n}%;
      background-color: ${t?"none":this.color};
      border-left: ${t?"2px solid "+this.color:"none"};
      border-radius: 2px;
      box-sizing: border-box;
      transition: background-color 0.2s ease;
      cursor: ${this.drag?"grab":"default"};
      pointer-events: all;
    `),!t&&this.resize&&this.addResizeHandles(e),e}renderPosition(){const e=this.start/this.totalDuration,t=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*e+"%",this.element.style.right=100*t+"%"}initMouseEvents(){const{element:e}=this;e&&(e.addEventListener("click",t=>this.emit("click",t)),e.addEventListener("mouseenter",t=>this.emit("over",t)),e.addEventListener("mouseleave",t=>this.emit("leave",t)),e.addEventListener("dblclick",t=>this.emit("dblclick",t)),Le(e,t=>this.onMove(t),()=>this.onStartMoving(),()=>this.onEndMoving()))}onStartMoving(){this.drag&&(this.element.style.cursor="grabbing")}onEndMoving(){this.drag&&(this.element.style.cursor="grab",this.emit("update-end"))}_onUpdate(e,t){if(!this.element.parentElement)return;const i=e/this.element.parentElement.clientWidth*this.totalDuration,n=t&&t!=="start"?this.start:this.start+i,r=t&&t!=="end"?this.end:this.end+i,o=r-n;n>=0&&r<=this.totalDuration&&n<=r&&o>=this.minLength&&o<=this.maxLength&&(this.start=n,this.end=r,this.renderPosition(),this.emit("update"))}onMove(e){this.drag&&this._onUpdate(e)}onResize(e,t){this.resize&&this._onUpdate(e,t)}onEndResizing(){this.resize&&this.emit("update-end")}_setTotalDuration(e){this.totalDuration=e,this.renderPosition()}play(){this.emit("play")}setContent(e){var t;if((t=this.content)===null||t===void 0||t.remove(),e){if(typeof e=="string"){this.content=document.createElement("div");const i=this.start===this.end;this.content.style.padding=`0.2em ${i?.2:.4}em`,this.content.textContent=e}else this.content=e;this.content.setAttribute("part","region-content"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(e){var t,i;if(e.color&&(this.color=e.color,this.element.style.backgroundColor=this.color),e.drag!==void 0&&(this.drag=e.drag,this.element.style.cursor=this.drag?"grab":"default"),e.start!==void 0||e.end!==void 0){const n=this.start===this.end;this.start=this.clampPosition((t=e.start)!==null&&t!==void 0?t:this.start),this.end=this.clampPosition((i=e.end)!==null&&i!==void 0?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(e.content&&this.setContent(e.content),e.id&&(this.id=e.id,this.setPart()),e.resize!==void 0&&e.resize!==this.resize){const n=this.start===this.end;this.resize=e.resize,this.resize&&!n?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit("remove"),this.element.remove(),this.element=null}}class Ne extends Mt{constructor(e){super(e),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(e){return new Ne(e)}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let e=[];this.subscriptions.push(this.wavesurfer.on("timeupdate",t=>{const i=this.regions.filter(n=>n.start<=t&&n.end>=t);i.forEach(n=>{e.includes(n)||this.emit("region-in",n)}),e.forEach(n=>{i.includes(n)||this.emit("region-out",n)}),e=i}))}initRegionsContainer(){const e=document.createElement("div");return e.setAttribute("style",`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      pointer-events: none;
    `),e}getRegions(){return this.regions}avoidOverlapping(e){if(!e.content)return;const t=e.content,i=t.getBoundingClientRect().left,n=e.element.scrollWidth,r=this.regions.filter(o=>{if(o===e||!o.content)return!1;const a=o.content.getBoundingClientRect().left,u=o.element.scrollWidth;return i<a+u&&a<i+n}).map(o=>{var a;return((a=o.content)===null||a===void 0?void 0:a.getBoundingClientRect().height)||0}).reduce((o,a)=>o+a,0);t.style.marginTop=`${r}px`}saveRegion(e){this.regionsContainer.appendChild(e.element),this.avoidOverlapping(e),this.regions.push(e);const t=[e.on("update-end",()=>{this.avoidOverlapping(e),this.emit("region-updated",e)}),e.on("play",()=>{var i,n;(i=this.wavesurfer)===null||i===void 0||i.play(),(n=this.wavesurfer)===null||n===void 0||n.setTime(e.start)}),e.on("click",i=>{this.emit("region-clicked",e,i)}),e.on("dblclick",i=>{this.emit("region-double-clicked",e,i)}),e.once("remove",()=>{t.forEach(i=>i()),this.regions=this.regions.filter(i=>i!==e)})];this.subscriptions.push(...t),this.emit("region-created",e)}addRegion(e){var t,i;if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const n=this.wavesurfer.getDuration(),r=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getDecodedData())===null||i===void 0?void 0:i.numberOfChannels,o=new xe(e,n,r);return n?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once("ready",a=>{o._setTotalDuration(a),this.saveRegion(o)})),o}enableDragSelection(e){var t,i;const n=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getWrapper())===null||i===void 0?void 0:i.querySelector("div");if(!n)return()=>{};let r=null,o=0;return Le(n,(a,u,d)=>{r&&r._onUpdate(a,d>o?"end":"start")},a=>{var u,d;if(o=a,!this.wavesurfer)return;const l=this.wavesurfer.getDuration(),c=(d=(u=this.wavesurfer)===null||u===void 0?void 0:u.getDecodedData())===null||d===void 0?void 0:d.numberOfChannels,w=this.wavesurfer.getWrapper().clientWidth,f=a/w*l,p=(a+5)/w*l;r=new xe(Object.assign(Object.assign({},e),{start:f,end:p}),l,c),this.regionsContainer.appendChild(r.element)},()=>{r&&(this.saveRegion(r),r=null)})}clearRegions(){this.regions.forEach(e=>e.remove())}destroy(){this.clearRegions(),super.destroy()}}function Dt(s){let e,t;return e=new dt({}),{c(){Y(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,n){J(e,i,n),t=!0},i(i){t||(O(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Q(e,i)}}}function Tt(s){let e,t;return e=new at({}),{c(){Y(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,n){J(e,i,n),t=!0},i(i){t||(O(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Q(e,i)}}}function St(s){let e,t;return e=new ct({}),{c(){Y(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,n){J(e,i,n),t=!0},i(i){t||(O(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Q(e,i)}}}function Rt(s){let e,t,i,n;const r=[St,Tt,Dt],o=[];function a(u,d){return u[0]==0?0:u[0]<.5?1:u[0]>=.5?2:-1}return~(e=a(s))&&(t=o[e]=r[e](s)),{c(){t&&t.c(),i=ke()},l(u){t&&t.l(u),i=ke()},m(u,d){~e&&o[e].m(u,d),X(u,i,d),n=!0},p(u,[d]){let l=e;e=a(u),e!==l&&(t&&(fe(),z(o[l],1,1,()=>{o[l]=null}),me()),~e?(t=o[e],t||(t=o[e]=r[e](u),t.c()),O(t,1),t.m(i.parentNode,i)):t=null)},i(u){n||(O(t),n=!0)},o(u){z(t),n=!1},d(u){u&&y(i),~e&&o[e].d(u)}}}function Pt(s,e,t){let{currentVolume:i}=e;return s.$$set=n=>{"currentVolume"in n&&t(0,i=n.currentVolume)},[i]}class At extends le{constructor(e){super(),ae(this,e,Pt,Rt,ue,{currentVolume:0})}}function Wt(s){let e,t,i;return{c(){e=H("input"),this.h()},l(n){e=x(n,"INPUT",{id:!0,class:!0,type:!0,min:!0,max:!0,step:!0}),this.h()},h(){h(e,"id","volume"),h(e,"class","volume-slider svelte-wuo8j5"),h(e,"type","range"),h(e,"min","0"),h(e,"max","1"),h(e,"step","0.01"),e.value=s[0]},m(n,r){X(n,e,r),s[4](e),t||(i=[Z(e,"focusout",s[5]),Z(e,"input",s[6])],t=!0)},p(n,[r]){r&1&&(e.value=n[0])},i:I,o:I,d(n){n&&y(e),s[4](null),t=!1,Re(i)}}}function Ot(s,e,t){let{currentVolume:i=1}=e,{show_volume_slider:n=!1}=e,{waveform:r}=e,o;Xe(()=>{a()});const a=()=>{let c=o;c&&(c.style.background=`linear-gradient(to right, var(--color-accent) ${i*100}%, var(--neutral-400) ${i*100}%)`)};function u(c){oe[c?"unshift":"push"](()=>{o=c,t(3,o)})}const d=()=>t(1,n=!1),l=c=>{c.target instanceof HTMLInputElement&&(t(0,i=parseFloat(c.target.value)),r==null||r.setVolume(i))};return s.$$set=c=>{"currentVolume"in c&&t(0,i=c.currentVolume),"show_volume_slider"in c&&t(1,n=c.show_volume_slider),"waveform"in c&&t(2,r=c.waveform)},s.$$.update=()=>{s.$$.dirty&1&&a()},[i,n,r,o,u,d,l]}class zt extends le{constructor(e){super(),ae(this,e,Ot,Wt,ue,{currentVolume:0,show_volume_slider:1,waveform:2})}}function Ie(s){let e,t,i,n;function r(u){s[27](u)}function o(u){s[28](u)}let a={waveform:s[2]};return s[12]!==void 0&&(a.currentVolume=s[12]),s[1]!==void 0&&(a.show_volume_slider=s[1]),e=new zt({props:a}),oe.push(()=>Ce(e,"currentVolume",r)),oe.push(()=>Ce(e,"show_volume_slider",o)),{c(){Y(e.$$.fragment)},l(u){K(e.$$.fragment,u)},m(u,d){J(e,u,d),n=!0},p(u,d){const l={};d[0]&4&&(l.waveform=u[2]),!t&&d[0]&4096&&(t=!0,l.currentVolume=u[12],Ee(()=>t=!1)),!i&&d[0]&2&&(i=!0,l.show_volume_slider=u[1],Ee(()=>i=!1)),e.$set(l)},i(u){n||(O(e.$$.fragment,u),n=!0)},o(u){z(e.$$.fragment,u),n=!1},d(u){Q(e,u)}}}function Nt(s){let e,t;return e=new et({}),{c(){Y(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,n){J(e,i,n),t=!0},i(i){t||(O(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Q(e,i)}}}function Vt(s){let e,t;return e=new Qe({}),{c(){Y(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,n){J(e,i,n),t=!0},i(i){t||(O(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Q(e,i)}}}function Ue(s){let e,t,i,n,r,o=s[6]&&s[0]===""&&Fe(s);const a=[jt,Bt],u=[];function d(l,c){return l[0]===""?0:1}return t=d(s),i=u[t]=a[t](s),{c(){o&&o.c(),e=ee(),i.c(),n=ke()},l(l){o&&o.l(l),e=te(l),i.l(l),n=ke()},m(l,c){o&&o.m(l,c),X(l,e,c),u[t].m(l,c),X(l,n,c),r=!0},p(l,c){l[6]&&l[0]===""?o?(o.p(l,c),c[0]&65&&O(o,1)):(o=Fe(l),o.c(),O(o,1),o.m(e.parentNode,e)):o&&(fe(),z(o,1,1,()=>{o=null}),me());let w=t;t=d(l),t===w?u[t].p(l,c):(fe(),z(u[w],1,1,()=>{u[w]=null}),me(),i=u[t],i?i.p(l,c):(i=u[t]=a[t](l),i.c()),O(i,1),i.m(n.parentNode,n))},i(l){r||(O(o),O(i),r=!0)},o(l){z(o),z(i),r=!1},d(l){l&&(y(e),y(n)),o&&o.d(l),u[t].d(l)}}}function Fe(s){let e,t,i,n,r;return t=new tt({}),{c(){e=H("button"),Y(t.$$.fragment),this.h()},l(o){e=x(o,"BUTTON",{class:!0,"aria-label":!0});var a=T(e);K(t.$$.fragment,a),a.forEach(y),this.h()},h(){h(e,"class","action icon svelte-ije4bl"),h(e,"aria-label","Reset audio")},m(o,a){X(o,e,a),J(t,e,null),i=!0,n||(r=Z(e,"click",s[33]),n=!0)},p:I,i(o){i||(O(t.$$.fragment,o),i=!0)},o(o){z(t.$$.fragment,o),i=!1},d(o){o&&y(e),Q(t),n=!1,r()}}}function Bt(s){let e,t="Trim",i,n,r="Cancel",o,a;return{c(){e=H("button"),e.textContent=t,i=ee(),n=H("button"),n.textContent=r,this.h()},l(u){e=x(u,"BUTTON",{class:!0,"data-svelte-h":!0}),Te(e)!=="svelte-1brf00d"&&(e.textContent=t),i=te(u),n=x(u,"BUTTON",{class:!0,"data-svelte-h":!0}),Te(n)!=="svelte-1r0ma01"&&(n.textContent=r),this.h()},h(){h(e,"class","text-button svelte-ije4bl"),h(n,"class","text-button svelte-ije4bl")},m(u,d){X(u,e,d),X(u,i,d),X(u,n,d),o||(a=[Z(e,"click",s[14]),Z(n,"click",s[16])],o=!0)},p:I,i:I,o:I,d(u){u&&(y(e),y(i),y(n)),o=!1,Re(a)}}}function jt(s){let e,t,i,n,r;return t=new $e({}),{c(){e=H("button"),Y(t.$$.fragment),this.h()},l(o){e=x(o,"BUTTON",{class:!0,"aria-label":!0});var a=T(e);K(t.$$.fragment,a),a.forEach(y),this.h()},h(){h(e,"class","action icon svelte-ije4bl"),h(e,"aria-label","Trim audio to selection")},m(o,a){X(o,e,a),J(t,e,null),i=!0,n||(r=Z(e,"click",s[16]),n=!0)},p:I,i(o){i||(O(t.$$.fragment,o),i=!0)},o(o){z(t.$$.fragment,o),i=!1},d(o){o&&y(e),Q(t),n=!1,r()}}}function Ht(s){let e,t,i,n,r,o,a,u,d,l,c,w,f,p,C,W,S,m,R,v,k,P,A,N,b,M,D,U,ie,he;n=new At({props:{currentVolume:s[12]}});let B=s[1]&&Ie(s);C=new rt({});const pe=[Vt,Nt],ne=[];function ge(L,V){return L[5]?0:1}R=ge(s),v=ne[R]=pe[R](s),N=new ot({});let j=s[10]&&s[7]&&Ue(s);return{c(){e=H("div"),t=H("div"),i=H("button"),Y(n.$$.fragment),r=ee(),B&&B.c(),o=ee(),a=H("button"),u=H("span"),d=we(s[11]),l=we("x"),w=ee(),f=H("div"),p=H("button"),Y(C.$$.fragment),S=ee(),m=H("button"),v.c(),P=ee(),A=H("button"),Y(N.$$.fragment),M=ee(),D=H("div"),j&&j.c(),this.h()},l(L){e=x(L,"DIV",{class:!0,"data-testid":!0});var V=T(e);t=x(V,"DIV",{class:!0});var $=T(t);i=x($,"BUTTON",{class:!0,"aria-label":!0});var _=T(i);K(n.$$.fragment,_),_.forEach(y),r=te($),B&&B.l($),o=te($),a=x($,"BUTTON",{class:!0,"aria-label":!0});var re=T(a);u=x(re,"SPAN",{});var se=T(u);d=ye(se,s[11]),l=ye(se,"x"),se.forEach(y),re.forEach(y),$.forEach(y),w=te(V),f=x(V,"DIV",{class:!0});var g=T(f);p=x(g,"BUTTON",{class:!0,"aria-label":!0});var G=T(p);K(C.$$.fragment,G),G.forEach(y),S=te(g),m=x(g,"BUTTON",{class:!0,"aria-label":!0});var ce=T(m);v.l(ce),ce.forEach(y),P=te(g),A=x(g,"BUTTON",{class:!0,"aria-label":!0});var de=T(A);K(N.$$.fragment,de),de.forEach(y),g.forEach(y),M=te(V),D=x(V,"DIV",{class:!0});var Ve=T(D);j&&j.l(Ve),Ve.forEach(y),V.forEach(y),this.h()},h(){h(i,"class","action icon volume svelte-ije4bl"),h(i,"aria-label","Adjust volume"),Me(i,"color",s[1]?"var(--color-accent)":"var(--neutral-400)"),h(a,"class","playback icon svelte-ije4bl"),h(a,"aria-label",c=`Adjust playback speed to ${s[13][(s[13].indexOf(s[11])+1)%s[13].length]}x`),De(a,"hidden",s[1]),h(t,"class","control-wrapper svelte-ije4bl"),h(p,"class","rewind icon svelte-ije4bl"),h(p,"aria-label",W=`Skip backwards by ${be(s[3],s[9].skip_length)} seconds`),h(m,"class","play-pause-button icon svelte-ije4bl"),h(m,"aria-label",k=s[5]?s[4]("audio.pause"):s[4]("audio.play")),h(A,"class","skip icon svelte-ije4bl"),h(A,"aria-label",b="Skip forward by "+be(s[3],s[9].skip_length)+" seconds"),h(f,"class","play-pause-wrapper svelte-ije4bl"),h(D,"class","settings-wrapper svelte-ije4bl"),h(e,"class","controls svelte-ije4bl"),h(e,"data-testid","waveform-controls")},m(L,V){X(L,e,V),E(e,t),E(t,i),J(n,i,null),E(t,r),B&&B.m(t,null),E(t,o),E(t,a),E(a,u),E(u,d),E(u,l),E(e,w),E(e,f),E(f,p),J(C,p,null),E(f,S),E(f,m),ne[R].m(m,null),E(f,P),E(f,A),J(N,A,null),E(e,M),E(e,D),j&&j.m(D,null),U=!0,ie||(he=[Z(i,"click",s[26]),Z(a,"click",s[29]),Z(p,"click",s[30]),Z(m,"click",s[31]),Z(A,"click",s[32])],ie=!0)},p(L,V){const $={};V[0]&4096&&($.currentVolume=L[12]),n.$set($),V[0]&2&&Me(i,"color",L[1]?"var(--color-accent)":"var(--neutral-400)"),L[1]?B?(B.p(L,V),V[0]&2&&O(B,1)):(B=Ie(L),B.c(),O(B,1),B.m(t,o)):B&&(fe(),z(B,1,1,()=>{B=null}),me()),(!U||V[0]&2048)&&Ge(d,L[11]),(!U||V[0]&2048&&c!==(c=`Adjust playback speed to ${L[13][(L[13].indexOf(L[11])+1)%L[13].length]}x`))&&h(a,"aria-label",c),(!U||V[0]&2)&&De(a,"hidden",L[1]),(!U||V[0]&520&&W!==(W=`Skip backwards by ${be(L[3],L[9].skip_length)} seconds`))&&h(p,"aria-label",W);let _=R;R=ge(L),R!==_&&(fe(),z(ne[_],1,1,()=>{ne[_]=null}),me(),v=ne[R],v||(v=ne[R]=pe[R](L),v.c()),O(v,1),v.m(m,null)),(!U||V[0]&48&&k!==(k=L[5]?L[4]("audio.pause"):L[4]("audio.play")))&&h(m,"aria-label",k),(!U||V[0]&520&&b!==(b="Skip forward by "+be(L[3],L[9].skip_length)+" seconds"))&&h(A,"aria-label",b),L[10]&&L[7]?j?(j.p(L,V),V[0]&1152&&O(j,1)):(j=Ue(L),j.c(),O(j,1),j.m(D,null)):j&&(fe(),z(j,1,1,()=>{j=null}),me())},i(L){U||(O(n.$$.fragment,L),O(B),O(C.$$.fragment,L),O(v),O(N.$$.fragment,L),O(j),U=!0)},o(L){z(n.$$.fragment,L),z(B),z(C.$$.fragment,L),z(v),z(N.$$.fragment,L),z(j),U=!1},d(L){L&&y(e),Q(n),B&&B.d(),Q(C),ne[R].d(),Q(N),j&&j.d(),ie=!1,Re(he)}}}function xt(s,e,t){let{waveform:i}=e,{audio_duration:n}=e,{i18n:r}=e,{playing:o}=e,{show_redo:a=!1}=e,{interactive:u=!1}=e,{handle_trim_audio:d}=e,{mode:l=""}=e,{container:c}=e,{handle_reset_value:w}=e,{waveform_options:f={}}=e,{trim_region_settings:p={}}=e,{show_volume_slider:C=!1}=e,{editable:W=!0}=e,{trimDuration:S=0}=e,m=[.5,1,1.5,2],R=m[1],v=null,k=null,P,A,N="",b=1;const M=()=>{v&&(t(22,k=v==null?void 0:v.addRegion({start:n/4,end:n/2,...p})),t(17,S=k.end-k.start))},D=()=>{if(i&&v&&k){const _=k.start,re=k.end;d(_,re),t(0,l=""),t(22,k=null)}},U=()=>{v==null||v.getRegions().forEach(_=>{_.remove()}),v==null||v.clearRegions()},ie=()=>{U(),l==="edit"?t(0,l=""):(t(0,l="edit"),M())},he=(_,re)=>{let se,g;k&&(_==="left"?re==="ArrowLeft"?(se=k.start-.05,g=k.end):(se=k.start+.05,g=k.end):re==="ArrowLeft"?(se=k.start,g=k.end-.05):(se=k.start,g=k.end+.05),k.setOptions({start:se,end:g}),t(17,S=k.end-k.start))},B=()=>t(1,C=!C);function pe(_){b=_,t(12,b)}function ne(_){C=_,t(1,C)}const ge=()=>{t(11,R=m[(m.indexOf(R)+1)%m.length]),i==null||i.setPlaybackRate(R)},j=()=>i==null?void 0:i.skip(be(n,f.skip_length)*-1),L=()=>i==null?void 0:i.playPause(),V=()=>i==null?void 0:i.skip(be(n,f.skip_length)),$=()=>{w(),U(),t(0,l="")};return s.$$set=_=>{"waveform"in _&&t(2,i=_.waveform),"audio_duration"in _&&t(3,n=_.audio_duration),"i18n"in _&&t(4,r=_.i18n),"playing"in _&&t(5,o=_.playing),"show_redo"in _&&t(6,a=_.show_redo),"interactive"in _&&t(7,u=_.interactive),"handle_trim_audio"in _&&t(18,d=_.handle_trim_audio),"mode"in _&&t(0,l=_.mode),"container"in _&&t(19,c=_.container),"handle_reset_value"in _&&t(8,w=_.handle_reset_value),"waveform_options"in _&&t(9,f=_.waveform_options),"trim_region_settings"in _&&t(20,p=_.trim_region_settings),"show_volume_slider"in _&&t(1,C=_.show_volume_slider),"editable"in _&&t(10,W=_.editable),"trimDuration"in _&&t(17,S=_.trimDuration)},s.$$.update=()=>{if(s.$$.dirty[0]&524292&&t(21,v=c&&i?i.registerPlugin(Ne.create()):null),s.$$.dirty[0]&2097152&&(v==null||v.on("region-out",_=>{_.play()})),s.$$.dirty[0]&2097152&&(v==null||v.on("region-updated",_=>{t(17,S=_.end-_.start)})),s.$$.dirty[0]&2097152&&(v==null||v.on("region-clicked",(_,re)=>{re.stopPropagation(),t(22,k=_),_.play()})),s.$$.dirty[0]&31981568&&k){const _=c.children[0].shadowRoot;t(24,A=_.querySelector('[data-resize="right"]')),t(23,P=_.querySelector('[data-resize="left"]')),P&&A&&(P.setAttribute("role","button"),A.setAttribute("role","button"),P==null||P.setAttribute("aria-label","Drag to adjust start time"),A==null||A.setAttribute("aria-label","Drag to adjust end time"),P==null||P.setAttribute("tabindex","0"),A==null||A.setAttribute("tabindex","0"),P.addEventListener("focus",()=>{v&&t(25,N="left")}),A.addEventListener("focus",()=>{v&&t(25,N="right")}))}s.$$.dirty[0]&35651584&&v&&window.addEventListener("keydown",_=>{_.key==="ArrowLeft"?he(N,"ArrowLeft"):_.key==="ArrowRight"&&he(N,"ArrowRight")})},[l,C,i,n,r,o,a,u,w,f,W,R,b,m,D,U,ie,S,d,c,p,v,k,P,A,N,B,pe,ne,ge,j,L,V,$]}class It extends le{constructor(e){super(),ae(this,e,xt,Ht,ue,{waveform:2,audio_duration:3,i18n:4,playing:5,show_redo:6,interactive:7,handle_trim_audio:18,mode:0,container:19,handle_reset_value:8,waveform_options:9,trim_region_settings:20,show_volume_slider:1,editable:10,trimDuration:17},null,[-1,-1])}}function Ut(s){let e,t,i,n,r,o,a="0:00",u,d,l,c,w="0:00",f,p,C,W,S,m,R,v=s[0]==="edit"&&s[18]>0&&qe(s);function k(b){s[32](b)}function P(b){s[33](b)}function A(b){s[34](b)}let N={container:s[10],waveform:s[11],playing:s[16],audio_duration:s[17],i18n:s[3],interactive:s[4],handle_trim_audio:s[21],show_redo:s[4],handle_reset_value:s[9],waveform_options:s[8],trim_region_settings:s[6],editable:s[5]};return s[0]!==void 0&&(N.mode=s[0]),s[18]!==void 0&&(N.trimDuration=s[18]),s[19]!==void 0&&(N.show_volume_slider=s[19]),p=new It({props:N}),oe.push(()=>Ce(p,"mode",k)),oe.push(()=>Ce(p,"trimDuration",P)),oe.push(()=>Ce(p,"show_volume_slider",A)),{c(){e=H("div"),t=H("div"),i=H("div"),n=ee(),r=H("div"),o=H("time"),o.textContent=a,u=ee(),d=H("div"),v&&v.c(),l=ee(),c=H("time"),c.textContent=w,f=ee(),Y(p.$$.fragment),this.h()},l(b){e=x(b,"DIV",{class:!0,"data-testid":!0});var M=T(e);t=x(M,"DIV",{class:!0});var D=T(t);i=x(D,"DIV",{id:!0,class:!0}),T(i).forEach(y),D.forEach(y),n=te(M),r=x(M,"DIV",{class:!0});var U=T(r);o=x(U,"TIME",{id:!0,class:!0,"data-svelte-h":!0}),Te(o)!=="svelte-lp3mlp"&&(o.textContent=a),u=te(U),d=x(U,"DIV",{});var ie=T(d);v&&v.l(ie),l=te(ie),c=x(ie,"TIME",{id:!0,class:!0,"data-svelte-h":!0}),Te(c)!=="svelte-1jd0owv"&&(c.textContent=w),ie.forEach(y),U.forEach(y),f=te(M),K(p.$$.fragment,M),M.forEach(y),this.h()},h(){h(i,"id","waveform"),h(i,"class","svelte-19usgod"),Me(i,"height",s[10]?null:"58px"),h(t,"class","waveform-container svelte-19usgod"),h(o,"id","time"),h(o,"class","svelte-19usgod"),h(c,"id","duration"),h(c,"class","svelte-19usgod"),h(r,"class","timestamps svelte-19usgod"),h(e,"class","component-wrapper svelte-19usgod"),h(e,"data-testid",m=s[2]?"waveform-"+s[2]:"unlabelled-audio")},m(b,M){X(b,e,M),E(e,t),E(t,i),s[29](i),E(e,n),E(e,r),E(r,o),s[30](o),E(r,u),E(r,d),v&&v.m(d,null),E(d,l),E(d,c),s[31](c),E(e,f),J(p,e,null),R=!0},p(b,M){M[0]&1024&&Me(i,"height",b[10]?null:"58px"),b[0]==="edit"&&b[18]>0?v?v.p(b,M):(v=qe(b),v.c(),v.m(d,l)):v&&(v.d(1),v=null);const D={};M[0]&1024&&(D.container=b[10]),M[0]&2048&&(D.waveform=b[11]),M[0]&65536&&(D.playing=b[16]),M[0]&131072&&(D.audio_duration=b[17]),M[0]&8&&(D.i18n=b[3]),M[0]&16&&(D.interactive=b[4]),M[0]&16&&(D.show_redo=b[4]),M[0]&512&&(D.handle_reset_value=b[9]),M[0]&256&&(D.waveform_options=b[8]),M[0]&64&&(D.trim_region_settings=b[6]),M[0]&32&&(D.editable=b[5]),!C&&M[0]&1&&(C=!0,D.mode=b[0],Ee(()=>C=!1)),!W&&M[0]&262144&&(W=!0,D.trimDuration=b[18],Ee(()=>W=!1)),!S&&M[0]&524288&&(S=!0,D.show_volume_slider=b[19],Ee(()=>S=!1)),p.$set(D),(!R||M[0]&4&&m!==(m=b[2]?"waveform-"+b[2]:"unlabelled-audio"))&&h(e,"data-testid",m)},i(b){R||(O(p.$$.fragment,b),R=!0)},o(b){z(p.$$.fragment,b),R=!1},d(b){b&&y(e),s[29](null),s[30](null),v&&v.d(),s[31](null),Q(p)}}}function Ft(s){let e,t;return e=new it({props:{size:"small",$$slots:{default:[qt]},$$scope:{ctx:s}}}),{c(){Y(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,n){J(e,i,n),t=!0},p(i,n){const r={};n[1]&256&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){t||(O(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Q(e,i)}}}function qe(s){let e,t=Se(s[18])+"",i;return{c(){e=H("time"),i=we(t),this.h()},l(n){e=x(n,"TIME",{id:!0,class:!0});var r=T(e);i=ye(r,t),r.forEach(y),this.h()},h(){h(e,"id","trim-duration"),h(e,"class","svelte-19usgod")},m(n,r){X(n,e,r),E(e,i)},p(n,r){r[0]&262144&&t!==(t=Se(n[18])+"")&&Ge(i,t)},d(n){n&&y(e)}}}function qt(s){let e,t;return e=new Je({}),{c(){Y(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,n){J(e,i,n),t=!0},i(i){t||(O(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Q(e,i)}}}function Xt(s){let e,t,i,n,r,o,a,u,d;const l=[Ft,Ut],c=[];function w(f,p){return f[1]===null?0:f[15]?1:-1}return~(n=w(s))&&(r=c[n]=l[n](s)),{c(){e=H("audio"),i=ee(),r&&r.c(),o=ke(),this.h()},l(f){e=x(f,"AUDIO",{class:!0}),T(e).forEach(y),i=te(f),r&&r.l(f),o=ke(),this.h()},h(){h(e,"class","standard-player svelte-19usgod"),e.controls=!0,e.autoplay=t=s[7].autoplay,De(e,"hidden",s[15])},m(f,p){X(f,e,p),s[26](e),X(f,i,p),~n&&c[n].m(f,p),X(f,o,p),a=!0,u||(d=[Z(e,"load",s[25]),Z(e,"ended",s[27]),Z(e,"play",s[28])],u=!0)},p(f,p){(!a||p[0]&128&&t!==(t=f[7].autoplay))&&(e.autoplay=t),(!a||p[0]&32768)&&De(e,"hidden",f[15]);let C=n;n=w(f),n===C?~n&&c[n].p(f,p):(r&&(fe(),z(c[C],1,1,()=>{c[C]=null}),me()),~n?(r=c[n],r?r.p(f,p):(r=c[n]=l[n](f),r.c()),O(r,1),r.m(o.parentNode,o)):r=null)},i(f){a||(O(r),a=!0)},o(f){z(r),a=!1},d(f){f&&(y(e),y(i),y(o)),s[26](null),~n&&c[n].d(f),u=!1,Re(d)}}}function Gt(s,e,t){let i,n,{value:r=null}=e,{label:o}=e,{i18n:a}=e,{dispatch_blob:u=()=>Promise.resolve()}=e,{interactive:d=!1}=e,{editable:l=!0}=e,{trim_region_settings:c={}}=e,{waveform_settings:w}=e,{waveform_options:f}=e,{mode:p=""}=e,{loop:C}=e,{handle_reset_value:W=()=>{}}=e,S,m,R=!1,v,k,P,A=0,N=!1,b,M=!1;const D=Ye(),U=()=>{t(11,m=ze.create({container:S,...w})),Be(r==null?void 0:r.url).then(g=>{if(g&&m)return m.load(g)})},ie=async(g,G)=>{t(0,p="");const ce=m==null?void 0:m.getDecodedData();ce&&await Lt(ce,g,G,w.sampleRate).then(async de=>{await u([de],"change"),m==null||m.destroy(),t(10,S.innerHTML="",S)}),D("edit")};async function he(g){M=!1,await Be(g).then(G=>{!G||r!=null&&r.is_stream||(f.show_recording_waveform?m==null||m.load(G):b&&t(14,b.src=G,b))})}function B(g){if(!(!g||!g.is_stream||!g.url))if(ve.isSupported()&&!M){const G=new ve({maxBufferLength:1,maxMaxBufferLength:1,lowLatencyMode:!0});G.loadSource(g.url),G.attachMedia(b),G.on(ve.Events.MANIFEST_PARSED,function(){w.autoplay&&b.play()}),G.on(ve.Events.ERROR,function(ce,de){if(console.error("HLS error:",ce,de),de.fatal)switch(de.type){case ve.ErrorTypes.NETWORK_ERROR:console.error("Fatal network error encountered, trying to recover"),G.startLoad();break;case ve.ErrorTypes.MEDIA_ERROR:console.error("Fatal media error encountered, trying to recover"),G.recoverMediaError();break;default:console.error("Fatal error, cannot recover"),G.destroy();break}}),M=!0}else M||(t(14,b.src=g.url,b),w.autoplay&&b.play(),M=!0)}Xe(()=>{window.addEventListener("keydown",g=>{!m||N||(g.key==="ArrowRight"&&p!=="edit"?He(m,.1):g.key==="ArrowLeft"&&p!=="edit"&&He(m,-.1))})});function pe(g){Ke.call(this,s,g)}function ne(g){oe[g?"unshift":"push"](()=>{b=g,t(14,b)})}const ge=()=>D("stop"),j=()=>D("play");function L(g){oe[g?"unshift":"push"](()=>{S=g,t(10,S),t(15,n),t(11,m),t(8,f),t(1,r)})}function V(g){oe[g?"unshift":"push"](()=>{v=g,t(12,v),t(11,m)})}function $(g){oe[g?"unshift":"push"](()=>{k=g,t(13,k),t(11,m)})}function _(g){p=g,t(0,p)}function re(g){A=g,t(18,A)}function se(g){N=g,t(19,N)}return s.$$set=g=>{"value"in g&&t(1,r=g.value),"label"in g&&t(2,o=g.label),"i18n"in g&&t(3,a=g.i18n),"dispatch_blob"in g&&t(22,u=g.dispatch_blob),"interactive"in g&&t(4,d=g.interactive),"editable"in g&&t(5,l=g.editable),"trim_region_settings"in g&&t(6,c=g.trim_region_settings),"waveform_settings"in g&&t(7,w=g.waveform_settings),"waveform_options"in g&&t(8,f=g.waveform_options),"mode"in g&&t(0,p=g.mode),"loop"in g&&t(23,C=g.loop),"handle_reset_value"in g&&t(9,W=g.handle_reset_value)},s.$$.update=()=>{s.$$.dirty[0]&2&&t(24,i=r==null?void 0:r.url),s.$$.dirty[0]&258&&t(15,n=f.show_recording_waveform&&!(r!=null&&r.is_stream)),s.$$.dirty[0]&35840&&n&&S!==void 0&&S!==null&&(m!==void 0&&m.destroy(),t(10,S.innerHTML="",S),U(),t(16,R=!1)),s.$$.dirty[0]&10240&&(m==null||m.on("decode",g=>{t(17,P=g),k&&t(13,k.textContent=Se(g),k)})),s.$$.dirty[0]&6144&&(m==null||m.on("timeupdate",g=>v&&t(12,v.textContent=Se(g),v))),s.$$.dirty[0]&2176&&(m==null||m.on("ready",()=>{w.autoplay?m==null||m.play():m==null||m.stop()})),s.$$.dirty[0]&8390656&&(m==null||m.on("finish",()=>{C?m==null||m.play():(t(16,R=!1),D("stop"))})),s.$$.dirty[0]&2048&&(m==null||m.on("pause",()=>{t(16,R=!1),D("pause")})),s.$$.dirty[0]&2048&&(m==null||m.on("play",()=>{t(16,R=!0),D("play")})),s.$$.dirty[0]&2048&&(m==null||m.on("load",()=>{D("load")})),s.$$.dirty[0]&16777216&&i&&he(i),s.$$.dirty[0]&16386&&b&&r!=null&&r.is_stream&&B(r)},[p,r,o,a,d,l,c,w,f,W,S,m,v,k,b,n,R,P,A,N,D,ie,u,C,i,pe,ne,ge,j,L,V,$,_,re,se]}class Zt extends le{constructor(e){super(),ae(this,e,Gt,Xt,ue,{value:1,label:2,i18n:3,dispatch_blob:22,interactive:4,editable:5,trim_region_settings:6,waveform_settings:7,waveform_options:8,mode:0,loop:23,handle_reset_value:9},null,[-1,-1])}}const ri=Zt;export{ri as A,It as W,ze as a,Lt as p,He as s};
//# sourceMappingURL=AudioPlayer.CnAk5fND.js.map
