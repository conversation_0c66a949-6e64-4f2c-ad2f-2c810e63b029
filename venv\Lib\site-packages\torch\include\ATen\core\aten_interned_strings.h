#pragma once

// @generated by torchgen/gen.py from aten_interned_strings.h

#if defined(TORCH_ASSERT_NO_OPERATORS) || defined(TORCH_ASSERT_ONLY_METHOD_OPERATORS)
#error This change adds a dependency on native_functions.yaml,          \
  meaning the file will need to be re-compiled every time an operator   \
  is changed or added. Consider if including <ATen/core/symbol.h> for   \
  the c10::Symbol class would be sufficient, or if your change would be \
  better placed in another file.
#endif

// ATen symbols correspond exactly to operators defined in ATen. Every
// symbol here corresponds exactly to an ATen operation defined in
// native_functions.yaml; attributes are in one-to-one correspondence
// with their ATen name.

#define FORALL_ATEN_BASE_SYMBOLS(_) \
_(aten, __and__) \
_(aten, __iand__) \
_(aten, __ilshift__) \
_(aten, __ior__) \
_(aten, __irshift__) \
_(aten, __ixor__) \
_(aten, __lshift__) \
_(aten, __or__) \
_(aten, __rshift__) \
_(aten, __xor__) \
_(aten, _adaptive_avg_pool2d) \
_(aten, _adaptive_avg_pool2d_backward) \
_(aten, _adaptive_avg_pool3d) \
_(aten, _adaptive_avg_pool3d_backward) \
_(aten, _add_batch_dim) \
_(aten, _add_relu) \
_(aten, _add_relu_) \
_(aten, _addmm_activation) \
_(aten, _aminmax) \
_(aten, _amp_foreach_non_finite_check_and_unscale) \
_(aten, _amp_foreach_non_finite_check_and_unscale_) \
_(aten, _amp_update_scale) \
_(aten, _amp_update_scale_) \
_(aten, _assert_async) \
_(aten, _assert_scalar) \
_(aten, _assert_tensor_metadata) \
_(aten, _autocast_to_full_precision) \
_(aten, _autocast_to_reduced_precision) \
_(aten, _backward) \
_(aten, _batch_norm_impl_index) \
_(aten, _batch_norm_impl_index_backward) \
_(aten, _batch_norm_no_update) \
_(aten, _batch_norm_with_update) \
_(aten, _batch_norm_with_update_functional) \
_(aten, _cast_Byte) \
_(aten, _cast_Char) \
_(aten, _cast_Double) \
_(aten, _cast_Float) \
_(aten, _cast_Half) \
_(aten, _cast_Int) \
_(aten, _cast_Long) \
_(aten, _cast_Short) \
_(aten, _cdist_backward) \
_(aten, _cdist_forward) \
_(aten, _cholesky_solve_helper) \
_(aten, _choose_qparams_per_tensor) \
_(aten, _chunk_cat) \
_(aten, _coalesce) \
_(aten, _coalesced) \
_(aten, _coalesced_) \
_(aten, _compute_linear_combination) \
_(aten, _conj) \
_(aten, _conj_copy) \
_(aten, _conj_physical) \
_(aten, _conv_depthwise2d) \
_(aten, _convert_indices_from_coo_to_csr) \
_(aten, _convert_indices_from_csr_to_coo) \
_(aten, _convert_weight_to_int4pack) \
_(aten, _convert_weight_to_int4pack_for_cpu) \
_(aten, _convolution) \
_(aten, _convolution_double_backward) \
_(aten, _convolution_mode) \
_(aten, _copy_from) \
_(aten, _copy_from_and_resize) \
_(aten, _cslt_compress) \
_(aten, _cslt_sparse_mm) \
_(aten, _cslt_sparse_mm_search) \
_(aten, _ctc_loss) \
_(aten, _ctc_loss_backward) \
_(aten, _cudnn_attention_forward) \
_(aten, _cudnn_ctc_loss) \
_(aten, _cudnn_init_dropout_state) \
_(aten, _cudnn_rnn) \
_(aten, _cudnn_rnn_backward) \
_(aten, _cudnn_rnn_flatten_weight) \
_(aten, _cufft_clear_plan_cache) \
_(aten, _cufft_get_plan_cache_max_size) \
_(aten, _cufft_get_plan_cache_size) \
_(aten, _cufft_set_plan_cache_max_size) \
_(aten, _cummax_helper) \
_(aten, _cummin_helper) \
_(aten, _debug_has_internal_overlap) \
_(aten, _dimI) \
_(aten, _dimV) \
_(aten, _dim_arange) \
_(aten, _dirichlet_grad) \
_(aten, _dyn_quant_matmul_4bit) \
_(aten, _dyn_quant_pack_4bit_weight) \
_(aten, _efficient_attention_backward) \
_(aten, _efficient_attention_forward) \
_(aten, _efficientzerotensor) \
_(aten, _embedding_bag) \
_(aten, _embedding_bag_backward) \
_(aten, _embedding_bag_dense_backward) \
_(aten, _embedding_bag_forward_only) \
_(aten, _embedding_bag_per_sample_weights_backward) \
_(aten, _embedding_bag_sparse_backward) \
_(aten, _empty_affine_quantized) \
_(aten, _empty_per_channel_affine_quantized) \
_(aten, _euclidean_dist) \
_(aten, _fake_quantize_learnable_per_channel_affine) \
_(aten, _fake_quantize_learnable_per_channel_affine_backward) \
_(aten, _fake_quantize_learnable_per_tensor_affine) \
_(aten, _fake_quantize_learnable_per_tensor_affine_backward) \
_(aten, _fake_quantize_per_tensor_affine_cachemask_tensor_qparams) \
_(aten, _fft_c2c) \
_(aten, _fft_c2r) \
_(aten, _fft_r2c) \
_(aten, _fill_mem_eff_dropout_mask) \
_(aten, _fill_mem_eff_dropout_mask_) \
_(aten, _flash_attention_backward) \
_(aten, _flash_attention_forward) \
_(aten, _foobar) \
_(aten, _foreach_abs) \
_(aten, _foreach_abs_) \
_(aten, _foreach_acos) \
_(aten, _foreach_acos_) \
_(aten, _foreach_add) \
_(aten, _foreach_add_) \
_(aten, _foreach_addcdiv) \
_(aten, _foreach_addcdiv_) \
_(aten, _foreach_addcmul) \
_(aten, _foreach_addcmul_) \
_(aten, _foreach_asin) \
_(aten, _foreach_asin_) \
_(aten, _foreach_atan) \
_(aten, _foreach_atan_) \
_(aten, _foreach_ceil) \
_(aten, _foreach_ceil_) \
_(aten, _foreach_clamp_max) \
_(aten, _foreach_clamp_max_) \
_(aten, _foreach_clamp_min) \
_(aten, _foreach_clamp_min_) \
_(aten, _foreach_copy) \
_(aten, _foreach_copy_) \
_(aten, _foreach_cos) \
_(aten, _foreach_cos_) \
_(aten, _foreach_cosh) \
_(aten, _foreach_cosh_) \
_(aten, _foreach_div) \
_(aten, _foreach_div_) \
_(aten, _foreach_erf) \
_(aten, _foreach_erf_) \
_(aten, _foreach_erfc) \
_(aten, _foreach_erfc_) \
_(aten, _foreach_exp) \
_(aten, _foreach_exp_) \
_(aten, _foreach_expm1) \
_(aten, _foreach_expm1_) \
_(aten, _foreach_floor) \
_(aten, _foreach_floor_) \
_(aten, _foreach_frac) \
_(aten, _foreach_frac_) \
_(aten, _foreach_lerp) \
_(aten, _foreach_lerp_) \
_(aten, _foreach_lgamma) \
_(aten, _foreach_lgamma_) \
_(aten, _foreach_log) \
_(aten, _foreach_log10) \
_(aten, _foreach_log10_) \
_(aten, _foreach_log1p) \
_(aten, _foreach_log1p_) \
_(aten, _foreach_log2) \
_(aten, _foreach_log2_) \
_(aten, _foreach_log_) \
_(aten, _foreach_max) \
_(aten, _foreach_maximum) \
_(aten, _foreach_maximum_) \
_(aten, _foreach_minimum) \
_(aten, _foreach_minimum_) \
_(aten, _foreach_mul) \
_(aten, _foreach_mul_) \
_(aten, _foreach_neg) \
_(aten, _foreach_neg_) \
_(aten, _foreach_norm) \
_(aten, _foreach_pow) \
_(aten, _foreach_pow_) \
_(aten, _foreach_reciprocal) \
_(aten, _foreach_reciprocal_) \
_(aten, _foreach_round) \
_(aten, _foreach_round_) \
_(aten, _foreach_rsqrt) \
_(aten, _foreach_rsqrt_) \
_(aten, _foreach_sigmoid) \
_(aten, _foreach_sigmoid_) \
_(aten, _foreach_sign) \
_(aten, _foreach_sign_) \
_(aten, _foreach_sin) \
_(aten, _foreach_sin_) \
_(aten, _foreach_sinh) \
_(aten, _foreach_sinh_) \
_(aten, _foreach_sqrt) \
_(aten, _foreach_sqrt_) \
_(aten, _foreach_sub) \
_(aten, _foreach_sub_) \
_(aten, _foreach_tan) \
_(aten, _foreach_tan_) \
_(aten, _foreach_tanh) \
_(aten, _foreach_tanh_) \
_(aten, _foreach_trunc) \
_(aten, _foreach_trunc_) \
_(aten, _foreach_zero) \
_(aten, _foreach_zero_) \
_(aten, _functional_assert_async) \
_(aten, _functional_assert_scalar) \
_(aten, _functional_sym_constrain_range) \
_(aten, _functional_sym_constrain_range_for_size) \
_(aten, _fused_adagrad) \
_(aten, _fused_adagrad_) \
_(aten, _fused_adam) \
_(aten, _fused_adam_) \
_(aten, _fused_adamw) \
_(aten, _fused_adamw_) \
_(aten, _fused_dropout) \
_(aten, _fused_moving_avg_obs_fq_helper) \
_(aten, _fused_moving_avg_obs_fq_helper_functional) \
_(aten, _fused_sdp_choice) \
_(aten, _fused_sgd) \
_(aten, _fused_sgd_) \
_(aten, _fw_primal) \
_(aten, _fw_primal_copy) \
_(aten, _gather_sparse_backward) \
_(aten, _grid_sampler_2d_cpu_fallback) \
_(aten, _grid_sampler_2d_cpu_fallback_backward) \
_(aten, _has_compatible_shallow_copy_type) \
_(aten, _has_same_storage_numel) \
_(aten, _histogramdd_bin_edges) \
_(aten, _histogramdd_from_bin_cts) \
_(aten, _histogramdd_from_bin_tensors) \
_(aten, _index_put_impl) \
_(aten, _index_put_impl_) \
_(aten, _indices) \
_(aten, _indices_copy) \
_(aten, _int_mm) \
_(aten, _is_all_true) \
_(aten, _is_any_true) \
_(aten, _is_zerotensor) \
_(aten, _jagged_to_padded_dense_forward) \
_(aten, _lazy_clone) \
_(aten, _linalg_check_errors) \
_(aten, _linalg_det) \
_(aten, _linalg_eigh) \
_(aten, _linalg_eigvals) \
_(aten, _linalg_slogdet) \
_(aten, _linalg_solve_ex) \
_(aten, _linalg_svd) \
_(aten, _local_scalar_dense) \
_(aten, _log_softmax) \
_(aten, _log_softmax_backward_data) \
_(aten, _logcumsumexp) \
_(aten, _lstm_mps) \
_(aten, _lu_with_info) \
_(aten, _make_dep_token) \
_(aten, _make_dual) \
_(aten, _make_dual_copy) \
_(aten, _make_per_channel_quantized_tensor) \
_(aten, _make_per_tensor_quantized_tensor) \
_(aten, _masked_scale) \
_(aten, _masked_softmax) \
_(aten, _masked_softmax_backward) \
_(aten, _mixed_dtypes_linear) \
_(aten, _mkldnn_reshape) \
_(aten, _mkldnn_transpose) \
_(aten, _mkldnn_transpose_) \
_(aten, _mps_convolution) \
_(aten, _mps_convolution_transpose) \
_(aten, _native_batch_norm_legit) \
_(aten, _native_batch_norm_legit_functional) \
_(aten, _native_batch_norm_legit_no_training) \
_(aten, _native_multi_head_attention) \
_(aten, _neg_view) \
_(aten, _neg_view_copy) \
_(aten, _nested_compute_contiguous_strides_offsets) \
_(aten, _nested_from_padded) \
_(aten, _nested_from_padded_and_nested_example) \
_(aten, _nested_from_padded_tensor) \
_(aten, _nested_get_jagged_dummy) \
_(aten, _nested_get_lengths) \
_(aten, _nested_get_max_seqlen) \
_(aten, _nested_get_min_seqlen) \
_(aten, _nested_get_offsets) \
_(aten, _nested_get_ragged_idx) \
_(aten, _nested_get_values) \
_(aten, _nested_get_values_copy) \
_(aten, _nested_select_backward) \
_(aten, _nested_sum_backward) \
_(aten, _nested_tensor_from_mask) \
_(aten, _nested_tensor_from_mask_left_aligned) \
_(aten, _nested_tensor_from_tensor_list) \
_(aten, _nested_tensor_size) \
_(aten, _nested_tensor_softmax_with_shape) \
_(aten, _nested_tensor_storage_offsets) \
_(aten, _nested_tensor_strides) \
_(aten, _nested_view_from_buffer) \
_(aten, _nested_view_from_buffer_copy) \
_(aten, _nested_view_from_jagged) \
_(aten, _nested_view_from_jagged_copy) \
_(aten, _new_zeros_with_same_feature_meta) \
_(aten, _nnpack_available) \
_(aten, _nnpack_spatial_convolution) \
_(aten, _nnz) \
_(aten, _pack_padded_sequence) \
_(aten, _pack_padded_sequence_backward) \
_(aten, _pad_circular) \
_(aten, _pad_enum) \
_(aten, _pad_packed_sequence) \
_(aten, _padded_dense_to_jagged_forward) \
_(aten, _pdist_backward) \
_(aten, _pdist_forward) \
_(aten, _pin_memory) \
_(aten, _prelu_kernel) \
_(aten, _prelu_kernel_backward) \
_(aten, _print) \
_(aten, _propagate_xla_data) \
_(aten, _remove_batch_dim) \
_(aten, _reshape_alias) \
_(aten, _reshape_alias_copy) \
_(aten, _reshape_copy) \
_(aten, _reshape_from_tensor) \
_(aten, _resize_output) \
_(aten, _resize_output_) \
_(aten, _rowwise_prune) \
_(aten, _safe_softmax) \
_(aten, _sample_dirichlet) \
_(aten, _saturate_weight_to_fp16) \
_(aten, _scaled_dot_product_attention_math) \
_(aten, _scaled_dot_product_attention_math_for_mps) \
_(aten, _scaled_dot_product_cudnn_attention) \
_(aten, _scaled_dot_product_cudnn_attention_backward) \
_(aten, _scaled_dot_product_efficient_attention) \
_(aten, _scaled_dot_product_efficient_attention_backward) \
_(aten, _scaled_dot_product_flash_attention) \
_(aten, _scaled_dot_product_flash_attention_backward) \
_(aten, _scaled_dot_product_flash_attention_for_cpu) \
_(aten, _scaled_dot_product_flash_attention_for_cpu_backward) \
_(aten, _scaled_dot_product_fused_attention_overrideable) \
_(aten, _scaled_dot_product_fused_attention_overrideable_backward) \
_(aten, _scaled_grouped_mm) \
_(aten, _scaled_mm) \
_(aten, _segment_reduce_backward) \
_(aten, _shape_as_tensor) \
_(aten, _slow_conv2d_backward) \
_(aten, _slow_conv2d_forward) \
_(aten, _sobol_engine_draw) \
_(aten, _sobol_engine_ff) \
_(aten, _sobol_engine_ff_) \
_(aten, _sobol_engine_initialize_state) \
_(aten, _sobol_engine_initialize_state_) \
_(aten, _sobol_engine_scramble) \
_(aten, _sobol_engine_scramble_) \
_(aten, _softmax) \
_(aten, _softmax_backward_data) \
_(aten, _sparse_addmm) \
_(aten, _sparse_broadcast_to) \
_(aten, _sparse_broadcast_to_copy) \
_(aten, _sparse_bsc_tensor_unsafe) \
_(aten, _sparse_bsr_tensor_unsafe) \
_(aten, _sparse_compressed_tensor_unsafe) \
_(aten, _sparse_compressed_tensor_with_dims) \
_(aten, _sparse_coo_tensor_unsafe) \
_(aten, _sparse_coo_tensor_with_dims) \
_(aten, _sparse_coo_tensor_with_dims_and_tensors) \
_(aten, _sparse_csc_tensor_unsafe) \
_(aten, _sparse_csr_prod) \
_(aten, _sparse_csr_sum) \
_(aten, _sparse_csr_tensor_unsafe) \
_(aten, _sparse_log_softmax) \
_(aten, _sparse_log_softmax_backward_data) \
_(aten, _sparse_mask_projection) \
_(aten, _sparse_mm) \
_(aten, _sparse_mm_reduce_impl) \
_(aten, _sparse_mm_reduce_impl_backward) \
_(aten, _sparse_semi_structured_addmm) \
_(aten, _sparse_semi_structured_apply) \
_(aten, _sparse_semi_structured_apply_dense) \
_(aten, _sparse_semi_structured_linear) \
_(aten, _sparse_semi_structured_mm) \
_(aten, _sparse_semi_structured_tile) \
_(aten, _sparse_softmax) \
_(aten, _sparse_softmax_backward_data) \
_(aten, _sparse_sparse_matmul) \
_(aten, _sparse_sum) \
_(aten, _sparse_sum_backward) \
_(aten, _spdiags) \
_(aten, _spsolve) \
_(aten, _stack) \
_(aten, _standard_gamma) \
_(aten, _standard_gamma_grad) \
_(aten, _test_ambiguous_defaults) \
_(aten, _test_autograd_multiple_dispatch) \
_(aten, _test_autograd_multiple_dispatch_view) \
_(aten, _test_autograd_multiple_dispatch_view_copy) \
_(aten, _test_check_tensor) \
_(aten, _test_functorch_fallback) \
_(aten, _test_optional_filled_intlist) \
_(aten, _test_optional_floatlist) \
_(aten, _test_optional_intlist) \
_(aten, _test_parallel_materialize) \
_(aten, _test_serialization_subcmul) \
_(aten, _test_string_default) \
_(aten, _test_warn_in_autograd) \
_(aten, _thnn_differentiable_gru_cell_backward) \
_(aten, _thnn_differentiable_lstm_cell_backward) \
_(aten, _thnn_fused_gru_cell) \
_(aten, _thnn_fused_gru_cell_backward) \
_(aten, _thnn_fused_lstm_cell) \
_(aten, _thnn_fused_lstm_cell_backward) \
_(aten, _thnn_fused_lstm_cell_backward_impl) \
_(aten, _to_copy) \
_(aten, _to_cpu) \
_(aten, _to_dense) \
_(aten, _to_sparse) \
_(aten, _to_sparse_bsc) \
_(aten, _to_sparse_bsr) \
_(aten, _to_sparse_csc) \
_(aten, _to_sparse_csr) \
_(aten, _to_sparse_semi_structured) \
_(aten, _transform_bias_rescale_qkv) \
_(aten, _transformer_encoder_layer_fwd) \
_(aten, _trilinear) \
_(aten, _triton_multi_head_attention) \
_(aten, _triton_scaled_dot_attention) \
_(aten, _unique) \
_(aten, _unique2) \
_(aten, _unpack_dual) \
_(aten, _unsafe_index) \
_(aten, _unsafe_index_put) \
_(aten, _unsafe_masked_index) \
_(aten, _unsafe_masked_index_put_accumulate) \
_(aten, _unsafe_view) \
_(aten, _upsample_bicubic2d_aa) \
_(aten, _upsample_bicubic2d_aa_backward) \
_(aten, _upsample_bilinear2d_aa) \
_(aten, _upsample_bilinear2d_aa_backward) \
_(aten, _upsample_nearest_exact1d) \
_(aten, _upsample_nearest_exact1d_backward) \
_(aten, _upsample_nearest_exact2d) \
_(aten, _upsample_nearest_exact2d_backward) \
_(aten, _upsample_nearest_exact3d) \
_(aten, _upsample_nearest_exact3d_backward) \
_(aten, _use_cudnn_ctc_loss) \
_(aten, _use_cudnn_rnn_flatten_weight) \
_(aten, _validate_compressed_sparse_indices) \
_(aten, _validate_sparse_bsc_tensor_args) \
_(aten, _validate_sparse_bsr_tensor_args) \
_(aten, _validate_sparse_compressed_tensor_args) \
_(aten, _validate_sparse_coo_tensor_args) \
_(aten, _validate_sparse_csc_tensor_args) \
_(aten, _validate_sparse_csr_tensor_args) \
_(aten, _values) \
_(aten, _values_copy) \
_(aten, _version) \
_(aten, _weight_int4pack_mm) \
_(aten, _weight_int4pack_mm_for_cpu) \
_(aten, _weight_int8pack_mm) \
_(aten, _weight_norm) \
_(aten, _weight_norm_differentiable_backward) \
_(aten, _weight_norm_interface) \
_(aten, _weight_norm_interface_backward) \
_(aten, _wrapped_linear_prepack) \
_(aten, _wrapped_quantized_linear_prepacked) \
_(aten, abs) \
_(aten, abs_) \
_(aten, absolute) \
_(aten, absolute_) \
_(aten, acos) \
_(aten, acos_) \
_(aten, acosh) \
_(aten, acosh_) \
_(aten, adaptive_avg_pool1d) \
_(aten, adaptive_avg_pool2d) \
_(aten, adaptive_avg_pool3d) \
_(aten, adaptive_avg_pool3d_backward) \
_(aten, adaptive_max_pool1d) \
_(aten, adaptive_max_pool2d) \
_(aten, adaptive_max_pool2d_backward) \
_(aten, adaptive_max_pool3d) \
_(aten, adaptive_max_pool3d_backward) \
_(aten, add) \
_(aten, add_) \
_(aten, addbmm) \
_(aten, addbmm_) \
_(aten, addcdiv) \
_(aten, addcdiv_) \
_(aten, addcmul) \
_(aten, addcmul_) \
_(aten, addmm) \
_(aten, addmm_) \
_(aten, addmv) \
_(aten, addmv_) \
_(aten, addr) \
_(aten, addr_) \
_(aten, adjoint) \
_(aten, affine_grid_generator) \
_(aten, affine_grid_generator_backward) \
_(aten, alias) \
_(aten, alias_copy) \
_(aten, align_as) \
_(aten, align_tensors) \
_(aten, align_to) \
_(aten, all) \
_(aten, allclose) \
_(aten, alpha_dropout) \
_(aten, alpha_dropout_) \
_(aten, amax) \
_(aten, amin) \
_(aten, aminmax) \
_(aten, angle) \
_(aten, any) \
_(aten, arange) \
_(aten, arccos) \
_(aten, arccos_) \
_(aten, arccosh) \
_(aten, arccosh_) \
_(aten, arcsin) \
_(aten, arcsin_) \
_(aten, arcsinh) \
_(aten, arcsinh_) \
_(aten, arctan) \
_(aten, arctan2) \
_(aten, arctan2_) \
_(aten, arctan_) \
_(aten, arctanh) \
_(aten, arctanh_) \
_(aten, argmax) \
_(aten, argmin) \
_(aten, argsort) \
_(aten, argwhere) \
_(aten, as_strided) \
_(aten, as_strided_) \
_(aten, as_strided_copy) \
_(aten, as_strided_scatter) \
_(aten, asin) \
_(aten, asin_) \
_(aten, asinh) \
_(aten, asinh_) \
_(aten, atan) \
_(aten, atan2) \
_(aten, atan2_) \
_(aten, atan_) \
_(aten, atanh) \
_(aten, atanh_) \
_(aten, atleast_1d) \
_(aten, atleast_2d) \
_(aten, atleast_3d) \
_(aten, avg_pool1d) \
_(aten, avg_pool2d) \
_(aten, avg_pool2d_backward) \
_(aten, avg_pool3d) \
_(aten, avg_pool3d_backward) \
_(aten, baddbmm) \
_(aten, baddbmm_) \
_(aten, bartlett_window) \
_(aten, batch_norm) \
_(aten, batch_norm_backward) \
_(aten, batch_norm_backward_elemt) \
_(aten, batch_norm_backward_reduce) \
_(aten, batch_norm_elemt) \
_(aten, batch_norm_gather_stats) \
_(aten, batch_norm_gather_stats_with_counts) \
_(aten, batch_norm_stats) \
_(aten, batch_norm_update_stats) \
_(aten, bernoulli) \
_(aten, bernoulli_) \
_(aten, bilinear) \
_(aten, binary_cross_entropy) \
_(aten, binary_cross_entropy_backward) \
_(aten, binary_cross_entropy_with_logits) \
_(aten, bincount) \
_(aten, binomial) \
_(aten, bitwise_and) \
_(aten, bitwise_and_) \
_(aten, bitwise_left_shift) \
_(aten, bitwise_left_shift_) \
_(aten, bitwise_not) \
_(aten, bitwise_not_) \
_(aten, bitwise_or) \
_(aten, bitwise_or_) \
_(aten, bitwise_right_shift) \
_(aten, bitwise_right_shift_) \
_(aten, bitwise_xor) \
_(aten, bitwise_xor_) \
_(aten, blackman_window) \
_(aten, block_diag) \
_(aten, bmm) \
_(aten, broadcast_tensors) \
_(aten, broadcast_to) \
_(aten, bucketize) \
_(aten, can_cast) \
_(aten, cartesian_prod) \
_(aten, cat) \
_(aten, cauchy) \
_(aten, cauchy_) \
_(aten, ccol_indices) \
_(aten, ccol_indices_copy) \
_(aten, cdist) \
_(aten, ceil) \
_(aten, ceil_) \
_(aten, celu) \
_(aten, celu_) \
_(aten, chain_matmul) \
_(aten, chalf) \
_(aten, channel_shuffle) \
_(aten, cholesky) \
_(aten, cholesky_inverse) \
_(aten, cholesky_solve) \
_(aten, choose_qparams_optimized) \
_(aten, chunk) \
_(aten, clamp) \
_(aten, clamp_) \
_(aten, clamp_max) \
_(aten, clamp_max_) \
_(aten, clamp_min) \
_(aten, clamp_min_) \
_(aten, clip) \
_(aten, clip_) \
_(aten, clone) \
_(aten, coalesce) \
_(aten, col2im) \
_(aten, col_indices) \
_(aten, col_indices_copy) \
_(aten, column_stack) \
_(aten, combinations) \
_(aten, complex) \
_(aten, concat) \
_(aten, concatenate) \
_(aten, conj) \
_(aten, conj_physical) \
_(aten, conj_physical_) \
_(aten, constant_pad_nd) \
_(aten, contiguous) \
_(aten, conv1d) \
_(aten, conv2d) \
_(aten, conv3d) \
_(aten, conv_depthwise3d) \
_(aten, conv_tbc) \
_(aten, conv_tbc_backward) \
_(aten, conv_transpose1d) \
_(aten, conv_transpose2d) \
_(aten, conv_transpose3d) \
_(aten, convolution) \
_(aten, convolution_backward) \
_(aten, convolution_backward_overrideable) \
_(aten, convolution_overrideable) \
_(aten, copy) \
_(aten, copy_) \
_(aten, copy_sparse_to_sparse) \
_(aten, copy_sparse_to_sparse_) \
_(aten, copysign) \
_(aten, copysign_) \
_(aten, corrcoef) \
_(aten, cos) \
_(aten, cos_) \
_(aten, cosh) \
_(aten, cosh_) \
_(aten, cosine_embedding_loss) \
_(aten, cosine_similarity) \
_(aten, count_nonzero) \
_(aten, cov) \
_(aten, cross) \
_(aten, cross_entropy_loss) \
_(aten, crow_indices) \
_(aten, crow_indices_copy) \
_(aten, ctc_loss) \
_(aten, cudnn_affine_grid_generator) \
_(aten, cudnn_affine_grid_generator_backward) \
_(aten, cudnn_batch_norm) \
_(aten, cudnn_batch_norm_backward) \
_(aten, cudnn_convolution) \
_(aten, cudnn_convolution_add_relu) \
_(aten, cudnn_convolution_relu) \
_(aten, cudnn_convolution_transpose) \
_(aten, cudnn_grid_sampler) \
_(aten, cudnn_grid_sampler_backward) \
_(aten, cudnn_is_acceptable) \
_(aten, cummax) \
_(aten, cummaxmin_backward) \
_(aten, cummin) \
_(aten, cumprod) \
_(aten, cumprod_) \
_(aten, cumprod_backward) \
_(aten, cumsum) \
_(aten, cumsum_) \
_(aten, cumulative_trapezoid) \
_(aten, data) \
_(aten, deg2rad) \
_(aten, deg2rad_) \
_(aten, dense_dim) \
_(aten, dequantize) \
_(aten, det) \
_(aten, detach) \
_(aten, detach_) \
_(aten, detach_copy) \
_(aten, diag) \
_(aten, diag_embed) \
_(aten, diagflat) \
_(aten, diagonal) \
_(aten, diagonal_backward) \
_(aten, diagonal_copy) \
_(aten, diagonal_scatter) \
_(aten, diff) \
_(aten, digamma) \
_(aten, digamma_) \
_(aten, dist) \
_(aten, div) \
_(aten, div_) \
_(aten, divide) \
_(aten, divide_) \
_(aten, dot) \
_(aten, dropout) \
_(aten, dropout_) \
_(aten, dsplit) \
_(aten, dstack) \
_(aten, einsum) \
_(aten, elu) \
_(aten, elu_) \
_(aten, elu_backward) \
_(aten, embedding) \
_(aten, embedding_backward) \
_(aten, embedding_bag) \
_(aten, embedding_dense_backward) \
_(aten, embedding_renorm) \
_(aten, embedding_renorm_) \
_(aten, embedding_sparse_backward) \
_(aten, empty) \
_(aten, empty_like) \
_(aten, empty_permuted) \
_(aten, empty_quantized) \
_(aten, empty_strided) \
_(aten, eq) \
_(aten, eq_) \
_(aten, equal) \
_(aten, erf) \
_(aten, erf_) \
_(aten, erfc) \
_(aten, erfc_) \
_(aten, erfinv) \
_(aten, erfinv_) \
_(aten, exp) \
_(aten, exp2) \
_(aten, exp2_) \
_(aten, exp_) \
_(aten, expand) \
_(aten, expand_as) \
_(aten, expand_copy) \
_(aten, expm1) \
_(aten, expm1_) \
_(aten, exponential) \
_(aten, exponential_) \
_(aten, eye) \
_(aten, fake_quantize_per_channel_affine) \
_(aten, fake_quantize_per_channel_affine_cachemask) \
_(aten, fake_quantize_per_channel_affine_cachemask_backward) \
_(aten, fake_quantize_per_tensor_affine) \
_(aten, fake_quantize_per_tensor_affine_cachemask) \
_(aten, fake_quantize_per_tensor_affine_cachemask_backward) \
_(aten, fbgemm_linear_fp16_weight) \
_(aten, fbgemm_linear_fp16_weight_fp32_activation) \
_(aten, fbgemm_linear_int8_weight) \
_(aten, fbgemm_linear_int8_weight_fp32_activation) \
_(aten, fbgemm_linear_quantize_weight) \
_(aten, fbgemm_pack_gemm_matrix_fp16) \
_(aten, fbgemm_pack_quantized_matrix) \
_(aten, feature_alpha_dropout) \
_(aten, feature_alpha_dropout_) \
_(aten, feature_dropout) \
_(aten, feature_dropout_) \
_(aten, fft_fft) \
_(aten, fft_fft2) \
_(aten, fft_fftfreq) \
_(aten, fft_fftn) \
_(aten, fft_fftshift) \
_(aten, fft_hfft) \
_(aten, fft_hfft2) \
_(aten, fft_hfftn) \
_(aten, fft_ifft) \
_(aten, fft_ifft2) \
_(aten, fft_ifftn) \
_(aten, fft_ifftshift) \
_(aten, fft_ihfft) \
_(aten, fft_ihfft2) \
_(aten, fft_ihfftn) \
_(aten, fft_irfft) \
_(aten, fft_irfft2) \
_(aten, fft_irfftn) \
_(aten, fft_rfft) \
_(aten, fft_rfft2) \
_(aten, fft_rfftfreq) \
_(aten, fft_rfftn) \
_(aten, fill) \
_(aten, fill_) \
_(aten, fill_diagonal) \
_(aten, fill_diagonal_) \
_(aten, fix) \
_(aten, fix_) \
_(aten, flatten) \
_(aten, flatten_dense_tensors) \
_(aten, flip) \
_(aten, fliplr) \
_(aten, flipud) \
_(aten, float_power) \
_(aten, float_power_) \
_(aten, floor) \
_(aten, floor_) \
_(aten, floor_divide) \
_(aten, floor_divide_) \
_(aten, fmax) \
_(aten, fmin) \
_(aten, fmod) \
_(aten, fmod_) \
_(aten, frac) \
_(aten, frac_) \
_(aten, fractional_max_pool2d) \
_(aten, fractional_max_pool2d_backward) \
_(aten, fractional_max_pool3d) \
_(aten, fractional_max_pool3d_backward) \
_(aten, frexp) \
_(aten, frobenius_norm) \
_(aten, from_file) \
_(aten, full) \
_(aten, full_like) \
_(aten, fused_moving_avg_obs_fake_quant) \
_(aten, gather) \
_(aten, gather_backward) \
_(aten, gcd) \
_(aten, gcd_) \
_(aten, ge) \
_(aten, ge_) \
_(aten, gelu) \
_(aten, gelu_) \
_(aten, gelu_backward) \
_(aten, geometric) \
_(aten, geometric_) \
_(aten, geqrf) \
_(aten, ger) \
_(aten, glu) \
_(aten, glu_backward) \
_(aten, glu_backward_jvp) \
_(aten, glu_jvp) \
_(aten, gradient) \
_(aten, greater) \
_(aten, greater_) \
_(aten, greater_equal) \
_(aten, greater_equal_) \
_(aten, grid_sampler) \
_(aten, grid_sampler_2d) \
_(aten, grid_sampler_2d_backward) \
_(aten, grid_sampler_3d) \
_(aten, grid_sampler_3d_backward) \
_(aten, group_norm) \
_(aten, gru) \
_(aten, gru_cell) \
_(aten, gt) \
_(aten, gt_) \
_(aten, hamming_window) \
_(aten, hann_window) \
_(aten, hardshrink) \
_(aten, hardshrink_backward) \
_(aten, hardsigmoid) \
_(aten, hardsigmoid_) \
_(aten, hardsigmoid_backward) \
_(aten, hardswish) \
_(aten, hardswish_) \
_(aten, hardswish_backward) \
_(aten, hardtanh) \
_(aten, hardtanh_) \
_(aten, hardtanh_backward) \
_(aten, heaviside) \
_(aten, heaviside_) \
_(aten, hinge_embedding_loss) \
_(aten, histc) \
_(aten, histogram) \
_(aten, histogramdd) \
_(aten, hsplit) \
_(aten, hspmm) \
_(aten, hstack) \
_(aten, huber_loss) \
_(aten, huber_loss_backward) \
_(aten, hypot) \
_(aten, hypot_) \
_(aten, i0) \
_(aten, i0_) \
_(aten, igamma) \
_(aten, igamma_) \
_(aten, igammac) \
_(aten, igammac_) \
_(aten, im2col) \
_(aten, imag) \
_(aten, index) \
_(aten, index_add) \
_(aten, index_add_) \
_(aten, index_copy) \
_(aten, index_copy_) \
_(aten, index_fill) \
_(aten, index_fill_) \
_(aten, index_put) \
_(aten, index_put_) \
_(aten, index_reduce) \
_(aten, index_reduce_) \
_(aten, index_select) \
_(aten, index_select_backward) \
_(aten, indices) \
_(aten, indices_copy) \
_(aten, infinitely_differentiable_gelu_backward) \
_(aten, inner) \
_(aten, instance_norm) \
_(aten, int_repr) \
_(aten, inverse) \
_(aten, is_coalesced) \
_(aten, is_complex) \
_(aten, is_conj) \
_(aten, is_distributed) \
_(aten, is_floating_point) \
_(aten, is_inference) \
_(aten, is_leaf) \
_(aten, is_neg) \
_(aten, is_nonzero) \
_(aten, is_pinned) \
_(aten, is_same_size) \
_(aten, is_set_to) \
_(aten, is_signed) \
_(aten, is_vulkan_available) \
_(aten, isclose) \
_(aten, isfinite) \
_(aten, isin) \
_(aten, isinf) \
_(aten, isnan) \
_(aten, isneginf) \
_(aten, isposinf) \
_(aten, isreal) \
_(aten, istft) \
_(aten, item) \
_(aten, kaiser_window) \
_(aten, kl_div) \
_(aten, kron) \
_(aten, kthvalue) \
_(aten, l1_loss) \
_(aten, layer_norm) \
_(aten, lcm) \
_(aten, lcm_) \
_(aten, ldexp) \
_(aten, ldexp_) \
_(aten, le) \
_(aten, le_) \
_(aten, leaky_relu) \
_(aten, leaky_relu_) \
_(aten, leaky_relu_backward) \
_(aten, lerp) \
_(aten, lerp_) \
_(aten, less) \
_(aten, less_) \
_(aten, less_equal) \
_(aten, less_equal_) \
_(aten, lgamma) \
_(aten, lgamma_) \
_(aten, lift) \
_(aten, lift_fresh) \
_(aten, lift_fresh_copy) \
_(aten, linalg_cholesky) \
_(aten, linalg_cholesky_ex) \
_(aten, linalg_cond) \
_(aten, linalg_cross) \
_(aten, linalg_det) \
_(aten, linalg_diagonal) \
_(aten, linalg_eig) \
_(aten, linalg_eigh) \
_(aten, linalg_eigvals) \
_(aten, linalg_eigvalsh) \
_(aten, linalg_householder_product) \
_(aten, linalg_inv) \
_(aten, linalg_inv_ex) \
_(aten, linalg_ldl_factor) \
_(aten, linalg_ldl_factor_ex) \
_(aten, linalg_ldl_solve) \
_(aten, linalg_lstsq) \
_(aten, linalg_lu) \
_(aten, linalg_lu_factor) \
_(aten, linalg_lu_factor_ex) \
_(aten, linalg_lu_solve) \
_(aten, linalg_matmul) \
_(aten, linalg_matrix_exp) \
_(aten, linalg_matrix_norm) \
_(aten, linalg_matrix_power) \
_(aten, linalg_matrix_rank) \
_(aten, linalg_multi_dot) \
_(aten, linalg_norm) \
_(aten, linalg_pinv) \
_(aten, linalg_qr) \
_(aten, linalg_slogdet) \
_(aten, linalg_solve) \
_(aten, linalg_solve_ex) \
_(aten, linalg_solve_triangular) \
_(aten, linalg_svd) \
_(aten, linalg_svdvals) \
_(aten, linalg_tensorinv) \
_(aten, linalg_tensorsolve) \
_(aten, linalg_vander) \
_(aten, linalg_vecdot) \
_(aten, linalg_vector_norm) \
_(aten, linear) \
_(aten, linear_backward) \
_(aten, linspace) \
_(aten, log) \
_(aten, log10) \
_(aten, log10_) \
_(aten, log1p) \
_(aten, log1p_) \
_(aten, log2) \
_(aten, log2_) \
_(aten, log_) \
_(aten, log_normal) \
_(aten, log_normal_) \
_(aten, log_sigmoid) \
_(aten, log_sigmoid_backward) \
_(aten, log_sigmoid_forward) \
_(aten, log_softmax) \
_(aten, logaddexp) \
_(aten, logaddexp2) \
_(aten, logcumsumexp) \
_(aten, logdet) \
_(aten, logical_and) \
_(aten, logical_and_) \
_(aten, logical_not) \
_(aten, logical_not_) \
_(aten, logical_or) \
_(aten, logical_or_) \
_(aten, logical_xor) \
_(aten, logical_xor_) \
_(aten, logit) \
_(aten, logit_) \
_(aten, logit_backward) \
_(aten, logspace) \
_(aten, logsumexp) \
_(aten, lshift) \
_(aten, lstm) \
_(aten, lstm_cell) \
_(aten, lstm_mps_backward) \
_(aten, lt) \
_(aten, lt_) \
_(aten, lu_solve) \
_(aten, lu_unpack) \
_(aten, mH) \
_(aten, mT) \
_(aten, margin_ranking_loss) \
_(aten, masked_fill) \
_(aten, masked_fill_) \
_(aten, masked_scatter) \
_(aten, masked_scatter_) \
_(aten, masked_scatter_backward) \
_(aten, masked_select) \
_(aten, masked_select_backward) \
_(aten, matmul) \
_(aten, matmul_backward) \
_(aten, matrix_H) \
_(aten, matrix_exp) \
_(aten, matrix_exp_backward) \
_(aten, matrix_power) \
_(aten, max) \
_(aten, max_pool1d) \
_(aten, max_pool1d_with_indices) \
_(aten, max_pool2d) \
_(aten, max_pool2d_backward) \
_(aten, max_pool2d_with_indices) \
_(aten, max_pool2d_with_indices_backward) \
_(aten, max_pool3d) \
_(aten, max_pool3d_with_indices) \
_(aten, max_pool3d_with_indices_backward) \
_(aten, max_unpool2d) \
_(aten, max_unpool3d) \
_(aten, maximum) \
_(aten, mean) \
_(aten, median) \
_(aten, meshgrid) \
_(aten, min) \
_(aten, minimum) \
_(aten, miopen_batch_norm) \
_(aten, miopen_batch_norm_backward) \
_(aten, miopen_convolution) \
_(aten, miopen_convolution_add_relu) \
_(aten, miopen_convolution_relu) \
_(aten, miopen_convolution_transpose) \
_(aten, miopen_depthwise_convolution) \
_(aten, miopen_rnn) \
_(aten, miopen_rnn_backward) \
_(aten, mish) \
_(aten, mish_) \
_(aten, mish_backward) \
_(aten, mkldnn_adaptive_avg_pool2d) \
_(aten, mkldnn_adaptive_avg_pool2d_backward) \
_(aten, mkldnn_convolution) \
_(aten, mkldnn_linear) \
_(aten, mkldnn_linear_backward) \
_(aten, mkldnn_linear_backward_input) \
_(aten, mkldnn_linear_backward_weights) \
_(aten, mkldnn_max_pool2d) \
_(aten, mkldnn_max_pool2d_backward) \
_(aten, mkldnn_max_pool3d) \
_(aten, mkldnn_max_pool3d_backward) \
_(aten, mkldnn_reorder_conv2d_weight) \
_(aten, mkldnn_reorder_conv3d_weight) \
_(aten, mkldnn_rnn_layer) \
_(aten, mkldnn_rnn_layer_backward) \
_(aten, mm) \
_(aten, mode) \
_(aten, moveaxis) \
_(aten, movedim) \
_(aten, mps_convolution_backward) \
_(aten, mps_convolution_transpose_backward) \
_(aten, mse_loss) \
_(aten, mse_loss_backward) \
_(aten, msort) \
_(aten, mul) \
_(aten, mul_) \
_(aten, multi_margin_loss) \
_(aten, multi_margin_loss_backward) \
_(aten, multilabel_margin_loss) \
_(aten, multilabel_margin_loss_backward) \
_(aten, multilabel_margin_loss_forward) \
_(aten, multinomial) \
_(aten, multiply) \
_(aten, multiply_) \
_(aten, mv) \
_(aten, mvlgamma) \
_(aten, mvlgamma_) \
_(aten, nan_to_num) \
_(aten, nan_to_num_) \
_(aten, nanmean) \
_(aten, nanmedian) \
_(aten, nanquantile) \
_(aten, nansum) \
_(aten, narrow) \
_(aten, narrow_copy) \
_(aten, native_batch_norm) \
_(aten, native_batch_norm_backward) \
_(aten, native_channel_shuffle) \
_(aten, native_dropout) \
_(aten, native_dropout_backward) \
_(aten, native_group_norm) \
_(aten, native_group_norm_backward) \
_(aten, native_layer_norm) \
_(aten, native_layer_norm_backward) \
_(aten, native_norm) \
_(aten, ne) \
_(aten, ne_) \
_(aten, neg) \
_(aten, neg_) \
_(aten, negative) \
_(aten, negative_) \
_(aten, nested_to_padded_tensor) \
_(aten, new_empty) \
_(aten, new_empty_strided) \
_(aten, new_full) \
_(aten, new_ones) \
_(aten, new_zeros) \
_(aten, nextafter) \
_(aten, nextafter_) \
_(aten, nll_loss) \
_(aten, nll_loss2d) \
_(aten, nll_loss2d_backward) \
_(aten, nll_loss2d_forward) \
_(aten, nll_loss_backward) \
_(aten, nll_loss_forward) \
_(aten, nll_loss_nd) \
_(aten, nonzero) \
_(aten, nonzero_numpy) \
_(aten, nonzero_static) \
_(aten, norm) \
_(aten, norm_except_dim) \
_(aten, normal) \
_(aten, normal_) \
_(aten, normal_functional) \
_(aten, not_equal) \
_(aten, not_equal_) \
_(aten, nuclear_norm) \
_(aten, numpy_T) \
_(aten, one_hot) \
_(aten, ones) \
_(aten, ones_like) \
_(aten, orgqr) \
_(aten, ormqr) \
_(aten, outer) \
_(aten, output_nr) \
_(aten, pad) \
_(aten, pad_sequence) \
_(aten, pairwise_distance) \
_(aten, pdist) \
_(aten, permute) \
_(aten, permute_copy) \
_(aten, pin_memory) \
_(aten, pinverse) \
_(aten, pixel_shuffle) \
_(aten, pixel_unshuffle) \
_(aten, poisson) \
_(aten, poisson_nll_loss) \
_(aten, polar) \
_(aten, polygamma) \
_(aten, polygamma_) \
_(aten, positive) \
_(aten, pow) \
_(aten, pow_) \
_(aten, prelu) \
_(aten, prod) \
_(aten, promote_types) \
_(aten, put) \
_(aten, put_) \
_(aten, q_per_channel_axis) \
_(aten, q_per_channel_scales) \
_(aten, q_per_channel_zero_points) \
_(aten, q_scale) \
_(aten, q_zero_point) \
_(aten, qr) \
_(aten, qscheme) \
_(aten, quantile) \
_(aten, quantize_per_channel) \
_(aten, quantize_per_tensor) \
_(aten, quantize_per_tensor_dynamic) \
_(aten, quantized_batch_norm) \
_(aten, quantized_gru_cell) \
_(aten, quantized_lstm_cell) \
_(aten, quantized_max_pool1d) \
_(aten, quantized_max_pool2d) \
_(aten, quantized_max_pool3d) \
_(aten, quantized_rnn_relu_cell) \
_(aten, quantized_rnn_tanh_cell) \
_(aten, rad2deg) \
_(aten, rad2deg_) \
_(aten, rand) \
_(aten, rand_like) \
_(aten, randint) \
_(aten, randint_like) \
_(aten, randn) \
_(aten, randn_like) \
_(aten, random) \
_(aten, random_) \
_(aten, randperm) \
_(aten, range) \
_(aten, ravel) \
_(aten, real) \
_(aten, reciprocal) \
_(aten, reciprocal_) \
_(aten, record_stream) \
_(aten, refine_names) \
_(aten, reflection_pad1d) \
_(aten, reflection_pad1d_backward) \
_(aten, reflection_pad2d) \
_(aten, reflection_pad2d_backward) \
_(aten, reflection_pad3d) \
_(aten, reflection_pad3d_backward) \
_(aten, relu) \
_(aten, relu6) \
_(aten, relu6_) \
_(aten, relu_) \
_(aten, remainder) \
_(aten, remainder_) \
_(aten, rename) \
_(aten, rename_) \
_(aten, renorm) \
_(aten, renorm_) \
_(aten, repeat) \
_(aten, repeat_interleave) \
_(aten, replication_pad1d) \
_(aten, replication_pad1d_backward) \
_(aten, replication_pad2d) \
_(aten, replication_pad2d_backward) \
_(aten, replication_pad3d) \
_(aten, replication_pad3d_backward) \
_(aten, requires_grad) \
_(aten, requires_grad_) \
_(aten, reshape) \
_(aten, reshape_as) \
_(aten, resize) \
_(aten, resize_) \
_(aten, resize_as) \
_(aten, resize_as_) \
_(aten, resize_as_sparse) \
_(aten, resize_as_sparse_) \
_(aten, resolve_conj) \
_(aten, resolve_neg) \
_(aten, result_type) \
_(aten, retain_grad) \
_(aten, retains_grad) \
_(aten, rms_norm) \
_(aten, rnn_relu) \
_(aten, rnn_relu_cell) \
_(aten, rnn_tanh) \
_(aten, rnn_tanh_cell) \
_(aten, roll) \
_(aten, rot90) \
_(aten, round) \
_(aten, round_) \
_(aten, row_indices) \
_(aten, row_indices_copy) \
_(aten, row_stack) \
_(aten, rrelu) \
_(aten, rrelu_) \
_(aten, rrelu_with_noise) \
_(aten, rrelu_with_noise_) \
_(aten, rrelu_with_noise_backward) \
_(aten, rrelu_with_noise_functional) \
_(aten, rshift) \
_(aten, rsqrt) \
_(aten, rsqrt_) \
_(aten, rsub) \
_(aten, scalar_tensor) \
_(aten, scaled_dot_product_attention) \
_(aten, scatter) \
_(aten, scatter_) \
_(aten, scatter_add) \
_(aten, scatter_add_) \
_(aten, scatter_reduce) \
_(aten, scatter_reduce_) \
_(aten, searchsorted) \
_(aten, segment_reduce) \
_(aten, select) \
_(aten, select_backward) \
_(aten, select_copy) \
_(aten, select_scatter) \
_(aten, selu) \
_(aten, selu_) \
_(aten, set) \
_(aten, set_) \
_(aten, set_data) \
_(aten, sgn) \
_(aten, sgn_) \
_(aten, sigmoid) \
_(aten, sigmoid_) \
_(aten, sigmoid_backward) \
_(aten, sign) \
_(aten, sign_) \
_(aten, signbit) \
_(aten, silu) \
_(aten, silu_) \
_(aten, silu_backward) \
_(aten, sin) \
_(aten, sin_) \
_(aten, sinc) \
_(aten, sinc_) \
_(aten, sinh) \
_(aten, sinh_) \
_(aten, size) \
_(aten, slice) \
_(aten, slice_backward) \
_(aten, slice_copy) \
_(aten, slice_inverse) \
_(aten, slice_scatter) \
_(aten, slogdet) \
_(aten, slow_conv3d) \
_(aten, slow_conv3d_forward) \
_(aten, slow_conv_dilated2d) \
_(aten, slow_conv_dilated3d) \
_(aten, slow_conv_transpose2d) \
_(aten, slow_conv_transpose3d) \
_(aten, smm) \
_(aten, smooth_l1_loss) \
_(aten, smooth_l1_loss_backward) \
_(aten, soft_margin_loss) \
_(aten, soft_margin_loss_backward) \
_(aten, softmax) \
_(aten, softplus) \
_(aten, softplus_backward) \
_(aten, softshrink) \
_(aten, softshrink_backward) \
_(aten, sort) \
_(aten, sparse_bsc_tensor) \
_(aten, sparse_bsr_tensor) \
_(aten, sparse_compressed_tensor) \
_(aten, sparse_coo_tensor) \
_(aten, sparse_csc_tensor) \
_(aten, sparse_csr_tensor) \
_(aten, sparse_dim) \
_(aten, sparse_mask) \
_(aten, sparse_resize) \
_(aten, sparse_resize_) \
_(aten, sparse_resize_and_clear) \
_(aten, sparse_resize_and_clear_) \
_(aten, sparse_sampled_addmm) \
_(aten, special_airy_ai) \
_(aten, special_bessel_j0) \
_(aten, special_bessel_j1) \
_(aten, special_bessel_y0) \
_(aten, special_bessel_y1) \
_(aten, special_chebyshev_polynomial_t) \
_(aten, special_chebyshev_polynomial_u) \
_(aten, special_chebyshev_polynomial_v) \
_(aten, special_chebyshev_polynomial_w) \
_(aten, special_digamma) \
_(aten, special_entr) \
_(aten, special_erf) \
_(aten, special_erfc) \
_(aten, special_erfcx) \
_(aten, special_erfinv) \
_(aten, special_exp2) \
_(aten, special_expit) \
_(aten, special_expm1) \
_(aten, special_gammainc) \
_(aten, special_gammaincc) \
_(aten, special_gammaln) \
_(aten, special_hermite_polynomial_h) \
_(aten, special_hermite_polynomial_he) \
_(aten, special_i0) \
_(aten, special_i0e) \
_(aten, special_i1) \
_(aten, special_i1e) \
_(aten, special_laguerre_polynomial_l) \
_(aten, special_legendre_polynomial_p) \
_(aten, special_log1p) \
_(aten, special_log_ndtr) \
_(aten, special_log_softmax) \
_(aten, special_logit) \
_(aten, special_logsumexp) \
_(aten, special_modified_bessel_i0) \
_(aten, special_modified_bessel_i1) \
_(aten, special_modified_bessel_k0) \
_(aten, special_modified_bessel_k1) \
_(aten, special_multigammaln) \
_(aten, special_ndtr) \
_(aten, special_ndtri) \
_(aten, special_polygamma) \
_(aten, special_psi) \
_(aten, special_round) \
_(aten, special_scaled_modified_bessel_k0) \
_(aten, special_scaled_modified_bessel_k1) \
_(aten, special_shifted_chebyshev_polynomial_t) \
_(aten, special_shifted_chebyshev_polynomial_u) \
_(aten, special_shifted_chebyshev_polynomial_v) \
_(aten, special_shifted_chebyshev_polynomial_w) \
_(aten, special_sinc) \
_(aten, special_softmax) \
_(aten, special_spherical_bessel_j0) \
_(aten, special_xlog1py) \
_(aten, special_xlogy) \
_(aten, special_zeta) \
_(aten, split) \
_(aten, split_copy) \
_(aten, split_with_sizes) \
_(aten, split_with_sizes_copy) \
_(aten, sqrt) \
_(aten, sqrt_) \
_(aten, square) \
_(aten, square_) \
_(aten, squeeze) \
_(aten, squeeze_) \
_(aten, squeeze_copy) \
_(aten, sspaddmm) \
_(aten, stack) \
_(aten, std) \
_(aten, std_mean) \
_(aten, stft) \
_(aten, stride) \
_(aten, sub) \
_(aten, sub_) \
_(aten, subtract) \
_(aten, subtract_) \
_(aten, sum) \
_(aten, sum_to_size) \
_(aten, svd) \
_(aten, swapaxes) \
_(aten, swapaxes_) \
_(aten, swapdims) \
_(aten, swapdims_) \
_(aten, sym_constrain_range) \
_(aten, sym_constrain_range_for_size) \
_(aten, sym_numel) \
_(aten, sym_size) \
_(aten, sym_storage_offset) \
_(aten, sym_stride) \
_(aten, t) \
_(aten, t_) \
_(aten, t_copy) \
_(aten, take) \
_(aten, take_along_dim) \
_(aten, tan) \
_(aten, tan_) \
_(aten, tanh) \
_(aten, tanh_) \
_(aten, tanh_backward) \
_(aten, tensor_split) \
_(aten, tensordot) \
_(aten, thnn_conv2d) \
_(aten, threshold) \
_(aten, threshold_) \
_(aten, threshold_backward) \
_(aten, tile) \
_(aten, to) \
_(aten, to_dense) \
_(aten, to_dense_backward) \
_(aten, to_mkldnn) \
_(aten, to_mkldnn_backward) \
_(aten, to_padded_tensor) \
_(aten, to_sparse) \
_(aten, to_sparse_bsc) \
_(aten, to_sparse_bsr) \
_(aten, to_sparse_csc) \
_(aten, to_sparse_csr) \
_(aten, topk) \
_(aten, trace) \
_(aten, trace_backward) \
_(aten, transpose) \
_(aten, transpose_) \
_(aten, transpose_copy) \
_(aten, trapezoid) \
_(aten, trapz) \
_(aten, triangular_solve) \
_(aten, tril) \
_(aten, tril_) \
_(aten, tril_indices) \
_(aten, triplet_margin_loss) \
_(aten, triu) \
_(aten, triu_) \
_(aten, triu_indices) \
_(aten, true_divide) \
_(aten, true_divide_) \
_(aten, trunc) \
_(aten, trunc_) \
_(aten, type_as) \
_(aten, unbind) \
_(aten, unbind_copy) \
_(aten, unflatten) \
_(aten, unflatten_dense_tensors) \
_(aten, unfold) \
_(aten, unfold_backward) \
_(aten, unfold_copy) \
_(aten, uniform) \
_(aten, uniform_) \
_(aten, unique_consecutive) \
_(aten, unique_dim) \
_(aten, unique_dim_consecutive) \
_(aten, unsafe_chunk) \
_(aten, unsafe_split) \
_(aten, unsafe_split_with_sizes) \
_(aten, unsqueeze) \
_(aten, unsqueeze_) \
_(aten, unsqueeze_copy) \
_(aten, upsample_bicubic2d) \
_(aten, upsample_bicubic2d_backward) \
_(aten, upsample_bilinear2d) \
_(aten, upsample_bilinear2d_backward) \
_(aten, upsample_linear1d) \
_(aten, upsample_linear1d_backward) \
_(aten, upsample_nearest1d) \
_(aten, upsample_nearest1d_backward) \
_(aten, upsample_nearest2d) \
_(aten, upsample_nearest2d_backward) \
_(aten, upsample_nearest3d) \
_(aten, upsample_nearest3d_backward) \
_(aten, upsample_trilinear3d) \
_(aten, upsample_trilinear3d_backward) \
_(aten, value_selecting_reduction_backward) \
_(aten, values) \
_(aten, values_copy) \
_(aten, vander) \
_(aten, var) \
_(aten, var_mean) \
_(aten, vdot) \
_(aten, view) \
_(aten, view_as) \
_(aten, view_as_complex) \
_(aten, view_as_complex_copy) \
_(aten, view_as_real) \
_(aten, view_as_real_copy) \
_(aten, view_copy) \
_(aten, vsplit) \
_(aten, vstack) \
_(aten, where) \
_(aten, xlogy) \
_(aten, xlogy_) \
_(aten, zero) \
_(aten, zero_) \
_(aten, zeros) \
_(aten, zeros_like)

#define FORALL_ATTR_BASE_SYMBOLS(_) \
_(attr, A) \
_(attr, B) \
_(attr, C) \
_(attr, H) \
_(attr, HxW) \
_(attr, K) \
_(attr, L) \
_(attr, LD) \
_(attr, LU) \
_(attr, LU_data) \
_(attr, LU_pivots) \
_(attr, M) \
_(attr, N) \
_(attr, P) \
_(attr, Q) \
_(attr, R) \
_(attr, S) \
_(attr, U) \
_(attr, UPLO) \
_(attr, V) \
_(attr, Vh) \
_(attr, W) \
_(attr, X) \
_(attr, a) \
_(attr, abs) \
_(attr, accumulate) \
_(attr, accumulate_matches) \
_(attr, activation) \
_(attr, addends) \
_(attr, adjoint) \
_(attr, alg_id) \
_(attr, algorithm) \
_(attr, alibi_slopes) \
_(attr, align_corners) \
_(attr, align_to_window) \
_(attr, allow_tf32) \
_(attr, alpha) \
_(attr, amsgrad) \
_(attr, anchor) \
_(attr, angle) \
_(attr, any) \
_(attr, api_name) \
_(attr, append) \
_(attr, approximate) \
_(attr, arg1) \
_(attr, arg2) \
_(attr, arg3) \
_(attr, arg_out) \
_(attr, assert_msg) \
_(attr, assume_unique) \
_(attr, atol) \
_(attr, attn_bias) \
_(attr, attn_mask) \
_(attr, average_attn_weights) \
_(attr, averaging_const) \
_(attr, aweights) \
_(attr, axis) \
_(attr, axis0) \
_(attr, axis1) \
_(attr, b) \
_(attr, b_hh) \
_(attr, b_ih) \
_(attr, bag_size) \
_(attr, base) \
_(attr, batch1) \
_(attr, batch2) \
_(attr, batch_dim) \
_(attr, batch_first) \
_(attr, batch_size) \
_(attr, batch_sizes) \
_(attr, benchmark) \
_(attr, beta) \
_(attr, beta1) \
_(attr, beta2) \
_(attr, bias) \
_(attr, bias_defined) \
_(attr, bias_g) \
_(attr, bias_requires_grad) \
_(attr, bias_sizes) \
_(attr, bidirectional) \
_(attr, bin_edges) \
_(attr, bins) \
_(attr, bit_width) \
_(attr, blank) \
_(attr, block_size) \
_(attr, blocksize) \
_(attr, boundaries) \
_(attr, buffer) \
_(attr, ccol_indices) \
_(attr, cdim) \
_(attr, cdist) \
_(attr, ceil_mode) \
_(attr, cell_state_fwd) \
_(attr, center) \
_(attr, ch_axis) \
_(attr, check_errors) \
_(attr, chunks) \
_(attr, coalesced) \
_(attr, coefficients) \
_(attr, col) \
_(attr, col_indices) \
_(attr, col_offsets) \
_(attr, col_offsets_hh) \
_(attr, col_offsets_ih) \
_(attr, compressed_A) \
_(attr, compressed_idx) \
_(attr, compressed_indices) \
_(attr, compressed_indices_dtype) \
_(attr, compute_log_sumexp) \
_(attr, compute_mode) \
_(attr, compute_uv) \
_(attr, compute_v) \
_(attr, condition) \
_(attr, copy) \
_(attr, correction) \
_(attr, count) \
_(attr, count_include_pad) \
_(attr, counts) \
_(attr, cpu_dtype) \
_(attr, cpu_enabled) \
_(attr, cpu_nested_shape_example) \
_(attr, create_graph) \
_(attr, crow_indices) \
_(attr, cu_seqlens_k) \
_(attr, cu_seqlens_q) \
_(attr, cuda_dtype) \
_(attr, cuda_enabled) \
_(attr, cudnn_enable) \
_(attr, cudnn_enabled) \
_(attr, cum_seq_k) \
_(attr, cum_seq_q) \
_(attr, custom_mask_type) \
_(attr, cx) \
_(attr, cx_) \
_(attr, cx_tmp) \
_(attr, cy) \
_(attr, cy_) \
_(attr, d) \
_(attr, dampening) \
_(attr, data) \
_(attr, decimals) \
_(attr, delta) \
_(attr, dense) \
_(attr, dense_B) \
_(attr, dense_dim) \
_(attr, density) \
_(attr, dep_token) \
_(attr, descending) \
_(attr, destination) \
_(attr, deterministic) \
_(attr, device) \
_(attr, device_index) \
_(attr, dgrad_glu) \
_(attr, diagonal) \
_(attr, diagonals) \
_(attr, dilation) \
_(attr, dim) \
_(attr, dim0) \
_(attr, dim1) \
_(attr, dim2) \
_(attr, dimension) \
_(attr, dims) \
_(attr, dims_other) \
_(attr, dims_self) \
_(attr, divisor_override) \
_(attr, downscale_factor) \
_(attr, driver) \
_(attr, dropout) \
_(attr, dropout_mask) \
_(attr, dropout_p) \
_(attr, dropout_seed) \
_(attr, dropout_state) \
_(attr, dst) \
_(attr, dtype) \
_(attr, dual) \
_(attr, dummy) \
_(attr, dx) \
_(attr, edge_order) \
_(attr, eigenvalues) \
_(attr, eigenvectors) \
_(attr, eigvals) \
_(attr, eigvecs) \
_(attr, element) \
_(attr, elements) \
_(attr, ellipsis_idx) \
_(attr, embed_dim) \
_(attr, enable_gqa) \
_(attr, end) \
_(attr, end_dim) \
_(attr, eps) \
_(attr, epsilon) \
_(attr, equal_nan) \
_(attr, equation) \
_(attr, exp_avg_sqs) \
_(attr, exp_avgs) \
_(attr, expand1) \
_(attr, expand2) \
_(attr, expand3) \
_(attr, exponent) \
_(attr, exponential_average_factor) \
_(attr, fake_quant_enabled) \
_(attr, fake_quant_on) \
_(attr, ffn_bias_1) \
_(attr, ffn_bias_2) \
_(attr, ffn_weight_1) \
_(attr, ffn_weight_2) \
_(attr, filename) \
_(attr, fill) \
_(attr, fill_value) \
_(attr, flat) \
_(attr, forward) \
_(attr, found_inf) \
_(attr, from) \
_(attr, from_) \
_(attr, full) \
_(attr, full_matrices) \
_(attr, fuse_transform_0213) \
_(attr, fweights) \
_(attr, g) \
_(attr, gO) \
_(attr, generator) \
_(attr, ggI) \
_(attr, ggW) \
_(attr, ggb) \
_(attr, glu) \
_(attr, grad) \
_(attr, grad_bias) \
_(attr, grad_cy) \
_(attr, grad_factor) \
_(attr, grad_glu) \
_(attr, grad_hy) \
_(attr, grad_in) \
_(attr, grad_input) \
_(attr, grad_input_mask) \
_(attr, grad_out) \
_(attr, grad_out_) \
_(attr, grad_output) \
_(attr, grad_scale) \
_(attr, grad_w) \
_(attr, grad_weight) \
_(attr, grad_x) \
_(attr, grad_y) \
_(attr, gradient) \
_(attr, grads) \
_(attr, grid) \
_(attr, group) \
_(attr, groups) \
_(attr, growth_interval) \
_(attr, growth_tracker) \
_(attr, half_to_float) \
_(attr, has_bias) \
_(attr, has_biases) \
_(attr, hermitian) \
_(attr, hidden_bias) \
_(attr, hidden_gates) \
_(attr, hidden_size) \
_(attr, high) \
_(attr, hist) \
_(attr, hop_length) \
_(attr, hx) \
_(attr, hx_) \
_(attr, hy_) \
_(attr, i1) \
_(attr, i2) \
_(attr, i3) \
_(attr, ignore_index) \
_(attr, imag) \
_(attr, impl_index) \
_(attr, implicit) \
_(attr, in_features) \
_(attr, include_last_offset) \
_(attr, include_self) \
_(attr, increasing) \
_(attr, ind) \
_(attr, index) \
_(attr, index_dtype) \
_(attr, indexing) \
_(attr, indices) \
_(attr, info) \
_(attr, initial) \
_(attr, innerKTiles) \
_(attr, inp) \
_(attr, input) \
_(attr, input1) \
_(attr, input2) \
_(attr, input3) \
_(attr, input_bias) \
_(attr, input_dtype) \
_(attr, input_g) \
_(attr, input_gates) \
_(attr, input_lengths) \
_(attr, input_scale) \
_(attr, input_size) \
_(attr, input_sizes) \
_(attr, input_zero_point) \
_(attr, inputs) \
_(attr, interpolation) \
_(attr, interpolation_mode) \
_(attr, inv_scale) \
_(attr, inverse) \
_(attr, invert) \
_(attr, invstd) \
_(attr, is_causal) \
_(attr, is_coalesced) \
_(attr, is_crow) \
_(attr, is_first_step) \
_(attr, is_matrix) \
_(attr, is_result) \
_(attr, is_target) \
_(attr, k) \
_(attr, keepdim) \
_(attr, kernel_size) \
_(attr, key) \
_(attr, label_smoothing) \
_(attr, lambd) \
_(attr, largest) \
_(attr, last_dim_size) \
_(attr, layersOutputs) \
_(attr, layout) \
_(attr, left) \
_(attr, length) \
_(attr, lengths) \
_(attr, level) \
_(attr, like) \
_(attr, list) \
_(attr, log_alpha) \
_(attr, log_input) \
_(attr, log_probs) \
_(attr, log_target) \
_(attr, logabsdet) \
_(attr, logsumexp) \
_(attr, low) \
_(attr, lower) \
_(attr, lr) \
_(attr, lr_decay) \
_(attr, ltm) \
_(attr, m) \
_(attr, mantissa) \
_(attr, margin) \
_(attr, mask) \
_(attr, mask_check) \
_(attr, mask_type) \
_(attr, masked_grad) \
_(attr, mat) \
_(attr, mat1) \
_(attr, mat1_meta) \
_(attr, mat2) \
_(attr, matrices) \
_(attr, max) \
_(attr, max_exp_avg_sqs) \
_(attr, max_k) \
_(attr, max_lengths) \
_(attr, max_norm) \
_(attr, max_q) \
_(attr, max_seqlen) \
_(attr, max_seqlen_k) \
_(attr, max_seqlen_q) \
_(attr, max_size) \
_(attr, max_val) \
_(attr, max_values) \
_(attr, maximize) \
_(attr, maximum_indices) \
_(attr, maxnorm) \
_(attr, mean) \
_(attr, median) \
_(attr, memory_format) \
_(attr, meta) \
_(attr, min) \
_(attr, min_indices) \
_(attr, min_seqlen) \
_(attr, min_val) \
_(attr, minlength) \
_(attr, mode) \
_(attr, momentum) \
_(attr, momentum_buffer_list) \
_(attr, n) \
_(attr, n_bins) \
_(attr, n_fft) \
_(attr, names) \
_(attr, nan) \
_(attr, need_weights) \
_(attr, neg_log_likelihood) \
_(attr, negative) \
_(attr, negative_slope) \
_(attr, neginf) \
_(attr, nested_size) \
_(attr, nested_strides) \
_(attr, nesterov) \
_(attr, new_data) \
_(attr, nnz) \
_(attr, noise) \
_(attr, non_blocking) \
_(attr, norm) \
_(attr, norm_bias_1) \
_(attr, norm_bias_2) \
_(attr, norm_first) \
_(attr, norm_type) \
_(attr, norm_weight_1) \
_(attr, norm_weight_2) \
_(attr, normalization) \
_(attr, normalized) \
_(attr, normalized_shape) \
_(attr, nt_example) \
_(attr, num_chunks) \
_(attr, num_classes) \
_(attr, num_generated) \
_(attr, num_groups) \
_(attr, num_head) \
_(attr, num_heads) \
_(attr, num_layers) \
_(attr, num_parallel) \
_(attr, num_samples) \
_(attr, num_splits_key) \
_(attr, num_weights) \
_(attr, numel) \
_(attr, observer_on) \
_(attr, offs) \
_(attr, offset) \
_(attr, offset2bag) \
_(attr, offsets) \
_(attr, onesided) \
_(attr, ord) \
_(attr, order) \
_(attr, other) \
_(attr, out) \
_(attr, out0) \
_(attr, out1) \
_(attr, out2) \
_(attr, out3) \
_(attr, out4) \
_(attr, out5) \
_(attr, out6) \
_(attr, out_channel) \
_(attr, out_dim) \
_(attr, out_dtype) \
_(attr, out_features) \
_(attr, out_int32) \
_(attr, outdim) \
_(attr, output) \
_(attr, output_mask) \
_(attr, output_padding) \
_(attr, output_scale) \
_(attr, output_size) \
_(attr, output_zero_point) \
_(attr, p) \
_(attr, packed) \
_(attr, packed_hh) \
_(attr, packed_ih) \
_(attr, packed_weight) \
_(attr, packed_weights) \
_(attr, pad) \
_(attr, pad_mode) \
_(attr, padded) \
_(attr, padding) \
_(attr, padding_idx) \
_(attr, padding_mode) \
_(attr, padding_side) \
_(attr, padding_value) \
_(attr, params) \
_(attr, path) \
_(attr, pdist) \
_(attr, per_row_fake_quant) \
_(attr, per_sample_weights) \
_(attr, periodic) \
_(attr, philox_offset) \
_(attr, philox_seed) \
_(attr, physical_layout) \
_(attr, pin_memory) \
_(attr, pivot) \
_(attr, pivots) \
_(attr, plain_idx) \
_(attr, plain_indices) \
_(attr, pos_weight) \
_(attr, posinf) \
_(attr, positive) \
_(attr, pow) \
_(attr, prepend) \
_(attr, primal) \
_(attr, prob) \
_(attr, proj_bias) \
_(attr, proj_size) \
_(attr, proj_weight) \
_(attr, q) \
_(attr, qGroupSize) \
_(attr, qScaleAndZeros) \
_(attr, qkv) \
_(attr, qkv_bias) \
_(attr, qkv_weight) \
_(attr, qtensor) \
_(attr, quant_max) \
_(attr, quant_min) \
_(attr, quasi) \
_(attr, query) \
_(attr, r) \
_(attr, ragged_idx) \
_(attr, random_samples) \
_(attr, range) \
_(attr, rank) \
_(attr, ratio) \
_(attr, rcond) \
_(attr, real) \
_(attr, reduce) \
_(attr, reduce_range) \
_(attr, reduction) \
_(attr, repeats) \
_(attr, replacement) \
_(attr, requires_grad) \
_(attr, reserve) \
_(attr, reserveSpace) \
_(attr, reservedSpace) \
_(attr, residuals) \
_(attr, result) \
_(attr, retain_graph) \
_(attr, return_complex) \
_(attr, return_counts) \
_(attr, return_debug_mask) \
_(attr, return_inverse) \
_(attr, reverse) \
_(attr, right) \
_(attr, rng_state) \
_(attr, rounding_mode) \
_(attr, row) \
_(attr, row_indices) \
_(attr, rstd) \
_(attr, rtol) \
_(attr, running_max) \
_(attr, running_mean) \
_(attr, running_min) \
_(attr, running_var) \
_(attr, s) \
_(attr, save_invstd) \
_(attr, save_mean) \
_(attr, save_var) \
_(attr, save_var_transform) \
_(attr, saved_g) \
_(attr, saved_norms) \
_(attr, saved_v) \
_(attr, scalar) \
_(attr, scalar1) \
_(attr, scalar2) \
_(attr, scalars) \
_(attr, scale) \
_(attr, scale_a) \
_(attr, scale_b) \
_(attr, scale_backoff_factor) \
_(attr, scale_factors) \
_(attr, scale_grad_by_freq) \
_(attr, scale_growth_factor) \
_(attr, scale_hh) \
_(attr, scale_ih) \
_(attr, scale_result) \
_(attr, scales) \
_(attr, scales_d) \
_(attr, scales_h) \
_(attr, scales_w) \
_(attr, scales_zeros) \
_(attr, sections) \
_(attr, seed) \
_(attr, self) \
_(attr, self_is_result) \
_(attr, self_num_batch_dims) \
_(attr, self_or_result) \
_(attr, self_sizes) \
_(attr, seqlen_k) \
_(attr, sequences) \
_(attr, seqused_k) \
_(attr, shape) \
_(attr, shared) \
_(attr, shared_storage_dqdkdv) \
_(attr, shifts) \
_(attr, side) \
_(attr, sigma) \
_(attr, sign) \
_(attr, singular_values) \
_(attr, size) \
_(attr, sizes) \
_(attr, skip_first) \
_(attr, sobolstate) \
_(attr, solution) \
_(attr, some) \
_(attr, sorted) \
_(attr, sorted_sequence) \
_(attr, sorter) \
_(attr, source) \
_(attr, spacing) \
_(attr, sparse) \
_(attr, sparse_dim) \
_(attr, sparse_grad) \
_(attr, split_k) \
_(attr, split_k_one_kernel) \
_(attr, split_size) \
_(attr, split_sizes) \
_(attr, src) \
_(attr, stable) \
_(attr, start) \
_(attr, start_dim) \
_(attr, state_steps) \
_(attr, state_sums) \
_(attr, std) \
_(attr, step) \
_(attr, steps) \
_(attr, storage_offset) \
_(attr, stride) \
_(attr, sum_S) \
_(attr, sum_dy) \
_(attr, sum_dy_xmu) \
_(attr, sumdim) \
_(attr, swap) \
_(attr, symmetric_quant) \
_(attr, t) \
_(attr, tangent) \
_(attr, target) \
_(attr, target_lengths) \
_(attr, targets) \
_(attr, tau) \
_(attr, tensor) \
_(attr, tensor1) \
_(attr, tensor2) \
_(attr, tensor_indices_or_sections) \
_(attr, tensors) \
_(attr, tensors1) \
_(attr, test_element) \
_(attr, test_elements) \
_(attr, the_template) \
_(attr, theta) \
_(attr, thread_masks) \
_(attr, threshold) \
_(attr, to) \
_(attr, tol) \
_(attr, total) \
_(attr, total_L) \
_(attr, total_length) \
_(attr, total_weight) \
_(attr, train) \
_(attr, training) \
_(attr, transpose) \
_(attr, transpose_result) \
_(attr, transposed) \
_(attr, type1) \
_(attr, type2) \
_(attr, unbiased) \
_(attr, unitriangular) \
_(attr, unpack_data) \
_(attr, unpack_pivots) \
_(attr, unroll_dim) \
_(attr, unsafe) \
_(attr, unused) \
_(attr, update) \
_(attr, upper) \
_(attr, upscale_factor) \
_(attr, use_cutlass) \
_(attr, use_fast_accum) \
_(attr, use_gelu) \
_(attr, use_input_stats) \
_(attr, v) \
_(attr, value) \
_(attr, values) \
_(attr, var) \
_(attr, vec) \
_(attr, vec1) \
_(attr, vec2) \
_(attr, w_hh) \
_(attr, w_ih) \
_(attr, weight) \
_(attr, weight0) \
_(attr, weight1) \
_(attr, weight2) \
_(attr, weight3) \
_(attr, weight4) \
_(attr, weight_arr) \
_(attr, weight_buf) \
_(attr, weight_decay) \
_(attr, weight_g) \
_(attr, weight_scale) \
_(attr, weight_stride0) \
_(attr, weight_zero_point) \
_(attr, weights) \
_(attr, win_length) \
_(attr, window) \
_(attr, window_length) \
_(attr, window_size) \
_(attr, window_size_left) \
_(attr, window_size_right) \
_(attr, with_replacement) \
_(attr, workspace) \
_(attr, wrap) \
_(attr, x) \
_(attr, x1) \
_(attr, x2) \
_(attr, y) \
_(attr, z) \
_(attr, z_state) \
_(attr, zero_infinity) \
_(attr, zero_point) \
_(attr, zero_point_hh) \
_(attr, zero_point_ih) \
_(attr, zero_points)
