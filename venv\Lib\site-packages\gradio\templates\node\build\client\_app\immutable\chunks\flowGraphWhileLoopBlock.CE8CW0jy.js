import{c as l}from"./declarationMapper.UBCwU7BT.js";import{h as n,R as s}from"./index.BoI39RQH.js";import{b as r}from"./KHR_interactivity.DEAVS2UW.js";class t extends r{constructor(i){super(i),this.config=i,this.condition=this.registerDataInput("condition",l),this.executionFlow=this._registerSignalOutput("executionFlow"),this.completed=this._registerSignalOutput("completed"),this._unregisterSignalOutput("out")}_execute(i,h){var a;let o=this.condition.getValue(i);(a=this.config)!=null&&a.doWhile&&!o&&this.executionFlow._activateSignal(i);let e=0;for(;o;){if(this.executionFlow._activateSignal(i),++e,e>=t.MaxLoopCount){n.Warn("FlowGraphWhileLoopBlock: Max loop count reached. Breaking.");break}o=this.condition.getValue(i)}this.completed._activateSignal(i)}getClassName(){return"FlowGraphWhileLoopBlock"}}t.MaxLoopCount=1e3;s("FlowGraphWhileLoopBlock",t);export{t as FlowGraphWhileLoopBlock};
//# sourceMappingURL=flowGraphWhileLoopBlock.CE8CW0jy.js.map
