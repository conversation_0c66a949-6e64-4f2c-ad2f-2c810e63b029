import{x as $s,k as Eo}from"./index-B7J2Z2jS.js";import{g as kp}from"./color-Bp-SbB1W.js";import{h as Gn,i as le,a as z,w as $o,s as fi,$ as B,b as V,c as di,d as A,e as ce,l as Tp,W as Op,f as Ap,m as Ar,g as pi,j as Rp,p as Ip,v as Lp,k as Yl,n as Pp,o as zp,H as Dp,q as ws}from"./vega-tooltip.module-DXXMRpdW.js";import"./svelte/svelte.js";import"./time-Bgyi_H-V.js";import"./step-Ce-xBr2D.js";import"./linear-CV3SENcB.js";import"./init-Dmth1JHB.js";import"./dsv-DB8NKgIY.js";import"./range-OtVwhkKS.js";import"./ordinal-BeghXfj9.js";import"./arc-Ctxh2KTd.js";import"./dispatch-kxCwF96_.js";function jp(e,t,n,i){let r=t.getPropertyValue("--color-accent"),s=t.getPropertyValue("--body-text-color"),o=t.getPropertyValue("--border-color-primary"),a=t.fontFamily,c=t.getPropertyValue("--block-title-text-weight");const l=h=>h.endsWith("px")?parseFloat(h.slice(0,-2)):12;let u=l(t.getPropertyValue("--text-md")),f=l(t.getPropertyValue("--text-sm")),d={autosize:{type:"fit",contains:"padding"},axis:{labelFont:a,labelColor:s,titleFont:a,titleColor:s,tickColor:o,labelFontSize:f,gridColor:o,titleFontWeight:"normal",titleFontSize:f,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:s,labelFont:a,titleColor:s,titleFont:a,titleFontWeight:"normal",titleFontSize:f,labelFontWeight:"normal",offset:2},title:{color:s,font:a,fontSize:u,fontWeight:c,anchor:"middle"},view:{stroke:o}};e.config=d;let p=e.encoding,g=e.layer;switch(n){case"scatter":e.config.mark={stroke:r},p.color&&p.color.type=="nominal"?p.color.scale.range=p.color.scale.range.map((h,m)=>Cs(i,m)):p.color&&p.color.type=="quantitative"&&(p.color.scale.range=["#eff6ff","#1e3a8a"],p.color.scale.range.interpolate="hsl");break;case"line":e.config.mark={stroke:r,cursor:"crosshair"},g.forEach(h=>{h.encoding.color&&(h.encoding.color.scale.range=h.encoding.color.scale.range.map((m,y)=>Cs(i,y)))});break;case"bar":e.config.mark={opacity:.8,fill:r},p.color&&(p.color.scale.range=p.color.scale.range.map((h,m)=>Cs(i,m)));break}return e}function Cs(e,t){let n=e[t%e.length];return n&&n in $s?$s[n]?.primary:n||$s[kp(t)].primary}const Mp="vega-lite",Up='Dominik Moritz, Kanit "Ham" Wongsuphasawat, Arvind Satyanarayan, Jeffrey Heer',Bp="5.12.0",Wp=["Kanit Wongsuphasawat (http://kanitw.yellowpigz.com)","Dominik Moritz (https://www.domoritz.de)","Arvind Satyanarayan (https://arvindsatya.com)","Jeffrey Heer (https://jheer.org)"],Gp="https://vega.github.io/vega-lite/",qp="Vega-Lite is a concise high-level language for interactive visualization.",Hp=["vega","chart","visualization"],Vp="build/vega-lite.js",Xp="build/vega-lite.min.js",Yp="build/vega-lite.min.js",Kp="build/src/index",Jp="build/src/index.d.ts",Qp={vl2pdf:"./bin/vl2pdf",vl2png:"./bin/vl2png",vl2svg:"./bin/vl2svg",vl2vg:"./bin/vl2vg"},Zp=["bin","build","src","vega-lite*","tsconfig.json"],eg={changelog:"conventional-changelog -p angular -r 2",prebuild:"yarn clean:build",build:"yarn build:only","build:only":"tsc -p tsconfig.build.json && rollup -c","prebuild:examples":"yarn build:only","build:examples":"yarn data && TZ=America/Los_Angeles scripts/build-examples.sh","prebuild:examples-full":"yarn build:only","build:examples-full":"TZ=America/Los_Angeles scripts/build-examples.sh 1","build:example":"TZ=America/Los_Angeles scripts/build-example.sh","build:toc":"yarn build:jekyll && scripts/generate-toc","build:site":"rollup -c site/rollup.config.mjs","build:jekyll":"pushd site && bundle exec jekyll build -q && popd","build:versions":"scripts/update-version.sh",clean:"yarn clean:build && del-cli 'site/data/*' 'examples/compiled/*.png' && find site/examples ! -name 'index.md' ! -name 'data' -type f -delete","clean:build":"del-cli 'build/*' !build/vega-lite-schema.json",data:"rsync -r node_modules/vega-datasets/data/* site/data",schema:"mkdir -p build && ts-json-schema-generator -f tsconfig.json -p src/index.ts -t TopLevelSpec --no-type-check --no-ref-encode > build/vega-lite-schema.json && yarn renameschema && cp build/vega-lite-schema.json site/_data/",renameschema:"scripts/rename-schema.sh",presite:"yarn data && yarn schema && yarn build:site && yarn build:versions && scripts/create-example-pages.sh",site:"yarn site:only","site:only":"pushd site && bundle exec jekyll serve -I -l && popd",prettierbase:"prettier '**/*.{md,css,yml}'",format:"eslint . --fix && yarn prettierbase --write",lint:"eslint . && yarn prettierbase --check",jest:"NODE_OPTIONS=--experimental-vm-modules npx jest",test:"yarn jest test/ && yarn lint && yarn schema && yarn jest examples/ && yarn test:runtime","test:cover":"yarn jest --collectCoverage test/","test:inspect":"node --inspect-brk --experimental-vm-modules ./node_modules/.bin/jest --runInBand test","test:runtime":"NODE_OPTIONS=--experimental-vm-modules TZ=America/Los_Angeles npx jest test-runtime/ --config test-runtime/jest-config.json","test:runtime:generate":"yarn build:only && del-cli test-runtime/resources && VL_GENERATE_TESTS=true yarn test:runtime",watch:"tsc -p tsconfig.build.json -w","watch:site":"yarn build:site -w","watch:test":"yarn jest --watch test/","watch:test:runtime":"NODE_OPTIONS=--experimental-vm-modules TZ=America/Los_Angeles npx jest --watch test-runtime/ --config test-runtime/jest-config.json",release:"release-it"},tg={type:"git",url:"https://github.com/vega/vega-lite.git"},ng="BSD-3-Clause",ig={url:"https://github.com/vega/vega-lite/issues"},rg={"@babel/core":"^7.21.8","@babel/plugin-proposal-class-properties":"^7.18.6","@babel/preset-env":"^7.21.5","@babel/preset-typescript":"^7.21.5","@release-it/conventional-changelog":"^5.1.1","@rollup/plugin-alias":"^5.0.0","@rollup/plugin-babel":"^6.0.3","@rollup/plugin-commonjs":"^25.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.0.2","@rollup/plugin-terser":"^0.4.1","@types/chai":"^4.3.5","@types/d3":"^7.4.0","@types/jest":"^27.4.1","@types/pako":"^2.0.0","@typescript-eslint/eslint-plugin":"^5.59.5","@typescript-eslint/parser":"^5.59.5",ajv:"^8.12.0","ajv-formats":"^2.1.1",chai:"^4.3.7",cheerio:"^1.0.0-rc.12","conventional-changelog-cli":"^3.0.0",d3:"^7.8.4","del-cli":"^5.0.0",eslint:"^8.40.0","eslint-config-prettier":"^8.8.0","eslint-plugin-jest":"^27.2.1","eslint-plugin-prettier":"^4.2.1","highlight.js":"^11.8.0",jest:"^27.5.1","jest-dev-server":"^6.1.1",mkdirp:"^3.0.1",pako:"^2.1.0",prettier:"^2.8.8",puppeteer:"^15.0.0","release-it":"^15.10.3",rollup:"^3.21.6","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-sourcemaps":"^0.6.3",serve:"^14.2.0",terser:"^5.17.3","ts-jest":"^29.1.0","ts-json-schema-generator":"^1.2.0",typescript:"~4.9.5","vega-cli":"^5.25.0","vega-datasets":"^2.7.0","vega-embed":"^6.22.1","vega-tooltip":"^0.32.0","yaml-front-matter":"^4.1.1"},sg={"@types/clone":"~2.1.1",clone:"~2.1.2","fast-deep-equal":"~3.1.3","fast-json-stable-stringify":"~2.1.0","json-stringify-pretty-compact":"~3.0.0",tslib:"~2.5.0","vega-event-selector":"~3.0.1","vega-expression":"~5.1.0","vega-util":"~1.17.2",yargs:"~17.7.2"},og={vega:"^5.24.0"},ag={node:">=16"},cg={name:Mp,author:Up,version:Bp,collaborators:Wp,homepage:Gp,description:qp,keywords:Hp,main:Vp,unpkg:Xp,jsdelivr:Yp,module:Kp,types:Jp,bin:Qp,files:Zp,scripts:eg,repository:tg,license:ng,bugs:ig,devDependencies:rg,dependencies:sg,peerDependencies:og,engines:ag};var Kl={exports:{}};(function(e){var t=function(){function n(d,p){return p!=null&&d instanceof p}var i;try{i=Map}catch{i=function(){}}var r;try{r=Set}catch{r=function(){}}var s;try{s=Promise}catch{s=function(){}}function o(d,p,g,h,m){typeof p=="object"&&(g=p.depth,h=p.prototype,m=p.includeNonEnumerable,p=p.circular);var y=[],b=[],k=typeof Buffer<"u";typeof p>"u"&&(p=!0),typeof g>"u"&&(g=1/0);function T(x,_){if(x===null)return null;if(_===0)return x;var C,N;if(typeof x!="object")return x;if(n(x,i))C=new i;else if(n(x,r))C=new r;else if(n(x,s))C=new s(function(P,O){x.then(function(D){P(T(D,_-1))},function(D){O(T(D,_-1))})});else if(o.__isArray(x))C=[];else if(o.__isRegExp(x))C=new RegExp(x.source,f(x)),x.lastIndex&&(C.lastIndex=x.lastIndex);else if(o.__isDate(x))C=new Date(x.getTime());else{if(k&&Buffer.isBuffer(x))return Buffer.allocUnsafe?C=Buffer.allocUnsafe(x.length):C=new Buffer(x.length),x.copy(C),C;n(x,Error)?C=Object.create(x):typeof h>"u"?(N=Object.getPrototypeOf(x),C=Object.create(N)):(C=Object.create(h),N=h)}if(p){var Y=y.indexOf(x);if(Y!=-1)return b[Y];y.push(x),b.push(C)}n(x,i)&&x.forEach(function(P,O){var D=T(O,_-1),q=T(P,_-1);C.set(D,q)}),n(x,r)&&x.forEach(function(P){var O=T(P,_-1);C.add(O)});for(var ne in x){var de;N&&(de=Object.getOwnPropertyDescriptor(N,ne)),!(de&&de.set==null)&&(C[ne]=T(x[ne],_-1))}if(Object.getOwnPropertySymbols)for(var Se=Object.getOwnPropertySymbols(x),ne=0;ne<Se.length;ne++){var F=Se[ne],$=Object.getOwnPropertyDescriptor(x,F);$&&!$.enumerable&&!m||(C[F]=T(x[F],_-1),$.enumerable||Object.defineProperty(C,F,{enumerable:!1}))}if(m)for(var L=Object.getOwnPropertyNames(x),ne=0;ne<L.length;ne++){var R=L[ne],$=Object.getOwnPropertyDescriptor(x,R);$&&$.enumerable||(C[R]=T(x[R],_-1),Object.defineProperty(C,R,{enumerable:!1}))}return C}return T(d,g)}o.clonePrototype=function(p){if(p===null)return null;var g=function(){};return g.prototype=p,new g};function a(d){return Object.prototype.toString.call(d)}o.__objToStr=a;function c(d){return typeof d=="object"&&a(d)==="[object Date]"}o.__isDate=c;function l(d){return typeof d=="object"&&a(d)==="[object Array]"}o.__isArray=l;function u(d){return typeof d=="object"&&a(d)==="[object RegExp]"}o.__isRegExp=u;function f(d){var p="";return d.global&&(p+="g"),d.ignoreCase&&(p+="i"),d.multiline&&(p+="m"),p}return o.__getRegExpFlags=f,o}();e.exports&&(e.exports=t)})(Kl);var lg=Kl.exports;const ug=Eo(lg);var fg=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var i,r,s;if(Array.isArray(t)){if(i=t.length,i!=n.length)return!1;for(r=i;r--!==0;)if(!e(t[r],n[r]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(s=Object.keys(t),i=s.length,i!==Object.keys(n).length)return!1;for(r=i;r--!==0;)if(!Object.prototype.hasOwnProperty.call(n,s[r]))return!1;for(r=i;r--!==0;){var o=s[r];if(!e(t[o],n[o]))return!1}return!0}return t!==t&&n!==n};const dg=Eo(fg);var pg=function(e,t){t||(t={}),typeof t=="function"&&(t={cmp:t});var n=typeof t.cycles=="boolean"?t.cycles:!1,i=t.cmp&&function(s){return function(o){return function(a,c){var l={key:a,value:o[a]},u={key:c,value:o[c]};return s(l,u)}}}(t.cmp),r=[];return function s(o){if(o&&o.toJSON&&typeof o.toJSON=="function"&&(o=o.toJSON()),o!==void 0){if(typeof o=="number")return isFinite(o)?""+o:"null";if(typeof o!="object")return JSON.stringify(o);var a,c;if(Array.isArray(o)){for(c="[",a=0;a<o.length;a++)a&&(c+=","),c+=s(o[a])||"null";return c+"]"}if(o===null)return"null";if(r.indexOf(o)!==-1){if(n)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var l=r.push(o)-1,u=Object.keys(o).sort(i&&i(o));for(c="",a=0;a<u.length;a++){var f=u[a],d=s(o[f]);d&&(c&&(c+=","),c+=JSON.stringify(f)+":"+d)}return r.splice(l,1),"{"+c+"}"}}(e)};const wo=Eo(pg);function Co(e){return!!e.or}function Fo(e){return!!e.and}function No(e){return!!e.not}function pr(e,t){if(No(e))pr(e.not,t);else if(Fo(e))for(const n of e.and)pr(n,t);else if(Co(e))for(const n of e.or)pr(n,t);else t(e)}function qn(e,t){return No(e)?{not:qn(e.not,t)}:Fo(e)?{and:e.and.map(n=>qn(n,t))}:Co(e)?{or:e.or.map(n=>qn(n,t))}:t(e)}const lt=dg,j=ug;function Jl(e){throw new Error(e)}function Jn(e,t){const n={};for(const i of t)Gn(e,i)&&(n[i]=e[i]);return n}function ke(e,t){const n={...e};for(const i of t)delete n[i];return n}Set.prototype.toJSON=function(){return`Set(${[...this].map(e=>wo(e)).join(",")})`};const ee=wo;function W(e){if(le(e))return e;const t=z(e)?e:wo(e);if(t.length<250)return t;let n=0;for(let i=0;i<t.length;i++){const r=t.charCodeAt(i);n=(n<<5)-n+r,n=n&n}return n}function Vs(e){return e===!1||e===null}function G(e,t){return e.includes(t)}function xn(e,t){let n=0;for(const[i,r]of e.entries())if(t(r,i,n++))return!0;return!1}function _o(e,t){let n=0;for(const[i,r]of e.entries())if(!t(r,i,n++))return!1;return!0}function Ql(e,...t){for(const n of t)gg(e,n??{});return e}function gg(e,t){for(const n of v(t))$o(e,n,t[n],!0)}function ut(e,t){const n=[],i={};let r;for(const s of e)r=t(s),!(r in i)&&(i[r]=1,n.push(s));return n}function hg(e,t){const n=v(e),i=v(t);if(n.length!==i.length)return!1;for(const r of n)if(e[r]!==t[r])return!1;return!0}function Zl(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function ko(e,t){for(const n of e)if(t.has(n))return!0;return!1}function Xs(e){const t=new Set;for(const n of e){const r=fi(n).map((o,a)=>a===0?o:`[${o}]`),s=r.map((o,a)=>r.slice(0,a+1).join(""));for(const o of s)t.add(o)}return t}function To(e,t){return e===void 0||t===void 0?!0:ko(Xs(e),Xs(t))}function J(e){return v(e).length===0}const v=Object.keys,ve=Object.values,Gt=Object.entries;function Pi(e){return e===!0||e===!1}function se(e){const t=e.replace(/\W/g,"_");return(e.match(/^\d+/)?"_":"")+t}function Ai(e,t){return No(e)?`!(${Ai(e.not,t)})`:Fo(e)?`(${e.and.map(n=>Ai(n,t)).join(") && (")})`:Co(e)?`(${e.or.map(n=>Ai(n,t)).join(") || (")})`:t(e)}function gr(e,t){if(t.length===0)return!0;const n=t.shift();return n in e&&gr(e[n],t)&&delete e[n],J(e)}function Wi(e){return e.charAt(0).toUpperCase()+e.substr(1)}function Oo(e,t="datum"){const n=fi(e),i=[];for(let r=1;r<=n.length;r++){const s=`[${n.slice(0,r).map(B).join("][")}]`;i.push(`${t}${s}`)}return i.join(" && ")}function eu(e,t="datum"){return`${t}[${B(fi(e).join("."))}]`}function mg(e){return e.replace(/(\[|\]|\.|'|")/g,"\\$1")}function Me(e){return`${fi(e).map(mg).join("\\.")}`}function vn(e,t,n){return e.replace(new RegExp(t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"g"),n)}function Ao(e){return`${fi(e).join(".")}`}function Qn(e){return e?fi(e).length:0}function ue(...e){for(const t of e)if(t!==void 0)return t}let tu=42;function nu(e){const t=++tu;return e?String(e)+t:t}function yg(){tu=42}function iu(e){return ru(e)?e:`__${e}`}function ru(e){return e.startsWith("__")}function zi(e){if(e!==void 0)return(e%360+360)%360}function Rr(e){return le(e)?!0:!isNaN(e)&&!isNaN(parseFloat(e))}const Nt="row",_t="column",Ir="facet",oe="x",me="y",et="x2",yt="y2",Kt="xOffset",gi="yOffset",tt="radius",Rt="radius2",Be="theta",It="theta2",nt="latitude",it="longitude",rt="latitude2",Ue="longitude2",Ae="color",bt="fill",xt="stroke",Re="shape",Lt="size",Tn="angle",Pt="opacity",Jt="fillOpacity",Qt="strokeOpacity",Zt="strokeWidth",en="strokeDash",Gi="text",Zn="order",qi="detail",Lr="key",Sn="tooltip",Pr="href",zr="url",Dr="description",bg={x:1,y:1,x2:1,y2:1},su={theta:1,theta2:1,radius:1,radius2:1};function ou(e){return e in su}const Ro={longitude:1,longitude2:1,latitude:1,latitude2:1};function au(e){switch(e){case nt:return"y";case rt:return"y2";case it:return"x";case Ue:return"x2"}}function cu(e){return e in Ro}const xg=v(Ro),Io={...bg,...su,...Ro,xOffset:1,yOffset:1,color:1,fill:1,stroke:1,opacity:1,fillOpacity:1,strokeOpacity:1,strokeWidth:1,strokeDash:1,size:1,angle:1,shape:1,order:1,text:1,detail:1,key:1,tooltip:1,href:1,url:1,description:1};function Hn(e){return e===Ae||e===bt||e===xt}const lu={row:1,column:1,facet:1},je=v(lu),Lo={...Io,...lu},vg=v(Lo),{order:Cw,detail:Fw,tooltip:Nw,...Sg}=Lo,{row:_w,column:kw,facet:Tw,...Eg}=Sg;function $g(e){return!!Eg[e]}function uu(e){return!!Lo[e]}const wg=[et,yt,rt,Ue,It,Rt];function fu(e){return On(e)!==e}function On(e){switch(e){case et:return oe;case yt:return me;case rt:return nt;case Ue:return it;case It:return Be;case Rt:return tt}return e}function qt(e){if(ou(e))switch(e){case Be:return"startAngle";case It:return"endAngle";case tt:return"outerRadius";case Rt:return"innerRadius"}return e}function vt(e){switch(e){case oe:return et;case me:return yt;case nt:return rt;case it:return Ue;case Be:return It;case tt:return Rt}}function Ie(e){switch(e){case oe:case et:return"width";case me:case yt:return"height"}}function du(e){switch(e){case oe:return"xOffset";case me:return"yOffset";case et:return"x2Offset";case yt:return"y2Offset";case Be:return"thetaOffset";case tt:return"radiusOffset";case It:return"theta2Offset";case Rt:return"radius2Offset"}}function Po(e){switch(e){case oe:return"xOffset";case me:return"yOffset"}}function pu(e){switch(e){case"xOffset":return"x";case"yOffset":return"y"}}const Cg=v(Io),{x:Ow,y:Aw,x2:Rw,y2:Iw,xOffset:Lw,yOffset:Pw,latitude:zw,longitude:Dw,latitude2:jw,longitude2:Mw,theta:Uw,theta2:Bw,radius:Ww,radius2:Gw,...zo}=Io,Fg=v(zo),Do={x:1,y:1},St=v(Do);function ye(e){return e in Do}const jo={theta:1,radius:1},Ng=v(jo);function jr(e){return e==="width"?oe:me}const gu={xOffset:1,yOffset:1};function hi(e){return e in gu}const{text:qw,tooltip:Hw,href:Vw,url:Xw,description:Yw,detail:Kw,key:Jw,order:Qw,...hu}=zo,_g=v(hu);function kg(e){return!!zo[e]}function Tg(e){switch(e){case Ae:case bt:case xt:case Lt:case Re:case Pt:case Zt:case en:return!0;case Jt:case Qt:case Tn:return!1}}const mu={...Do,...jo,...gu,...hu},Mr=v(mu);function zt(e){return!!mu[e]}function Og(e,t){return Rg(e)[t]}const yu={arc:"always",area:"always",bar:"always",circle:"always",geoshape:"always",image:"always",line:"always",rule:"always",point:"always",rect:"always",square:"always",trail:"always",text:"always",tick:"always"},{geoshape:Zw,...Ag}=yu;function Rg(e){switch(e){case Ae:case bt:case xt:case Dr:case qi:case Lr:case Sn:case Pr:case Zn:case Pt:case Jt:case Qt:case Zt:case Ir:case Nt:case _t:return yu;case oe:case me:case Kt:case gi:case nt:case it:return Ag;case et:case yt:case rt:case Ue:return{area:"always",bar:"always",image:"always",rect:"always",rule:"always",circle:"binned",point:"binned",square:"binned",tick:"binned",line:"binned",trail:"binned"};case Lt:return{point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",text:"always",line:"always",trail:"always"};case en:return{line:"always",point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",geoshape:"always"};case Re:return{point:"always",geoshape:"always"};case Gi:return{text:"always"};case Tn:return{point:"always",square:"always",text:"always"};case zr:return{image:"always"};case Be:return{text:"always",arc:"always"};case tt:return{text:"always",arc:"always"};case It:case Rt:return{arc:"always"}}}function Fs(e){switch(e){case oe:case me:case Be:case tt:case Kt:case gi:case Lt:case Tn:case Zt:case Pt:case Jt:case Qt:case et:case yt:case It:case Rt:return;case Ir:case Nt:case _t:case Re:case en:case Gi:case Sn:case Pr:case zr:case Dr:return"discrete";case Ae:case bt:case xt:return"flexible";case nt:case it:case rt:case Ue:case qi:case Lr:case Zn:return}}const Ig={argmax:1,argmin:1,average:1,count:1,distinct:1,product:1,max:1,mean:1,median:1,min:1,missing:1,q1:1,q3:1,ci0:1,ci1:1,stderr:1,stdev:1,stdevp:1,sum:1,valid:1,values:1,variance:1,variancep:1},Lg={count:1,min:1,max:1};function Ot(e){return!!e&&!!e.argmin}function tn(e){return!!e&&!!e.argmax}function Mo(e){return z(e)&&!!Ig[e]}const Pg=new Set(["count","valid","missing","distinct"]);function bu(e){return z(e)&&Pg.has(e)}function zg(e){return z(e)&&G(["min","max"],e)}const Dg=new Set(["count","sum","distinct","valid","missing"]),jg=new Set(["mean","average","median","q1","q3","min","max"]);function xu(e){return di(e)&&(e=Qr(e,void 0)),"bin"+v(e).map(t=>Ur(e[t])?se(`_${t}_${Gt(e[t])}`):se(`_${t}_${e[t]}`)).join("")}function te(e){return e===!0||An(e)&&!e.binned}function xe(e){return e==="binned"||An(e)&&e.binned===!0}function An(e){return V(e)}function Ur(e){return e?.param}function ac(e){switch(e){case Nt:case _t:case Lt:case Ae:case bt:case xt:case Zt:case Pt:case Jt:case Qt:case Re:return 6;case en:return 4;default:return 10}}function Hi(e){return!!e?.expr}function _e(e){const t=v(e||{}),n={};for(const i of t)n[i]=Pe(e[i]);return n}function vu(e){const{anchor:t,frame:n,offset:i,orient:r,angle:s,limit:o,color:a,subtitleColor:c,subtitleFont:l,subtitleFontSize:u,subtitleFontStyle:f,subtitleFontWeight:d,subtitleLineHeight:p,subtitlePadding:g,...h}=e,m={...h,...a?{fill:a}:{}},y={...t?{anchor:t}:{},...n?{frame:n}:{},...i?{offset:i}:{},...r?{orient:r}:{},...s!==void 0?{angle:s}:{},...o!==void 0?{limit:o}:{}},b={...c?{subtitleColor:c}:{},...l?{subtitleFont:l}:{},...u?{subtitleFontSize:u}:{},...f?{subtitleFontStyle:f}:{},...d?{subtitleFontWeight:d}:{},...p?{subtitleLineHeight:p}:{},...g?{subtitlePadding:g}:{}},k=Jn(e,["align","baseline","dx","dy","limit"]);return{titleMarkConfig:m,subtitleMarkConfig:k,nonMarkTitleProperties:y,subtitle:b}}function Ut(e){return z(e)||A(e)&&z(e[0])}function I(e){return!!e?.signal}function nn(e){return!!e.step}function Mg(e){return A(e)?!1:"fields"in e&&!("data"in e)}function Ug(e){return A(e)?!1:"fields"in e&&"data"in e}function Ft(e){return A(e)?!1:"field"in e&&"data"in e}const Bg={aria:1,description:1,ariaRole:1,ariaRoleDescription:1,blend:1,opacity:1,fill:1,fillOpacity:1,stroke:1,strokeCap:1,strokeWidth:1,strokeOpacity:1,strokeDash:1,strokeDashOffset:1,strokeJoin:1,strokeOffset:1,strokeMiterLimit:1,startAngle:1,endAngle:1,padAngle:1,innerRadius:1,outerRadius:1,size:1,shape:1,interpolate:1,tension:1,orient:1,align:1,baseline:1,text:1,dir:1,dx:1,dy:1,ellipsis:1,limit:1,radius:1,theta:1,angle:1,font:1,fontSize:1,fontWeight:1,fontStyle:1,lineBreak:1,lineHeight:1,cursor:1,href:1,tooltip:1,cornerRadius:1,cornerRadiusTopLeft:1,cornerRadiusTopRight:1,cornerRadiusBottomLeft:1,cornerRadiusBottomRight:1,aspect:1,width:1,height:1,url:1,smooth:1},Wg=v(Bg),Gg={arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1},Ys=["cornerRadius","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight"];function Su(e){const t=A(e.condition)?e.condition.map(cc):cc(e.condition);return{...Pe(e),condition:t}}function Pe(e){if(Hi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return e}function cc(e){if(Hi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return e}function re(e){if(Hi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return I(e)?e:e!==void 0?{value:e}:void 0}function qg(e){return I(e)?e.signal:B(e)}function lc(e){return I(e)?e.signal:B(e.value)}function Ve(e){return I(e)?e.signal:e==null?null:B(e)}function Hg(e,t,n){for(const i of n){const r=At(i,t.markDef,t.config);r!==void 0&&(e[i]=re(r))}return e}function Eu(e){return[].concat(e.type,e.style??[])}function X(e,t,n,i={}){const{vgChannel:r,ignoreVgConfig:s}=i;return r&&t[r]!==void 0?t[r]:t[e]!==void 0?t[e]:s&&(!r||r===e)?void 0:At(e,t,n,i)}function At(e,t,n,{vgChannel:i}={}){return ue(i?hr(e,t,n.style):void 0,hr(e,t,n.style),i?n[t.type][i]:void 0,n[t.type][e],i?n.mark[i]:n.mark[e])}function hr(e,t,n){return $u(e,Eu(t),n)}function $u(e,t,n){t=ce(t);let i;for(const r of t){const s=n[r];s&&s[e]!==void 0&&(i=s[e])}return i}function wu(e,t){return ce(e).reduce((n,i)=>(n.field.push(w(i,t)),n.order.push(i.sort??"ascending"),n),{field:[],order:[]})}function Cu(e,t){const n=[...e];return t.forEach(i=>{for(const r of n)if(lt(r,i))return;n.push(i)}),n}function Fu(e,t){return lt(e,t)||!t?e:e?[...ce(e),...ce(t)].join(", "):t}function Nu(e,t){const n=e.value,i=t.value;if(n==null||i===null)return{explicit:e.explicit,value:null};if((Ut(n)||I(n))&&(Ut(i)||I(i)))return{explicit:e.explicit,value:Fu(n,i)};if(Ut(n)||I(n))return{explicit:e.explicit,value:n};if(Ut(i)||I(i))return{explicit:e.explicit,value:i};if(!Ut(n)&&!I(n)&&!Ut(i)&&!I(i))return{explicit:e.explicit,value:Cu(n,i)};throw new Error("It should never reach here")}function Uo(e){return`Invalid specification ${ee(e)}. Make sure the specification includes at least one of the following properties: "mark", "layer", "facet", "hconcat", "vconcat", "concat", or "repeat".`}const Vg='Autosize "fit" only works for single views and layered views.';function uc(e){return`${e=="width"?"Width":"Height"} "container" only works for single views and layered views.`}function fc(e){const t=e=="width"?"Width":"Height",n=e=="width"?"x":"y";return`${t} "container" only works well with autosize "fit" or "fit-${n}".`}function dc(e){return e?`Dropping "fit-${e}" because spec has discrete ${Ie(e)}.`:'Dropping "fit" because spec has discrete size.'}function Bo(e){return`Unknown field for ${e}. Cannot calculate view size.`}function pc(e){return`Cannot project a selection on encoding channel "${e}", which has no field.`}function Xg(e,t){return`Cannot project a selection on encoding channel "${e}" as it uses an aggregate function ("${t}").`}function Yg(e){return`The "nearest" transform is not supported for ${e} marks.`}function _u(e){return`Selection not supported for ${e} yet.`}function Kg(e){return`Cannot find a selection named "${e}".`}const Jg="Scale bindings are currently only supported for scales with unbinned, continuous domains.",Qg="Legend bindings are only supported for selections over an individual field or encoding channel.";function Zg(e){return`Lookups can only be performed on selection parameters. "${e}" is a variable parameter.`}function eh(e){return`Cannot define and lookup the "${e}" selection in the same view. Try moving the lookup into a second, layered view?`}const th="The same selection must be used to override scale domains in a layered view.",nh='Interval selections should be initialized using "x", "y", "longitude", or "latitude" keys.';function ih(e){return`Unknown repeated value "${e}".`}function gc(e){return`The "columns" property cannot be used when "${e}" has nested row/column.`}const rh="Axes cannot be shared in concatenated or repeated views yet (https://github.com/vega/vega-lite/issues/2415).";function sh(e){return`Unrecognized parse "${e}".`}function hc(e,t,n){return`An ancestor parsed field "${e}" as ${n} but a child wants to parse the field as ${t}.`}const oh="Attempt to add the same child twice.";function ah(e){return`Ignoring an invalid transform: ${ee(e)}.`}const ch='If "from.fields" is not specified, "as" has to be a string that specifies the key to be used for the data from the secondary source.';function mc(e){return`Config.customFormatTypes is not true, thus custom format type and format for channel ${e} are dropped.`}function lh(e){const{parentProjection:t,projection:n}=e;return`Layer's shared projection ${ee(t)} is overridden by a child projection ${ee(n)}.`}const uh="Arc marks uses theta channel rather than angle, replacing angle with theta.";function fh(e){return`${e}Offset dropped because ${e} is continuous`}function dh(e){return`There is no ${e} encoding. Replacing ${e}Offset encoding as ${e}.`}function ph(e,t,n){return`Channel ${e} is a ${t}. Converted to {value: ${ee(n)}}.`}function ku(e){return`Invalid field type "${e}".`}function gh(e,t){return`Invalid field type "${e}" for aggregate: "${t}", using "quantitative" instead.`}function hh(e){return`Invalid aggregation operator "${e}".`}function Tu(e,t){const{fill:n,stroke:i}=t;return`Dropping color ${e} as the plot also has ${n&&i?"fill and stroke":n?"fill":"stroke"}.`}function mh(e){return`Position range does not support relative band size for ${e}.`}function Ks(e,t){return`Dropping ${ee(e)} from channel "${t}" since it does not contain any data field, datum, value, or signal.`}const yh="Line marks cannot encode size with a non-groupby field. You may want to use trail marks instead.";function Br(e,t,n){return`${e} dropped as it is incompatible with "${t}".`}function bh(e){return`${e} encoding has no scale, so specified scale is ignored.`}function xh(e){return`${e}-encoding is dropped as ${e} is not a valid encoding channel.`}function vh(e){return`${e} encoding should be discrete (ordinal / nominal / binned).`}function Sh(e){return`${e} encoding should be discrete (ordinal / nominal / binned) or use a discretizing scale (e.g. threshold).`}function Eh(e){return`Facet encoding dropped as ${e.join(" and ")} ${e.length>1?"are":"is"} also specified.`}function Ns(e,t){return`Using discrete channel "${e}" to encode "${t}" field can be misleading as it does not encode ${t==="ordinal"?"order":"magnitude"}.`}function $h(e){return`The ${e} for range marks cannot be an expression`}function wh(e,t){return`Line mark is for continuous lines and thus cannot be used with ${e&&t?"x2 and y2":e?"x2":"y2"}. We will use the rule mark (line segments) instead.`}function Ch(e,t){return`Specified orient "${e}" overridden with "${t}".`}function Fh(e){return`Cannot use the scale property "${e}" with non-color channel.`}function Nh(e){return`Cannot use the relative band size with ${e} scale.`}function _h(e){return`Using unaggregated domain with raw field has no effect (${ee(e)}).`}function kh(e){return`Unaggregated domain not applicable for "${e}" since it produces values outside the origin domain of the source data.`}function Th(e){return`Unaggregated domain is currently unsupported for log scale (${ee(e)}).`}function Oh(e){return`Cannot apply size to non-oriented mark "${e}".`}function Ah(e,t,n){return`Channel "${e}" does not work with "${t}" scale. We are using "${n}" scale instead.`}function Rh(e,t){return`FieldDef does not work with "${e}" scale. We are using "${t}" scale instead.`}function Ou(e,t,n){return`${n}-scale's "${t}" is dropped as it does not work with ${e} scale.`}function Au(e){return`The step for "${e}" is dropped because the ${e==="width"?"x":"y"} is continuous.`}function Ih(e,t,n,i){return`Conflicting ${t.toString()} property "${e.toString()}" (${ee(n)} and ${ee(i)}). Using ${ee(n)}.`}function Lh(e,t,n,i){return`Conflicting ${t.toString()} property "${e.toString()}" (${ee(n)} and ${ee(i)}). Using the union of the two domains.`}function Ph(e){return`Setting the scale to be independent for "${e}" means we also have to set the guide (axis or legend) to be independent.`}function zh(e){return`Dropping sort property ${ee(e)} as unioned domains only support boolean or op "count", "min", and "max".`}const yc="Domains that should be unioned has conflicting sort properties. Sort will be set to true.",Dh="Detected faceted independent scales that union domain of multiple fields from different data sources. We will use the first field. The result view size may be incorrect.",jh="Detected faceted independent scales that union domain of the same fields from different source. We will assume that this is the same field from a different fork of the same data source. However, if this is not the case, the result view size may be incorrect.",Mh="Detected faceted independent scales that union domain of multiple fields from the same data source. We will use the first field. The result view size may be incorrect.";function Uh(e){return`Cannot stack "${e}" if there is already "${e}2".`}function Bh(e){return`Cannot stack non-linear scale (${e}).`}function Wh(e){return`Stacking is applied even though the aggregate function is non-summative ("${e}").`}function mr(e,t){return`Invalid ${e}: ${ee(t)}.`}function Gh(e){return`Dropping day from datetime ${ee(e)} as day cannot be combined with other units.`}function qh(e,t){return`${t?"extent ":""}${t&&e?"and ":""}${e?"center ":""}${t&&e?"are ":"is "}not needed when data are aggregated.`}function Hh(e,t,n){return`${e} is not usually used with ${t} for ${n}.`}function Vh(e,t){return`Continuous axis should not have customized aggregation function ${e}; ${t} already agregates the axis.`}function bc(e){return`1D error band does not support ${e}.`}function Ru(e){return`Channel ${e} is required for "binned" bin.`}function Xh(e){return`Channel ${e} should not be used with "binned" bin.`}function Yh(e){return`Domain for ${e} is required for threshold scale.`}const Iu=Tp(Op);let ei=Iu;function Kh(e){return ei=e,ei}function Jh(){return ei=Iu,ei}function S(...e){ei.warn(...e)}function Qh(...e){ei.debug(...e)}function Rn(e){if(e&&V(e)){for(const t of Go)if(t in e)return!0}return!1}const Lu=["january","february","march","april","may","june","july","august","september","october","november","december"],Zh=Lu.map(e=>e.substr(0,3)),Pu=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"],em=Pu.map(e=>e.substr(0,3));function tm(e){if(Rr(e)&&(e=+e),le(e))return e>4&&S(mr("quarter",e)),e-1;throw new Error(mr("quarter",e))}function nm(e){if(Rr(e)&&(e=+e),le(e))return e-1;{const t=e.toLowerCase(),n=Lu.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=Zh.indexOf(i);if(r!==-1)return r;throw new Error(mr("month",e))}}function im(e){if(Rr(e)&&(e=+e),le(e))return e%7;{const t=e.toLowerCase(),n=Pu.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=em.indexOf(i);if(r!==-1)return r;throw new Error(mr("day",e))}}function Wo(e,t){const n=[];if(t&&e.day!==void 0&&v(e).length>1&&(S(Gh(e)),e=j(e),delete e.day),e.year!==void 0?n.push(e.year):n.push(2012),e.month!==void 0){const i=t?nm(e.month):e.month;n.push(i)}else if(e.quarter!==void 0){const i=t?tm(e.quarter):e.quarter;n.push(le(i)?i*3:`${i}*3`)}else n.push(0);if(e.date!==void 0)n.push(e.date);else if(e.day!==void 0){const i=t?im(e.day):e.day;n.push(le(i)?i+1:`${i}+1`)}else n.push(1);for(const i of["hours","minutes","seconds","milliseconds"]){const r=e[i];n.push(typeof r>"u"?0:r)}return n}function En(e){const n=Wo(e,!0).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function rm(e){const n=Wo(e,!1).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function sm(e){const t=Wo(e,!0);return e.utc?+new Date(Date.UTC(...t)):+new Date(...t)}const zu={year:1,quarter:1,month:1,week:1,day:1,dayofyear:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1},Go=v(zu);function om(e){return!!zu[e]}function In(e){return V(e)?e.binned:Du(e)}function Du(e){return e&&e.startsWith("binned")}function qo(e){return e.startsWith("utc")}function am(e){return e.substring(3)}const cm={"year-month":"%b %Y ","year-month-date":"%b %d, %Y "};function Wr(e){return Go.filter(t=>Mu(e,t))}function ju(e){const t=Wr(e);return t[t.length-1]}function Mu(e,t){const n=e.indexOf(t);return!(n<0||n>0&&t==="seconds"&&e.charAt(n-1)==="i"||e.length>n+3&&t==="day"&&e.charAt(n+3)==="o"||n>0&&t==="year"&&e.charAt(n-1)==="f")}function lm(e,t,{end:n}={end:!1}){const i=Oo(t),r=qo(e)?"utc":"";function s(c){return c==="quarter"?`(${r}quarter(${i})-1)`:`${r}${c}(${i})`}let o;const a={};for(const c of Go)Mu(e,c)&&(a[c]=s(c),o=c);return n&&(a[o]+="+1"),rm(a)}function Uu(e){if(!e)return;const t=Wr(e);return`timeUnitSpecifier(${ee(t)}, ${ee(cm)})`}function um(e,t,n){if(!e)return;const i=Uu(e);return`${n||qo(e)?"utc":"time"}Format(${t}, ${i})`}function he(e){if(!e)return;let t;return z(e)?Du(e)?t={unit:e.substring(6),binned:!0}:t={unit:e}:V(e)&&(t={...e,...e.unit?{unit:e.unit}:{}}),qo(t.unit)&&(t.utc=!0,t.unit=am(t.unit)),t}function fm(e){const{utc:t,...n}=he(e);return n.unit?(t?"utc":"")+v(n).map(i=>se(`${i==="unit"?"":`_${i}_`}${n[i]}`)).join(""):(t?"utc":"")+"timeunit"+v(n).map(i=>se(`_${i}_${n[i]}`)).join("")}function Bu(e,t=n=>n){const n=he(e),i=ju(n.unit);if(i&&i!=="day"){const r={year:2001,month:1,date:1,hours:0,minutes:0,seconds:0,milliseconds:0},{step:s,part:o}=Wu(i,n.step),a={...r,[o]:+r[o]+s};return`${t(En(a))} - ${t(En(r))}`}}const dm={year:1,month:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1};function pm(e){return!!dm[e]}function Wu(e,t=1){if(pm(e))return{part:e,step:t};switch(e){case"day":case"dayofyear":return{part:"date",step:t};case"quarter":return{part:"month",step:t*3};case"week":return{part:"date",step:t*7}}}function gm(e){return e?.param}function Ho(e){return!!e?.field&&e.equal!==void 0}function Vo(e){return!!e?.field&&e.lt!==void 0}function Xo(e){return!!e?.field&&e.lte!==void 0}function Yo(e){return!!e?.field&&e.gt!==void 0}function Ko(e){return!!e?.field&&e.gte!==void 0}function Jo(e){if(e?.field){if(A(e.range)&&e.range.length===2)return!0;if(I(e.range))return!0}return!1}function Qo(e){return!!e?.field&&(A(e.oneOf)||A(e.in))}function hm(e){return!!e?.field&&e.valid!==void 0}function Gu(e){return Qo(e)||Ho(e)||Jo(e)||Vo(e)||Yo(e)||Xo(e)||Ko(e)}function st(e,t){return Zr(e,{timeUnit:t,wrapTime:!0})}function mm(e,t){return e.map(n=>st(n,t))}function qu(e,t=!0){const{field:n}=e,i=he(e.timeUnit),{unit:r,binned:s}=i||{},o=w(e,{expr:"datum"}),a=r?`time(${s?o:lm(r,n)})`:o;if(Ho(e))return`${a}===${st(e.equal,r)}`;if(Vo(e)){const c=e.lt;return`${a}<${st(c,r)}`}else if(Yo(e)){const c=e.gt;return`${a}>${st(c,r)}`}else if(Xo(e)){const c=e.lte;return`${a}<=${st(c,r)}`}else if(Ko(e)){const c=e.gte;return`${a}>=${st(c,r)}`}else{if(Qo(e))return`indexof([${mm(e.oneOf,r).join(",")}], ${a}) !== -1`;if(hm(e))return Zo(a,e.valid);if(Jo(e)){const{range:c}=e,l=I(c)?{signal:`${c.signal}[0]`}:c[0],u=I(c)?{signal:`${c.signal}[1]`}:c[1];if(l!==null&&u!==null&&t)return"inrange("+a+", ["+st(l,r)+", "+st(u,r)+"])";const f=[];return l!==null&&f.push(`${a} >= ${st(l,r)}`),u!==null&&f.push(`${a} <= ${st(u,r)}`),f.length>0?f.join(" && "):"true"}}throw new Error(`Invalid field predicate: ${ee(e)}`)}function Zo(e,t=!0){return t?`isValid(${e}) && isFinite(+${e})`:`!isValid(${e}) || !isFinite(+${e})`}function ym(e){return Gu(e)&&e.timeUnit?{...e,timeUnit:he(e.timeUnit)}:e}const Vi={quantitative:"quantitative",ordinal:"ordinal",temporal:"temporal",nominal:"nominal",geojson:"geojson"};function bm(e){return e==="quantitative"||e==="temporal"}function Hu(e){return e==="ordinal"||e==="nominal"}const $n=Vi.quantitative,ea=Vi.ordinal,ti=Vi.temporal,ta=Vi.nominal,mi=Vi.geojson;function xm(e){if(e)switch(e=e.toLowerCase(),e){case"q":case $n:return"quantitative";case"t":case ti:return"temporal";case"o":case ea:return"ordinal";case"n":case ta:return"nominal";case mi:return"geojson"}}const Te={LINEAR:"linear",LOG:"log",POW:"pow",SQRT:"sqrt",SYMLOG:"symlog",IDENTITY:"identity",SEQUENTIAL:"sequential",TIME:"time",UTC:"utc",QUANTILE:"quantile",QUANTIZE:"quantize",THRESHOLD:"threshold",BIN_ORDINAL:"bin-ordinal",ORDINAL:"ordinal",POINT:"point",BAND:"band"},Js={linear:"numeric",log:"numeric",pow:"numeric",sqrt:"numeric",symlog:"numeric",identity:"numeric",sequential:"numeric",time:"time",utc:"time",ordinal:"ordinal","bin-ordinal":"bin-ordinal",point:"ordinal-position",band:"ordinal-position",quantile:"discretizing",quantize:"discretizing",threshold:"discretizing"};function vm(e,t){const n=Js[e],i=Js[t];return n===i||n==="ordinal-position"&&i==="time"||i==="ordinal-position"&&n==="time"}const Sm={linear:0,log:1,pow:1,sqrt:1,symlog:1,identity:1,sequential:1,time:0,utc:0,point:10,band:11,ordinal:0,"bin-ordinal":0,quantile:0,quantize:0,threshold:0};function xc(e){return Sm[e]}const Vu=new Set(["linear","log","pow","sqrt","symlog"]),Xu=new Set([...Vu,"time","utc"]);function Yu(e){return Vu.has(e)}const Ku=new Set(["quantile","quantize","threshold"]),Em=new Set([...Xu,...Ku,"sequential","identity"]),$m=new Set(["ordinal","bin-ordinal","point","band"]);function be(e){return $m.has(e)}function ze(e){return Em.has(e)}function Xe(e){return Xu.has(e)}function ni(e){return Ku.has(e)}const wm={pointPadding:.5,barBandPaddingInner:.1,rectBandPaddingInner:0,bandWithNestedOffsetPaddingInner:.2,bandWithNestedOffsetPaddingOuter:.2,minBandSize:2,minFontSize:8,maxFontSize:40,minOpacity:.3,maxOpacity:.8,minSize:9,minStrokeWidth:1,maxStrokeWidth:4,quantileCount:4,quantizeCount:4,zero:!0};function Cm(e){return!z(e)&&!!e.name}function Ju(e){return e?.param}function Fm(e){return e?.unionWith}function Nm(e){return V(e)&&"field"in e}const _m={type:1,domain:1,domainMax:1,domainMin:1,domainMid:1,align:1,range:1,rangeMax:1,rangeMin:1,scheme:1,bins:1,reverse:1,round:1,clamp:1,nice:1,base:1,exponent:1,constant:1,interpolate:1,zero:1,padding:1,paddingInner:1,paddingOuter:1},{type:e0,domain:t0,range:n0,rangeMax:i0,rangeMin:r0,scheme:s0,...km}=_m,Tm=v(km);function Qs(e,t){switch(t){case"type":case"domain":case"reverse":case"range":return!0;case"scheme":case"interpolate":return!["point","band","identity"].includes(e);case"bins":return!["point","band","identity","ordinal"].includes(e);case"round":return Xe(e)||e==="band"||e==="point";case"padding":case"rangeMin":case"rangeMax":return Xe(e)||["point","band"].includes(e);case"paddingOuter":case"align":return["point","band"].includes(e);case"paddingInner":return e==="band";case"domainMax":case"domainMid":case"domainMin":case"clamp":return Xe(e);case"nice":return Xe(e)||e==="quantize"||e==="threshold";case"exponent":return e==="pow";case"base":return e==="log";case"constant":return e==="symlog";case"zero":return ze(e)&&!G(["log","time","utc","threshold","quantile"],e)}}function Qu(e,t){switch(t){case"interpolate":case"scheme":case"domainMid":return Hn(e)?void 0:Fh(t);case"align":case"type":case"bins":case"domain":case"domainMax":case"domainMin":case"range":case"base":case"exponent":case"constant":case"nice":case"padding":case"paddingInner":case"paddingOuter":case"rangeMax":case"rangeMin":case"reverse":case"round":case"clamp":case"zero":return}}function Om(e,t){return G([ea,ta],t)?e===void 0||be(e):t===ti?G([Te.TIME,Te.UTC,void 0],e):t===$n?Yu(e)||ni(e)||e===void 0:!0}function Am(e,t,n=!1){if(!zt(e))return!1;switch(e){case oe:case me:case Kt:case gi:case Be:case tt:return Xe(t)||t==="band"?!0:t==="point"?!n:!1;case Lt:case Zt:case Pt:case Jt:case Qt:case Tn:return Xe(t)||ni(t)||G(["band","point","ordinal"],t);case Ae:case bt:case xt:return t!=="band";case en:case Re:return t==="ordinal"||ni(t)}}const Ne={arc:"arc",area:"area",bar:"bar",image:"image",line:"line",point:"point",rect:"rect",rule:"rule",text:"text",tick:"tick",trail:"trail",circle:"circle",square:"square",geoshape:"geoshape"},Zu=Ne.arc,Gr=Ne.area,qr=Ne.bar,Rm=Ne.image,Hr=Ne.line,Vr=Ne.point,Im=Ne.rect,yr=Ne.rule,ef=Ne.text,na=Ne.tick,Lm=Ne.trail,ia=Ne.circle,ra=Ne.square,tf=Ne.geoshape;function rn(e){return["line","area","trail"].includes(e)}function sa(e){return["rect","bar","image","arc"].includes(e)}const Pm=new Set(v(Ne));function gt(e){return e.type}const zm=["stroke","strokeWidth","strokeDash","strokeDashOffset","strokeOpacity","strokeJoin","strokeMiterLimit"],Dm=["fill","fillOpacity"],jm=[...zm,...Dm],Mm={color:1,filled:1,invalid:1,order:1,radius2:1,theta2:1,timeUnitBandSize:1,timeUnitBandPosition:1},vc=v(Mm),Um={area:["line","point"],bar:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],rect:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],line:["point"],tick:["bandSize","thickness"]},Bm={color:"#4c78a8",invalid:"filter",timeUnitBandSize:1},Wm={mark:1,arc:1,area:1,bar:1,circle:1,image:1,line:1,point:1,rect:1,rule:1,square:1,text:1,tick:1,trail:1,geoshape:1},nf=v(Wm);function wn(e){return e&&e.band!=null}const Gm={horizontal:["cornerRadiusTopRight","cornerRadiusBottomRight"],vertical:["cornerRadiusTopLeft","cornerRadiusTopRight"]},rf=5,qm={binSpacing:1,continuousBandSize:rf,minBandSize:.25,timeUnitBandPosition:.5},Hm={binSpacing:0,continuousBandSize:rf,minBandSize:.25,timeUnitBandPosition:.5},Vm={thickness:1};function Xm(e){return gt(e)?e.type:e}function oa(e){const{channel:t,channelDef:n,markDef:i,scale:r,config:s}=e,o=ca(e);return E(n)&&!bu(n.aggregate)&&r&&Xe(r.get("type"))?Ym({fieldDef:n,channel:t,markDef:i,ref:o,config:s}):o}function Ym({fieldDef:e,channel:t,markDef:n,ref:i,config:r}){return rn(n.type)?i:X("invalid",n,r)===null?[Km(e,t),i]:i}function Km(e,t){const n=aa(e,!0),r=On(t)==="y"?{field:{group:"height"}}:{value:0};return{test:n,...r}}function aa(e,t=!0){return Zo(z(e)?e:w(e,{expr:"datum"}),!t)}function Jm(e){const{datum:t}=e;return Rn(t)?En(t):`${ee(t)}`}function hn(e,t,n,i){const r={};if(t&&(r.scale=t),Et(e)){const{datum:s}=e;Rn(s)?r.signal=En(s):I(s)?r.signal=s.signal:Hi(s)?r.signal=s.expr:r.value=s}else r.field=w(e,n);if(i){const{offset:s,band:o}=i;s&&(r.offset=s),o&&(r.band=o)}return r}function br({scaleName:e,fieldOrDatumDef:t,fieldOrDatumDef2:n,offset:i,startSuffix:r,bandPosition:s=.5}){const o=0<s&&s<1?"datum":void 0,a=w(t,{expr:o,suffix:r}),c=n!==void 0?w(n,{expr:o}):w(t,{suffix:"end",expr:o}),l={};if(s===0||s===1){l.scale=e;const u=s===0?a:c;l.field=u}else{const u=I(s)?`${s.signal} * ${a} + (1-${s.signal}) * ${c}`:`${s} * ${a} + ${1-s} * ${c}`;l.signal=`scale("${e}", ${u})`}return i&&(l.offset=i),l}function Qm({scaleName:e,fieldDef:t}){const n=w(t,{expr:"datum"}),i=w(t,{expr:"datum",suffix:"end"});return`abs(scale("${e}", ${i}) - scale("${e}", ${n}))`}function ca({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l,bandPosition:u}){if(t){if(M(t)){const f=o?.get("type");if(Fe(t)){u??(u=df({fieldDef:t,fieldDef2:n,markDef:i,config:r}));const{bin:d,timeUnit:p,type:g}=t;if(te(d)||u&&p&&g===ti)return a?.impute?hn(t,s,{binSuffix:"mid"},{offset:c}):u&&!be(f)?br({scaleName:s,fieldOrDatumDef:t,bandPosition:u,offset:c}):hn(t,s,Ji(t,e)?{binSuffix:"range"}:{},{offset:c});if(xe(d)){if(E(n))return br({scaleName:s,fieldOrDatumDef:t,fieldOrDatumDef2:n,bandPosition:u,offset:c});S(Ru(e===oe?et:yt))}}return hn(t,s,be(f)?{binSuffix:"range"}:{},{offset:c,band:f==="band"?u??t.bandPosition??.5:void 0})}else if(Qe(t)){const f=t.value,d=c?{offset:c}:{};return{...Ri(e,f),...d}}}return Ap(l)&&(l=l()),l&&{...l,...c?{offset:c}:{}}}function Ri(e,t){return G(["x","x2"],e)&&t==="width"?{field:{group:"width"}}:G(["y","y2"],e)&&t==="height"?{field:{group:"height"}}:re(t)}function Cn(e){return e&&e!=="number"&&e!=="time"}function sf(e,t,n){return`${e}(${t}${n?`, ${ee(n)}`:""})`}const Zm=" – ";function la({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s}){if(Cn(n))return Ye({fieldOrDatumDef:e,format:t,formatType:n,expr:i,config:s});const o=of(e,i,r),a=ii(e);if(t===void 0&&n===void 0&&s.customFormatTypes){if(a==="quantitative"){if(r&&s.normalizedNumberFormatType)return Ye({fieldOrDatumDef:e,format:s.normalizedNumberFormat,formatType:s.normalizedNumberFormatType,expr:i,config:s});if(s.numberFormatType)return Ye({fieldOrDatumDef:e,format:s.numberFormat,formatType:s.numberFormatType,expr:i,config:s})}if(a==="temporal"&&s.timeFormatType&&E(e)&&e.timeUnit===void 0)return Ye({fieldOrDatumDef:e,format:s.timeFormat,formatType:s.timeFormatType,expr:i,config:s})}if(si(e)){const c=ty({field:o,timeUnit:E(e)?he(e.timeUnit)?.unit:void 0,format:t,formatType:s.timeFormatType,rawTimeFormat:s.timeFormat,isUTCScale:Ln(e)&&e.scale?.type===Te.UTC});return c?{signal:c}:void 0}if(t=Zs({type:a,specifiedFormat:t,config:s,normalizeStack:r}),E(e)&&te(e.bin)){const c=w(e,{expr:i,binSuffix:"end"});return{signal:Xi(o,c,t,n,s)}}else return t||ii(e)==="quantitative"?{signal:`${lf(o,t)}`}:{signal:`isValid(${o}) ? ${o} : ""+${o}`}}function of(e,t,n){return E(e)?n?`${w(e,{expr:t,suffix:"end"})}-${w(e,{expr:t,suffix:"start"})}`:w(e,{expr:t}):Jm(e)}function Ye({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s,field:o}){if(o??(o=of(e,i,r)),o!=="datum.value"&&E(e)&&te(e.bin)){const a=w(e,{expr:i,binSuffix:"end"});return{signal:Xi(o,a,t,n,s)}}return{signal:sf(n,o,t)}}function af(e,t,n,i,r,s){if(!(z(i)&&Cn(i))&&!(n===void 0&&i===void 0&&r.customFormatTypes&&ii(e)==="quantitative"&&(r.normalizedNumberFormatType&&ri(e)&&e.stack==="normalize"||r.numberFormatType))){if(ri(e)&&e.stack==="normalize"&&r.normalizedNumberFormat)return Zs({type:"quantitative",config:r,normalizeStack:!0});if(si(e)){const o=E(e)?he(e.timeUnit)?.unit:void 0;return o===void 0&&r.customFormatTypes&&r.timeFormatType?void 0:ey({specifiedFormat:n,timeUnit:o,config:r,omitTimeFormatConfig:s})}return Zs({type:t,specifiedFormat:n,config:r})}}function cf(e,t,n){if(e&&(I(e)||e==="number"||e==="time"))return e;if(si(t)&&n!=="time"&&n!=="utc")return E(t)&&he(t?.timeUnit)?.utc?"utc":"time"}function Zs({type:e,specifiedFormat:t,config:n,normalizeStack:i}){if(z(t))return t;if(e===$n)return i?n.normalizedNumberFormat:n.numberFormat}function ey({specifiedFormat:e,timeUnit:t,config:n,omitTimeFormatConfig:i}){return e||(t?{signal:Uu(t)}:i?void 0:n.timeFormat)}function lf(e,t){return`format(${e}, "${t||""}")`}function Sc(e,t,n,i){return Cn(n)?sf(n,e,t):lf(e,(z(t)?t:void 0)??i.numberFormat)}function Xi(e,t,n,i,r){if(n===void 0&&i===void 0&&r.customFormatTypes&&r.numberFormatType)return Xi(e,t,r.numberFormat,r.numberFormatType,r);const s=Sc(e,n,i,r),o=Sc(t,n,i,r);return`${Zo(e,!1)} ? "null" : ${s} + "${Zm}" + ${o}`}function ty({field:e,timeUnit:t,format:n,formatType:i,rawTimeFormat:r,isUTCScale:s}){return!t||n?!t&&i?`${i}(${e}, '${n}')`:(n=z(n)?n:r,`${s?"utc":"time"}Format(${e}, '${n}')`):um(t,e,s)}const Xr="min",ny={x:1,y:1,color:1,fill:1,stroke:1,strokeWidth:1,size:1,shape:1,fillOpacity:1,strokeOpacity:1,opacity:1,text:1};function Ec(e){return e in ny}function uf(e){return!!e?.encoding}function ft(e){return e&&(e.op==="count"||!!e.field)}function ff(e){return e&&A(e)}function Yi(e){return"row"in e||"column"in e}function ua(e){return!!e&&"header"in e}function Yr(e){return"facet"in e}function iy(e){return e.param}function ry(e){return e&&!z(e)&&"repeat"in e}function $c(e){const{field:t,timeUnit:n,bin:i,aggregate:r}=e;return{...n?{timeUnit:n}:{},...i?{bin:i}:{},...r?{aggregate:r}:{},field:t}}function fa(e){return"sort"in e}function df({fieldDef:e,fieldDef2:t,markDef:n,config:i}){if(M(e)&&e.bandPosition!==void 0)return e.bandPosition;if(E(e)){const{timeUnit:r,bin:s}=e;if(r&&!t)return sa(n.type)?0:At("timeUnitBandPosition",n,i);if(te(s))return .5}}function pf({channel:e,fieldDef:t,fieldDef2:n,markDef:i,config:r,scaleType:s,useVlSizeChannel:o}){const a=Ie(e),c=X(o?"size":a,i,r,{vgChannel:a});if(c!==void 0)return c;if(E(t)){const{timeUnit:l,bin:u}=t;if(l&&!n)return{band:At("timeUnitBandSize",i,r)};if(te(u)&&!be(s))return{band:1}}if(sa(i.type))return s?be(s)?r[i.type]?.discreteBandSize||{band:1}:r[i.type]?.continuousBandSize:r[i.type]?.discreteBandSize}function gf(e,t,n,i){return te(e.bin)||e.timeUnit&&Fe(e)&&e.type==="temporal"?df({fieldDef:e,fieldDef2:t,markDef:n,config:i})!==void 0:!1}function hf(e){return e&&!!e.sort&&!e.field}function Kr(e){return e&&"condition"in e}function Jr(e){const t=e?.condition;return!!t&&!A(t)&&E(t)}function Ki(e){const t=e?.condition;return!!t&&!A(t)&&M(t)}function sy(e){const t=e?.condition;return!!t&&(A(t)||Qe(t))}function E(e){return e&&(!!e.field||e.aggregate==="count")}function ii(e){return e?.type}function Et(e){return e&&"datum"in e}function Bt(e){return Fe(e)&&!vr(e)||xr(e)}function wc(e){return Fe(e)&&e.type==="quantitative"&&!e.bin||xr(e)}function xr(e){return Et(e)&&le(e.datum)}function M(e){return E(e)||Et(e)}function Fe(e){return e&&("field"in e||e.aggregate==="count")&&"type"in e}function Qe(e){return e&&"value"in e&&"value"in e}function Ln(e){return e&&("scale"in e||"sort"in e)}function ri(e){return e&&("axis"in e||"stack"in e||"impute"in e)}function mf(e){return e&&"legend"in e}function yf(e){return e&&("format"in e||"formatType"in e)}function oy(e){return ke(e,["legend","axis","header","scale"])}function ay(e){return"op"in e}function w(e,t={}){let n=e.field;const i=t.prefix;let r=t.suffix,s="";if(ly(e))n=iu("count");else{let o;if(!t.nofn)if(ay(e))o=e.op;else{const{bin:a,aggregate:c,timeUnit:l}=e;te(a)?(o=xu(a),r=(t.binSuffix??"")+(t.suffix??"")):c?tn(c)?(s=`["${n}"]`,n=`argmax_${c.argmax}`):Ot(c)?(s=`["${n}"]`,n=`argmin_${c.argmin}`):o=String(c):l&&!In(l)&&(o=fm(l),r=(!["range","mid"].includes(t.binSuffix)&&t.binSuffix||"")+(t.suffix??""))}o&&(n=n?`${o}_${n}`:o)}return r&&(n=`${n}_${r}`),i&&(n=`${i}_${n}`),t.forAs?Ao(n):t.expr?eu(n,t.expr)+s:Me(n)+s}function vr(e){switch(e.type){case"nominal":case"ordinal":case"geojson":return!0;case"quantitative":return E(e)&&!!e.bin;case"temporal":return!1}throw new Error(ku(e.type))}function cy(e){return Ln(e)&&ni(e.scale?.type)}function ly(e){return e.aggregate==="count"}function uy(e,t){const{field:n,bin:i,timeUnit:r,aggregate:s}=e;if(s==="count")return t.countTitle;if(te(i))return`${n} (binned)`;if(r&&!In(r)){const o=he(r)?.unit;if(o)return`${n} (${Wr(o).join("-")})`}else if(s)return tn(s)?`${n} for max ${s.argmax}`:Ot(s)?`${n} for min ${s.argmin}`:`${Wi(s)} of ${n}`;return n}function fy(e){const{aggregate:t,bin:n,timeUnit:i,field:r}=e;if(tn(t))return`${r} for argmax(${t.argmax})`;if(Ot(t))return`${r} for argmin(${t.argmin})`;const s=i&&!In(i)?he(i):void 0,o=t||s?.unit||s?.maxbins&&"timeunit"||te(n)&&"bin";return o?`${o.toUpperCase()}(${r})`:r}const bf=(e,t)=>{switch(t.fieldTitle){case"plain":return e.field;case"functional":return fy(e);default:return uy(e,t)}};let xf=bf;function vf(e){xf=e}function dy(){vf(bf)}function Vn(e,t,{allowDisabling:n,includeDefault:i=!0}){const r=da(e)?.title;if(!E(e))return r??e.title;const s=e,o=i?pa(s,t):void 0;return n?ue(r,s.title,o):r??s.title??o}function da(e){if(ri(e)&&e.axis)return e.axis;if(mf(e)&&e.legend)return e.legend;if(ua(e)&&e.header)return e.header}function pa(e,t){return xf(e,t)}function Sr(e){if(yf(e)){const{format:t,formatType:n}=e;return{format:t,formatType:n}}else{const t=da(e)??{},{format:n,formatType:i}=t;return{format:n,formatType:i}}}function py(e,t){switch(t){case"latitude":case"longitude":return"quantitative";case"row":case"column":case"facet":case"shape":case"strokeDash":return"nominal";case"order":return"ordinal"}if(fa(e)&&A(e.sort))return"ordinal";const{aggregate:n,bin:i,timeUnit:r}=e;if(r)return"temporal";if(i||n&&!tn(n)&&!Ot(n))return"quantitative";if(Ln(e)&&e.scale?.type)switch(Js[e.scale.type]){case"numeric":case"discretizing":return"quantitative";case"time":return"temporal"}return"nominal"}function ht(e){if(E(e))return e;if(Jr(e))return e.condition}function fe(e){if(M(e))return e;if(Ki(e))return e.condition}function Sf(e,t,n,i={}){if(z(e)||le(e)||di(e)){const r=z(e)?"string":le(e)?"number":"boolean";return S(ph(t,r,e)),{value:e}}return M(e)?Er(e,t,n,i):Ki(e)?{...e,condition:Er(e.condition,t,n,i)}:e}function Er(e,t,n,i){if(yf(e)){const{format:r,formatType:s,...o}=e;if(Cn(s)&&!n.customFormatTypes)return S(mc(t)),Er(o,t,n,i)}else{const r=ri(e)?"axis":mf(e)?"legend":ua(e)?"header":null;if(r&&e[r]){const{format:s,formatType:o,...a}=e[r];if(Cn(o)&&!n.customFormatTypes)return S(mc(t)),Er({...e,[r]:a},t,n,i)}}return E(e)?ga(e,t,i):gy(e)}function gy(e){let t=e.type;if(t)return e;const{datum:n}=e;return t=le(n)?"quantitative":z(n)?"nominal":Rn(n)?"temporal":void 0,{...e,type:t}}function ga(e,t,{compositeMark:n=!1}={}){const{aggregate:i,timeUnit:r,bin:s,field:o}=e,a={...e};if(!n&&i&&!Mo(i)&&!tn(i)&&!Ot(i)&&(S(hh(i)),delete a.aggregate),r&&(a.timeUnit=he(r)),o&&(a.field=`${o}`),te(s)&&(a.bin=Qr(s,t)),xe(s)&&!ye(t)&&S(Xh(t)),Fe(a)){const{type:c}=a,l=xm(c);c!==l&&(a.type=l),c!=="quantitative"&&bu(i)&&(S(gh(c,i)),a.type="quantitative")}else if(!fu(t)){const c=py(a,t);a.type=c}if(Fe(a)){const{compatible:c,warning:l}=hy(a,t)||{};c===!1&&S(l)}if(fa(a)&&z(a.sort)){const{sort:c}=a;if(Ec(c))return{...a,sort:{encoding:c}};const l=c.substr(1);if(c.charAt(0)==="-"&&Ec(l))return{...a,sort:{encoding:l,order:"descending"}}}if(ua(a)){const{header:c}=a;if(c){const{orient:l,...u}=c;if(l)return{...a,header:{...u,labelOrient:c.labelOrient||l,titleOrient:c.titleOrient||l}}}}return a}function Qr(e,t){return di(e)?{maxbins:ac(t)}:e==="binned"?{binned:!0}:!e.maxbins&&!e.step?{...e,maxbins:ac(t)}:e}const Dn={compatible:!0};function hy(e,t){const n=e.type;if(n==="geojson"&&t!=="shape")return{compatible:!1,warning:`Channel ${t} should not be used with a geojson data.`};switch(t){case Nt:case _t:case Ir:return vr(e)?Dn:{compatible:!1,warning:vh(t)};case oe:case me:case Kt:case gi:case Ae:case bt:case xt:case Gi:case qi:case Lr:case Sn:case Pr:case zr:case Tn:case Be:case tt:case Dr:return Dn;case it:case Ue:case nt:case rt:return n!==$n?{compatible:!1,warning:`Channel ${t} should be used with a quantitative field only, not ${e.type} field.`}:Dn;case Pt:case Jt:case Qt:case Zt:case Lt:case It:case Rt:case et:case yt:return n==="nominal"&&!e.sort?{compatible:!1,warning:`Channel ${t} should not be used with an unsorted discrete field.`}:Dn;case Re:case en:return!vr(e)&&!cy(e)?{compatible:!1,warning:Sh(t)}:Dn;case Zn:return e.type==="nominal"&&!("sort"in e)?{compatible:!1,warning:"Channel order is inappropriate for nominal field, which has no inherent order."}:Dn}}function si(e){const{formatType:t}=Sr(e);return t==="time"||!t&&my(e)}function my(e){return e&&(e.type==="temporal"||E(e)&&!!e.timeUnit)}function Zr(e,{timeUnit:t,type:n,wrapTime:i,undefinedIfExprNotRequired:r}){const s=t&&he(t)?.unit;let o=s||n==="temporal",a;return Hi(e)?a=e.expr:I(e)?a=e.signal:Rn(e)?(o=!0,a=En(e)):(z(e)||le(e))&&o&&(a=`datetime(${ee(e)})`,om(s)&&(le(e)&&e<1e4||z(e)&&isNaN(Date.parse(e)))&&(a=En({[s]:e}))),a?i&&o?`time(${a})`:a:r?void 0:ee(e)}function Ef(e,t){const{type:n}=e;return t.map(i=>{const r=E(e)&&!In(e.timeUnit)?e.timeUnit:void 0,s=Zr(i,{timeUnit:r,type:n,undefinedIfExprNotRequired:!0});return s!==void 0?{signal:s}:i})}function Ji(e,t){return te(e.bin)?zt(t)&&["ordinal","nominal"].includes(e.type):(console.warn("Only call this method for binned field defs."),!1)}const Cc={labelAlign:{part:"labels",vgProp:"align"},labelBaseline:{part:"labels",vgProp:"baseline"},labelColor:{part:"labels",vgProp:"fill"},labelFont:{part:"labels",vgProp:"font"},labelFontSize:{part:"labels",vgProp:"fontSize"},labelFontStyle:{part:"labels",vgProp:"fontStyle"},labelFontWeight:{part:"labels",vgProp:"fontWeight"},labelOpacity:{part:"labels",vgProp:"opacity"},labelOffset:null,labelPadding:null,gridColor:{part:"grid",vgProp:"stroke"},gridDash:{part:"grid",vgProp:"strokeDash"},gridDashOffset:{part:"grid",vgProp:"strokeDashOffset"},gridOpacity:{part:"grid",vgProp:"opacity"},gridWidth:{part:"grid",vgProp:"strokeWidth"},tickColor:{part:"ticks",vgProp:"stroke"},tickDash:{part:"ticks",vgProp:"strokeDash"},tickDashOffset:{part:"ticks",vgProp:"strokeDashOffset"},tickOpacity:{part:"ticks",vgProp:"opacity"},tickSize:null,tickWidth:{part:"ticks",vgProp:"strokeWidth"}};function Qi(e){return e?.condition}const $f=["domain","grid","labels","ticks","title"],yy={grid:"grid",gridCap:"grid",gridColor:"grid",gridDash:"grid",gridDashOffset:"grid",gridOpacity:"grid",gridScale:"grid",gridWidth:"grid",orient:"main",bandPosition:"both",aria:"main",description:"main",domain:"main",domainCap:"main",domainColor:"main",domainDash:"main",domainDashOffset:"main",domainOpacity:"main",domainWidth:"main",format:"main",formatType:"main",labelAlign:"main",labelAngle:"main",labelBaseline:"main",labelBound:"main",labelColor:"main",labelFlush:"main",labelFlushOffset:"main",labelFont:"main",labelFontSize:"main",labelFontStyle:"main",labelFontWeight:"main",labelLimit:"main",labelLineHeight:"main",labelOffset:"main",labelOpacity:"main",labelOverlap:"main",labelPadding:"main",labels:"main",labelSeparation:"main",maxExtent:"main",minExtent:"main",offset:"both",position:"main",tickCap:"main",tickColor:"main",tickDash:"main",tickDashOffset:"main",tickMinStep:"both",tickOffset:"both",tickOpacity:"main",tickRound:"both",ticks:"main",tickSize:"main",tickWidth:"both",title:"main",titleAlign:"main",titleAnchor:"main",titleAngle:"main",titleBaseline:"main",titleColor:"main",titleFont:"main",titleFontSize:"main",titleFontStyle:"main",titleFontWeight:"main",titleLimit:"main",titleLineHeight:"main",titleOpacity:"main",titlePadding:"main",titleX:"main",titleY:"main",encode:"both",scale:"both",tickBand:"both",tickCount:"both",tickExtra:"both",translate:"both",values:"both",zindex:"both"},wf={orient:1,aria:1,bandPosition:1,description:1,domain:1,domainCap:1,domainColor:1,domainDash:1,domainDashOffset:1,domainOpacity:1,domainWidth:1,format:1,formatType:1,grid:1,gridCap:1,gridColor:1,gridDash:1,gridDashOffset:1,gridOpacity:1,gridWidth:1,labelAlign:1,labelAngle:1,labelBaseline:1,labelBound:1,labelColor:1,labelFlush:1,labelFlushOffset:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelLineHeight:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labels:1,labelSeparation:1,maxExtent:1,minExtent:1,offset:1,position:1,tickBand:1,tickCap:1,tickColor:1,tickCount:1,tickDash:1,tickDashOffset:1,tickExtra:1,tickMinStep:1,tickOffset:1,tickOpacity:1,tickRound:1,ticks:1,tickSize:1,tickWidth:1,title:1,titleAlign:1,titleAnchor:1,titleAngle:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titlePadding:1,titleX:1,titleY:1,translate:1,values:1,zindex:1},by={...wf,style:1,labelExpr:1,encoding:1};function Fc(e){return!!by[e]}const xy={axis:1,axisBand:1,axisBottom:1,axisDiscrete:1,axisLeft:1,axisPoint:1,axisQuantitative:1,axisRight:1,axisTemporal:1,axisTop:1,axisX:1,axisXBand:1,axisXDiscrete:1,axisXPoint:1,axisXQuantitative:1,axisXTemporal:1,axisY:1,axisYBand:1,axisYDiscrete:1,axisYPoint:1,axisYQuantitative:1,axisYTemporal:1},Cf=v(xy);function Dt(e){return"mark"in e}class es{constructor(t,n){this.name=t,this.run=n}hasMatchingType(t){return Dt(t)?Xm(t.mark)===this.name:!1}}function mn(e,t){const n=e&&e[t];return n?A(n)?xn(n,i=>!!i.field):E(n)||Jr(n):!1}function Ff(e,t){const n=e&&e[t];return n?A(n)?xn(n,i=>!!i.field):E(n)||Et(n)||Ki(n):!1}function eo(e,t){if(ye(t)){const n=e[t];if((E(n)||Et(n))&&(Hu(n.type)||E(n)&&n.timeUnit)){const i=Po(t);return Ff(e,i)}}return!1}function ha(e){return xn(vg,t=>{if(mn(e,t)){const n=e[t];if(A(n))return xn(n,i=>!!i.aggregate);{const i=ht(n);return i&&!!i.aggregate}}return!1})}function Nf(e,t){const n=[],i=[],r=[],s=[],o={};return ma(e,(a,c)=>{if(E(a)){const{field:l,aggregate:u,bin:f,timeUnit:d,...p}=a;if(u||d||f){const h=da(a)?.title;let m=w(a,{forAs:!0});const y={...h?[]:{title:Vn(a,t,{allowDisabling:!0})},...p,field:m};if(u){let b;if(tn(u)?(b="argmax",m=w({op:"argmax",field:u.argmax},{forAs:!0}),y.field=`${m}.${l}`):Ot(u)?(b="argmin",m=w({op:"argmin",field:u.argmin},{forAs:!0}),y.field=`${m}.${l}`):u!=="boxplot"&&u!=="errorbar"&&u!=="errorband"&&(b=u),b){const k={op:b,as:m};l&&(k.field=l),s.push(k)}}else if(n.push(m),Fe(a)&&te(f)){if(i.push({bin:f,field:l,as:m}),n.push(w(a,{binSuffix:"end"})),Ji(a,c)&&n.push(w(a,{binSuffix:"range"})),ye(c)){const b={field:`${m}_end`};o[`${c}2`]=b}y.bin="binned",fu(c)||(y.type=$n)}else if(d&&!In(d)){r.push({timeUnit:d,field:l,as:m});const b=Fe(a)&&a.type!==ti&&"time";b&&(c===Gi||c===Sn?y.formatType=b:kg(c)?y.legend={formatType:b,...y.legend}:ye(c)&&(y.axis={formatType:b,...y.axis}))}o[c]=y}else n.push(l),o[c]=e[c]}else o[c]=e[c]}),{bins:i,timeUnits:r,aggregate:s,groupby:n,encoding:o}}function vy(e,t,n){const i=Og(t,n);if(i){if(i==="binned"){const r=e[t===et?oe:me];return!!(E(r)&&E(e[t])&&xe(r.bin))}}else return!1;return!0}function Sy(e,t,n,i){const r={};for(const s of v(e))uu(s)||S(xh(s));for(let s of Cg){if(!e[s])continue;const o=e[s];if(hi(s)){const a=pu(s),c=r[a];if(E(c)){if(bm(c.type)&&E(o)&&!c.timeUnit){S(fh(a));continue}}else s=a,S(dh(a))}if(s==="angle"&&t==="arc"&&!e.theta&&(S(uh),s=Be),!vy(e,s,t)){S(Br(s,t));continue}if(s===Lt&&t==="line"&&ht(e[s])?.aggregate){S(yh);continue}if(s===Ae&&(n?"fill"in e:"stroke"in e)){S(Tu("encoding",{fill:"fill"in e,stroke:"stroke"in e}));continue}if(s===qi||s===Zn&&!A(o)&&!Qe(o)||s===Sn&&A(o)){if(o){if(s===Zn){const a=e[s];if(hf(a)){r[s]=a;continue}}r[s]=ce(o).reduce((a,c)=>(E(c)?a.push(ga(c,s)):S(Ks(c,s)),a),[])}}else{if(s===Sn&&o===null)r[s]=null;else if(!E(o)&&!Et(o)&&!Qe(o)&&!Kr(o)&&!I(o)){S(Ks(o,s));continue}r[s]=Sf(o,s,i)}}return r}function ts(e,t){const n={};for(const i of v(e)){const r=Sf(e[i],i,t,{compositeMark:!0});n[i]=r}return n}function Ey(e){const t=[];for(const n of v(e))if(mn(e,n)){const i=e[n],r=ce(i);for(const s of r)E(s)?t.push(s):Jr(s)&&t.push(s.condition)}return t}function ma(e,t,n){if(e)for(const i of v(e)){const r=e[i];if(A(r))for(const s of r)t.call(n,s,i);else t.call(n,r,i)}}function $y(e,t,n,i){return e?v(e).reduce((r,s)=>{const o=e[s];return A(o)?o.reduce((a,c)=>t.call(i,a,c,s),r):t.call(i,r,o,s)},n):n}function _f(e,t){return v(t).reduce((n,i)=>{switch(i){case oe:case me:case Pr:case Dr:case zr:case et:case yt:case Kt:case gi:case Be:case It:case tt:case Rt:case nt:case it:case rt:case Ue:case Gi:case Re:case Tn:case Sn:return n;case Zn:if(e==="line"||e==="trail")return n;case qi:case Lr:{const r=t[i];if(A(r)||E(r))for(const s of ce(r))s.aggregate||n.push(w(s,{}));return n}case Lt:if(e==="trail")return n;case Ae:case bt:case xt:case Pt:case Jt:case Qt:case en:case Zt:{const r=ht(t[i]);return r&&!r.aggregate&&n.push(w(r,{})),n}}},[])}function wy(e){const{tooltip:t,...n}=e;if(!t)return{filteredEncoding:n};let i,r;if(A(t)){for(const s of t)s.aggregate?(i||(i=[]),i.push(s)):(r||(r=[]),r.push(s));i&&(n.tooltip=i)}else t.aggregate?n.tooltip=t:r=t;return A(r)&&r.length===1&&(r=r[0]),{customTooltipWithoutAggregatedField:r,filteredEncoding:n}}function to(e,t,n,i=!0){if("tooltip"in n)return{tooltip:n.tooltip};const r=e.map(({fieldPrefix:o,titlePrefix:a})=>{const c=i?` of ${ya(t)}`:"";return{field:o+t.field,type:t.type,title:I(a)?{signal:`${a}"${escape(c)}"`}:a+c}}),s=Ey(n).map(oy);return{tooltip:[...r,...ut(s,W)]}}function ya(e){const{title:t,field:n}=e;return ue(t,n)}function ba(e,t,n,i,r){const{scale:s,axis:o}=n;return({partName:a,mark:c,positionPrefix:l,endPositionPrefix:u=void 0,extraEncoding:f={}})=>{const d=ya(n);return kf(e,a,r,{mark:c,encoding:{[t]:{field:`${l}_${n.field}`,type:n.type,...d!==void 0?{title:d}:{},...s!==void 0?{scale:s}:{},...o!==void 0?{axis:o}:{}},...z(u)?{[`${t}2`]:{field:`${u}_${n.field}`}}:{},...i,...f}})}}function kf(e,t,n,i){const{clip:r,color:s,opacity:o}=e,a=e.type;return e[t]||e[t]===void 0&&n[t]?[{...i,mark:{...n[t],...r?{clip:r}:{},...s?{color:s}:{},...o?{opacity:o}:{},...gt(i.mark)?i.mark:{type:i.mark},style:`${a}-${String(t)}`,...di(e[t])?{}:e[t]}}]:[]}function Tf(e,t,n){const{encoding:i}=e,r=t==="vertical"?"y":"x",s=i[r],o=i[`${r}2`],a=i[`${r}Error`],c=i[`${r}Error2`];return{continuousAxisChannelDef:ir(s,n),continuousAxisChannelDef2:ir(o,n),continuousAxisChannelDefError:ir(a,n),continuousAxisChannelDefError2:ir(c,n),continuousAxis:r}}function ir(e,t){if(e?.aggregate){const{aggregate:n,...i}=e;return n!==t&&S(Vh(n,t)),i}else return e}function Of(e,t){const{mark:n,encoding:i}=e,{x:r,y:s}=i;if(gt(n)&&n.orient)return n.orient;if(Bt(r)){if(Bt(s)){const o=E(r)&&r.aggregate,a=E(s)&&s.aggregate;if(!o&&a===t)return"vertical";if(!a&&o===t)return"horizontal";if(o===t&&a===t)throw new Error("Both x and y cannot have aggregate");return si(s)&&!si(r)?"horizontal":"vertical"}return"horizontal"}else{if(Bt(s))return"vertical";throw new Error(`Need a valid continuous axis for ${t}s`)}}const $r="boxplot",Cy=["box","median","outliers","rule","ticks"],Fy=new es($r,Rf);function Af(e){return le(e)?"tukey":e}function Rf(e,{config:t}){e={...e,encoding:ts(e.encoding,t)};const{mark:n,encoding:i,params:r,projection:s,...o}=e,a=gt(n)?n:{type:n};r&&S(_u("boxplot"));const c=a.extent??t.boxplot.extent,l=X("size",a,t),u=a.invalid,f=Af(c),{bins:d,timeUnits:p,transform:g,continuousAxisChannelDef:h,continuousAxis:m,groupby:y,aggregate:b,encodingWithoutContinuousAxis:k,ticksOrient:T,boxOrient:x,customTooltipWithoutAggregatedField:_}=Ny(e,c,t),{color:C,size:N,...Y}=k,ne=_p=>ba(a,m,h,_p,t.boxplot),de=ne(Y),Se=ne(k),F=ne({...Y,...N?{size:N}:{}}),$=to([{fieldPrefix:f==="min-max"?"upper_whisker_":"max_",titlePrefix:"Max"},{fieldPrefix:"upper_box_",titlePrefix:"Q3"},{fieldPrefix:"mid_box_",titlePrefix:"Median"},{fieldPrefix:"lower_box_",titlePrefix:"Q1"},{fieldPrefix:f==="min-max"?"lower_whisker_":"min_",titlePrefix:"Min"}],h,k),L={type:"tick",color:"black",opacity:1,orient:T,invalid:u,aria:!1},R=f==="min-max"?$:to([{fieldPrefix:"upper_whisker_",titlePrefix:"Upper Whisker"},{fieldPrefix:"lower_whisker_",titlePrefix:"Lower Whisker"}],h,k),P=[...de({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"lower_whisker",endPositionPrefix:"lower_box",extraEncoding:R}),...de({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"upper_box",endPositionPrefix:"upper_whisker",extraEncoding:R}),...de({partName:"ticks",mark:L,positionPrefix:"lower_whisker",extraEncoding:R}),...de({partName:"ticks",mark:L,positionPrefix:"upper_whisker",extraEncoding:R})],O=[...f!=="tukey"?P:[],...Se({partName:"box",mark:{type:"bar",...l?{size:l}:{},orient:x,invalid:u,ariaRoleDescription:"box"},positionPrefix:"lower_box",endPositionPrefix:"upper_box",extraEncoding:$}),...F({partName:"median",mark:{type:"tick",invalid:u,...V(t.boxplot.median)&&t.boxplot.median.color?{color:t.boxplot.median.color}:{},...l?{size:l}:{},orient:T,aria:!1},positionPrefix:"mid_box",extraEncoding:$})];if(f==="min-max")return{...o,transform:(o.transform??[]).concat(g),layer:O};const D=`datum["lower_box_${h.field}"]`,q=`datum["upper_box_${h.field}"]`,U=`(${q} - ${D})`,Q=`${D} - ${c} * ${U}`,Ee=`${q} + ${c} * ${U}`,ie=`datum["${h.field}"]`,sn={joinaggregate:If(h.field),groupby:y},Es={transform:[{filter:`(${Q} <= ${ie}) && (${ie} <= ${Ee})`},{aggregate:[{op:"min",field:h.field,as:`lower_whisker_${h.field}`},{op:"max",field:h.field,as:`upper_whisker_${h.field}`},{op:"min",field:`lower_box_${h.field}`,as:`lower_box_${h.field}`},{op:"max",field:`upper_box_${h.field}`,as:`upper_box_${h.field}`},...b],groupby:y}],layer:P},{tooltip:fw,...Fp}=Y,{scale:nc,axis:Np}=h,ic=ya(h),rc=ke(Np,["title"]),sc=kf(a,"outliers",t.boxplot,{transform:[{filter:`(${ie} < ${Q}) || (${ie} > ${Ee})`}],mark:"point",encoding:{[m]:{field:h.field,type:h.type,...ic!==void 0?{title:ic}:{},...nc!==void 0?{scale:nc}:{},...J(rc)?{}:{axis:rc}},...Fp,...C?{color:C}:{},..._?{tooltip:_}:{}}})[0];let nr;const oc=[...d,...p,sn];return sc?nr={transform:oc,layer:[sc,Es]}:(nr=Es,nr.transform.unshift(...oc)),{...o,layer:[nr,{transform:g,layer:O}]}}function If(e){return[{op:"q1",field:e,as:`lower_box_${e}`},{op:"q3",field:e,as:`upper_box_${e}`}]}function Ny(e,t,n){const i=Of(e,$r),{continuousAxisChannelDef:r,continuousAxis:s}=Tf(e,i,$r),o=r.field,a=Af(t),c=[...If(o),{op:"median",field:o,as:`mid_box_${o}`},{op:"min",field:o,as:(a==="min-max"?"lower_whisker_":"min_")+o},{op:"max",field:o,as:(a==="min-max"?"upper_whisker_":"max_")+o}],l=a==="min-max"||a==="tukey"?[]:[{calculate:`datum["upper_box_${o}"] - datum["lower_box_${o}"]`,as:`iqr_${o}`},{calculate:`min(datum["upper_box_${o}"] + datum["iqr_${o}"] * ${t}, datum["max_${o}"])`,as:`upper_whisker_${o}`},{calculate:`max(datum["lower_box_${o}"] - datum["iqr_${o}"] * ${t}, datum["min_${o}"])`,as:`lower_whisker_${o}`}],{[s]:u,...f}=e.encoding,{customTooltipWithoutAggregatedField:d,filteredEncoding:p}=wy(f),{bins:g,timeUnits:h,aggregate:m,groupby:y,encoding:b}=Nf(p,n),k=i==="vertical"?"horizontal":"vertical",T=i,x=[...g,...h,{aggregate:[...m,...c],groupby:y},...l];return{bins:g,timeUnits:h,transform:x,groupby:y,aggregate:m,continuousAxisChannelDef:r,continuousAxis:s,encodingWithoutContinuousAxis:b,ticksOrient:k,boxOrient:T,customTooltipWithoutAggregatedField:d}}const xa="errorbar",_y=["ticks","rule"],ky=new es(xa,Lf);function Lf(e,{config:t}){e={...e,encoding:ts(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,ticksOrient:o,markDef:a,outerSpec:c,tooltipEncoding:l}=Pf(e,xa,t);delete s.size;const u=ba(a,r,i,s,t.errorbar),f=a.thickness,d=a.size,p={type:"tick",orient:o,aria:!1,...f!==void 0?{thickness:f}:{},...d!==void 0?{size:d}:{}},g=[...u({partName:"ticks",mark:p,positionPrefix:"lower",extraEncoding:l}),...u({partName:"ticks",mark:p,positionPrefix:"upper",extraEncoding:l}),...u({partName:"rule",mark:{type:"rule",ariaRoleDescription:"errorbar",...f!==void 0?{size:f}:{}},positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:l})];return{...c,transform:n,...g.length>1?{layer:g}:{...g[0]}}}function Ty(e,t){const{encoding:n}=e;if(Oy(n))return{orient:Of(e,t),inputType:"raw"};const i=Ay(n),r=Ry(n),s=n.x,o=n.y;if(i){if(r)throw new Error(`${t} cannot be both type aggregated-upper-lower and aggregated-error`);const a=n.x2,c=n.y2;if(M(a)&&M(c))throw new Error(`${t} cannot have both x2 and y2`);if(M(a)){if(Bt(s))return{orient:"horizontal",inputType:"aggregated-upper-lower"};throw new Error(`Both x and x2 have to be quantitative in ${t}`)}else if(M(c)){if(Bt(o))return{orient:"vertical",inputType:"aggregated-upper-lower"};throw new Error(`Both y and y2 have to be quantitative in ${t}`)}throw new Error("No ranged axis")}else{const a=n.xError,c=n.xError2,l=n.yError,u=n.yError2;if(M(c)&&!M(a))throw new Error(`${t} cannot have xError2 without xError`);if(M(u)&&!M(l))throw new Error(`${t} cannot have yError2 without yError`);if(M(a)&&M(l))throw new Error(`${t} cannot have both xError and yError with both are quantiative`);if(M(a)){if(Bt(s))return{orient:"horizontal",inputType:"aggregated-error"};throw new Error("All x, xError, and xError2 (if exist) have to be quantitative")}else if(M(l)){if(Bt(o))return{orient:"vertical",inputType:"aggregated-error"};throw new Error("All y, yError, and yError2 (if exist) have to be quantitative")}throw new Error("No ranged axis")}}function Oy(e){return(M(e.x)||M(e.y))&&!M(e.x2)&&!M(e.y2)&&!M(e.xError)&&!M(e.xError2)&&!M(e.yError)&&!M(e.yError2)}function Ay(e){return M(e.x2)||M(e.y2)}function Ry(e){return M(e.xError)||M(e.xError2)||M(e.yError)||M(e.yError2)}function Pf(e,t,n){const{mark:i,encoding:r,params:s,projection:o,...a}=e,c=gt(i)?i:{type:i};s&&S(_u(t));const{orient:l,inputType:u}=Ty(e,t),{continuousAxisChannelDef:f,continuousAxisChannelDef2:d,continuousAxisChannelDefError:p,continuousAxisChannelDefError2:g,continuousAxis:h}=Tf(e,l,t),{errorBarSpecificAggregate:m,postAggregateCalculates:y,tooltipSummary:b,tooltipTitleWithFieldName:k}=Iy(c,f,d,p,g,u,t,n),{[h]:T,[h==="x"?"x2":"y2"]:x,[h==="x"?"xError":"yError"]:_,[h==="x"?"xError2":"yError2"]:C,...N}=r,{bins:Y,timeUnits:ne,aggregate:de,groupby:Se,encoding:F}=Nf(N,n),$=[...de,...m],L=u!=="raw"?[]:Se,R=to(b,f,F,k);return{transform:[...a.transform??[],...Y,...ne,...$.length===0?[]:[{aggregate:$,groupby:L}],...y],groupby:L,continuousAxisChannelDef:f,continuousAxis:h,encodingWithoutContinuousAxis:F,ticksOrient:l==="vertical"?"horizontal":"vertical",markDef:c,outerSpec:a,tooltipEncoding:R}}function Iy(e,t,n,i,r,s,o,a){let c=[],l=[];const u=t.field;let f,d=!1;if(s==="raw"){const p=e.center?e.center:e.extent?e.extent==="iqr"?"median":"mean":a.errorbar.center,g=e.extent?e.extent:p==="mean"?"stderr":"iqr";if(p==="median"!=(g==="iqr")&&S(Hh(p,g,o)),g==="stderr"||g==="stdev")c=[{op:g,field:u,as:`extent_${u}`},{op:p,field:u,as:`center_${u}`}],l=[{calculate:`datum["center_${u}"] + datum["extent_${u}"]`,as:`upper_${u}`},{calculate:`datum["center_${u}"] - datum["extent_${u}"]`,as:`lower_${u}`}],f=[{fieldPrefix:"center_",titlePrefix:Wi(p)},{fieldPrefix:"upper_",titlePrefix:Nc(p,g,"+")},{fieldPrefix:"lower_",titlePrefix:Nc(p,g,"-")}],d=!0;else{let h,m,y;g==="ci"?(h="mean",m="ci0",y="ci1"):(h="median",m="q1",y="q3"),c=[{op:m,field:u,as:`lower_${u}`},{op:y,field:u,as:`upper_${u}`},{op:h,field:u,as:`center_${u}`}],f=[{fieldPrefix:"upper_",titlePrefix:Vn({field:u,aggregate:y,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"lower_",titlePrefix:Vn({field:u,aggregate:m,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"center_",titlePrefix:Vn({field:u,aggregate:h,type:"quantitative"},a,{allowDisabling:!1})}]}}else{(e.center||e.extent)&&S(qh(e.center,e.extent)),s==="aggregated-upper-lower"?(f=[],l=[{calculate:`datum["${n.field}"]`,as:`upper_${u}`},{calculate:`datum["${u}"]`,as:`lower_${u}`}]):s==="aggregated-error"&&(f=[{fieldPrefix:"",titlePrefix:u}],l=[{calculate:`datum["${u}"] + datum["${i.field}"]`,as:`upper_${u}`}],r?l.push({calculate:`datum["${u}"] + datum["${r.field}"]`,as:`lower_${u}`}):l.push({calculate:`datum["${u}"] - datum["${i.field}"]`,as:`lower_${u}`}));for(const p of l)f.push({fieldPrefix:p.as.substring(0,6),titlePrefix:vn(vn(p.calculate,'datum["',""),'"]',"")})}return{postAggregateCalculates:l,errorBarSpecificAggregate:c,tooltipSummary:f,tooltipTitleWithFieldName:d}}function Nc(e,t,n){return`${Wi(e)} ${n} ${t}`}const va="errorband",Ly=["band","borders"],Py=new es(va,zf);function zf(e,{config:t}){e={...e,encoding:ts(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,markDef:o,outerSpec:a,tooltipEncoding:c}=Pf(e,va,t),l=o,u=ba(l,r,i,s,t.errorband),f=e.encoding.x!==void 0&&e.encoding.y!==void 0;let d={type:f?"area":"rect"},p={type:f?"line":"rule"};const g={...l.interpolate?{interpolate:l.interpolate}:{},...l.tension&&l.interpolate?{tension:l.tension}:{}};return f?(d={...d,...g,ariaRoleDescription:"errorband"},p={...p,...g,aria:!1}):l.interpolate?S(bc("interpolate")):l.tension&&S(bc("tension")),{...a,transform:n,layer:[...u({partName:"band",mark:d,positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:c}),...u({partName:"borders",mark:p,positionPrefix:"lower",extraEncoding:c}),...u({partName:"borders",mark:p,positionPrefix:"upper",extraEncoding:c})]}}const Df={};function Sa(e,t,n){const i=new es(e,t);Df[e]={normalizer:i,parts:n}}function zy(){return v(Df)}Sa($r,Rf,Cy);Sa(xa,Lf,_y);Sa(va,zf,Ly);const Dy=["gradientHorizontalMaxLength","gradientHorizontalMinLength","gradientVerticalMaxLength","gradientVerticalMinLength","unselectedOpacity"],jf={titleAlign:"align",titleAnchor:"anchor",titleAngle:"angle",titleBaseline:"baseline",titleColor:"color",titleFont:"font",titleFontSize:"fontSize",titleFontStyle:"fontStyle",titleFontWeight:"fontWeight",titleLimit:"limit",titleLineHeight:"lineHeight",titleOrient:"orient",titlePadding:"offset"},Mf={labelAlign:"align",labelAnchor:"anchor",labelAngle:"angle",labelBaseline:"baseline",labelColor:"color",labelFont:"font",labelFontSize:"fontSize",labelFontStyle:"fontStyle",labelFontWeight:"fontWeight",labelLimit:"limit",labelLineHeight:"lineHeight",labelOrient:"orient",labelPadding:"offset"},jy=v(jf),My=v(Mf),Uy={header:1,headerRow:1,headerColumn:1,headerFacet:1},Uf=v(Uy),Bf=["size","shape","fill","stroke","strokeDash","strokeWidth","opacity"],By={gradientHorizontalMaxLength:200,gradientHorizontalMinLength:100,gradientVerticalMaxLength:200,gradientVerticalMinLength:64,unselectedOpacity:.35},Wy={aria:1,clipHeight:1,columnPadding:1,columns:1,cornerRadius:1,description:1,direction:1,fillColor:1,format:1,formatType:1,gradientLength:1,gradientOpacity:1,gradientStrokeColor:1,gradientStrokeWidth:1,gradientThickness:1,gridAlign:1,labelAlign:1,labelBaseline:1,labelColor:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labelSeparation:1,legendX:1,legendY:1,offset:1,orient:1,padding:1,rowPadding:1,strokeColor:1,symbolDash:1,symbolDashOffset:1,symbolFillColor:1,symbolLimit:1,symbolOffset:1,symbolOpacity:1,symbolSize:1,symbolStrokeColor:1,symbolStrokeWidth:1,symbolType:1,tickCount:1,tickMinStep:1,title:1,titleAlign:1,titleAnchor:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titleOrient:1,titlePadding:1,type:1,values:1,zindex:1},Ze="_vgsid_",Gy={point:{on:"click",fields:[Ze],toggle:"event.shiftKey",resolve:"global",clear:"dblclick"},interval:{on:"[mousedown, window:mouseup] > window:mousemove!",encodings:["x","y"],translate:"[mousedown, window:mouseup] > window:mousemove!",zoom:"wheel!",mark:{fill:"#333",fillOpacity:.125,stroke:"white"},resolve:"global",clear:"dblclick"}};function Ea(e){return e==="legend"||!!e?.legend}function _s(e){return Ea(e)&&V(e)}function $a(e){return!!e?.select}function Wf(e){const t=[];for(const n of e||[]){if($a(n))continue;const{expr:i,bind:r,...s}=n;if(r&&i){const o={...s,bind:r,init:i};t.push(o)}else{const o={...s,...i?{update:i}:{},...r?{bind:r}:{}};t.push(o)}}return t}function qy(e){return ns(e)||Ca(e)||wa(e)}function wa(e){return"concat"in e}function ns(e){return"vconcat"in e}function Ca(e){return"hconcat"in e}function Gf({step:e,offsetIsDiscrete:t}){return t?e.for??"offset":"position"}function mt(e){return V(e)&&e.step!==void 0}function _c(e){return e.view||e.width||e.height}const kc=20,Hy={align:1,bounds:1,center:1,columns:1,spacing:1},Vy=v(Hy);function Xy(e,t,n){const i=n[t],r={},{spacing:s,columns:o}=i;s!==void 0&&(r.spacing=s),o!==void 0&&(Yr(e)&&!Yi(e.facet)||wa(e))&&(r.columns=o),ns(e)&&(r.columns=1);for(const a of Vy)if(e[a]!==void 0)if(a==="spacing"){const c=e[a];r[a]=le(c)?c:{row:c.row??s,column:c.column??s}}else r[a]=e[a];return r}function no(e,t){return e[t]??e[t==="width"?"continuousWidth":"continuousHeight"]}function wr(e,t){const n=Cr(e,t);return mt(n)?n.step:qf}function Cr(e,t){const n=e[t]??e[t==="width"?"discreteWidth":"discreteHeight"];return ue(n,{step:e.step})}const qf=20,Yy={continuousWidth:200,continuousHeight:200,step:qf},Ky={background:"white",padding:5,timeFormat:"%b %d, %Y",countTitle:"Count of Records",view:Yy,mark:Bm,arc:{},area:{},bar:qm,circle:{},geoshape:{},image:{},line:{},point:{},rect:Hm,rule:{color:"black"},square:{},text:{color:"black"},tick:Vm,trail:{},boxplot:{size:14,extent:1.5,box:{},median:{color:"white"},outliers:{},rule:{},ticks:null},errorbar:{center:"mean",rule:!0,ticks:!1},errorband:{band:{opacity:.3},borders:!1},scale:wm,projection:{},legend:By,header:{titlePadding:10,labelPadding:10},headerColumn:{},headerRow:{},headerFacet:{},selection:Gy,style:{},title:{},facet:{spacing:kc},concat:{spacing:kc},normalizedNumberFormat:".0%"},$t=["#4c78a8","#f58518","#e45756","#72b7b2","#54a24b","#eeca3b","#b279a2","#ff9da6","#9d755d","#bab0ac"],Tc={text:11,guideLabel:10,guideTitle:11,groupTitle:13,groupSubtitle:12},Oc={blue:$t[0],orange:$t[1],red:$t[2],teal:$t[3],green:$t[4],yellow:$t[5],purple:$t[6],pink:$t[7],brown:$t[8],gray0:"#000",gray1:"#111",gray2:"#222",gray3:"#333",gray4:"#444",gray5:"#555",gray6:"#666",gray7:"#777",gray8:"#888",gray9:"#999",gray10:"#aaa",gray11:"#bbb",gray12:"#ccc",gray13:"#ddd",gray14:"#eee",gray15:"#fff"};function Jy(e={}){return{signals:[{name:"color",value:V(e)?{...Oc,...e}:Oc}],mark:{color:{signal:"color.blue"}},rule:{color:{signal:"color.gray0"}},text:{color:{signal:"color.gray0"}},style:{"guide-label":{fill:{signal:"color.gray0"}},"guide-title":{fill:{signal:"color.gray0"}},"group-title":{fill:{signal:"color.gray0"}},"group-subtitle":{fill:{signal:"color.gray0"}},cell:{stroke:{signal:"color.gray8"}}},axis:{domainColor:{signal:"color.gray13"},gridColor:{signal:"color.gray8"},tickColor:{signal:"color.gray13"}},range:{category:[{signal:"color.blue"},{signal:"color.orange"},{signal:"color.red"},{signal:"color.teal"},{signal:"color.green"},{signal:"color.yellow"},{signal:"color.purple"},{signal:"color.pink"},{signal:"color.brown"},{signal:"color.grey8"}]}}}function Qy(e){return{signals:[{name:"fontSize",value:V(e)?{...Tc,...e}:Tc}],text:{fontSize:{signal:"fontSize.text"}},style:{"guide-label":{fontSize:{signal:"fontSize.guideLabel"}},"guide-title":{fontSize:{signal:"fontSize.guideTitle"}},"group-title":{fontSize:{signal:"fontSize.groupTitle"}},"group-subtitle":{fontSize:{signal:"fontSize.groupSubtitle"}}}}}function Zy(e){return{text:{font:e},style:{"guide-label":{font:e},"guide-title":{font:e},"group-title":{font:e},"group-subtitle":{font:e}}}}function Hf(e){const t=v(e||{}),n={};for(const i of t){const r=e[i];n[i]=Qi(r)?Su(r):Pe(r)}return n}function eb(e){const t=v(e),n={};for(const i of t)n[i]=Hf(e[i]);return n}const tb=[...nf,...Cf,...Uf,"background","padding","legend","lineBreak","scale","style","title","view"];function Vf(e={}){const{color:t,font:n,fontSize:i,selection:r,...s}=e,o=Ar({},j(Ky),n?Zy(n):{},t?Jy(t):{},i?Qy(i):{},s||{});r&&$o(o,"selection",r,!0);const a=ke(o,tb);for(const c of["background","lineBreak","padding"])o[c]&&(a[c]=Pe(o[c]));for(const c of nf)o[c]&&(a[c]=_e(o[c]));for(const c of Cf)o[c]&&(a[c]=Hf(o[c]));for(const c of Uf)o[c]&&(a[c]=_e(o[c]));return o.legend&&(a.legend=_e(o.legend)),o.scale&&(a.scale=_e(o.scale)),o.style&&(a.style=eb(o.style)),o.title&&(a.title=_e(o.title)),o.view&&(a.view=_e(o.view)),a}const nb=new Set(["view",...Pm]),ib=["color","fontSize","background","padding","facet","concat","numberFormat","numberFormatType","normalizedNumberFormat","normalizedNumberFormatType","timeFormat","countTitle","header","axisQuantitative","axisTemporal","axisDiscrete","axisPoint","axisXBand","axisXPoint","axisXDiscrete","axisXQuantitative","axisXTemporal","axisYBand","axisYPoint","axisYDiscrete","axisYQuantitative","axisYTemporal","scale","selection","overlay"],rb={view:["continuousWidth","continuousHeight","discreteWidth","discreteHeight","step"],...Um};function sb(e){e=j(e);for(const t of ib)delete e[t];if(e.axis)for(const t in e.axis)Qi(e.axis[t])&&delete e.axis[t];if(e.legend)for(const t of Dy)delete e.legend[t];if(e.mark){for(const t of vc)delete e.mark[t];e.mark.tooltip&&V(e.mark.tooltip)&&delete e.mark.tooltip}e.params&&(e.signals=(e.signals||[]).concat(Wf(e.params)),delete e.params);for(const t of nb){for(const i of vc)delete e[t][i];const n=rb[t];if(n)for(const i of n)delete e[t][i];ab(e,t)}for(const t of zy())delete e[t];ob(e);for(const t in e)V(e[t])&&J(e[t])&&delete e[t];return J(e)?void 0:e}function ob(e){const{titleMarkConfig:t,subtitleMarkConfig:n,subtitle:i}=vu(e.title);J(t)||(e.style["group-title"]={...e.style["group-title"],...t}),J(n)||(e.style["group-subtitle"]={...e.style["group-subtitle"],...n}),J(i)?delete e.title:e.title=i}function ab(e,t,n,i){const r=e[t];t==="view"&&(n="cell");const s={...r,...e.style[n??t]};J(s)||(e.style[n??t]=s),delete e[t]}function is(e){return"layer"in e}function cb(e){return"repeat"in e}function lb(e){return!A(e.repeat)&&e.repeat.layer}class Fa{map(t,n){return Yr(t)?this.mapFacet(t,n):cb(t)?this.mapRepeat(t,n):Ca(t)?this.mapHConcat(t,n):ns(t)?this.mapVConcat(t,n):wa(t)?this.mapConcat(t,n):this.mapLayerOrUnit(t,n)}mapLayerOrUnit(t,n){if(is(t))return this.mapLayer(t,n);if(Dt(t))return this.mapUnit(t,n);throw new Error(Uo(t))}mapLayer(t,n){return{...t,layer:t.layer.map(i=>this.mapLayerOrUnit(i,n))}}mapHConcat(t,n){return{...t,hconcat:t.hconcat.map(i=>this.map(i,n))}}mapVConcat(t,n){return{...t,vconcat:t.vconcat.map(i=>this.map(i,n))}}mapConcat(t,n){const{concat:i,...r}=t;return{...r,concat:i.map(s=>this.map(s,n))}}mapFacet(t,n){return{...t,spec:this.map(t.spec,n)}}mapRepeat(t,n){return{...t,spec:this.map(t.spec,n)}}}const ub={zero:1,center:1,normalize:1};function fb(e){return e in ub}const db=new Set([Zu,qr,Gr,yr,Vr,ia,ra,Hr,ef,na]),pb=new Set([qr,Gr,Zu]);function jn(e){return E(e)&&ii(e)==="quantitative"&&!e.bin}function Ac(e,t,{orient:n,type:i}){const r=t==="x"?"y":"radius",s=t==="x",o=e[t],a=e[r];if(E(o)&&E(a))if(jn(o)&&jn(a)){if(o.stack)return t;if(a.stack)return r;const c=E(o)&&!!o.aggregate,l=E(a)&&!!a.aggregate;if(c!==l)return c?t:r;if(s&&i==="bar"){if(n==="vertical")return r;if(n==="horizontal")return t}}else{if(jn(o))return t;if(jn(a))return r}else{if(jn(o))return t;if(jn(a))return r}}function gb(e){switch(e){case"x":return"y";case"y":return"x";case"theta":return"radius";case"radius":return"theta"}}function Xf(e,t){const n=gt(e)?e:{type:e},i=n.type;if(!db.has(i))return null;const r=Ac(t,"x",n)||Ac(t,"theta",n);if(!r)return null;const s=t[r],o=E(s)?w(s,{}):void 0,a=gb(r),c=[],l=new Set;if(t[a]){const d=t[a],p=E(d)?w(d,{}):void 0;p&&p!==o&&(c.push(a),l.add(p));const g=a==="x"?"xOffset":"yOffset",h=t[g],m=E(h)?w(h,{}):void 0;m&&m!==o&&(c.push(g),l.add(m))}const u=Fg.reduce((d,p)=>{if(p!=="tooltip"&&mn(t,p)){const g=t[p];for(const h of ce(g)){const m=ht(h);if(m.aggregate)continue;const y=w(m,{});(!y||!l.has(y))&&d.push({channel:p,fieldDef:m})}}return d},[]);let f;return s.stack!==void 0?di(s.stack)?f=s.stack?"zero":null:f=s.stack:pb.has(i)&&(f="zero"),!f||!fb(f)||ha(t)&&u.length===0?null:s?.scale?.type&&s?.scale?.type!==Te.LINEAR?(s?.stack&&S(Bh(s.scale.type)),null):M(t[vt(r)])?(s.stack!==void 0&&S(Uh(r)),null):(E(s)&&s.aggregate&&!Dg.has(s.aggregate)&&S(Wh(s.aggregate)),{groupbyChannels:c,groupbyFields:l,fieldChannel:r,impute:s.impute===null?!1:rn(i),stackBy:u,offset:f})}function hb(e){const{point:t,line:n,...i}=e;return v(i).length>1?i:i.type}function mb(e){for(const t of["line","area","rule","trail"])e[t]&&(e={...e,[t]:ke(e[t],["point","line"])});return e}function ks(e,t={},n){return e.point==="transparent"?{opacity:0}:e.point?V(e.point)?e.point:{}:e.point!==void 0?null:t.point||n.shape?V(t.point)?t.point:{}:void 0}function Rc(e,t={}){return e.line?e.line===!0?{}:e.line:e.line!==void 0?null:t.line?t.line===!0?{}:t.line:void 0}class yb{constructor(){this.name="path-overlay"}hasMatchingType(t,n){if(Dt(t)){const{mark:i,encoding:r}=t,s=gt(i)?i:{type:i};switch(s.type){case"line":case"rule":case"trail":return!!ks(s,n[s.type],r);case"area":return!!ks(s,n[s.type],r)||!!Rc(s,n[s.type])}}return!1}run(t,n,i){const{config:r}=n,{params:s,projection:o,mark:a,name:c,encoding:l,...u}=t,f=ts(l,r),d=gt(a)?a:{type:a},p=ks(d,r[d.type],f),g=d.type==="area"&&Rc(d,r[d.type]),h=[{name:c,...s?{params:s}:{},mark:hb({...d.type==="area"&&d.opacity===void 0&&d.fillOpacity===void 0?{opacity:.7}:{},...d}),encoding:ke(f,["shape"])}],m=Xf(d,f);let y=f;if(m){const{fieldChannel:b,offset:k}=m;y={...f,[b]:{...f[b],...k?{stack:k}:{}}}}return y=ke(y,["y2","x2"]),g&&h.push({...o?{projection:o}:{},mark:{type:"line",...Jn(d,["clip","interpolate","tension","tooltip"]),...g},encoding:y}),p&&h.push({...o?{projection:o}:{},mark:{type:"point",opacity:1,filled:!0,...Jn(d,["clip","tooltip"]),...p},encoding:y}),i({...u,layer:h},{...n,config:mb(r)})}}function bb(e,t){return t?Yi(e)?Kf(e,t):Yf(e,t):e}function Ts(e,t){return t?Kf(e,t):e}function io(e,t,n){const i=t[e];if(ry(i)){if(i.repeat in n)return{...t,[e]:n[i.repeat]};S(ih(i.repeat));return}return t}function Yf(e,t){if(e=io("field",e,t),e!==void 0){if(e===null)return null;if(fa(e)&&ft(e.sort)){const n=io("field",e.sort,t);e={...e,...n?{sort:n}:{}}}return e}}function Ic(e,t){if(E(e))return Yf(e,t);{const n=io("datum",e,t);return n!==e&&!n.type&&(n.type="nominal"),n}}function Lc(e,t){if(M(e)){const n=Ic(e,t);if(n)return n;if(Kr(e))return{condition:e.condition}}else{if(Ki(e)){const n=Ic(e.condition,t);if(n)return{...e,condition:n};{const{condition:i,...r}=e;return r}}return e}}function Kf(e,t){const n={};for(const i in e)if(Gn(e,i)){const r=e[i];if(A(r))n[i]=r.map(s=>Lc(s,t)).filter(s=>s);else{const s=Lc(r,t);s!==void 0&&(n[i]=s)}}return n}class xb{constructor(){this.name="RuleForRangedLine"}hasMatchingType(t){if(Dt(t)){const{encoding:n,mark:i}=t;if(i==="line"||gt(i)&&i.type==="line")for(const r of wg){const s=On(r),o=n[s];if(n[r]&&(E(o)&&!xe(o.bin)||Et(o)))return!0}}return!1}run(t,n,i){const{encoding:r,mark:s}=t;return S(wh(!!r.x2,!!r.y2)),i({...t,mark:V(s)?{...s,type:"rule"}:"rule"},n)}}class vb extends Fa{constructor(){super(...arguments),this.nonFacetUnitNormalizers=[Fy,ky,Py,new yb,new xb]}map(t,n){if(Dt(t)){const i=mn(t.encoding,Nt),r=mn(t.encoding,_t),s=mn(t.encoding,Ir);if(i||r||s)return this.mapFacetedUnit(t,n)}return super.map(t,n)}mapUnit(t,n){const{parentEncoding:i,parentProjection:r}=n,s=Ts(t.encoding,n.repeater),o={...t,...t.name?{name:[n.repeaterPrefix,t.name].filter(c=>c).join("_")}:{},...s?{encoding:s}:{}};if(i||r)return this.mapUnitWithParentEncodingOrProjection(o,n);const a=this.mapLayerOrUnit.bind(this);for(const c of this.nonFacetUnitNormalizers)if(c.hasMatchingType(o,n.config))return c.run(o,n,a);return o}mapRepeat(t,n){return lb(t)?this.mapLayerRepeat(t,n):this.mapNonLayerRepeat(t,n)}mapLayerRepeat(t,n){const{repeat:i,spec:r,...s}=t,{row:o,column:a,layer:c}=i,{repeater:l={},repeaterPrefix:u=""}=n;return o||a?this.mapRepeat({...t,repeat:{...o?{row:o}:{},...a?{column:a}:{}},spec:{repeat:{layer:c},spec:r}},n):{...s,layer:c.map(f=>{const d={...l,layer:f},p=`${(r.name?`${r.name}_`:"")+u}child__layer_${se(f)}`,g=this.mapLayerOrUnit(r,{...n,repeater:d,repeaterPrefix:p});return g.name=p,g})}}mapNonLayerRepeat(t,n){const{repeat:i,spec:r,data:s,...o}=t;!A(i)&&t.columns&&(t=ke(t,["columns"]),S(gc("repeat")));const a=[],{repeater:c={},repeaterPrefix:l=""}=n,u=!A(i)&&i.row||[c?c.row:null],f=!A(i)&&i.column||[c?c.column:null],d=A(i)&&i||[c?c.repeat:null];for(const g of d)for(const h of u)for(const m of f){const y={repeat:g,row:h,column:m,layer:c.layer},b=(r.name?`${r.name}_`:"")+l+"child__"+(A(i)?`${se(g)}`:(i.row?`row_${se(h)}`:"")+(i.column?`column_${se(m)}`:"")),k=this.map(r,{...n,repeater:y,repeaterPrefix:b});k.name=b,a.push(ke(k,["data"]))}const p=A(i)?t.columns:i.column?i.column.length:1;return{data:r.data??s,align:"all",...o,columns:p,concat:a}}mapFacet(t,n){const{facet:i}=t;return Yi(i)&&t.columns&&(t=ke(t,["columns"]),S(gc("facet"))),super.mapFacet(t,n)}mapUnitWithParentEncodingOrProjection(t,n){const{encoding:i,projection:r}=t,{parentEncoding:s,parentProjection:o,config:a}=n,c=zc({parentProjection:o,projection:r}),l=Pc({parentEncoding:s,encoding:Ts(i,n.repeater)});return this.mapUnit({...t,...c?{projection:c}:{},...l?{encoding:l}:{}},{config:a})}mapFacetedUnit(t,n){const{row:i,column:r,facet:s,...o}=t.encoding,{mark:a,width:c,projection:l,height:u,view:f,params:d,encoding:p,...g}=t,{facetMapping:h,layout:m}=this.getFacetMappingAndLayout({row:i,column:r,facet:s},n),y=Ts(o,n.repeater);return this.mapFacet({...g,...m,facet:h,spec:{...c?{width:c}:{},...u?{height:u}:{},...f?{view:f}:{},...l?{projection:l}:{},mark:a,encoding:y,...d?{params:d}:{}}},n)}getFacetMappingAndLayout(t,n){const{row:i,column:r,facet:s}=t;if(i||r){s&&S(Eh([...i?[Nt]:[],...r?[_t]:[]]));const o={},a={};for(const c of[Nt,_t]){const l=t[c];if(l){const{align:u,center:f,spacing:d,columns:p,...g}=l;o[c]=g;for(const h of["align","center","spacing"])l[h]!==void 0&&(a[h]??(a[h]={}),a[h][c]=l[h])}}return{facetMapping:o,layout:a}}else{const{align:o,center:a,spacing:c,columns:l,...u}=s;return{facetMapping:bb(u,n.repeater),layout:{...o?{align:o}:{},...a?{center:a}:{},...c?{spacing:c}:{},...l?{columns:l}:{}}}}}mapLayer(t,{parentEncoding:n,parentProjection:i,...r}){const{encoding:s,projection:o,...a}=t,c={...r,parentEncoding:Pc({parentEncoding:n,encoding:s,layer:!0}),parentProjection:zc({parentProjection:i,projection:o})};return super.mapLayer({...a,...t.name?{name:[c.repeaterPrefix,t.name].filter(l=>l).join("_")}:{}},c)}}function Pc({parentEncoding:e,encoding:t={},layer:n}){let i={};if(e){const r=new Set([...v(e),...v(t)]);for(const s of r){const o=t[s],a=e[s];if(M(o)){const c={...a,...o};i[s]=c}else Ki(o)?i[s]={...o,condition:{...a,...o.condition}}:o||o===null?i[s]=o:(n||Qe(a)||I(a)||M(a)||A(a))&&(i[s]=a)}}else i=t;return!i||J(i)?void 0:i}function zc(e){const{parentProjection:t,projection:n}=e;return t&&n&&S(lh({parentProjection:t,projection:n})),n??t}function Na(e){return"filter"in e}function Sb(e){return e?.stop!==void 0}function Jf(e){return"lookup"in e}function Eb(e){return"data"in e}function $b(e){return"param"in e}function wb(e){return"pivot"in e}function Cb(e){return"density"in e}function Fb(e){return"quantile"in e}function Nb(e){return"regression"in e}function _b(e){return"loess"in e}function kb(e){return"sample"in e}function Tb(e){return"window"in e}function Ob(e){return"joinaggregate"in e}function Ab(e){return"flatten"in e}function Rb(e){return"calculate"in e}function Qf(e){return"bin"in e}function Ib(e){return"impute"in e}function Lb(e){return"timeUnit"in e}function Pb(e){return"aggregate"in e}function zb(e){return"stack"in e}function Db(e){return"fold"in e}function jb(e){return"extent"in e&&!("density"in e)}function Mb(e){return e.map(t=>Na(t)?{filter:qn(t.filter,ym)}:t)}class Ub extends Fa{map(t,n){return n.emptySelections??(n.emptySelections={}),n.selectionPredicates??(n.selectionPredicates={}),t=Dc(t,n),super.map(t,n)}mapLayerOrUnit(t,n){if(t=Dc(t,n),t.encoding){const i={};for(const[r,s]of Gt(t.encoding))i[r]=Zf(s,n);t={...t,encoding:i}}return super.mapLayerOrUnit(t,n)}mapUnit(t,n){const{selection:i,...r}=t;return i?{...r,params:Gt(i).map(([s,o])=>{const{init:a,bind:c,empty:l,...u}=o;u.type==="single"?(u.type="point",u.toggle=!1):u.type==="multi"&&(u.type="point"),n.emptySelections[s]=l!=="none";for(const f of ve(n.selectionPredicates[s]??{}))f.empty=l!=="none";return{name:s,value:a,select:u,bind:c}})}:t}}function Dc(e,t){const{transform:n,...i}=e;if(n){const r=n.map(s=>{if(Na(s))return{filter:ro(s,t)};if(Qf(s)&&An(s.bin))return{...s,bin:ed(s.bin)};if(Jf(s)){const{selection:o,...a}=s.from;return o?{...s,from:{param:o,...a}}:s}return s});return{...i,transform:r}}return e}function Zf(e,t){const n=j(e);if(E(n)&&An(n.bin)&&(n.bin=ed(n.bin)),Ln(n)&&n.scale?.domain?.selection){const{selection:i,...r}=n.scale.domain;n.scale.domain={...r,...i?{param:i}:{}}}if(Kr(n))if(A(n.condition))n.condition=n.condition.map(i=>{const{selection:r,param:s,test:o,...a}=i;return s?i:{...a,test:ro(i,t)}});else{const{selection:i,param:r,test:s,...o}=Zf(n.condition,t);n.condition=r?n.condition:{...o,test:ro(n.condition,t)}}return n}function ed(e){const t=e.extent;if(t?.selection){const{selection:n,...i}=t;return{...e,extent:{...i,param:n}}}return e}function ro(e,t){const n=i=>qn(i,r=>{var s;const o=t.emptySelections[r]??!0,a={param:r,empty:o};return(s=t.selectionPredicates)[r]??(s[r]=[]),t.selectionPredicates[r].push(a),a});return e.selection?n(e.selection):qn(e.test||e.filter,i=>i.selection?n(i.selection):i)}class so extends Fa{map(t,n){const i=n.selections??[];if(t.params&&!Dt(t)){const r=[];for(const s of t.params)$a(s)?i.push(s):r.push(s);t.params=r}return n.selections=i,super.map(t,n)}mapUnit(t,n){const i=n.selections;if(!i||!i.length)return t;const r=(n.path??[]).concat(t.name),s=[];for(const o of i)if(!o.views||!o.views.length)s.push(o);else for(const a of o.views)(z(a)&&(a===t.name||r.includes(a))||A(a)&&a.map(c=>r.indexOf(c)).every((c,l,u)=>c!==-1&&(l===0||c>u[l-1])))&&s.push(o);return s.length&&(t.params=s),t}}for(const e of["mapFacet","mapRepeat","mapHConcat","mapVConcat","mapLayer"]){const t=so.prototype[e];so.prototype[e]=function(n,i){return t.call(this,n,Bb(n,i))}}function Bb(e,t){return e.name?{...t,path:(t.path??[]).concat(e.name)}:t}function td(e,t){t===void 0&&(t=Vf(e.config));const n=Hb(e,t),{width:i,height:r}=e,s=Vb(n,{width:i,height:r,autosize:e.autosize},t);return{...n,...s?{autosize:s}:{}}}const Wb=new vb,Gb=new Ub,qb=new so;function Hb(e,t={}){const n={config:t};return qb.map(Wb.map(Gb.map(e,n),n),n)}function jc(e){return z(e)?{type:e}:e??{}}function Vb(e,t,n){let{width:i,height:r}=t;const s=Dt(e)||is(e),o={};s?i=="container"&&r=="container"?(o.type="fit",o.contains="padding"):i=="container"?(o.type="fit-x",o.contains="padding"):r=="container"&&(o.type="fit-y",o.contains="padding"):(i=="container"&&(S(uc("width")),i=void 0),r=="container"&&(S(uc("height")),r=void 0));const a={type:"pad",...o,...n?jc(n.autosize):{},...jc(e.autosize)};if(a.type==="fit"&&!s&&(S(Vg),a.type="pad"),i=="container"&&!(a.type=="fit"||a.type=="fit-x")&&S(fc("width")),r=="container"&&!(a.type=="fit"||a.type=="fit-y")&&S(fc("height")),!lt(a,{type:"pad"}))return a}function Xb(e){return e==="fit"||e==="fit-x"||e==="fit-y"}function Yb(e){return e?`fit-${jr(e)}`:"fit"}const Kb=["background","padding"];function Mc(e,t){const n={};for(const i of Kb)e&&e[i]!==void 0&&(n[i]=Pe(e[i]));return t&&(n.params=e.params),n}class jt{constructor(t={},n={}){this.explicit=t,this.implicit=n}clone(){return new jt(j(this.explicit),j(this.implicit))}combine(){return{...this.explicit,...this.implicit}}get(t){return ue(this.explicit[t],this.implicit[t])}getWithExplicit(t){return this.explicit[t]!==void 0?{explicit:!0,value:this.explicit[t]}:this.implicit[t]!==void 0?{explicit:!1,value:this.implicit[t]}:{explicit:!1,value:void 0}}setWithExplicit(t,{value:n,explicit:i}){n!==void 0&&this.set(t,n,i)}set(t,n,i){return delete this[i?"implicit":"explicit"][t],this[i?"explicit":"implicit"][t]=n,this}copyKeyFromSplit(t,{explicit:n,implicit:i}){n[t]!==void 0?this.set(t,n[t],!0):i[t]!==void 0&&this.set(t,i[t],!1)}copyKeyFromObject(t,n){n[t]!==void 0&&this.set(t,n[t],!0)}copyAll(t){for(const n of v(t.combine())){const i=t.getWithExplicit(n);this.setWithExplicit(n,i)}}}function ct(e){return{explicit:!0,value:e}}function Le(e){return{explicit:!1,value:e}}function nd(e){return(t,n,i,r)=>{const s=e(t.value,n.value);return s>0?t:s<0?n:rs(t,n,i,r)}}function rs(e,t,n,i){return e.explicit&&t.explicit&&S(Ih(n,i,e.value,t.value)),e}function Ht(e,t,n,i,r=rs){return e===void 0||e.value===void 0?t:e.explicit&&!t.explicit?e:t.explicit&&!e.explicit?t:lt(e.value,t.value)?e:r(e,t,n,i)}class Jb extends jt{constructor(t={},n={},i=!1){super(t,n),this.explicit=t,this.implicit=n,this.parseNothing=i}clone(){const t=super.clone();return t.parseNothing=this.parseNothing,t}}function oi(e){return"url"in e}function Di(e){return"values"in e}function id(e){return"name"in e&&!oi(e)&&!Di(e)&&!Wt(e)}function Wt(e){return e&&(rd(e)||sd(e)||_a(e))}function rd(e){return"sequence"in e}function sd(e){return"sphere"in e}function _a(e){return"graticule"in e}var Z;(function(e){e[e.Raw=0]="Raw",e[e.Main=1]="Main",e[e.Row=2]="Row",e[e.Column=3]="Column",e[e.Lookup=4]="Lookup"})(Z||(Z={}));function od(e){const{signals:t,hasLegend:n,index:i,...r}=e;return r.field=Me(r.field),r}function Fn(e,t=!0,n=Rp){if(A(e)){const i=e.map(r=>Fn(r,t,n));return t?`[${i.join(", ")}]`:i}else if(Rn(e))return n(t?En(e):sm(e));return t?n(ee(e)):e}function Qb(e,t){for(const n of ve(e.component.selection??{})){const i=n.name;let r=`${i}${Xt}, ${n.resolve==="global"?"true":`{unit: ${yn(e)}}`}`;for(const s of os)s.defined(n)&&(s.signals&&(t=s.signals(e,n,t)),s.modifyExpr&&(r=s.modifyExpr(e,n,r)));t.push({name:i+Tx,on:[{events:{signal:n.name+Xt},update:`modify(${B(n.name+Nn)}, ${r})`}]})}return ka(t)}function Zb(e,t){if(e.component.selection&&v(e.component.selection).length){const n=B(e.getName("cell"));t.unshift({name:"facet",value:{},on:[{events:pi("mousemove","scope"),update:`isTuple(facet) ? facet : group(${n}).datum`}]})}return ka(t)}function ex(e,t){let n=!1;for(const i of ve(e.component.selection??{})){const r=i.name,s=B(r+Nn);if(t.filter(a=>a.name===r).length===0){const a=i.resolve==="global"?"union":i.resolve,c=i.type==="point"?", true, true)":")";t.push({name:i.name,update:`${$d}(${s}, ${B(a)}${c}`})}n=!0;for(const a of os)a.defined(i)&&a.topLevelSignals&&(t=a.topLevelSignals(e,i,t))}return n&&t.filter(r=>r.name==="unit").length===0&&t.unshift({name:"unit",value:{},on:[{events:"mousemove",update:"isTuple(group()) ? group() : unit"}]}),ka(t)}function tx(e,t){const n=[...t],i=yn(e,{escape:!1});for(const r of ve(e.component.selection??{})){const s={name:r.name+Nn};if(r.project.hasSelectionId&&(s.transform=[{type:"collect",sort:{field:Ze}}]),r.init){const a=r.project.items.map(od);s.values=r.project.hasSelectionId?r.init.map(c=>({unit:i,[Ze]:Fn(c,!1)[0]})):r.init.map(c=>({unit:i,fields:a,values:Fn(c,!1)}))}n.filter(a=>a.name===r.name+Nn).length||n.push(s)}return n}function ad(e,t){for(const n of ve(e.component.selection??{}))for(const i of os)i.defined(n)&&i.marks&&(t=i.marks(e,n,t));return t}function nx(e,t){for(const n of e.children)ae(n)&&(t=ad(n,t));return t}function ix(e,t,n,i){const r=_d(e,t.param,t);return{signal:ze(n.get("type"))&&A(i)&&i[0]>i[1]?`isValid(${r}) && reverse(${r})`:r}}function ka(e){return e.map(t=>(t.on&&!t.on.length&&delete t.on,t))}class K{constructor(t,n){this.debugName=n,this._children=[],this._parent=null,t&&(this.parent=t)}clone(){throw new Error("Cannot clone node")}get parent(){return this._parent}set parent(t){this._parent=t,t&&t.addChild(this)}get children(){return this._children}numChildren(){return this._children.length}addChild(t,n){if(this._children.includes(t)){S(oh);return}n!==void 0?this._children.splice(n,0,t):this._children.push(t)}removeChild(t){const n=this._children.indexOf(t);return this._children.splice(n,1),n}remove(){let t=this._parent.removeChild(this);for(const n of this._children)n._parent=this._parent,this._parent.addChild(n,t++)}insertAsParentOf(t){const n=t.parent;n.removeChild(this),this.parent=n,t.parent=this}swapWithParent(){const t=this._parent,n=t.parent;for(const r of this._children)r.parent=t;this._children=[],t.removeChild(this);const i=t.parent.removeChild(t);this._parent=n,n.addChild(this,i),t.parent=this}}class Ce extends K{clone(){const t=new this.constructor;return t.debugName=`clone_${this.debugName}`,t._source=this._source,t._name=`clone_${this._name}`,t.type=this.type,t.refCounts=this.refCounts,t.refCounts[t._name]=0,t}constructor(t,n,i,r){super(t,n),this.type=i,this.refCounts=r,this._source=this._name=n,this.refCounts&&!(this._name in this.refCounts)&&(this.refCounts[this._name]=0)}dependentFields(){return new Set}producedFields(){return new Set}hash(){return this._hash===void 0&&(this._hash=`Output ${nu()}`),this._hash}getSource(){return this.refCounts[this._name]++,this._source}isRequired(){return!!this.refCounts[this._name]}setSource(t){this._source=t}}function Os(e){return e.as!==void 0}function Uc(e){return`${e}_end`}class dt extends K{clone(){return new dt(null,j(this.formula))}constructor(t,n){super(t),this.formula=n}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s)=>{const{field:o,timeUnit:a}=s;if(a){let c;if(In(a)){if(ae(n)){const{mark:l}=n;(sa(l)||s.bandPosition)&&(c={timeUnit:he(a),field:o})}}else c={as:w(s,{forAs:!0}),field:o,timeUnit:a};c&&(r[W(c)]=c)}return r},{});return J(i)?null:new dt(t,i)}static makeFromTransform(t,n){const{timeUnit:i,...r}={...n},s=he(i),o={...r,timeUnit:s};return new dt(t,{[W(o)]:o})}merge(t){this.formula={...this.formula};for(const n in t.formula)this.formula[n]||(this.formula[n]=t.formula[n]);for(const n of t.children)t.removeChild(n),n.parent=this;t.remove()}removeFormulas(t){const n={};for(const[i,r]of Gt(this.formula)){const s=Os(r)?r.as:`${r.field}_end`;t.has(s)||(n[i]=r)}this.formula=n}producedFields(){return new Set(ve(this.formula).map(t=>Os(t)?t.as:Uc(t.field)))}dependentFields(){return new Set(ve(this.formula).map(t=>t.field))}hash(){return`TimeUnit ${W(this.formula)}`}assemble(){const t=[];for(const n of ve(this.formula))if(Os(n)){const{field:i,as:r,timeUnit:s}=n,{unit:o,utc:a,...c}=he(s);t.push({field:Me(i),type:"timeunit",...o?{units:Wr(o)}:{},...a?{timezone:"utc"}:{},...c,as:[r,`${r}_end`]})}else if(n){const{field:i,timeUnit:r}=n,s=ju(r?.unit),{part:o,step:a}=Wu(s,r.step);t.push({type:"formula",expr:`timeOffset('${o}', datum['${i}'], ${a})`,as:Uc(i)})}return t}}const Zi="_tuple_fields";class rx{constructor(...t){this.items=t,this.hasChannel={},this.hasField={},this.hasSelectionId=!1}}const sx={defined:()=>!0,parse:(e,t,n)=>{const i=t.name,r=t.project??(t.project=new rx),s={},o={},a=new Set,c=(g,h)=>{const m=h==="visual"?g.channel:g.field;let y=se(`${i}_${m}`);for(let b=1;a.has(y);b++)y=se(`${i}_${m}_${b}`);return a.add(y),{[h]:y}},l=t.type,u=e.config.selection[l],f=n.value!==void 0?ce(n.value):null;let{fields:d,encodings:p}=V(n.select)?n.select:{};if(!d&&!p&&f){for(const g of f)if(V(g))for(const h of v(g))$g(h)?(p||(p=[])).push(h):l==="interval"?(S(nh),p=u.encodings):(d??(d=[])).push(h)}!d&&!p&&(p=u.encodings,"fields"in u&&(d=u.fields));for(const g of p??[]){const h=e.fieldDef(g);if(h){let m=h.field;if(h.aggregate){S(Xg(g,h.aggregate));continue}else if(!m){S(pc(g));continue}if(h.timeUnit&&!In(h.timeUnit)){m=e.vgField(g);const y={timeUnit:h.timeUnit,as:m,field:h.field};o[W(y)]=y}if(!s[m]){const y=l==="interval"&&zt(g)&&ze(e.getScaleComponent(g).get("type"))?"R":h.bin?"R-RE":"E",b={field:m,channel:g,type:y,index:r.items.length};b.signals={...c(b,"data"),...c(b,"visual")},r.items.push(s[m]=b),r.hasField[m]=s[m],r.hasSelectionId=r.hasSelectionId||m===Ze,cu(g)?(b.geoChannel=g,b.channel=au(g),r.hasChannel[b.channel]=s[m]):r.hasChannel[g]=s[m]}}else S(pc(g))}for(const g of d??[]){if(r.hasField[g])continue;const h={type:"E",field:g,index:r.items.length};h.signals={...c(h,"data")},r.items.push(h),r.hasField[g]=h,r.hasSelectionId=r.hasSelectionId||g===Ze}f&&(t.init=f.map(g=>r.items.map(h=>V(g)?g[h.geoChannel||h.channel]!==void 0?g[h.geoChannel||h.channel]:g[h.field]:g))),J(o)||(r.timeUnit=new dt(null,o))},signals:(e,t,n)=>{const i=t.name+Zi;return n.filter(s=>s.name===i).length>0||t.project.hasSelectionId?n:n.concat({name:i,value:t.project.items.map(od)})}},kt={defined:e=>e.type==="interval"&&e.resolve==="global"&&e.bind&&e.bind==="scales",parse:(e,t)=>{const n=t.scales=[];for(const i of t.project.items){const r=i.channel;if(!zt(r))continue;const s=e.getScaleComponent(r),o=s?s.get("type"):void 0;if(!s||!ze(o)){S(Jg);continue}s.set("selectionExtent",{param:t.name,field:i.field},!0),n.push(i)}},topLevelSignals:(e,t,n)=>{const i=t.scales.filter(o=>n.filter(a=>a.name===o.signals.data).length===0);if(!e.parent||Bc(e)||i.length===0)return n;const r=n.filter(o=>o.name===t.name)[0];let s=r.update;if(s.indexOf($d)>=0)r.update=`{${i.map(o=>`${B(Me(o.field))}: ${o.signals.data}`).join(", ")}}`;else{for(const o of i){const a=`${B(Me(o.field))}: ${o.signals.data}`;s.includes(a)||(s=`${s.substring(0,s.length-1)}, ${a}}`)}r.update=s}return n.concat(i.map(o=>({name:o.signals.data})))},signals:(e,t,n)=>{if(e.parent&&!Bc(e))for(const i of t.scales){const r=n.filter(s=>s.name===i.signals.data)[0];r.push="outer",delete r.value,delete r.update}return n}};function oo(e,t){return`domain(${B(e.scaleName(t))})`}function Bc(e){return e.parent&&Si(e.parent)&&!e.parent.parent}const Xn="_brush",cd="_scale_trigger",$i="geo_interval_init_tick",ld="_init",ox="_center",ax={defined:e=>e.type==="interval",parse:(e,t,n)=>{var i;if(e.hasProjection){const r={...V(n.select)?n.select:{}};r.fields=[Ze],r.encodings||(r.encodings=n.value?v(n.value):[it,nt]),n.select={type:"interval",...r}}if(t.translate&&!kt.defined(t)){const r=`!event.item || event.item.mark.name !== ${B(t.name+Xn)}`;for(const s of t.events){if(!s.between){S(`${s} is not an ordered event stream for interval selections.`);continue}const o=ce((i=s.between[0]).filter??(i.filter=[]));o.indexOf(r)<0&&o.push(r)}}},signals:(e,t,n)=>{const i=t.name,r=i+Xt,s=ve(t.project.hasChannel).filter(a=>a.channel===oe||a.channel===me),o=t.init?t.init[0]:null;if(n.push(...s.reduce((a,c)=>a.concat(cx(e,t,c,o&&o[c.index])),[])),e.hasProjection){const a=B(e.projectionName()),c=e.projectionName()+ox,{x:l,y:u}=t.project.hasChannel,f=l&&l.signals.visual,d=u&&u.signals.visual,p=l?o&&o[l.index]:`${c}[0]`,g=u?o&&o[u.index]:`${c}[1]`,h=x=>e.getSizeSignalRef(x).signal,m=`[[${f?f+"[0]":"0"}, ${d?d+"[0]":"0"}],[${f?f+"[1]":h("width")}, ${d?d+"[1]":h("height")}]]`;o&&(n.unshift({name:i+ld,init:`[scale(${a}, [${l?p[0]:p}, ${u?g[0]:g}]), scale(${a}, [${l?p[1]:p}, ${u?g[1]:g}])]`}),(!l||!u)&&(n.find(_=>_.name===c)||n.unshift({name:c,update:`invert(${a}, [${h("width")}/2, ${h("height")}/2])`})));const y=`intersect(${m}, {markname: ${B(e.getName("marks"))}}, unit.mark)`,b=`{unit: ${yn(e)}}`,k=`vlSelectionTuples(${y}, ${b})`,T=s.map(x=>x.signals.visual);return n.concat({name:r,on:[{events:[...T.length?[{signal:T.join(" || ")}]:[],...o?[{signal:$i}]:[]],update:k}]})}else{if(!kt.defined(t)){const l=i+cd,u=s.map(f=>{const d=f.channel,{data:p,visual:g}=f.signals,h=B(e.scaleName(d)),m=e.getScaleComponent(d).get("type"),y=ze(m)?"+":"";return`(!isArray(${p}) || (${y}invert(${h}, ${g})[0] === ${y}${p}[0] && ${y}invert(${h}, ${g})[1] === ${y}${p}[1]))`});u.length&&n.push({name:l,value:{},on:[{events:s.map(f=>({scale:e.scaleName(f.channel)})),update:u.join(" && ")+` ? ${l} : {}`}]})}const a=s.map(l=>l.signals.data),c=`unit: ${yn(e)}, fields: ${i+Zi}, values`;return n.concat({name:r,...o?{init:`{${c}: ${Fn(o)}}`}:{},...a.length?{on:[{events:[{signal:a.join(" || ")}],update:`${a.join(" && ")} ? {${c}: [${a}]} : null`}]}:{}})}},topLevelSignals:(e,t,n)=>(ae(e)&&e.hasProjection&&t.init&&(n.filter(r=>r.name===$i).length||n.unshift({name:$i,value:null,on:[{events:"timer{1}",update:`${$i} === null ? {} : ${$i}`}]})),n),marks:(e,t,n)=>{const i=t.name,{x:r,y:s}=t.project.hasChannel,o=r?.signals.visual,a=s?.signals.visual,c=`data(${B(t.name+Nn)})`;if(kt.defined(t)||!r&&!s)return n;const l={x:r!==void 0?{signal:`${o}[0]`}:{value:0},y:s!==void 0?{signal:`${a}[0]`}:{value:0},x2:r!==void 0?{signal:`${o}[1]`}:{field:{group:"width"}},y2:s!==void 0?{signal:`${a}[1]`}:{field:{group:"height"}}};if(t.resolve==="global")for(const h of v(l))l[h]=[{test:`${c}.length && ${c}[0].unit === ${yn(e)}`,...l[h]},{value:0}];const{fill:u,fillOpacity:f,cursor:d,...p}=t.mark,g=v(p).reduce((h,m)=>(h[m]=[{test:[r!==void 0&&`${o}[0] !== ${o}[1]`,s!==void 0&&`${a}[0] !== ${a}[1]`].filter(y=>y).join(" && "),value:p[m]},{value:null}],h),{});return[{name:`${i+Xn}_bg`,type:"rect",clip:!0,encode:{enter:{fill:{value:u},fillOpacity:{value:f}},update:l}},...n,{name:i+Xn,type:"rect",clip:!0,encode:{enter:{...d?{cursor:{value:d}}:{},fill:{value:"transparent"}},update:{...l,...g}}}]}};function cx(e,t,n,i){const r=!e.hasProjection,s=n.channel,o=n.signals.visual,a=B(r?e.scaleName(s):e.projectionName()),c=d=>`scale(${a}, ${d})`,l=e.getSizeSignalRef(s===oe?"width":"height").signal,u=`${s}(unit)`,f=t.events.reduce((d,p)=>[...d,{events:p.between[0],update:`[${u}, ${u}]`},{events:p,update:`[${o}[0], clamp(${u}, 0, ${l})]`}],[]);if(r){const d=n.signals.data,p=kt.defined(t),g=e.getScaleComponent(s),h=g?g.get("type"):void 0,m=i?{init:Fn(i,!0,c)}:{value:[]};return f.push({events:{signal:t.name+cd},update:ze(h)?`[${c(`${d}[0]`)}, ${c(`${d}[1]`)}]`:"[0, 0]"}),p?[{name:d,on:[]}]:[{name:o,...m,on:f},{name:d,...i?{init:Fn(i)}:{},on:[{events:{signal:o},update:`${o}[0] === ${o}[1] ? null : invert(${a}, ${o})`}]}]}else{const d=s===oe?0:1,p=t.name+ld,g=i?{init:`[${p}[0][${d}], ${p}[1][${d}]]`}:{value:[]};return[{name:o,...g,on:f}]}}const lx={defined:e=>e.type==="point",signals:(e,t,n)=>{const i=t.name,r=i+Zi,s=t.project,o="(item().isVoronoi ? datum.datum : datum)",a=ve(e.component.selection??{}).reduce((f,d)=>d.type==="interval"?f.concat(d.name+Xn):f,[]).map(f=>`indexof(item().mark.name, '${f}') < 0`).join(" && "),c=`datum && item().mark.marktype !== 'group' && indexof(item().mark.role, 'legend') < 0${a?` && ${a}`:""}`;let l=`unit: ${yn(e)}, `;if(t.project.hasSelectionId)l+=`${Ze}: ${o}[${B(Ze)}]`;else{const f=s.items.map(d=>e.fieldDef(d.channel)?.bin?`[${o}[${B(e.vgField(d.channel,{}))}], ${o}[${B(e.vgField(d.channel,{binSuffix:"end"}))}]]`:`${o}[${B(d.field)}]`).join(", ");l+=`fields: ${r}, values: [${f}]`}const u=t.events;return n.concat([{name:i+Xt,on:u?[{events:u,update:`${c} ? {${l}} : null`,force:!0}]:[]}])}};function yi(e,t,n,i){const r=Kr(t)&&t.condition,s=i(t);if(r){const a=ce(r).map(c=>{const l=i(c);if(iy(c)){const{param:u,empty:f}=c;return{test:Nd(e,{param:u,empty:f}),...l}}else return{test:_r(e,c.test),...l}});return{[n]:[...a,...s!==void 0?[s]:[]]}}else return s!==void 0?{[n]:s}:{}}function Ta(e,t="text"){const n=e.encoding[t];return yi(e,n,t,i=>ss(i,e.config))}function ss(e,t,n="datum"){if(e){if(Qe(e))return re(e.value);if(M(e)){const{format:i,formatType:r}=Sr(e);return la({fieldOrDatumDef:e,format:i,formatType:r,expr:n,config:t})}}}function ud(e,t={}){const{encoding:n,markDef:i,config:r,stack:s}=e,o=n.tooltip;if(A(o))return{tooltip:Wc({tooltip:o},s,r,t)};{const a=t.reactiveGeom?"datum.datum":"datum";return yi(e,o,"tooltip",c=>{const l=ss(c,r,a);if(l)return l;if(c===null)return;let u=X("tooltip",i,r);if(u===!0&&(u={content:"encoding"}),z(u))return{value:u};if(V(u))return I(u)?u:u.content==="encoding"?Wc(n,s,r,t):{signal:a}})}}function fd(e,t,n,{reactiveGeom:i}={}){const r={...n,...n.tooltipFormat},s={},o=i?"datum.datum":"datum",a=[];function c(u,f){const d=On(f),p=Fe(u)?u:{...u,type:e[d].type},g=p.title||pa(p,r),h=ce(g).join(", ");let m;if(ye(f)){const y=f==="x"?"x2":"y2",b=ht(e[y]);if(xe(p.bin)&&b){const k=w(p,{expr:o}),T=w(b,{expr:o}),{format:x,formatType:_}=Sr(p);m=Xi(k,T,x,_,r),s[y]=!0}}if((ye(f)||f===Be||f===tt)&&t&&t.fieldChannel===f&&t.offset==="normalize"){const{format:y,formatType:b}=Sr(p);m=la({fieldOrDatumDef:p,format:y,formatType:b,expr:o,config:r,normalizeStack:!0}).signal}m??(m=ss(p,r,o).signal),a.push({channel:f,key:h,value:m})}ma(e,(u,f)=>{E(u)?c(u,f):Jr(u)&&c(u.condition,f)});const l={};for(const{channel:u,key:f,value:d}of a)!s[u]&&!l[f]&&(l[f]=d);return l}function Wc(e,t,n,{reactiveGeom:i}={}){const r=fd(e,t,n,{reactiveGeom:i}),s=Gt(r).map(([o,a])=>`"${o}": ${a}`);return s.length>0?{signal:`{${s.join(", ")}}`}:void 0}function ux(e){const{markDef:t,config:n}=e,i=X("aria",t,n);return i===!1?{}:{...i?{aria:i}:{},...fx(e),...dx(e)}}function fx(e){const{mark:t,markDef:n,config:i}=e;if(i.aria===!1)return{};const r=X("ariaRoleDescription",n,i);return r!=null?{ariaRoleDescription:{value:r}}:t in Gg?{}:{ariaRoleDescription:{value:t}}}function dx(e){const{encoding:t,markDef:n,config:i,stack:r}=e,s=t.description;if(s)return yi(e,s,"description",c=>ss(c,e.config));const o=X("description",n,i);if(o!=null)return{description:re(o)};if(i.aria===!1)return{};const a=fd(t,r,i);if(!J(a))return{description:{signal:Gt(a).map(([c,l],u)=>`"${u>0?"; ":""}${c}: " + (${l})`).join(" + ")}}}function ge(e,t,n={}){const{markDef:i,encoding:r,config:s}=t,{vgChannel:o}=n;let{defaultRef:a,defaultValue:c}=n;a===void 0&&(c??(c=X(e,i,s,{vgChannel:o,ignoreVgConfig:!0})),c!==void 0&&(a=re(c)));const l=r[e];return yi(t,l,o??e,u=>ca({channel:e,channelDef:u,markDef:i,config:s,scaleName:t.scaleName(e),scale:t.getScaleComponent(e),stack:null,defaultRef:a}))}function dd(e,t={filled:void 0}){const{markDef:n,encoding:i,config:r}=e,{type:s}=n,o=t.filled??X("filled",n,r),a=G(["bar","point","circle","square","geoshape"],s)?"transparent":void 0,c=X(o===!0?"color":void 0,n,r,{vgChannel:"fill"})??r.mark[o===!0&&"color"]??a,l=X(o===!1?"color":void 0,n,r,{vgChannel:"stroke"})??r.mark[o===!1&&"color"],u=o?"fill":"stroke",f={...c?{fill:re(c)}:{},...l?{stroke:re(l)}:{}};return n.color&&(o?n.fill:n.stroke)&&S(Tu("property",{fill:"fill"in n,stroke:"stroke"in n})),{...f,...ge("color",e,{vgChannel:u,defaultValue:o?c:l}),...ge("fill",e,{defaultValue:i.fill?c:void 0}),...ge("stroke",e,{defaultValue:i.stroke?l:void 0})}}function px(e){const{encoding:t,mark:n}=e,i=t.order;return!rn(n)&&Qe(i)?yi(e,i,"zindex",r=>re(r.value)):{}}function ai({channel:e,markDef:t,encoding:n={},model:i,bandPosition:r}){const s=`${e}Offset`,o=t[s],a=n[s];if((s==="xOffset"||s==="yOffset")&&a)return{offsetType:"encoding",offset:ca({channel:s,channelDef:a,markDef:t,config:i?.config,scaleName:i.scaleName(s),scale:i.getScaleComponent(s),stack:null,defaultRef:re(o),bandPosition:r})};const c=t[s];return c?{offsetType:"visual",offset:c}:{}}function $e(e,t,{defaultPos:n,vgChannel:i}){const{encoding:r,markDef:s,config:o,stack:a}=t,c=r[e],l=r[vt(e)],u=t.scaleName(e),f=t.getScaleComponent(e),{offset:d,offsetType:p}=ai({channel:e,markDef:s,encoding:r,model:t,bandPosition:.5}),g=Oa({model:t,defaultPos:n,channel:e,scaleName:u,scale:f}),h=!c&&ye(e)&&(r.latitude||r.longitude)?{field:t.getName(e)}:gx({channel:e,channelDef:c,channel2Def:l,markDef:s,config:o,scaleName:u,scale:f,stack:a,offset:d,defaultRef:g,bandPosition:p==="encoding"?0:void 0});return h?{[i||e]:h}:void 0}function gx(e){const{channel:t,channelDef:n,scaleName:i,stack:r,offset:s,markDef:o}=e;if(M(n)&&r&&t===r.fieldChannel){if(E(n)){let a=n.bandPosition;if(a===void 0&&o.type==="text"&&(t==="radius"||t==="theta")&&(a=.5),a!==void 0)return br({scaleName:i,fieldOrDatumDef:n,startSuffix:"start",bandPosition:a,offset:s})}return hn(n,i,{suffix:"end"},{offset:s})}return oa(e)}function Oa({model:e,defaultPos:t,channel:n,scaleName:i,scale:r}){const{markDef:s,config:o}=e;return()=>{const a=On(n),c=qt(n),l=X(n,s,o,{vgChannel:c});if(l!==void 0)return Ri(n,l);switch(t){case"zeroOrMin":case"zeroOrMax":if(i){const u=r.get("type");if(!G([Te.LOG,Te.TIME,Te.UTC],u)){if(r.domainDefinitelyIncludesZero())return{scale:i,value:0}}}if(t==="zeroOrMin")return a==="y"?{field:{group:"height"}}:{value:0};switch(a){case"radius":return{signal:`min(${e.width.signal},${e.height.signal})/2`};case"theta":return{signal:"2*PI"};case"x":return{field:{group:"width"}};case"y":return{value:0}}break;case"mid":return{...e[Ie(n)],mult:.5}}}}const hx={left:"x",center:"xc",right:"x2"},mx={top:"y",middle:"yc",bottom:"y2"};function pd(e,t,n,i="middle"){if(e==="radius"||e==="theta")return qt(e);const r=e==="x"?"align":"baseline",s=X(r,t,n);let o;return I(s)?(S($h(r)),o=void 0):o=s,e==="x"?hx[o||(i==="top"?"left":"center")]:mx[o||i]}function Fr(e,t,{defaultPos:n,defaultPos2:i,range:r}){return r?gd(e,t,{defaultPos:n,defaultPos2:i}):$e(e,t,{defaultPos:n})}function gd(e,t,{defaultPos:n,defaultPos2:i}){const{markDef:r,config:s}=t,o=vt(e),a=Ie(e),c=yx(t,i,o),l=c[a]?pd(e,r,s):qt(e);return{...$e(e,t,{defaultPos:n,vgChannel:l}),...c}}function yx(e,t,n){const{encoding:i,mark:r,markDef:s,stack:o,config:a}=e,c=On(n),l=Ie(n),u=qt(n),f=i[c],d=e.scaleName(c),p=e.getScaleComponent(c),{offset:g}=n in i||n in s?ai({channel:n,markDef:s,encoding:i,model:e}):ai({channel:c,markDef:s,encoding:i,model:e});if(!f&&(n==="x2"||n==="y2")&&(i.latitude||i.longitude)){const m=Ie(n),y=e.markDef[m];return y!=null?{[m]:{value:y}}:{[u]:{field:e.getName(n)}}}const h=bx({channel:n,channelDef:f,channel2Def:i[n],markDef:s,config:a,scaleName:d,scale:p,stack:o,offset:g,defaultRef:void 0});return h!==void 0?{[u]:h}:rr(n,s)||rr(n,{[n]:hr(n,s,a.style),[l]:hr(l,s,a.style)})||rr(n,a[r])||rr(n,a.mark)||{[u]:Oa({model:e,defaultPos:t,channel:n,scaleName:d,scale:p})()}}function bx({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l}){return M(t)&&a&&e.charAt(0)===a.fieldChannel.charAt(0)?hn(t,s,{suffix:"start"},{offset:c}):oa({channel:e,channelDef:n,scaleName:s,scale:o,stack:a,markDef:i,config:r,offset:c,defaultRef:l})}function rr(e,t){const n=Ie(e),i=qt(e);if(t[i]!==void 0)return{[i]:Ri(e,t[i])};if(t[e]!==void 0)return{[i]:Ri(e,t[e])};if(t[n]){const r=t[n];if(wn(r))S(mh(n));else return{[n]:Ri(e,r)}}}function Vt(e,t){const{config:n,encoding:i,markDef:r}=e,s=r.type,o=vt(t),a=Ie(t),c=i[t],l=i[o],u=e.getScaleComponent(t),f=u?u.get("type"):void 0,d=r.orient,p=i[a]??i.size??X("size",r,n,{vgChannel:a}),g=du(t),h=s==="bar"&&(t==="x"?d==="vertical":d==="horizontal");return E(c)&&(te(c.bin)||xe(c.bin)||c.timeUnit&&!l)&&!(p&&!wn(p))&&!i[g]&&!be(f)?Sx({fieldDef:c,fieldDef2:l,channel:t,model:e}):(M(c)&&be(f)||h)&&!l?vx(c,t,e):gd(t,e,{defaultPos:"zeroOrMax",defaultPos2:"zeroOrMin"})}function xx(e,t,n,i,r,s,o){if(wn(r))if(n){const c=n.get("type");if(c==="band"){let l=`bandwidth('${t}')`;r.band!==1&&(l=`${r.band} * ${l}`);const u=At("minBandSize",{type:o},i);return{signal:u?`max(${Ve(u)}, ${l})`:l}}else r.band!==1&&(S(Nh(c)),r=void 0)}else return{mult:r.band,field:{group:e}};else{if(I(r))return r;if(r)return{value:r}}if(n){const c=n.get("range");if(nn(c)&&le(c.step))return{value:c.step-2}}if(!s){const{bandPaddingInner:c,barBandPaddingInner:l,rectBandPaddingInner:u}=i.scale,f=ue(c,o==="bar"?l:u);if(I(f))return{signal:`(1 - (${f.signal})) * ${e}`};if(le(f))return{signal:`${1-f} * ${e}`}}return{value:wr(i.view,e)-2}}function vx(e,t,n){const{markDef:i,encoding:r,config:s,stack:o}=n,a=i.orient,c=n.scaleName(t),l=n.getScaleComponent(t),u=Ie(t),f=vt(t),d=du(t),p=n.scaleName(d),g=n.getScaleComponent(Po(t)),h=a==="horizontal"&&t==="y"||a==="vertical"&&t==="x";let m;(r.size||i.size)&&(h?m=ge("size",n,{vgChannel:u,defaultRef:re(i.size)}):S(Oh(i.type)));const y=!!m,b=pf({channel:t,fieldDef:e,markDef:i,config:s,scaleType:l?.get("type"),useVlSizeChannel:h});m=m||{[u]:xx(u,p||c,g||l,s,b,!!e,i.type)};const k=l?.get("type")==="band"&&wn(b)&&!y?"top":"middle",T=pd(t,i,s,k),x=T==="xc"||T==="yc",{offset:_,offsetType:C}=ai({channel:t,markDef:i,encoding:r,model:n,bandPosition:x?.5:0}),N=oa({channel:t,channelDef:e,markDef:i,config:s,scaleName:c,scale:l,stack:o,offset:_,defaultRef:Oa({model:n,defaultPos:"mid",channel:t,scaleName:c,scale:l}),bandPosition:x?C==="encoding"?0:.5:I(b)?{signal:`(1-${b})/2`}:wn(b)?(1-b.band)/2:0});if(u)return{[T]:N,...m};{const Y=qt(f),ne=m[u],de=_?{...ne,offset:_}:ne;return{[T]:N,[Y]:A(N)?[N[0],{...N[1],offset:de}]:{...N,offset:de}}}}function Gc(e,t,n,i,r,s,o){if(ou(e))return 0;const a=e==="x"||e==="y2",c=a?-t/2:t/2;if(I(n)||I(r)||I(i)||s){const l=Ve(n),u=Ve(r),f=Ve(i),d=Ve(s),g=s?`(${o} < ${d} ? ${a?"":"-"}0.5 * (${d} - (${o})) : ${c})`:c,h=f?`${f} + `:"",m=l?`(${l} ? -1 : 1) * `:"",y=u?`(${u} + ${g})`:g;return{signal:h+m+y}}else return r=r||0,i+(n?-r-c:+r+c)}function Sx({fieldDef:e,fieldDef2:t,channel:n,model:i}){const{config:r,markDef:s,encoding:o}=i,a=i.getScaleComponent(n),c=i.scaleName(n),l=a?a.get("type"):void 0,u=a.get("reverse"),f=pf({channel:n,fieldDef:e,markDef:s,config:r,scaleType:l}),p=i.component.axes[n]?.[0]?.get("translate")??.5,g=ye(n)?X("binSpacing",s,r)??0:0,h=vt(n),m=qt(n),y=qt(h),b=At("minBandSize",s,r),{offset:k}=ai({channel:n,markDef:s,encoding:o,model:i,bandPosition:0}),{offset:T}=ai({channel:h,markDef:s,encoding:o,model:i,bandPosition:0}),x=Qm({fieldDef:e,scaleName:c}),_=Gc(n,g,u,p,k,b,x),C=Gc(h,g,u,p,T??k,b,x),N=I(f)?{signal:`(1-${f.signal})/2`}:wn(f)?(1-f.band)/2:.5;if(te(e.bin)||e.timeUnit)return{[y]:qc({fieldDef:e,scaleName:c,bandPosition:N,offset:C}),[m]:qc({fieldDef:e,scaleName:c,bandPosition:I(N)?{signal:`1-${N.signal}`}:1-N,offset:_})};if(xe(e.bin)){const Y=hn(e,c,{},{offset:C});if(E(t))return{[y]:Y,[m]:hn(t,c,{},{offset:_})};if(An(e.bin)&&e.bin.step)return{[y]:Y,[m]:{signal:`scale("${c}", ${w(e,{expr:"datum"})} + ${e.bin.step})`,offset:_}}}S(Ru(h))}function qc({fieldDef:e,scaleName:t,bandPosition:n,offset:i}){return br({scaleName:t,fieldOrDatumDef:e,bandPosition:n,offset:i})}const Ex=new Set(["aria","width","height"]);function We(e,t){const{fill:n=void 0,stroke:i=void 0}=t.color==="include"?dd(e):{};return{...$x(e.markDef,t),...Hc(e,"fill",n),...Hc(e,"stroke",i),...ge("opacity",e),...ge("fillOpacity",e),...ge("strokeOpacity",e),...ge("strokeWidth",e),...ge("strokeDash",e),...px(e),...ud(e),...Ta(e,"href"),...ux(e)}}function Hc(e,t,n){const{config:i,mark:r,markDef:s}=e;if(X("invalid",s,i)==="hide"&&n&&!rn(r)){const a=wx(e,{invalid:!0,channels:Mr});if(a)return{[t]:[{test:a,value:null},...ce(n)]}}return n?{[t]:n}:{}}function $x(e,t){return Wg.reduce((n,i)=>(!Ex.has(i)&&e[i]!==void 0&&t[i]!=="ignore"&&(n[i]=re(e[i])),n),{})}function wx(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{const a=e.getScaleComponent(o);if(a){const c=a.get("type"),l=e.vgField(o,{expr:"datum"});l&&ze(c)&&(s[l]=!0)}return s},{}),r=v(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>aa(o,t)).join(` ${s} `)}}function Aa(e){const{config:t,markDef:n}=e;if(X("invalid",n,t)){const r=Cx(e,{channels:St});if(r)return{defined:{signal:r}}}return{}}function Cx(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{const a=e.getScaleComponent(o);if(a){const c=a.get("type"),l=e.vgField(o,{expr:"datum",binSuffix:e.stack?.impute?"mid":void 0});l&&ze(c)&&(s[l]=!0)}return s},{}),r=v(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>aa(o,t)).join(` ${s} `)}}function Vc(e,t){if(t!==void 0)return{[e]:re(t)}}const As="voronoi",hd={defined:e=>e.type==="point"&&e.nearest,parse:(e,t)=>{if(t.events)for(const n of t.events)n.markname=e.getName(As)},marks:(e,t,n)=>{const{x:i,y:r}=t.project.hasChannel,s=e.mark;if(rn(s))return S(Yg(s)),n;const o={name:e.getName(As),type:"path",interactive:!0,from:{data:e.getName("marks")},encode:{update:{fill:{value:"transparent"},strokeWidth:{value:.35},stroke:{value:"transparent"},isVoronoi:{value:!0},...ud(e,{reactiveGeom:!0})}},transform:[{type:"voronoi",x:{expr:i||!r?"datum.datum.x || 0":"0"},y:{expr:r||!i?"datum.datum.y || 0":"0"},size:[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]}]};let a=0,c=!1;return n.forEach((l,u)=>{const f=l.name??"";f===e.component.mark[0].name?a=u:f.indexOf(As)>=0&&(c=!0)}),c||n.splice(a+1,0,o),n}},md={defined:e=>e.type==="point"&&e.resolve==="global"&&e.bind&&e.bind!=="scales"&&!Ea(e.bind),parse:(e,t,n)=>wd(t,n),topLevelSignals:(e,t,n)=>{const i=t.name,r=t.project,s=t.bind,o=t.init&&t.init[0],a=hd.defined(t)?"(item().isVoronoi ? datum.datum : datum)":"datum";return r.items.forEach((c,l)=>{const u=se(`${i}_${c.field}`);n.filter(d=>d.name===u).length||n.unshift({name:u,...o?{init:Fn(o[l])}:{value:null},on:t.events?[{events:t.events,update:`datum && item().mark.marktype !== 'group' ? ${a}[${B(c.field)}] : null`}]:[],bind:s[c.field]??s[c.channel]??s})}),n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.filter(l=>l.name===i+Xt)[0],o=i+Zi,a=r.items.map(l=>se(`${i}_${l.field}`)),c=a.map(l=>`${l} !== null`).join(" && ");return a.length&&(s.update=`${c} ? {fields: ${o}, values: [${a.join(", ")}]} : null`),delete s.value,delete s.on,n}},Nr="_toggle",yd={defined:e=>e.type==="point"&&!!e.toggle,signals:(e,t,n)=>n.concat({name:t.name+Nr,value:!1,on:[{events:t.events,update:t.toggle}]}),modifyExpr:(e,t)=>{const n=t.name+Xt,i=t.name+Nr;return`${i} ? null : ${n}, `+(t.resolve==="global"?`${i} ? null : true, `:`${i} ? null : {unit: ${yn(e)}}, `)+`${i} ? ${n} : null`}},Fx={defined:e=>e.clear!==void 0&&e.clear!==!1,parse:(e,t)=>{t.clear&&(t.clear=z(t.clear)?pi(t.clear,"view"):t.clear)},topLevelSignals:(e,t,n)=>{if(md.defined(t))for(const i of t.project.items){const r=n.findIndex(s=>s.name===se(`${t.name}_${i.field}`));r!==-1&&n[r].on.push({events:t.clear,update:"null"})}return n},signals:(e,t,n)=>{function i(r,s){r!==-1&&n[r].on&&n[r].on.push({events:t.clear,update:s})}if(t.type==="interval")for(const r of t.project.items){const s=n.findIndex(o=>o.name===r.signals.visual);if(i(s,"[0, 0]"),s===-1){const o=n.findIndex(a=>a.name===r.signals.data);i(o,"null")}}else{let r=n.findIndex(s=>s.name===t.name+Xt);i(r,"null"),yd.defined(t)&&(r=n.findIndex(s=>s.name===t.name+Nr),i(r,"false"))}return n}},bd={defined:e=>{const t=e.resolve==="global"&&e.bind&&Ea(e.bind),n=e.project.items.length===1&&e.project.items[0].field!==Ze;return t&&!n&&S(Qg),t&&n},parse:(e,t,n)=>{const i=j(n);if(i.select=z(i.select)?{type:i.select,toggle:t.toggle}:{...i.select,toggle:t.toggle},wd(t,i),V(n.select)&&(n.select.on||n.select.clear)){const o='event.item && indexof(event.item.mark.role, "legend") < 0';for(const a of t.events)a.filter=ce(a.filter??[]),a.filter.includes(o)||a.filter.push(o)}const r=_s(t.bind)?t.bind.legend:"click",s=z(r)?pi(r,"view"):ce(r);t.bind={legend:{merge:s}}},topLevelSignals:(e,t,n)=>{const i=t.name,r=_s(t.bind)&&t.bind.legend,s=o=>a=>{const c=j(a);return c.markname=o,c};for(const o of t.project.items){if(!o.hasLegend)continue;const a=`${se(o.field)}_legend`,c=`${i}_${a}`;if(n.filter(u=>u.name===c).length===0){const u=r.merge.map(s(`${a}_symbols`)).concat(r.merge.map(s(`${a}_labels`))).concat(r.merge.map(s(`${a}_entries`)));n.unshift({name:c,...t.init?{}:{value:null},on:[{events:u,update:"isDefined(datum.value) ? datum.value : item().items[0].items[0].datum.value",force:!0},{events:r.merge,update:`!event.item || !datum ? null : ${c}`,force:!0}]})}}return n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.find(d=>d.name===i+Xt),o=i+Zi,a=r.items.filter(d=>d.hasLegend).map(d=>se(`${i}_${se(d.field)}_legend`)),l=`${a.map(d=>`${d} !== null`).join(" && ")} ? {fields: ${o}, values: [${a.join(", ")}]} : null`;t.events&&a.length>0?s.on.push({events:a.map(d=>({signal:d})),update:l}):a.length>0&&(s.update=l,delete s.value,delete s.on);const u=n.find(d=>d.name===i+Nr),f=_s(t.bind)&&t.bind.legend;return u&&(t.events?u.on.push({...u.on[0],events:f}):u.on[0].events=f),n}};function Nx(e,t,n){const i=e.fieldDef(t)?.field;for(const r of ve(e.component.selection??{})){const s=r.project.hasField[i]??r.project.hasChannel[t];if(s&&bd.defined(r)){const o=n.get("selections")??[];o.push(r.name),n.set("selections",o,!1),s.hasLegend=!0}}}const xd="_translate_anchor",vd="_translate_delta",_x={defined:e=>e.type==="interval"&&e.translate,signals:(e,t,n)=>{const i=t.name,r=kt.defined(t),s=i+xd,{x:o,y:a}=t.project.hasChannel;let c=pi(t.translate,"scope");return r||(c=c.map(l=>(l.between[0].markname=i+Xn,l))),n.push({name:s,value:{},on:[{events:c.map(l=>l.between[0]),update:"{x: x(unit), y: y(unit)"+(o!==void 0?`, extent_x: ${r?oo(e,oe):`slice(${o.signals.visual})`}`:"")+(a!==void 0?`, extent_y: ${r?oo(e,me):`slice(${a.signals.visual})`}`:"")+"}"}]},{name:i+vd,value:{},on:[{events:c,update:`{x: ${s}.x - x(unit), y: ${s}.y - y(unit)}`}]}),o!==void 0&&Xc(e,t,o,"width",n),a!==void 0&&Xc(e,t,a,"height",n),n}};function Xc(e,t,n,i,r){const s=t.name,o=s+xd,a=s+vd,c=n.channel,l=kt.defined(t),u=r.filter(x=>x.name===n.signals[l?"data":"visual"])[0],f=e.getSizeSignalRef(i).signal,d=e.getScaleComponent(c),p=d&&d.get("type"),g=d&&d.get("reverse"),h=l?c===oe?g?"":"-":g?"-":"":"",m=`${o}.extent_${c}`,y=`${h}${a}.${c} / ${l?`${f}`:`span(${m})`}`,b=!l||!d?"panLinear":p==="log"?"panLog":p==="symlog"?"panSymlog":p==="pow"?"panPow":"panLinear",k=l?p==="pow"?`, ${d.get("exponent")??1}`:p==="symlog"?`, ${d.get("constant")??1}`:"":"",T=`${b}(${m}, ${y}${k})`;u.on.push({events:{signal:a},update:l?T:`clampRange(${T}, 0, ${f})`})}const Sd="_zoom_anchor",Ed="_zoom_delta",kx={defined:e=>e.type==="interval"&&e.zoom,signals:(e,t,n)=>{const i=t.name,r=kt.defined(t),s=i+Ed,{x:o,y:a}=t.project.hasChannel,c=B(e.scaleName(oe)),l=B(e.scaleName(me));let u=pi(t.zoom,"scope");return r||(u=u.map(f=>(f.markname=i+Xn,f))),n.push({name:i+Sd,on:[{events:u,update:r?"{"+[c?`x: invert(${c}, x(unit))`:"",l?`y: invert(${l}, y(unit))`:""].filter(f=>f).join(", ")+"}":"{x: x(unit), y: y(unit)}"}]},{name:s,on:[{events:u,force:!0,update:"pow(1.001, event.deltaY * pow(16, event.deltaMode))"}]}),o!==void 0&&Yc(e,t,o,"width",n),a!==void 0&&Yc(e,t,a,"height",n),n}};function Yc(e,t,n,i,r){const s=t.name,o=n.channel,a=kt.defined(t),c=r.filter(b=>b.name===n.signals[a?"data":"visual"])[0],l=e.getSizeSignalRef(i).signal,u=e.getScaleComponent(o),f=u&&u.get("type"),d=a?oo(e,o):c.name,p=s+Ed,g=`${s}${Sd}.${o}`,h=!a||!u?"zoomLinear":f==="log"?"zoomLog":f==="symlog"?"zoomSymlog":f==="pow"?"zoomPow":"zoomLinear",m=a?f==="pow"?`, ${u.get("exponent")??1}`:f==="symlog"?`, ${u.get("constant")??1}`:"":"",y=`${h}(${d}, ${g}, ${p}${m})`;c.on.push({events:{signal:p},update:a?y:`clampRange(${y}, 0, ${l})`})}const Nn="_store",Xt="_tuple",Tx="_modify",$d="vlSelectionResolve",os=[lx,ax,sx,yd,md,kt,bd,Fx,_x,kx,hd];function Ox(e){let t=e.parent;for(;t&&!Je(t);)t=t.parent;return t}function yn(e,{escape:t}={escape:!0}){let n=t?B(e.name):e.name;const i=Ox(e);if(i){const{facet:r}=i;for(const s of je)r[s]&&(n+=` + '__facet_${s}_' + (facet[${B(i.vgField(s))}])`)}return n}function Ra(e){return ve(e.component.selection??{}).reduce((t,n)=>t||n.project.hasSelectionId,!1)}function wd(e,t){(z(t.select)||!t.select.on)&&delete e.events,(z(t.select)||!t.select.clear)&&delete e.clear,(z(t.select)||!t.select.toggle)&&delete e.toggle}function ao(e){const t=[];return e.type==="Identifier"?[e.name]:e.type==="Literal"?[e.value]:(e.type==="MemberExpression"&&(t.push(...ao(e.object)),t.push(...ao(e.property))),t)}function Cd(e){return e.object.type==="MemberExpression"?Cd(e.object):e.object.name==="datum"}function Fd(e){const t=Ip(e),n=new Set;return t.visit(i=>{i.type==="MemberExpression"&&Cd(i)&&n.add(ao(i).slice(1).join("."))}),n}class bi extends K{clone(){return new bi(null,this.model,j(this.filter))}constructor(t,n,i){super(t),this.model=n,this.filter=i,this.expr=_r(this.model,this.filter,this),this._dependentFields=Fd(this.expr)}dependentFields(){return this._dependentFields}producedFields(){return new Set}assemble(){return{type:"filter",expr:this.expr}}hash(){return`Filter ${this.expr}`}}function Ax(e,t){const n={},i=e.config.selection;if(!t||!t.length)return n;for(const r of t){const s=se(r.name),o=r.select,a=z(o)?o:o.type,c=V(o)?j(o):{type:a},l=i[a];for(const d in l)d==="fields"||d==="encodings"||(d==="mark"&&(c[d]={...l[d],...c[d]}),(c[d]===void 0||c[d]===!0)&&(c[d]=j(l[d]??c[d])));const u=n[s]={...c,name:s,type:a,init:r.value,bind:r.bind,events:z(c.on)?pi(c.on,"scope"):ce(j(c.on))},f=j(r);for(const d of os)d.defined(u)&&d.parse&&d.parse(e,u,f)}return n}function Nd(e,t,n,i="datum"){const r=z(t)?t:t.param,s=se(r),o=B(s+Nn);let a;try{a=e.getSelectionComponent(s,r)}catch{return`!!${s}`}if(a.project.timeUnit){const d=n??e.component.data.raw,p=a.project.timeUnit.clone();d.parent?p.insertAsParentOf(d):d.parent=p}const c=a.project.hasSelectionId?"vlSelectionIdTest(":"vlSelectionTest(",l=a.resolve==="global"?")":`, ${B(a.resolve)})`,u=`${c}${o}, ${i}${l}`,f=`length(data(${o}))`;return t.empty===!1?`${f} && ${u}`:`!${f} || ${u}`}function _d(e,t,n){const i=se(t),r=n.encoding;let s=n.field,o;try{o=e.getSelectionComponent(i,t)}catch{return i}if(!r&&!s)s=o.project.items[0].field,o.project.items.length>1&&S(`A "field" or "encoding" must be specified when using a selection as a scale domain. Using "field": ${B(s)}.`);else if(r&&!s){const a=o.project.items.filter(c=>c.channel===r);!a.length||a.length>1?(s=o.project.items[0].field,S((a.length?"Multiple ":"No ")+`matching ${B(r)} encoding found for selection ${B(n.param)}. Using "field": ${B(s)}.`)):s=a[0].field}return`${o.name}[${B(Me(s))}]`}function Rx(e,t){for(const[n,i]of Gt(e.component.selection??{})){const r=e.getName(`lookup_${n}`);e.component.data.outputNodes[r]=i.materialized=new Ce(new bi(t,e,{param:n}),r,Z.Lookup,e.component.data.outputNodeRefCounts)}}function _r(e,t,n){return Ai(t,i=>z(i)?i:gm(i)?Nd(e,i,n):qu(i))}function Ix(e,t){if(e)return A(e)&&!Ut(e)?e.map(n=>pa(n,t)).join(", "):e}function Rs(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i}function ki(e,t,n,i={header:!1}){const{disable:r,orient:s,scale:o,labelExpr:a,title:c,zindex:l,...u}=e.combine();if(!r){for(const f in u){const d=yy[f],p=u[f];if(d&&d!==t&&d!=="both")delete u[f];else if(Qi(p)){const{condition:g,...h}=p,m=ce(g),y=Cc[f];if(y){const{vgProp:b,part:k}=y,T=[...m.map(x=>{const{test:_,...C}=x;return{test:_r(null,_),...C}}),h];Rs(u,k,b,T),delete u[f]}else if(y===null){const b={signal:m.map(k=>{const{test:T,...x}=k;return`${_r(null,T)} ? ${lc(x)} : `}).join("")+lc(h)};u[f]=b}}else if(I(p)){const g=Cc[f];if(g){const{vgProp:h,part:m}=g;Rs(u,m,h,p),delete u[f]}}G(["labelAlign","labelBaseline"],f)&&u[f]===null&&delete u[f]}if(t==="grid"){if(!u.grid)return;if(u.encode){const{grid:f}=u.encode;u.encode={...f?{grid:f}:{}},J(u.encode)&&delete u.encode}return{scale:o,orient:s,...u,domain:!1,labels:!1,aria:!1,maxExtent:0,minExtent:0,ticks:!1,zindex:ue(l,0)}}else{if(!i.header&&e.mainExtracted)return;if(a!==void 0){let d=a;u.encode?.labels?.update&&I(u.encode.labels.update.text)&&(d=vn(a,"datum.label",u.encode.labels.update.text.signal)),Rs(u,"labels","text",{signal:d})}if(u.labelAlign===null&&delete u.labelAlign,u.encode){for(const d of $f)e.hasAxisPart(d)||delete u.encode[d];J(u.encode)&&delete u.encode}const f=Ix(c,n);return{scale:o,orient:s,grid:!1,...f?{title:f}:{},...u,...n.aria===!1?{aria:!1}:{},zindex:ue(l,0)}}}}function kd(e){const{axes:t}=e.component,n=[];for(const i of St)if(t[i]){for(const r of t[i])if(!r.get("disable")&&!r.get("gridScale")){const s=i==="x"?"height":"width",o=e.getSizeSignalRef(s).signal;s!==o&&n.push({name:s,update:o})}}return n}function Lx(e,t){const{x:n=[],y:i=[]}=e;return[...n.map(r=>ki(r,"grid",t)),...i.map(r=>ki(r,"grid",t)),...n.map(r=>ki(r,"main",t)),...i.map(r=>ki(r,"main",t))].filter(r=>r)}function Kc(e,t,n,i){return Object.assign.apply(null,[{},...e.map(r=>{if(r==="axisOrient"){const s=n==="x"?"bottom":"left",o=t[n==="x"?"axisBottom":"axisLeft"]||{},a=t[n==="x"?"axisTop":"axisRight"]||{},c=new Set([...v(o),...v(a)]),l={};for(const u of c.values())l[u]={signal:`${i.signal} === "${s}" ? ${Ve(o[u])} : ${Ve(a[u])}`};return l}return t[r]})])}function Px(e,t,n,i){const r=t==="band"?["axisDiscrete","axisBand"]:t==="point"?["axisDiscrete","axisPoint"]:Yu(t)?["axisQuantitative"]:t==="time"||t==="utc"?["axisTemporal"]:[],s=e==="x"?"axisX":"axisY",o=I(n)?"axisOrient":`axis${Wi(n)}`,a=[...r,...r.map(l=>s+l.substr(4))],c=["axis",o,s];return{vlOnlyAxisConfig:Kc(a,i,e,n),vgAxisConfig:Kc(c,i,e,n),axisConfigStyle:zx([...c,...a],i)}}function zx(e,t){const n=[{}];for(const i of e){let r=t[i]?.style;if(r){r=ce(r);for(const s of r)n.push(t.style[s])}}return Object.assign.apply(null,n)}function co(e,t,n,i={}){const r=$u(e,n,t);if(r!==void 0)return{configFrom:"style",configValue:r};for(const s of["vlOnlyAxisConfig","vgAxisConfig","axisConfigStyle"])if(i[s]?.[e]!==void 0)return{configFrom:s,configValue:i[s][e]};return{}}const Jc={scale:({model:e,channel:t})=>e.scaleName(t),format:({format:e})=>e,formatType:({formatType:e})=>e,grid:({fieldOrDatumDef:e,axis:t,scaleType:n})=>t.grid??Dx(n,e),gridScale:({model:e,channel:t})=>jx(e,t),labelAlign:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelAlign||Od(t,n,i),labelAngle:({labelAngle:e})=>e,labelBaseline:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelBaseline||Td(t,n,i),labelFlush:({axis:e,fieldOrDatumDef:t,channel:n})=>e.labelFlush??Ux(t.type,n),labelOverlap:({axis:e,fieldOrDatumDef:t,scaleType:n})=>e.labelOverlap??Bx(t.type,n,E(t)&&!!t.timeUnit,E(t)?t.sort:void 0),orient:({orient:e})=>e,tickCount:({channel:e,model:t,axis:n,fieldOrDatumDef:i,scaleType:r})=>{const s=e==="x"?"width":e==="y"?"height":void 0,o=s?t.getSizeSignalRef(s):void 0;return n.tickCount??Gx({fieldOrDatumDef:i,scaleType:r,size:o,values:n.values})},tickMinStep:qx,title:({axis:e,model:t,channel:n})=>{if(e.title!==void 0)return e.title;const i=Ad(t,n);if(i!==void 0)return i;const r=t.typedFieldDef(n),s=n==="x"?"x2":"y2",o=t.fieldDef(s);return Cu(r?[$c(r)]:[],E(o)?[$c(o)]:[])},values:({axis:e,fieldOrDatumDef:t})=>Hx(e,t),zindex:({axis:e,fieldOrDatumDef:t,mark:n})=>e.zindex??Vx(n,t)};function Dx(e,t){return!be(e)&&E(t)&&!te(t?.bin)&&!xe(t?.bin)}function jx(e,t){const n=t==="x"?"y":"x";if(e.getScaleComponent(n))return e.scaleName(n)}function Mx(e,t,n,i,r){const s=t?.labelAngle;if(s!==void 0)return I(s)?s:zi(s);{const{configValue:o}=co("labelAngle",i,t?.style,r);return o!==void 0?zi(o):n===oe&&G([ta,ea],e.type)&&!(E(e)&&e.timeUnit)?270:void 0}}function lo(e){return`(((${e.signal} % 360) + 360) % 360)`}function Td(e,t,n,i){if(e!==void 0)if(n==="x"){if(I(e)){const r=lo(e),s=I(t)?`(${t.signal} === "top")`:t==="top";return{signal:`(45 < ${r} && ${r} < 135) || (225 < ${r} && ${r} < 315) ? "middle" :(${r} <= 45 || 315 <= ${r}) === ${s} ? "bottom" : "top"`}}if(45<e&&e<135||225<e&&e<315)return"middle";if(I(t)){const r=e<=45||315<=e?"===":"!==";return{signal:`${t.signal} ${r} "top" ? "bottom" : "top"`}}return(e<=45||315<=e)==(t==="top")?"bottom":"top"}else{if(I(e)){const r=lo(e),s=I(t)?`(${t.signal} === "left")`:t==="left";return{signal:`${r} <= 45 || 315 <= ${r} || (135 <= ${r} && ${r} <= 225) ? ${i?'"middle"':"null"} : (45 <= ${r} && ${r} <= 135) === ${s} ? "top" : "bottom"`}}if(e<=45||315<=e||135<=e&&e<=225)return i?"middle":null;if(I(t)){const r=45<=e&&e<=135?"===":"!==";return{signal:`${t.signal} ${r} "left" ? "top" : "bottom"`}}return(45<=e&&e<=135)==(t==="left")?"top":"bottom"}}function Od(e,t,n){if(e===void 0)return;const i=n==="x",r=i?0:90,s=i?"bottom":"left";if(I(e)){const o=lo(e),a=I(t)?`(${t.signal} === "${s}")`:t===s;return{signal:`(${r?`(${o} + 90)`:o} % 180 === 0) ? ${i?null:'"center"'} :(${r} < ${o} && ${o} < ${180+r}) === ${a} ? "left" : "right"`}}if((e+r)%180===0)return i?null:"center";if(I(t)){const o=r<e&&e<180+r?"===":"!==";return{signal:`${`${t.signal} ${o} "${s}"`} ? "left" : "right"`}}return(r<e&&e<180+r)==(t===s)?"left":"right"}function Ux(e,t){if(t==="x"&&G(["quantitative","temporal"],e))return!0}function Bx(e,t,n,i){if(n&&!V(i)||e!=="nominal"&&e!=="ordinal")return t==="log"||t==="symlog"?"greedy":!0}function Wx(e){return e==="x"?"bottom":"left"}function Gx({fieldOrDatumDef:e,scaleType:t,size:n,values:i}){if(!i&&!be(t)&&t!=="log"){if(E(e)){if(te(e.bin))return{signal:`ceil(${n.signal}/10)`};if(e.timeUnit&&G(["month","hours","day","quarter"],he(e.timeUnit)?.unit))return}return{signal:`ceil(${n.signal}/40)`}}}function qx({format:e,fieldOrDatumDef:t}){if(e==="d")return 1;if(E(t)){const{timeUnit:n}=t;if(n){const i=Bu(n);if(i)return{signal:i}}}}function Ad(e,t){const n=t==="x"?"x2":"y2",i=e.fieldDef(t),r=e.fieldDef(n),s=i?i.title:void 0,o=r?r.title:void 0;if(s&&o)return Fu(s,o);if(s)return s;if(o)return o;if(s!==void 0)return s;if(o!==void 0)return o}function Hx(e,t){const n=e.values;if(A(n))return Ef(t,n);if(I(n))return n}function Vx(e,t){return e==="rect"&&vr(t)?1:0}class ci extends K{clone(){return new ci(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this._dependentFields=Fd(this.transform.calculate)}static parseAllForSortIndex(t,n){return n.forEachFieldDef((i,r)=>{if(Ln(i)&&ff(i.sort)){const{field:s,timeUnit:o}=i,a=i.sort,c=a.map((l,u)=>`${qu({field:s,timeUnit:o,equal:l})} ? ${u} : `).join("")+a.length;t=new ci(t,{calculate:c,as:li(i,r,{forAs:!0})})}}),t}producedFields(){return new Set([this.transform.as])}dependentFields(){return this._dependentFields}assemble(){return{type:"formula",expr:this.transform.calculate,as:this.transform.as}}hash(){return`Calculate ${W(this.transform)}`}}function li(e,t,n){return w(e,{prefix:t,suffix:"sort_index",...n??{}})}function as(e,t){return G(["top","bottom"],t)?"column":G(["left","right"],t)||e==="row"?"row":"column"}function ui(e,t,n,i){const r=i==="row"?n.headerRow:i==="column"?n.headerColumn:n.headerFacet;return ue((t||{})[e],r[e],n.header[e])}function cs(e,t,n,i){const r={};for(const s of e){const o=ui(s,t||{},n,i);o!==void 0&&(r[s]=o)}return r}const Ia=["row","column"],La=["header","footer"];function Xx(e,t){const n=e.component.layoutHeaders[t].title,i=e.config?e.config:void 0,r=e.component.layoutHeaders[t].facetFieldDef?e.component.layoutHeaders[t].facetFieldDef:void 0,{titleAnchor:s,titleAngle:o,titleOrient:a}=cs(["titleAnchor","titleAngle","titleOrient"],r.header,i,t),c=as(t,a),l=zi(o);return{name:`${t}-title`,type:"group",role:`${c}-title`,title:{text:n,...t==="row"?{orient:"left"}:{},style:"guide-title",...Id(l,c),...Rd(c,l,s),...Ld(i,r,t,jy,jf)}}}function Rd(e,t,n="middle"){switch(n){case"start":return{align:"left"};case"end":return{align:"right"}}const i=Od(t,e==="row"?"left":"top",e==="row"?"y":"x");return i?{align:i}:{}}function Id(e,t){const n=Td(e,t==="row"?"left":"top",t==="row"?"y":"x",!0);return n?{baseline:n}:{}}function Yx(e,t){const n=e.component.layoutHeaders[t],i=[];for(const r of La)if(n[r])for(const s of n[r]){const o=Jx(e,t,r,n,s);o!=null&&i.push(o)}return i}function Kx(e,t){const{sort:n}=e;return ft(n)?{field:w(n,{expr:"datum"}),order:n.order??"ascending"}:A(n)?{field:li(e,t,{expr:"datum"}),order:"ascending"}:{field:w(e,{expr:"datum"}),order:n??"ascending"}}function uo(e,t,n){const{format:i,formatType:r,labelAngle:s,labelAnchor:o,labelOrient:a,labelExpr:c}=cs(["format","formatType","labelAngle","labelAnchor","labelOrient","labelExpr"],e.header,n,t),l=la({fieldOrDatumDef:e,format:i,formatType:r,expr:"parent",config:n}).signal,u=as(t,a);return{text:{signal:c?vn(vn(c,"datum.label",l),"datum.value",w(e,{expr:"parent"})):l},...t==="row"?{orient:"left"}:{},style:"guide-label",frame:"group",...Id(s,u),...Rd(u,s,o),...Ld(n,e,t,My,Mf)}}function Jx(e,t,n,i,r){if(r){let s=null;const{facetFieldDef:o}=i,a=e.config?e.config:void 0;if(o&&r.labels){const{labelOrient:f}=cs(["labelOrient"],o.header,a,t);(t==="row"&&!G(["top","bottom"],f)||t==="column"&&!G(["left","right"],f))&&(s=uo(o,t,a))}const c=Je(e)&&!Yi(e.facet),l=r.axes,u=l?.length>0;if(s||u){const f=t==="row"?"height":"width";return{name:e.getName(`${t}_${n}`),type:"group",role:`${t}-${n}`,...i.facetFieldDef?{from:{data:e.getName(`${t}_domain`)},sort:Kx(o,t)}:{},...u&&c?{from:{data:e.getName(`facet_domain_${t}`)}}:{},...s?{title:s}:{},...r.sizeSignal?{encode:{update:{[f]:r.sizeSignal}}}:{},...u?{axes:l}:{}}}}return null}const Qx={column:{start:0,end:1},row:{start:1,end:0}};function Zx(e,t){return Qx[t][e]}function ev(e,t){const n={};for(const i of je){const r=e[i];if(r?.facetFieldDef){const{titleAnchor:s,titleOrient:o}=cs(["titleAnchor","titleOrient"],r.facetFieldDef.header,t,i),a=as(i,o),c=Zx(s,a);c!==void 0&&(n[a]=c)}}return J(n)?void 0:n}function Ld(e,t,n,i,r){const s={};for(const o of i){if(!r[o])continue;const a=ui(o,t?.header,e,n);a!==void 0&&(s[r[o]]=a)}return s}function Pa(e){return[...sr(e,"width"),...sr(e,"height"),...sr(e,"childWidth"),...sr(e,"childHeight")]}function sr(e,t){const n=t==="width"?"x":"y",i=e.component.layoutSize.get(t);if(!i||i==="merged")return[];const r=e.getSizeSignalRef(t).signal;if(i==="step"){const s=e.getScaleComponent(n);if(s){const o=s.get("type"),a=s.get("range");if(be(o)&&nn(a)){const c=e.scaleName(n);return Je(e.parent)&&e.parent.component.resolve.scale[n]==="independent"?[Qc(c,a)]:[Qc(c,a),{name:r,update:Pd(c,s,`domain('${c}').length`)}]}}throw new Error("layout size is step although width/height is not step.")}else if(i=="container"){const s=r.endsWith("width"),o=s?"containerSize()[0]":"containerSize()[1]",a=no(e.config.view,s?"width":"height"),c=`isFinite(${o}) ? ${o} : ${a}`;return[{name:r,init:c,on:[{update:c,events:"window:resize"}]}]}else return[{name:r,value:i}]}function Qc(e,t){const n=`${e}_step`;return I(t.step)?{name:n,update:t.step.signal}:{name:n,value:t.step}}function Pd(e,t,n){const i=t.get("type"),r=t.get("padding"),s=ue(t.get("paddingOuter"),r);let o=t.get("paddingInner");return o=i==="band"?o!==void 0?o:r:1,`bandspace(${n}, ${Ve(o)}, ${Ve(s)}) * ${e}_step`}function zd(e){return e==="childWidth"?"width":e==="childHeight"?"height":e}function Dd(e,t){return v(e).reduce((n,i)=>{const r=e[i];return{...n,...yi(t,r,i,s=>re(s.value))}},{})}function jd(e,t){if(Je(t))return e==="theta"?"independent":"shared";if(Si(t))return"shared";if(Wa(t))return ye(e)||e==="theta"||e==="radius"?"independent":"shared";throw new Error("invalid model type for resolve")}function za(e,t){const n=e.scale[t],i=ye(t)?"axis":"legend";return n==="independent"?(e[i][t]==="shared"&&S(Ph(t)),"independent"):e[i][t]||"shared"}const tv={...Wy,disable:1,labelExpr:1,selections:1,opacity:1,shape:1,stroke:1,fill:1,size:1,strokeWidth:1,strokeDash:1,encode:1},Md=v(tv);class nv extends jt{}const Zc={symbols:iv,gradient:rv,labels:sv,entries:ov};function iv(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r,legendType:s}){if(s!=="symbol")return;const{markDef:o,encoding:a,config:c,mark:l}=n,u=o.filled&&l!=="trail";let f={...Hg({},n,jm),...dd(n,{filled:u})};const d=r.get("symbolOpacity")??c.legend.symbolOpacity,p=r.get("symbolFillColor")??c.legend.symbolFillColor,g=r.get("symbolStrokeColor")??c.legend.symbolStrokeColor,h=d===void 0?Ud(a.opacity)??o.opacity:void 0;if(f.fill){if(i==="fill"||u&&i===Ae)delete f.fill;else if(f.fill.field)p?delete f.fill:(f.fill=re(c.legend.symbolBaseFillColor??"black"),f.fillOpacity=re(h??1));else if(A(f.fill)){const m=fo(a.fill??a.color)??o.fill??(u&&o.color);m&&(f.fill=re(m))}}if(f.stroke){if(i==="stroke"||!u&&i===Ae)delete f.stroke;else if(f.stroke.field||g)delete f.stroke;else if(A(f.stroke)){const m=ue(fo(a.stroke||a.color),o.stroke,u?o.color:void 0);m&&(f.stroke={value:m})}}if(i!==Pt){const m=E(t)&&Wd(n,r,t);m?f.opacity=[{test:m,...re(h??1)},re(c.legend.unselectedOpacity)]:h&&(f.opacity=re(h))}return f={...f,...e},J(f)?void 0:f}function rv(e,{model:t,legendType:n,legendCmpt:i}){if(n!=="gradient")return;const{config:r,markDef:s,encoding:o}=t;let a={};const l=(i.get("gradientOpacity")??r.legend.gradientOpacity)===void 0?Ud(o.opacity)||s.opacity:void 0;return l&&(a.opacity=re(l)),a={...a,...e},J(a)?void 0:a}function sv(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r}){const s=n.legend(i)||{},o=n.config,a=E(t)?Wd(n,r,t):void 0,c=a?[{test:a,value:1},{value:o.legend.unselectedOpacity}]:void 0,{format:l,formatType:u}=s;let f;Cn(u)?f=Ye({fieldOrDatumDef:t,field:"datum.value",format:l,formatType:u,config:o}):l===void 0&&u===void 0&&o.customFormatTypes&&(t.type==="quantitative"&&o.numberFormatType?f=Ye({fieldOrDatumDef:t,field:"datum.value",format:o.numberFormat,formatType:o.numberFormatType,config:o}):t.type==="temporal"&&o.timeFormatType&&E(t)&&t.timeUnit===void 0&&(f=Ye({fieldOrDatumDef:t,field:"datum.value",format:o.timeFormat,formatType:o.timeFormatType,config:o})));const d={...c?{opacity:c}:{},...f?{text:f}:{},...e};return J(d)?void 0:d}function ov(e,{legendCmpt:t}){return t.get("selections")?.length?{...e,fill:{value:"transparent"}}:e}function Ud(e){return Bd(e,(t,n)=>Math.max(t,n.value))}function fo(e){return Bd(e,(t,n)=>ue(t,n.value))}function Bd(e,t){if(sy(e))return ce(e.condition).reduce(t,e.value);if(Qe(e))return e.value}function Wd(e,t,n){const i=t.get("selections");if(!i?.length)return;const r=B(n.field);return i.map(s=>`(!length(data(${B(se(s)+Nn)})) || (${s}[${r}] && indexof(${s}[${r}], datum.value) >= 0))`).join(" || ")}const el={direction:({direction:e})=>e,format:({fieldOrDatumDef:e,legend:t,config:n})=>{const{format:i,formatType:r}=t;return af(e,e.type,i,r,n,!1)},formatType:({legend:e,fieldOrDatumDef:t,scaleType:n})=>{const{formatType:i}=e;return cf(i,t,n)},gradientLength:e=>{const{legend:t,legendConfig:n}=e;return t.gradientLength??n.gradientLength??pv(e)},labelOverlap:({legend:e,legendConfig:t,scaleType:n})=>e.labelOverlap??t.labelOverlap??gv(n),symbolType:({legend:e,markDef:t,channel:n,encoding:i})=>e.symbolType??cv(t.type,n,i.shape,t.shape),title:({fieldOrDatumDef:e,config:t})=>Vn(e,t,{allowDisabling:!0}),type:({legendType:e,scaleType:t,channel:n})=>{if(Hn(n)&&Xe(t)){if(e==="gradient")return}else if(e==="symbol")return;return e},values:({fieldOrDatumDef:e,legend:t})=>av(t,e)};function av(e,t){const n=e.values;if(A(n))return Ef(t,n);if(I(n))return n}function cv(e,t,n,i){if(t!=="shape"){const r=fo(n)??i;if(r)return r}switch(e){case"bar":case"rect":case"image":case"square":return"square";case"line":case"trail":case"rule":return"stroke";case"arc":case"point":case"circle":case"tick":case"geoshape":case"area":case"text":return"circle"}}function lv(e){const{legend:t}=e;return ue(t.type,uv(e))}function uv({channel:e,timeUnit:t,scaleType:n}){if(Hn(e)){if(G(["quarter","month","day"],t))return"symbol";if(Xe(n))return"gradient"}return"symbol"}function fv({legendConfig:e,legendType:t,orient:n,legend:i}){return i.direction??e[t?"gradientDirection":"symbolDirection"]??dv(n,t)}function dv(e,t){switch(e){case"top":case"bottom":return"horizontal";case"left":case"right":case"none":case void 0:return;default:return t==="gradient"?"horizontal":void 0}}function pv({legendConfig:e,model:t,direction:n,orient:i,scaleType:r}){const{gradientHorizontalMaxLength:s,gradientHorizontalMinLength:o,gradientVerticalMaxLength:a,gradientVerticalMinLength:c}=e;if(Xe(r))return n==="horizontal"?i==="top"||i==="bottom"?tl(t,"width",o,s):o:tl(t,"height",c,a)}function tl(e,t,n,i){return{signal:`clamp(${e.getSizeSignalRef(t).signal}, ${n}, ${i})`}}function gv(e){if(G(["quantile","threshold","log","symlog"],e))return"greedy"}function Gd(e){const t=ae(e)?hv(e):xv(e);return e.component.legends=t,t}function hv(e){const{encoding:t}=e,n={};for(const i of[Ae,...Bf]){const r=fe(t[i]);!r||!e.getScaleComponent(i)||i===Re&&E(r)&&r.type===mi||(n[i]=bv(e,i))}return n}function mv(e,t){const n=e.scaleName(t);if(e.mark==="trail"){if(t==="color")return{stroke:n};if(t==="size")return{strokeWidth:n}}return t==="color"?e.markDef.filled?{fill:n}:{stroke:n}:{[t]:n}}function yv(e,t,n,i){switch(t){case"disable":return n!==void 0;case"values":return!!n?.values;case"title":if(t==="title"&&e===i?.title)return!0}return e===(n||{})[t]}function bv(e,t){let n=e.legend(t);const{markDef:i,encoding:r,config:s}=e,o=s.legend,a=new nv({},mv(e,t));Nx(e,t,a);const c=n!==void 0?!n:o.disable;if(a.set("disable",c,n!==void 0),c)return a;n=n||{};const l=e.getScaleComponent(t).get("type"),u=fe(r[t]),f=E(u)?he(u.timeUnit)?.unit:void 0,d=n.orient||s.legend.orient||"right",p=lv({legend:n,channel:t,timeUnit:f,scaleType:l}),g=fv({legend:n,legendType:p,orient:d,legendConfig:o}),h={legend:n,channel:t,model:e,markDef:i,encoding:r,fieldOrDatumDef:u,legendConfig:o,config:s,scaleType:l,orient:d,legendType:p,direction:g};for(const T of Md){if(p==="gradient"&&T.startsWith("symbol")||p==="symbol"&&T.startsWith("gradient"))continue;const x=T in el?el[T](h):n[T];if(x!==void 0){const _=yv(x,T,n,e.fieldDef(t));(_||s.legend[T]===void 0)&&a.set(T,x,_)}}const m=n?.encoding??{},y=a.get("selections"),b={},k={fieldOrDatumDef:u,model:e,channel:t,legendCmpt:a,legendType:p};for(const T of["labels","legend","title","symbols","gradient","entries"]){const x=Dd(m[T]??{},e),_=T in Zc?Zc[T](x,k):x;_!==void 0&&!J(_)&&(b[T]={...y?.length&&E(u)?{name:`${se(u.field)}_legend_${T}`}:{},...y?.length?{interactive:!!y}:{},update:_})}return J(b)||a.set("encode",b,!!n?.encoding),a}function xv(e){const{legends:t,resolve:n}=e.component;for(const i of e.children){Gd(i);for(const r of v(i.component.legends))n.legend[r]=za(e.component.resolve,r),n.legend[r]==="shared"&&(t[r]=qd(t[r],i.component.legends[r]),t[r]||(n.legend[r]="independent",delete t[r]))}for(const i of v(t))for(const r of e.children)r.component.legends[i]&&n.legend[i]==="shared"&&delete r.component.legends[i];return t}function qd(e,t){if(!e)return t.clone();const n=e.getWithExplicit("orient"),i=t.getWithExplicit("orient");if(n.explicit&&i.explicit&&n.value!==i.value)return;let r=!1;for(const s of Md){const o=Ht(e.getWithExplicit(s),t.getWithExplicit(s),s,"legend",(a,c)=>{switch(s){case"symbolType":return vv(a,c);case"title":return Nu(a,c);case"type":return r=!0,Le("symbol")}return rs(a,c,s,"legend")});e.setWithExplicit(s,o)}return r&&(e.implicit?.encode?.gradient&&gr(e.implicit,["encode","gradient"]),e.explicit?.encode?.gradient&&gr(e.explicit,["encode","gradient"])),e}function vv(e,t){return t.value==="circle"?t:e}function Sv(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i}function Hd(e){const t=e.component.legends,n={};for(const r of v(t)){const s=e.getScaleComponent(r),o=ee(s.get("domains"));if(n[o])for(const a of n[o])qd(a,t[r])||n[o].push(t[r]);else n[o]=[t[r].clone()]}return ve(n).flat().map(r=>Ev(r,e.config)).filter(r=>r!==void 0)}function Ev(e,t){const{disable:n,labelExpr:i,selections:r,...s}=e.combine();if(!n){if(t.aria===!1&&s.aria==null&&(s.aria=!1),s.encode?.symbols){const o=s.encode.symbols.update;o.fill&&o.fill.value!=="transparent"&&!o.stroke&&!s.stroke&&(o.stroke={value:"transparent"});for(const a of Bf)s[a]&&delete o[a]}if(s.title||delete s.title,i!==void 0){let o=i;s.encode?.labels?.update&&I(s.encode.labels.update.text)&&(o=vn(i,"datum.label",s.encode.labels.update.text.signal)),Sv(s,"labels","text",{signal:o})}return s}}function $v(e){return Si(e)||Wa(e)?wv(e):Vd(e)}function wv(e){return e.children.reduce((t,n)=>t.concat(n.assembleProjections()),Vd(e))}function Vd(e){const t=e.component.projection;if(!t||t.merged)return[];const n=t.combine(),{name:i}=n;if(t.data){const r={signal:`[${t.size.map(o=>o.signal).join(", ")}]`},s=t.data.reduce((o,a)=>{const c=I(a)?a.signal:`data('${e.lookupDataSource(a)}')`;return G(o,c)||o.push(c),o},[]);if(s.length<=0)throw new Error("Projection's fit didn't find any data sources");return[{name:i,size:r,fit:{signal:s.length>1?`[${s.join(", ")}]`:s[0]},...n}]}else return[{name:i,translate:{signal:"[width / 2, height / 2]"},...n}]}const Cv=["type","clipAngle","clipExtent","center","rotate","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];class Xd extends jt{constructor(t,n,i,r){super({...n},{name:t}),this.specifiedProjection=n,this.size=i,this.data=r,this.merged=!1}get isFit(){return!!this.data}}function Yd(e){e.component.projection=ae(e)?Fv(e):kv(e)}function Fv(e){if(e.hasProjection){const t=_e(e.specifiedProjection),n=!(t&&(t.scale!=null||t.translate!=null)),i=n?[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]:void 0,r=n?Nv(e):void 0,s=new Xd(e.projectionName(!0),{..._e(e.config.projection)??{},...t??{}},i,r);return s.get("type")||s.set("type","equalEarth",!1),s}}function Nv(e){const t=[],{encoding:n}=e;for(const i of[[it,nt],[Ue,rt]])(fe(n[i[0]])||fe(n[i[1]]))&&t.push({signal:e.getName(`geojson_${t.length}`)});return e.channelHasField(Re)&&e.typedFieldDef(Re).type===mi&&t.push({signal:e.getName(`geojson_${t.length}`)}),t.length===0&&t.push(e.requestDataName(Z.Main)),t}function _v(e,t){const n=_o(Cv,r=>!!(!Gn(e.explicit,r)&&!Gn(t.explicit,r)||Gn(e.explicit,r)&&Gn(t.explicit,r)&&lt(e.get(r),t.get(r))));if(lt(e.size,t.size)){if(n)return e;if(lt(e.explicit,{}))return t;if(lt(t.explicit,{}))return e}return null}function kv(e){if(e.children.length===0)return;let t;for(const i of e.children)Yd(i);const n=_o(e.children,i=>{const r=i.component.projection;if(r)if(t){const s=_v(t,r);return s&&(t=s),!!s}else return t=r,!0;else return!0});if(t&&n){const i=e.projectionName(!0),r=new Xd(i,t.specifiedProjection,t.size,j(t.data));for(const s of e.children){const o=s.component.projection;o&&(o.isFit&&r.data.push(...s.component.projection.data),s.renameProjection(o.get("name"),i),o.merged=!0)}return r}}function Tv(e,t,n,i){if(Ji(t,n)){const r=ae(e)?e.axis(n)??e.legend(n)??{}:{},s=w(t,{expr:"datum"}),o=w(t,{expr:"datum",binSuffix:"end"});return{formulaAs:w(t,{binSuffix:"range",forAs:!0}),formula:Xi(s,o,r.format,r.formatType,i)}}return{}}function Kd(e,t){return`${xu(e)}_${t}`}function Ov(e,t){return{signal:e.getName(`${t}_bins`),extentSignal:e.getName(`${t}_extent`)}}function Da(e,t,n){const i=Qr(n,void 0)??{},r=Kd(i,t);return e.getName(`${r}_bins`)}function Av(e){return"as"in e}function nl(e,t,n){let i,r;Av(e)?i=z(e.as)?[e.as,`${e.as}_end`]:[e.as[0],e.as[1]]:i=[w(e,{forAs:!0}),w(e,{binSuffix:"end",forAs:!0})];const s={...Qr(t,void 0)},o=Kd(s,e.field),{signal:a,extentSignal:c}=Ov(n,o);if(Ur(s.extent)){const u=s.extent;r=_d(n,u.param,u),delete s.extent}const l={bin:s,field:e.field,as:[i],...a?{signal:a}:{},...c?{extentSignal:c}:{},...r?{span:r}:{}};return{key:o,binComponent:l}}class pt extends K{clone(){return new pt(null,j(this.bins))}constructor(t,n){super(t),this.bins=n}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s,o)=>{if(Fe(s)&&te(s.bin)){const{key:a,binComponent:c}=nl(s,s.bin,n);r[a]={...c,...r[a],...Tv(n,s,o,n.config)}}return r},{});return J(i)?null:new pt(t,i)}static makeFromTransform(t,n,i){const{key:r,binComponent:s}=nl(n,n.bin,i);return new pt(t,{[r]:s})}merge(t,n){for(const i of v(t.bins))i in this.bins?(n(t.bins[i].signal,this.bins[i].signal),this.bins[i].as=ut([...this.bins[i].as,...t.bins[i].as],W)):this.bins[i]=t.bins[i];for(const i of t.children)t.removeChild(i),i.parent=this;t.remove()}producedFields(){return new Set(ve(this.bins).map(t=>t.as).flat(2))}dependentFields(){return new Set(ve(this.bins).map(t=>t.field))}hash(){return`Bin ${W(this.bins)}`}assemble(){return ve(this.bins).flatMap(t=>{const n=[],[i,...r]=t.as,{extent:s,...o}=t.bin,a={type:"bin",field:Me(t.field),as:i,signal:t.signal,...Ur(s)?{extent:null}:{extent:s},...t.span?{span:{signal:`span(${t.span})`}}:{},...o};!s&&t.extentSignal&&(n.push({type:"extent",field:Me(t.field),signal:t.extentSignal}),a.extent={signal:t.extentSignal}),n.push(a);for(const c of r)for(let l=0;l<2;l++)n.push({type:"formula",expr:w({field:i[l]},{expr:"datum"}),as:c[l]});return t.formula&&n.push({type:"formula",expr:t.formula,as:t.formulaAs}),n})}}function Rv(e,t,n,i){const r=ae(i)?i.encoding[vt(t)]:void 0;if(Fe(n)&&ae(i)&&gf(n,r,i.markDef,i.config))e.add(w(n,{})),e.add(w(n,{suffix:"end"})),n.bin&&Ji(n,t)&&e.add(w(n,{binSuffix:"range"}));else if(cu(t)){const s=au(t);e.add(i.getName(s))}else e.add(w(n));return Ln(n)&&Nm(n.scale?.range)&&e.add(n.scale.range.field),e}function Iv(e,t){for(const n of v(t)){const i=t[n];for(const r of v(i))n in e?e[n][r]=new Set([...e[n][r]??[],...i[r]]):e[n]={[r]:i[r]}}}class Ke extends K{clone(){return new Ke(null,new Set(this.dimensions),j(this.measures))}constructor(t,n,i){super(t),this.dimensions=n,this.measures=i}get groupBy(){return this.dimensions}static makeFromEncoding(t,n){let i=!1;n.forEachFieldDef(o=>{o.aggregate&&(i=!0)});const r={},s=new Set;return!i||(n.forEachFieldDef((o,a)=>{const{aggregate:c,field:l}=o;if(c)if(c==="count")r["*"]??(r["*"]={}),r["*"].count=new Set([w(o,{forAs:!0})]);else{if(Ot(c)||tn(c)){const u=Ot(c)?"argmin":"argmax",f=c[u];r[f]??(r[f]={}),r[f][u]=new Set([w({op:u,field:f},{forAs:!0})])}else r[l]??(r[l]={}),r[l][c]=new Set([w(o,{forAs:!0})]);zt(a)&&n.scaleDomain(a)==="unaggregated"&&(r[l]??(r[l]={}),r[l].min=new Set([w({field:l,aggregate:"min"},{forAs:!0})]),r[l].max=new Set([w({field:l,aggregate:"max"},{forAs:!0})]))}else Rv(s,a,o,n)}),s.size+v(r).length===0)?null:new Ke(t,s,r)}static makeFromTransform(t,n){const i=new Set,r={};for(const s of n.aggregate){const{op:o,field:a,as:c}=s;o&&(o==="count"?(r["*"]??(r["*"]={}),r["*"].count=new Set([c||w(s,{forAs:!0})])):(r[a]??(r[a]={}),r[a][o]=new Set([c||w(s,{forAs:!0})])))}for(const s of n.groupby??[])i.add(s);return i.size+v(r).length===0?null:new Ke(t,i,r)}merge(t){return Zl(this.dimensions,t.dimensions)?(Iv(this.measures,t.measures),!0):(Qh("different dimensions, cannot merge"),!1)}addDimensions(t){t.forEach(this.dimensions.add,this.dimensions)}dependentFields(){return new Set([...this.dimensions,...v(this.measures)])}producedFields(){const t=new Set;for(const n of v(this.measures))for(const i of v(this.measures[n])){const r=this.measures[n][i];r.size===0?t.add(`${i}_${n}`):r.forEach(t.add,t)}return t}hash(){return`Aggregate ${W({dimensions:this.dimensions,measures:this.measures})}`}assemble(){const t=[],n=[],i=[];for(const s of v(this.measures))for(const o of v(this.measures[s]))for(const a of this.measures[s][o])i.push(a),t.push(o),n.push(s==="*"?null:Me(s));return{type:"aggregate",groupby:[...this.dimensions].map(Me),ops:t,fields:n,as:i}}}class xi extends K{constructor(t,n,i,r){super(t),this.model=n,this.name=i,this.data=r;for(const s of je){const o=n.facet[s];if(o){const{bin:a,sort:c}=o;this[s]={name:n.getName(`${s}_domain`),fields:[w(o),...te(a)?[w(o,{binSuffix:"end"})]:[]],...ft(c)?{sortField:c}:A(c)?{sortIndexField:li(o,s)}:{}}}}this.childModel=n.child}hash(){let t="Facet";for(const n of je)this[n]&&(t+=` ${n.charAt(0)}:${W(this[n])}`);return t}get fields(){const t=[];for(const n of je)this[n]?.fields&&t.push(...this[n].fields);return t}dependentFields(){const t=new Set(this.fields);for(const n of je)this[n]&&(this[n].sortField&&t.add(this[n].sortField.field),this[n].sortIndexField&&t.add(this[n].sortIndexField));return t}producedFields(){return new Set}getSource(){return this.name}getChildIndependentFieldsWithStep(){const t={};for(const n of St){const i=this.childModel.component.scales[n];if(i&&!i.merged){const r=i.get("type"),s=i.get("range");if(be(r)&&nn(s)){const o=ls(this.childModel,n),a=Ba(o);a?t[n]=a:S(Bo(n))}}}return t}assembleRowColumnHeaderData(t,n,i){const r={row:"y",column:"x",facet:void 0}[t],s=[],o=[],a=[];r&&i&&i[r]&&(n?(s.push(`distinct_${i[r]}`),o.push("max")):(s.push(i[r]),o.push("distinct")),a.push(`distinct_${i[r]}`));const{sortField:c,sortIndexField:l}=this[t];if(c){const{op:u=Xr,field:f}=c;s.push(f),o.push(u),a.push(w(c,{forAs:!0}))}else l&&(s.push(l),o.push("max"),a.push(l));return{name:this[t].name,source:n??this.data,transform:[{type:"aggregate",groupby:this[t].fields,...s.length?{fields:s,ops:o,as:a}:{}}]}}assembleFacetHeaderData(t){const{columns:n}=this.model.layout,{layoutHeaders:i}=this.model.component,r=[],s={};for(const c of Ia){for(const l of La){const u=(i[c]&&i[c][l])??[];for(const f of u)if(f.axes?.length>0){s[c]=!0;break}}if(s[c]){const l=`length(data("${this.facet.name}"))`,u=c==="row"?n?{signal:`ceil(${l} / ${n})`}:1:n?{signal:`min(${l}, ${n})`}:{signal:l};r.push({name:`${this.facet.name}_${c}`,transform:[{type:"sequence",start:0,stop:u}]})}}const{row:o,column:a}=s;return(o||a)&&r.unshift(this.assembleRowColumnHeaderData("facet",null,t)),r}assemble(){const t=[];let n=null;const i=this.getChildIndependentFieldsWithStep(),{column:r,row:s,facet:o}=this;if(r&&s&&(i.x||i.y)){n=`cross_${this.column.name}_${this.row.name}`;const a=[].concat(i.x??[],i.y??[]),c=a.map(()=>"distinct");t.push({name:n,source:this.data,transform:[{type:"aggregate",groupby:this.fields,fields:a,ops:c}]})}for(const a of[_t,Nt])this[a]&&t.push(this.assembleRowColumnHeaderData(a,n,i));if(o){const a=this.assembleFacetHeaderData(i);a&&t.push(...a)}return t}}function il(e){return e.startsWith("'")&&e.endsWith("'")||e.startsWith('"')&&e.endsWith('"')?e.slice(1,-1):e}function Lv(e,t){const n=Oo(e);if(t==="number")return`toNumber(${n})`;if(t==="boolean")return`toBoolean(${n})`;if(t==="string")return`toString(${n})`;if(t==="date")return`toDate(${n})`;if(t==="flatten")return n;if(t.startsWith("date:")){const i=il(t.slice(5,t.length));return`timeParse(${n},'${i}')`}else if(t.startsWith("utc:")){const i=il(t.slice(4,t.length));return`utcParse(${n},'${i}')`}else return S(sh(t)),null}function Pv(e){const t={};return pr(e.filter,n=>{if(Gu(n)){let i=null;Ho(n)?i=Pe(n.equal):Xo(n)?i=Pe(n.lte):Vo(n)?i=Pe(n.lt):Yo(n)?i=Pe(n.gt):Ko(n)?i=Pe(n.gte):Jo(n)?i=n.range[0]:Qo(n)&&(i=(n.oneOf??n.in)[0]),i&&(Rn(i)?t[n.field]="date":le(i)?t[n.field]="number":z(i)&&(t[n.field]="string")),n.timeUnit&&(t[n.field]="date")}}),t}function zv(e){const t={};function n(i){si(i)?t[i.field]="date":i.type==="quantitative"&&zg(i.aggregate)?t[i.field]="number":Qn(i.field)>1?i.field in t||(t[i.field]="flatten"):Ln(i)&&ft(i.sort)&&Qn(i.sort.field)>1&&(i.sort.field in t||(t[i.sort.field]="flatten"))}if((ae(e)||Je(e))&&e.forEachFieldDef((i,r)=>{if(Fe(i))n(i);else{const s=On(r),o=e.fieldDef(s);n({...i,type:o.type})}}),ae(e)){const{mark:i,markDef:r,encoding:s}=e;if(rn(i)&&!e.encoding.order){const o=r.orient==="horizontal"?"y":"x",a=s[o];E(a)&&a.type==="quantitative"&&!(a.field in t)&&(t[a.field]="number")}}return t}function Dv(e){const t={};if(ae(e)&&e.component.selection)for(const n of v(e.component.selection)){const i=e.component.selection[n];for(const r of i.project.items)!r.channel&&Qn(r.field)>1&&(t[r.field]="flatten")}return t}class we extends K{clone(){return new we(null,j(this._parse))}constructor(t,n){super(t),this._parse=n}hash(){return`Parse ${W(this._parse)}`}static makeExplicit(t,n,i){let r={};const s=n.data;return!Wt(s)&&s?.format?.parse&&(r=s.format.parse),this.makeWithAncestors(t,r,{},i)}static makeWithAncestors(t,n,i,r){for(const a of v(i)){const c=r.getWithExplicit(a);c.value!==void 0&&(c.explicit||c.value===i[a]||c.value==="derived"||i[a]==="flatten"?delete i[a]:S(hc(a,i[a],c.value)))}for(const a of v(n)){const c=r.get(a);c!==void 0&&(c===n[a]?delete n[a]:S(hc(a,n[a],c)))}const s=new jt(n,i);r.copyAll(s);const o={};for(const a of v(s.combine())){const c=s.get(a);c!==null&&(o[a]=c)}return v(o).length===0||r.parseNothing?null:new we(t,o)}get parse(){return this._parse}merge(t){this._parse={...this._parse,...t.parse},t.remove()}assembleFormatParse(){const t={};for(const n of v(this._parse)){const i=this._parse[n];Qn(n)===1&&(t[n]=i)}return t}producedFields(){return new Set(v(this._parse))}dependentFields(){return new Set(v(this._parse))}assembleTransforms(t=!1){return v(this._parse).filter(n=>t?Qn(n)>1:!0).map(n=>{const i=Lv(n,this._parse[n]);return i?{type:"formula",expr:i,as:Ao(n)}:null}).filter(n=>n!==null)}}class Yt extends K{clone(){return new Yt(null)}constructor(t){super(t)}dependentFields(){return new Set}producedFields(){return new Set([Ze])}hash(){return"Identifier"}assemble(){return{type:"identifier",as:Ze}}}class er extends K{clone(){return new er(null,this.params)}constructor(t,n){super(t),this.params=n}dependentFields(){return new Set}producedFields(){}hash(){return`Graticule ${W(this.params)}`}assemble(){return{type:"graticule",...this.params===!0?{}:this.params}}}class tr extends K{clone(){return new tr(null,this.params)}constructor(t,n){super(t),this.params=n}dependentFields(){return new Set}producedFields(){return new Set([this.params.as??"data"])}hash(){return`Hash ${W(this.params)}`}assemble(){return{type:"sequence",...this.params}}}class _n extends K{constructor(t){super(null),t??(t={name:"source"});let n;if(Wt(t)||(n=t.format?{...ke(t.format,["parse"])}:{}),Di(t))this._data={values:t.values};else if(oi(t)){if(this._data={url:t.url},!n.type){let i=/(?:\.([^.]+))?$/.exec(t.url)[1];G(["json","csv","tsv","dsv","topojson"],i)||(i="json"),n.type=i}}else sd(t)?this._data={values:[{type:"Sphere"}]}:(id(t)||Wt(t))&&(this._data={});this._generator=Wt(t),t.name&&(this._name=t.name),n&&!J(n)&&(this._data.format=n)}dependentFields(){return new Set}producedFields(){}get data(){return this._data}hasName(){return!!this._name}get isGenerator(){return this._generator}get dataName(){return this._name}set dataName(t){this._name=t}set parent(t){throw new Error("Source nodes have to be roots.")}remove(){throw new Error("Source nodes are roots and cannot be removed.")}hash(){throw new Error("Cannot hash sources")}assemble(){return{name:this._name,...this._data,transform:[]}}}var rl=function(e,t,n,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(e,n):r?r.value=n:t.set(e,n),n},jv=function(e,t,n,i){if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?i:n==="a"?i.call(e):i?i.value:t.get(e)},Ti;function ja(e){return e instanceof _n||e instanceof er||e instanceof tr}class Ma{constructor(){Ti.set(this,void 0),rl(this,Ti,!1,"f")}setModified(){rl(this,Ti,!0,"f")}get modifiedFlag(){return jv(this,Ti,"f")}}Ti=new WeakMap;class Pn extends Ma{getNodeDepths(t,n,i){i.set(t,n);for(const r of t.children)this.getNodeDepths(r,n+1,i);return i}optimize(t){const i=[...this.getNodeDepths(t,0,new Map).entries()].sort((r,s)=>s[1]-r[1]);for(const r of i)this.run(r[0]);return this.modifiedFlag}}class Ua extends Ma{optimize(t){this.run(t);for(const n of t.children)this.optimize(n);return this.modifiedFlag}}class Mv extends Ua{mergeNodes(t,n){const i=n.shift();for(const r of n)t.removeChild(r),r.parent=i,r.remove()}run(t){const n=t.children.map(r=>r.hash()),i={};for(let r=0;r<n.length;r++)i[n[r]]===void 0?i[n[r]]=[t.children[r]]:i[n[r]].push(t.children[r]);for(const r of v(i))i[r].length>1&&(this.setModified(),this.mergeNodes(t,i[r]))}}class Uv extends Ua{constructor(t){super(),this.requiresSelectionId=t&&Ra(t)}run(t){t instanceof Yt&&(this.requiresSelectionId&&(ja(t.parent)||t.parent instanceof Ke||t.parent instanceof we)||(this.setModified(),t.remove()))}}class Bv extends Ma{optimize(t){return this.run(t,new Set),this.modifiedFlag}run(t,n){let i=new Set;t instanceof dt&&(i=t.producedFields(),ko(i,n)&&(this.setModified(),t.removeFormulas(n),t.producedFields.length===0&&t.remove()));for(const r of t.children)this.run(r,new Set([...n,...i]))}}class Wv extends Ua{constructor(){super()}run(t){t instanceof Ce&&!t.isRequired()&&(this.setModified(),t.remove())}}class Gv extends Pn{run(t){if(!ja(t)&&!(t.numChildren()>1)){for(const n of t.children)if(n instanceof we)if(t instanceof we)this.setModified(),t.merge(n);else{if(To(t.producedFields(),n.dependentFields()))continue;this.setModified(),n.swapWithParent()}}}}class qv extends Pn{run(t){const n=[...t.children],i=t.children.filter(r=>r instanceof we);if(t.numChildren()>1&&i.length>=1){const r={},s=new Set;for(const o of i){const a=o.parse;for(const c of v(a))c in r?r[c]!==a[c]&&s.add(c):r[c]=a[c]}for(const o of s)delete r[o];if(!J(r)){this.setModified();const o=new we(t,r);for(const a of n){if(a instanceof we)for(const c of v(r))delete a.parse[c];t.removeChild(a),a.parent=o,a instanceof we&&v(a.parse).length===0&&a.remove()}}}}}class Hv extends Pn{run(t){t instanceof Ce||t.numChildren()>0||t instanceof xi||t instanceof _n||(this.setModified(),t.remove())}}class Vv extends Pn{run(t){const n=t.children.filter(r=>r instanceof dt),i=n.pop();for(const r of n)this.setModified(),i.merge(r)}}class Xv extends Pn{run(t){const n=t.children.filter(r=>r instanceof Ke),i={};for(const r of n){const s=W(r.groupBy);s in i||(i[s]=[]),i[s].push(r)}for(const r of v(i)){const s=i[r];if(s.length>1){const o=s.pop();for(const a of s)o.merge(a)&&(t.removeChild(a),a.parent=o,a.remove(),this.setModified())}}}}class Yv extends Pn{constructor(t){super(),this.model=t}run(t){const n=!(ja(t)||t instanceof bi||t instanceof we||t instanceof Yt),i=[],r=[];for(const s of t.children)s instanceof pt&&(n&&!To(t.producedFields(),s.dependentFields())?i.push(s):r.push(s));if(i.length>0){const s=i.pop();for(const o of i)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified(),t instanceof pt?t.merge(s,this.model.renameSignal.bind(this.model)):s.swapWithParent()}if(r.length>1){const s=r.pop();for(const o of r)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified()}}}class Kv extends Pn{run(t){const n=[...t.children];if(!xn(n,o=>o instanceof Ce)||t.numChildren()<=1)return;const r=[];let s;for(const o of n)if(o instanceof Ce){let a=o;for(;a.numChildren()===1;){const[c]=a.children;if(c instanceof Ce)a=c;else break}r.push(...a.children),s?(t.removeChild(o),o.parent=s.parent,s.parent.removeChild(s),s.parent=a,this.setModified()):s=a}else r.push(o);if(r.length){this.setModified();for(const o of r)o.parent.removeChild(o),o.parent=s}}}class zn extends K{clone(){return new zn(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut(this.transform.groupby.concat(t),n=>n)}dependentFields(){const t=new Set;return this.transform.groupby&&this.transform.groupby.forEach(t.add,t),this.transform.joinaggregate.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.joinaggregate.map(this.getDefaultName))}getDefaultName(t){return t.as??w(t)}hash(){return`JoinAggregateTransform ${W(this.transform)}`}assemble(){const t=[],n=[],i=[];for(const s of this.transform.joinaggregate)n.push(s.op),i.push(this.getDefaultName(s)),t.push(s.field===void 0?null:s.field);const r=this.transform.groupby;return{type:"joinaggregate",as:i,ops:n,fields:t,...r!==void 0?{groupby:r}:{}}}}function Jv(e){return e.stack.stackBy.reduce((t,n)=>{const i=n.fieldDef,r=w(i);return r&&t.push(r),t},[])}function Qv(e){return A(e)&&e.every(t=>z(t))&&e.length>1}class Tt extends K{clone(){return new Tt(null,j(this._stack))}constructor(t,n){super(t),this._stack=n}static makeFromTransform(t,n){const{stack:i,groupby:r,as:s,offset:o="zero"}=n,a=[],c=[];if(n.sort!==void 0)for(const f of n.sort)a.push(f.field),c.push(ue(f.order,"ascending"));const l={field:a,order:c};let u;return Qv(s)?u=s:z(s)?u=[s,`${s}_end`]:u=[`${n.stack}_start`,`${n.stack}_end`],new Tt(t,{dimensionFieldDefs:[],stackField:i,groupby:r,offset:o,sort:l,facetby:[],as:u})}static makeFromEncoding(t,n){const i=n.stack,{encoding:r}=n;if(!i)return null;const{groupbyChannels:s,fieldChannel:o,offset:a,impute:c}=i,l=s.map(p=>{const g=r[p];return ht(g)}).filter(p=>!!p),u=Jv(n),f=n.encoding.order;let d;if(A(f)||E(f))d=wu(f);else{const p=hf(f)?f.sort:o==="y"?"descending":"ascending";d=u.reduce((g,h)=>(g.field.push(h),g.order.push(p),g),{field:[],order:[]})}return new Tt(t,{dimensionFieldDefs:l,stackField:n.vgField(o),facetby:[],stackby:u,sort:d,offset:a,impute:c,as:[n.vgField(o,{suffix:"start",forAs:!0}),n.vgField(o,{suffix:"end",forAs:!0})]})}get stack(){return this._stack}addDimensions(t){this._stack.facetby.push(...t)}dependentFields(){const t=new Set;return t.add(this._stack.stackField),this.getGroupbyFields().forEach(t.add,t),this._stack.facetby.forEach(t.add,t),this._stack.sort.field.forEach(t.add,t),t}producedFields(){return new Set(this._stack.as)}hash(){return`Stack ${W(this._stack)}`}getGroupbyFields(){const{dimensionFieldDefs:t,impute:n,groupby:i}=this._stack;return t.length>0?t.map(r=>r.bin?n?[w(r,{binSuffix:"mid"})]:[w(r,{}),w(r,{binSuffix:"end"})]:[w(r)]).flat():i??[]}assemble(){const t=[],{facetby:n,dimensionFieldDefs:i,stackField:r,stackby:s,sort:o,offset:a,impute:c,as:l}=this._stack;if(c)for(const u of i){const{bandPosition:f=.5,bin:d}=u;if(d){const p=w(u,{expr:"datum"}),g=w(u,{expr:"datum",binSuffix:"end"});t.push({type:"formula",expr:`${f}*${p}+${1-f}*${g}`,as:w(u,{binSuffix:"mid",forAs:!0})})}t.push({type:"impute",field:r,groupby:[...s,...n],key:w(u,{binSuffix:"mid"}),method:"value",value:0})}return t.push({type:"stack",groupby:[...this.getGroupbyFields(),...n],field:r,sort:o,as:l,offset:a}),t}}class vi extends K{clone(){return new vi(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut(this.transform.groupby.concat(t),n=>n)}dependentFields(){const t=new Set;return(this.transform.groupby??[]).forEach(t.add,t),(this.transform.sort??[]).forEach(n=>t.add(n.field)),this.transform.window.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.window.map(this.getDefaultName))}getDefaultName(t){return t.as??w(t)}hash(){return`WindowTransform ${W(this.transform)}`}assemble(){const t=[],n=[],i=[],r=[];for(const f of this.transform.window)n.push(f.op),i.push(this.getDefaultName(f)),r.push(f.param===void 0?null:f.param),t.push(f.field===void 0?null:f.field);const s=this.transform.frame,o=this.transform.groupby;if(s&&s[0]===null&&s[1]===null&&n.every(f=>Mo(f)))return{type:"joinaggregate",as:i,ops:n,fields:t,...o!==void 0?{groupby:o}:{}};const a=[],c=[];if(this.transform.sort!==void 0)for(const f of this.transform.sort)a.push(f.field),c.push(f.order??"ascending");const l={field:a,order:c},u=this.transform.ignorePeers;return{type:"window",params:r,as:i,ops:n,fields:t,sort:l,...u!==void 0?{ignorePeers:u}:{},...o!==void 0?{groupby:o}:{},...s!==void 0?{frame:s}:{}}}}function Zv(e){function t(n){if(!(n instanceof xi)){const i=n.clone();if(i instanceof Ce){const r=go+i.getSource();i.setSource(r),e.model.component.data.outputNodes[r]=i}else(i instanceof Ke||i instanceof Tt||i instanceof vi||i instanceof zn)&&i.addDimensions(e.fields);for(const r of n.children.flatMap(t))r.parent=i;return[i]}return n.children.flatMap(t)}return t}function po(e){if(e instanceof xi)if(e.numChildren()===1&&!(e.children[0]instanceof Ce)){const t=e.children[0];(t instanceof Ke||t instanceof Tt||t instanceof vi||t instanceof zn)&&t.addDimensions(e.fields),t.swapWithParent(),po(e)}else{const t=e.model.component.data.main;Jd(t);const n=Zv(e),i=e.children.map(n).flat();for(const r of i)r.parent=t}else e.children.map(po)}function Jd(e){if(e instanceof Ce&&e.type===Z.Main&&e.numChildren()===1){const t=e.children[0];t instanceof xi||(t.swapWithParent(),Jd(e))}}const go="scale_",or=5;function ho(e){for(const t of e){for(const n of t.children)if(n.parent!==t)return!1;if(!ho(t.children))return!1}return!0}function Ge(e,t){let n=!1;for(const i of t)n=e.optimize(i)||n;return n}function sl(e,t,n){let i=e.sources,r=!1;return r=Ge(new Wv,i)||r,r=Ge(new Uv(t),i)||r,i=i.filter(s=>s.numChildren()>0),r=Ge(new Hv,i)||r,i=i.filter(s=>s.numChildren()>0),n||(r=Ge(new Gv,i)||r,r=Ge(new Yv(t),i)||r,r=Ge(new Bv,i)||r,r=Ge(new qv,i)||r,r=Ge(new Xv,i)||r,r=Ge(new Vv,i)||r,r=Ge(new Mv,i)||r,r=Ge(new Kv,i)||r),e.sources=i,r}function eS(e,t){ho(e.sources);let n=0,i=0;for(let r=0;r<or&&sl(e,t,!0);r++)n++;e.sources.map(po);for(let r=0;r<or&&sl(e,t,!1);r++)i++;ho(e.sources),Math.max(n,i)===or&&S(`Maximum optimization runs(${or}) reached.`)}class Oe{constructor(t){Object.defineProperty(this,"signal",{enumerable:!0,get:t})}static fromName(t,n){return new Oe(()=>t(n))}}function Qd(e){ae(e)?tS(e):nS(e)}function tS(e){const t=e.component.scales;for(const n of v(t)){const i=rS(e,n);if(t[n].setWithExplicit("domains",i),oS(e,n),e.component.data.isFaceted){let s=e;for(;!Je(s)&&s.parent;)s=s.parent;if(s.component.resolve.scale[n]==="shared")for(const a of i.value)Ft(a)&&(a.data=go+a.data.replace(go,""))}}}function nS(e){for(const n of e.children)Qd(n);const t=e.component.scales;for(const n of v(t)){let i,r=null;for(const s of e.children){const o=s.component.scales[n];if(o){i===void 0?i=o.getWithExplicit("domains"):i=Ht(i,o.getWithExplicit("domains"),"domains","scale",mo);const a=o.get("selectionExtent");r&&a&&r.param!==a.param&&S(th),r=a}}t[n].setWithExplicit("domains",i),r&&t[n].set("selectionExtent",r,!0)}}function iS(e,t,n,i){if(e==="unaggregated"){const{valid:r,reason:s}=ol(t,n);if(!r){S(s);return}}else if(e===void 0&&i.useUnaggregatedDomain){const{valid:r}=ol(t,n);if(r)return"unaggregated"}return e}function rS(e,t){const n=e.getScaleComponent(t).get("type"),{encoding:i}=e,r=iS(e.scaleDomain(t),e.typedFieldDef(t),n,e.config.scale);return r!==e.scaleDomain(t)&&(e.specifiedScales[t]={...e.specifiedScales[t],domain:r}),t==="x"&&fe(i.x2)?fe(i.x)?Ht(Mt(n,r,e,"x"),Mt(n,r,e,"x2"),"domain","scale",mo):Mt(n,r,e,"x2"):t==="y"&&fe(i.y2)?fe(i.y)?Ht(Mt(n,r,e,"y"),Mt(n,r,e,"y2"),"domain","scale",mo):Mt(n,r,e,"y2"):Mt(n,r,e,t)}function sS(e,t,n){return e.map(i=>({signal:`{data: ${Zr(i,{timeUnit:n,type:t})}}`}))}function Is(e,t,n){const i=he(n)?.unit;return t==="temporal"||i?sS(e,t,i):[e]}function Mt(e,t,n,i){const{encoding:r}=n,s=fe(r[i]),{type:o}=s,a=s.timeUnit;if(Fm(t)){const f=Mt(e,void 0,n,i),d=Is(t.unionWith,o,a);return ct([...d,...f.value])}else{if(I(t))return ct([t]);if(t&&t!=="unaggregated"&&!Ju(t))return ct(Is(t,o,a))}const c=n.stack;if(c&&i===c.fieldChannel){if(c.offset==="normalize")return Le([[0,1]]);const f=n.requestDataName(Z.Main);return Le([{data:f,field:n.vgField(i,{suffix:"start"})},{data:f,field:n.vgField(i,{suffix:"end"})}])}const l=zt(i)&&E(s)?aS(n,i,e):void 0;if(Et(s)){const f=Is([s.datum],o,a);return Le(f)}const u=s;if(t==="unaggregated"){const f=n.requestDataName(Z.Main),{field:d}=s;return Le([{data:f,field:w({field:d,aggregate:"min"})},{data:f,field:w({field:d,aggregate:"max"})}])}else if(te(u.bin)){if(be(e))return Le(e==="bin-ordinal"?[]:[{data:Pi(l)?n.requestDataName(Z.Main):n.requestDataName(Z.Raw),field:n.vgField(i,Ji(u,i)?{binSuffix:"range"}:{}),sort:l===!0||!V(l)?{field:n.vgField(i,{}),op:"min"}:l}]);{const{bin:f}=u;if(te(f)){const d=Da(n,u.field,f);return Le([new Oe(()=>{const p=n.getSignalName(d);return`[${p}.start, ${p}.stop]`})])}else return Le([{data:n.requestDataName(Z.Main),field:n.vgField(i,{})}])}}else if(u.timeUnit&&G(["time","utc"],e)&&gf(u,ae(n)?n.encoding[vt(i)]:void 0,n.markDef,n.config)){const f=n.requestDataName(Z.Main);return Le([{data:f,field:n.vgField(i)},{data:f,field:n.vgField(i,{suffix:"end"})}])}else return Le(l?[{data:Pi(l)?n.requestDataName(Z.Main):n.requestDataName(Z.Raw),field:n.vgField(i),sort:l}]:[{data:n.requestDataName(Z.Main),field:n.vgField(i)}])}function Ls(e,t){const{op:n,field:i,order:r}=e;return{op:n??(t?"sum":Xr),...i?{field:Me(i)}:{},...r?{order:r}:{}}}function oS(e,t){const n=e.component.scales[t],i=e.specifiedScales[t].domain,r=e.fieldDef(t)?.bin,s=Ju(i)&&i,o=An(r)&&Ur(r.extent)&&r.extent;(s||o)&&n.set("selectionExtent",s??o,!0)}function aS(e,t,n){if(!be(n))return;const i=e.fieldDef(t),r=i.sort;if(ff(r))return{op:"min",field:li(i,t),order:"ascending"};const{stack:s}=e,o=s?new Set([...s.groupbyFields,...s.stackBy.map(a=>a.fieldDef.field)]):void 0;if(ft(r)){const a=s&&!o.has(r.field);return Ls(r,a)}else if(uf(r)){const{encoding:a,order:c}=r,l=e.fieldDef(a),{aggregate:u,field:f}=l,d=s&&!o.has(f);if(Ot(u)||tn(u))return Ls({field:w(l),order:c},d);if(Mo(u)||!u)return Ls({op:u,field:f,order:c},d)}else{if(r==="descending")return{op:"min",field:e.vgField(t),order:"descending"};if(G(["ascending",void 0],r))return!0}}function ol(e,t){const{aggregate:n,type:i}=e;return n?z(n)&&!jg.has(n)?{valid:!1,reason:kh(n)}:i==="quantitative"&&t==="log"?{valid:!1,reason:Th(e)}:{valid:!0}:{valid:!1,reason:_h(e)}}function mo(e,t,n,i){return e.explicit&&t.explicit&&S(Lh(n,i,e.value,t.value)),{explicit:e.explicit,value:[...e.value,...t.value]}}function cS(e){const t=ut(e.map(o=>{if(Ft(o)){const{sort:a,...c}=o;return c}return o}),W),n=ut(e.map(o=>{if(Ft(o)){const a=o.sort;return a!==void 0&&!Pi(a)&&("op"in a&&a.op==="count"&&delete a.field,a.order==="ascending"&&delete a.order),a}}).filter(o=>o!==void 0),W);if(t.length===0)return;if(t.length===1){const o=e[0];if(Ft(o)&&n.length>0){let a=n[0];if(n.length>1){S(yc);const c=n.filter(l=>V(l)&&"op"in l&&l.op!=="min");n.every(l=>V(l)&&"op"in l)&&c.length===1?a=c[0]:a=!0}else if(V(a)&&"field"in a){const c=a.field;o.field===c&&(a=a.order?{order:a.order}:!0)}return{...o,sort:a}}return o}const i=ut(n.map(o=>Pi(o)||!("op"in o)||z(o.op)&&o.op in Lg?o:(S(zh(o)),!0)),W);let r;i.length===1?r=i[0]:i.length>1&&(S(yc),r=!0);const s=ut(e.map(o=>Ft(o)?o.data:null),o=>o);return s.length===1&&s[0]!==null?{data:s[0],fields:t.map(a=>a.field),...r?{sort:r}:{}}:{fields:t,...r?{sort:r}:{}}}function Ba(e){if(Ft(e)&&z(e.field))return e.field;if(Mg(e)){let t;for(const n of e.fields)if(Ft(n)&&z(n.field)){if(!t)t=n.field;else if(t!==n.field)return S(Dh),t}return S(jh),t}else if(Ug(e)){S(Mh);const t=e.fields[0];return z(t)?t:void 0}}function ls(e,t){const i=e.component.scales[t].get("domains").map(r=>(Ft(r)&&(r.data=e.lookupDataSource(r.data)),r));return cS(i)}function Zd(e){return Si(e)||Wa(e)?e.children.reduce((t,n)=>t.concat(Zd(n)),al(e)):al(e)}function al(e){return v(e.component.scales).reduce((t,n)=>{const i=e.component.scales[n];if(i.merged)return t;const r=i.combine(),{name:s,type:o,selectionExtent:a,domains:c,range:l,reverse:u,...f}=r,d=lS(r.range,s,n,e),p=ls(e,n),g=a?ix(e,a,i,p):null;return t.push({name:s,type:o,...p?{domain:p}:{},...g?{domainRaw:g}:{},range:d,...u!==void 0?{reverse:u}:{},...f}),t},[])}function lS(e,t,n,i){if(ye(n)){if(nn(e))return{step:{signal:`${t}_step`}}}else if(V(e)&&Ft(e))return{...e,data:i.lookupDataSource(e.data)};return e}class ep extends jt{constructor(t,n){super({},{name:t}),this.merged=!1,this.setWithExplicit("type",n)}domainDefinitelyIncludesZero(){return this.get("zero")!==!1?!0:xn(this.get("domains"),t=>A(t)&&t.length===2&&t[0]<=0&&t[1]>=0)}}const uS=["range","scheme"];function fS(e){const t=e.component.scales;for(const n of Mr){const i=t[n];if(!i)continue;const r=dS(n,e);i.setWithExplicit("range",r)}}function cl(e,t){const n=e.fieldDef(t);if(n?.bin){const{bin:i,field:r}=n,s=Ie(t),o=e.getName(s);if(V(i)&&i.binned&&i.step!==void 0)return new Oe(()=>{const a=e.scaleName(t),c=`(domain("${a}")[1] - domain("${a}")[0]) / ${i.step}`;return`${e.getSignalName(o)} / (${c})`});if(te(i)){const a=Da(e,r,i);return new Oe(()=>{const c=e.getSignalName(a),l=`(${c}.stop - ${c}.start) / ${c}.step`;return`${e.getSignalName(o)} / (${l})`})}}}function dS(e,t){const n=t.specifiedScales[e],{size:i}=t,s=t.getScaleComponent(e).get("type");for(const f of uS)if(n[f]!==void 0){const d=Qs(s,f),p=Qu(e,f);if(!d)S(Ou(s,f,e));else if(p)S(p);else switch(f){case"range":{const g=n.range;if(A(g)){if(ye(e))return ct(g.map(h=>{if(h==="width"||h==="height"){const m=t.getName(h),y=t.getSignalName.bind(t);return Oe.fromName(y,m)}return h}))}else if(V(g))return ct({data:t.requestDataName(Z.Main),field:g.field,sort:{op:"min",field:t.vgField(e)}});return ct(g)}case"scheme":return ct(pS(n[f]))}}const o=e===oe||e==="xOffset"?"width":"height",a=i[o];if(mt(a)){if(ye(e))if(be(s)){const f=tp(a,t,e);if(f)return ct({step:f})}else S(Au(o));else if(hi(e)){const f=e===Kt?"x":"y";if(t.getScaleComponent(f).get("type")==="band"){const g=np(a,s);if(g)return ct(g)}}}const{rangeMin:c,rangeMax:l}=n,u=gS(e,t);return(c!==void 0||l!==void 0)&&Qs(s,"rangeMin")&&A(u)&&u.length===2?ct([c??u[0],l??u[1]]):Le(u)}function pS(e){return Cm(e)?{scheme:e.name,...ke(e,["name"])}:{scheme:e}}function gS(e,t){const{size:n,config:i,mark:r,encoding:s}=t,o=t.getSignalName.bind(t),{type:a}=fe(s[e]),l=t.getScaleComponent(e).get("type"),{domain:u,domainMid:f}=t.specifiedScales[e];switch(e){case oe:case me:{if(G(["point","band"],l)){const g=ip(e,n,i.view);if(mt(g))return{step:tp(g,t,e)}}const d=Ie(e),p=t.getName(d);return e===me&&ze(l)?[Oe.fromName(o,p),0]:[0,Oe.fromName(o,p)]}case Kt:case gi:return hS(e,t,l);case Lt:{const d=t.component.scales[e].get("zero"),p=rp(r,d,i),g=bS(r,n,t,i);return ni(l)?yS(p,g,mS(l,i,u,e)):[p,g]}case Be:return[0,Math.PI*2];case Tn:return[0,360];case tt:return[0,new Oe(()=>{const d=t.getSignalName("width"),p=t.getSignalName("height");return`min(${d},${p})/2`})];case Zt:return[i.scale.minStrokeWidth,i.scale.maxStrokeWidth];case en:return[[1,0],[4,2],[2,1],[1,1],[1,2,4,2]];case Re:return"symbol";case Ae:case bt:case xt:return l==="ordinal"?a==="nominal"?"category":"ordinal":f!==void 0?"diverging":r==="rect"||r==="geoshape"?"heatmap":"ramp";case Pt:case Jt:case Qt:return[i.scale.minOpacity,i.scale.maxOpacity]}}function tp(e,t,n){const{encoding:i}=t,r=t.getScaleComponent(n),s=Po(n),o=i[s];if(Gf({step:e,offsetIsDiscrete:M(o)&&Hu(o.type)})==="offset"&&Ff(i,s)){const c=t.getScaleComponent(s);let u=`domain('${t.scaleName(s)}').length`;if(c.get("type")==="band"){const d=c.get("paddingInner")??c.get("padding")??0,p=c.get("paddingOuter")??c.get("padding")??0;u=`bandspace(${u}, ${d}, ${p})`}const f=r.get("paddingInner")??r.get("padding");return{signal:`${e.step} * ${u} / (1-${qg(f)})`}}else return e.step}function np(e,t){if(Gf({step:e,offsetIsDiscrete:be(t)})==="offset")return{step:e.step}}function hS(e,t,n){const i=e===Kt?"x":"y",s=t.getScaleComponent(i).get("type"),o=t.scaleName(i);if(s==="band"){const a=ip(i,t.size,t.config.view);if(mt(a)){const c=np(a,n);if(c)return c}return[0,{signal:`bandwidth('${o}')`}]}else{const a=t.encoding[i];if(E(a)&&a.timeUnit){const c=Bu(a.timeUnit,u=>`scale('${o}', ${u})`),l=t.config.scale.bandWithNestedOffsetPaddingInner;if(l){const u=I(l)?`${l.signal}/2`:`${l/2}`,f=I(l)?`(1 - ${l.signal}/2)`:`${1-l/2}`;return[{signal:`${u} * (${c})`},{signal:`${f} * (${c})`}]}return[0,{signal:c}]}return Jl(`Cannot use ${e} scale if ${i} scale is not discrete.`)}}function ip(e,t,n){const i=e===oe?"width":"height",r=t[i];return r||Cr(n,i)}function mS(e,t,n,i){switch(e){case"quantile":return t.scale.quantileCount;case"quantize":return t.scale.quantizeCount;case"threshold":return n!==void 0&&A(n)?n.length+1:(S(Yh(i)),3)}}function yS(e,t,n){const i=()=>{const r=Ve(t),s=Ve(e),o=`(${r} - ${s}) / (${n} - 1)`;return`sequence(${s}, ${r} + ${o}, ${o})`};return I(t)?new Oe(i):{signal:i()}}function rp(e,t,n){if(t)return I(t)?{signal:`${t.signal} ? 0 : ${rp(e,!1,n)}`}:0;switch(e){case"bar":case"tick":return n.scale.minBandSize;case"line":case"trail":case"rule":return n.scale.minStrokeWidth;case"text":return n.scale.minFontSize;case"point":case"square":case"circle":return n.scale.minSize}throw new Error(Br("size",e))}const ll=.95;function bS(e,t,n,i){const r={x:cl(n,"x"),y:cl(n,"y")};switch(e){case"bar":case"tick":{if(i.scale.maxBandSize!==void 0)return i.scale.maxBandSize;const s=ul(t,r,i.view);return le(s)?s-1:new Oe(()=>`${s.signal} - 1`)}case"line":case"trail":case"rule":return i.scale.maxStrokeWidth;case"text":return i.scale.maxFontSize;case"point":case"square":case"circle":{if(i.scale.maxSize)return i.scale.maxSize;const s=ul(t,r,i.view);return le(s)?Math.pow(ll*s,2):new Oe(()=>`pow(${ll} * ${s.signal}, 2)`)}}throw new Error(Br("size",e))}function ul(e,t,n){const i=mt(e.width)?e.width.step:wr(n,"width"),r=mt(e.height)?e.height.step:wr(n,"height");return t.x||t.y?new Oe(()=>`min(${[t.x?t.x.signal:i,t.y?t.y.signal:r].join(", ")})`):Math.min(i,r)}function sp(e,t){ae(e)?xS(e,t):ap(e,t)}function xS(e,t){const n=e.component.scales,{config:i,encoding:r,markDef:s,specifiedScales:o}=e;for(const a of v(n)){const c=o[a],l=n[a],u=e.getScaleComponent(a),f=fe(r[a]),d=c[t],p=u.get("type"),g=u.get("padding"),h=u.get("paddingInner"),m=Qs(p,t),y=Qu(a,t);if(d!==void 0&&(m?y&&S(y):S(Ou(p,t,a))),m&&y===void 0)if(d!==void 0){const b=f.timeUnit,k=f.type;switch(t){case"domainMax":case"domainMin":Rn(c[t])||k==="temporal"||b?l.set(t,{signal:Zr(c[t],{type:k,timeUnit:b})},!0):l.set(t,c[t],!0);break;default:l.copyKeyFromObject(t,c)}}else{const b=t in fl?fl[t]({model:e,channel:a,fieldOrDatumDef:f,scaleType:p,scalePadding:g,scalePaddingInner:h,domain:c.domain,domainMin:c.domainMin,domainMax:c.domainMax,markDef:s,config:i,hasNestedOffsetScale:eo(r,a),hasSecondaryRangeChannel:!!r[vt(a)]}):i.scale[t];b!==void 0&&l.set(t,b,!1)}}}const fl={bins:({model:e,fieldOrDatumDef:t})=>E(t)?vS(e,t):void 0,interpolate:({channel:e,fieldOrDatumDef:t})=>SS(e,t.type),nice:({scaleType:e,channel:t,domain:n,domainMin:i,domainMax:r,fieldOrDatumDef:s})=>ES(e,t,n,i,r,s),padding:({channel:e,scaleType:t,fieldOrDatumDef:n,markDef:i,config:r})=>$S(e,t,r.scale,n,i,r.bar),paddingInner:({scalePadding:e,channel:t,markDef:n,scaleType:i,config:r,hasNestedOffsetScale:s})=>wS(e,t,n.type,i,r.scale,s),paddingOuter:({scalePadding:e,channel:t,scaleType:n,scalePaddingInner:i,config:r,hasNestedOffsetScale:s})=>CS(e,t,n,i,r.scale,s),reverse:({fieldOrDatumDef:e,scaleType:t,channel:n,config:i})=>{const r=E(e)?e.sort:void 0;return FS(t,r,n,i.scale)},zero:({channel:e,fieldOrDatumDef:t,domain:n,markDef:i,scaleType:r,config:s,hasSecondaryRangeChannel:o})=>NS(e,t,n,i,r,s.scale,o)};function op(e){ae(e)?fS(e):ap(e,"range")}function ap(e,t){const n=e.component.scales;for(const i of e.children)t==="range"?op(i):sp(i,t);for(const i of v(n)){let r;for(const s of e.children){const o=s.component.scales[i];if(o){const a=o.getWithExplicit(t);r=Ht(r,a,t,"scale",nd((c,l)=>{switch(t){case"range":return c.step&&l.step?c.step-l.step:0}return 0}))}}n[i].setWithExplicit(t,r)}}function vS(e,t){const n=t.bin;if(te(n)){const i=Da(e,t.field,n);return new Oe(()=>e.getSignalName(i))}else if(xe(n)&&An(n)&&n.step!==void 0)return{step:n.step}}function SS(e,t){if(G([Ae,bt,xt],e)&&t!=="nominal")return"hcl"}function ES(e,t,n,i,r,s){if(!(ht(s)?.bin||A(n)||r!=null||i!=null||G([Te.TIME,Te.UTC],e)))return ye(t)?!0:void 0}function $S(e,t,n,i,r,s){if(ye(e)){if(Xe(t)){if(n.continuousPadding!==void 0)return n.continuousPadding;const{type:o,orient:a}=r;if(o==="bar"&&!(E(i)&&(i.bin||i.timeUnit))&&(a==="vertical"&&e==="x"||a==="horizontal"&&e==="y"))return s.continuousBandSize}if(t===Te.POINT)return n.pointPadding}}function wS(e,t,n,i,r,s=!1){if(e===void 0){if(ye(t)){const{bandPaddingInner:o,barBandPaddingInner:a,rectBandPaddingInner:c,bandWithNestedOffsetPaddingInner:l}=r;return s?l:ue(o,n==="bar"?a:c)}else if(hi(t)&&i===Te.BAND)return r.offsetBandPaddingInner}}function CS(e,t,n,i,r,s=!1){if(e===void 0){if(ye(t)){const{bandPaddingOuter:o,bandWithNestedOffsetPaddingOuter:a}=r;if(s)return a;if(n===Te.BAND)return ue(o,I(i)?{signal:`${i.signal}/2`}:i/2)}else if(hi(t)){if(n===Te.POINT)return .5;if(n===Te.BAND)return r.offsetBandPaddingOuter}}}function FS(e,t,n,i){if(n==="x"&&i.xReverse!==void 0)return ze(e)&&t==="descending"?I(i.xReverse)?{signal:`!${i.xReverse.signal}`}:!i.xReverse:i.xReverse;if(ze(e)&&t==="descending")return!0}function NS(e,t,n,i,r,s,o){if(!!n&&n!=="unaggregated"&&ze(r)){if(A(n)){const c=n[0],l=n[n.length-1];if(c<=0&&l>=0)return!0}return!1}if(e==="size"&&t.type==="quantitative"&&!ni(r))return!0;if(!(E(t)&&t.bin)&&G([...St,...Ng],e)){const{orient:c,type:l}=i;return G(["bar","area","line","trail"],l)&&(c==="horizontal"&&e==="y"||c==="vertical"&&e==="x")?!1:G(["bar","area"],l)&&!o?!0:s?.zero}return!1}function _S(e,t,n,i,r=!1){const s=kS(t,n,i,r),{type:o}=e;return zt(t)?o!==void 0?Am(t,o)?E(n)&&!Om(o,n.type)?(S(Rh(o,s)),s):o:(S(Ah(t,o,s)),s):s:null}function kS(e,t,n,i){switch(t.type){case"nominal":case"ordinal":{if(Hn(e)||Fs(e)==="discrete")return e==="shape"&&t.type==="ordinal"&&S(Ns(e,"ordinal")),"ordinal";if(ye(e)||hi(e)){if(G(["rect","bar","image","rule"],n.type)||i)return"band"}else if(n.type==="arc"&&e in jo)return"band";const r=n[Ie(e)];return wn(r)||ri(t)&&t.axis?.tickBand?"band":"point"}case"temporal":return Hn(e)?"time":Fs(e)==="discrete"?(S(Ns(e,"temporal")),"ordinal"):E(t)&&t.timeUnit&&he(t.timeUnit).utc?"utc":"time";case"quantitative":return Hn(e)?E(t)&&te(t.bin)?"bin-ordinal":"linear":Fs(e)==="discrete"?(S(Ns(e,"quantitative")),"ordinal"):"linear";case"geojson":return}throw new Error(ku(t.type))}function TS(e,{ignoreRange:t}={}){cp(e),Qd(e);for(const n of Tm)sp(e,n);t||op(e)}function cp(e){ae(e)?e.component.scales=OS(e):e.component.scales=RS(e)}function OS(e){const{encoding:t,mark:n,markDef:i}=e,r={};for(const s of Mr){const o=fe(t[s]);if(o&&n===tf&&s===Re&&o.type===mi)continue;let a=o&&o.scale;if(hi(s)){const c=pu(s);if(!eo(t,c)){a&&S(bh(s));continue}}if(o&&a!==null&&a!==!1){a??(a={});const c=eo(t,s),l=_S(a,s,o,i,c);r[s]=new ep(e.scaleName(`${s}`,!0),{value:l,explicit:a.type===l})}}return r}const AS=nd((e,t)=>xc(e)-xc(t));function RS(e){var t;const n=e.component.scales={},i={},r=e.component.resolve;for(const s of e.children){cp(s);for(const o of v(s.component.scales))if((t=r.scale)[o]??(t[o]=jd(o,e)),r.scale[o]==="shared"){const a=i[o],c=s.component.scales[o].getWithExplicit("type");a?vm(a.value,c.value)?i[o]=Ht(a,c,"type","scale",AS):(r.scale[o]="independent",delete i[o]):i[o]=c}}for(const s of v(i)){const o=e.scaleName(s,!0),a=i[s];n[s]=new ep(o,a);for(const c of e.children){const l=c.component.scales[s];l&&(c.renameScale(l.get("name"),o),l.merged=!0)}}return n}class Ps{constructor(){this.nameMap={}}rename(t,n){this.nameMap[t]=n}has(t){return this.nameMap[t]!==void 0}get(t){for(;this.nameMap[t]&&t!==this.nameMap[t];)t=this.nameMap[t];return t}}function ae(e){return e?.type==="unit"}function Je(e){return e?.type==="facet"}function Wa(e){return e?.type==="concat"}function Si(e){return e?.type==="layer"}class Ga{constructor(t,n,i,r,s,o,a){this.type=n,this.parent=i,this.config=s,this.correctDataNames=c=>(c.from?.data&&(c.from.data=this.lookupDataSource(c.from.data)),c.from?.facet?.data&&(c.from.facet.data=this.lookupDataSource(c.from.facet.data)),c),this.parent=i,this.config=s,this.view=_e(a),this.name=t.name??r,this.title=Ut(t.title)?{text:t.title}:t.title?_e(t.title):void 0,this.scaleNameMap=i?i.scaleNameMap:new Ps,this.projectionNameMap=i?i.projectionNameMap:new Ps,this.signalNameMap=i?i.signalNameMap:new Ps,this.data=t.data,this.description=t.description,this.transforms=Mb(t.transform??[]),this.layout=n==="layer"||n==="unit"?{}:Xy(t,n,s),this.component={data:{sources:i?i.component.data.sources:[],outputNodes:i?i.component.data.outputNodes:{},outputNodeRefCounts:i?i.component.data.outputNodeRefCounts:{},isFaceted:Yr(t)||i?.component.data.isFaceted&&t.data===void 0},layoutSize:new jt,layoutHeaders:{row:{},column:{},facet:{}},mark:null,resolve:{scale:{},axis:{},legend:{},...o?j(o):{}},selection:null,scales:null,projection:null,axes:{},legends:{}}}get width(){return this.getSizeSignalRef("width")}get height(){return this.getSizeSignalRef("height")}parse(){this.parseScale(),this.parseLayoutSize(),this.renameTopLevelLayoutSizeSignal(),this.parseSelections(),this.parseProjection(),this.parseData(),this.parseAxesAndHeaders(),this.parseLegends(),this.parseMarkGroup()}parseScale(){TS(this)}parseProjection(){Yd(this)}renameTopLevelLayoutSizeSignal(){this.getName("width")!=="width"&&this.renameSignal(this.getName("width"),"width"),this.getName("height")!=="height"&&this.renameSignal(this.getName("height"),"height")}parseLegends(){Gd(this)}assembleEncodeFromView(t){const{style:n,...i}=t,r={};for(const s of v(i)){const o=i[s];o!==void 0&&(r[s]=re(o))}return r}assembleGroupEncodeEntry(t){let n={};return this.view&&(n=this.assembleEncodeFromView(this.view)),!t&&(this.description&&(n.description=re(this.description)),this.type==="unit"||this.type==="layer")?{width:this.getSizeSignalRef("width"),height:this.getSizeSignalRef("height"),...n??{}}:J(n)?void 0:n}assembleLayout(){if(!this.layout)return;const{spacing:t,...n}=this.layout,{component:i,config:r}=this,s=ev(i.layoutHeaders,r);return{padding:t,...this.assembleDefaultLayout(),...n,...s?{titleBand:s}:{}}}assembleDefaultLayout(){return{}}assembleHeaderMarks(){const{layoutHeaders:t}=this.component;let n=[];for(const i of je)t[i].title&&n.push(Xx(this,i));for(const i of Ia)n=n.concat(Yx(this,i));return n}assembleAxes(){return Lx(this.component.axes,this.config)}assembleLegends(){return Hd(this)}assembleProjections(){return $v(this)}assembleTitle(){const{encoding:t,...n}=this.title??{},i={...vu(this.config.title).nonMarkTitleProperties,...n,...t?{encode:{update:t}}:{}};if(i.text)return G(["unit","layer"],this.type)?G(["middle",void 0],i.anchor)&&(i.frame??(i.frame="group")):i.anchor??(i.anchor="start"),J(i)?void 0:i}assembleGroup(t=[]){const n={};t=t.concat(this.assembleSignals()),t.length>0&&(n.signals=t);const i=this.assembleLayout();i&&(n.layout=i),n.marks=[].concat(this.assembleHeaderMarks(),this.assembleMarks());const r=!this.parent||Je(this.parent)?Zd(this):[];r.length>0&&(n.scales=r);const s=this.assembleAxes();s.length>0&&(n.axes=s);const o=this.assembleLegends();return o.length>0&&(n.legends=o),n}getName(t){return se((this.name?`${this.name}_`:"")+t)}getDataName(t){return this.getName(Z[t].toLowerCase())}requestDataName(t){const n=this.getDataName(t),i=this.component.data.outputNodeRefCounts;return i[n]=(i[n]||0)+1,n}getSizeSignalRef(t){if(Je(this.parent)){const n=zd(t),i=jr(n),r=this.component.scales[i];if(r&&!r.merged){const s=r.get("type"),o=r.get("range");if(be(s)&&nn(o)){const a=r.get("name"),c=ls(this,i),l=Ba(c);if(l){const u=w({aggregate:"distinct",field:l},{expr:"datum"});return{signal:Pd(a,r,u)}}else return S(Bo(i)),null}}}return{signal:this.signalNameMap.get(this.getName(t))}}lookupDataSource(t){const n=this.component.data.outputNodes[t];return n?n.getSource():t}getSignalName(t){return this.signalNameMap.get(t)}renameSignal(t,n){this.signalNameMap.rename(t,n)}renameScale(t,n){this.scaleNameMap.rename(t,n)}renameProjection(t,n){this.projectionNameMap.rename(t,n)}scaleName(t,n){if(n)return this.getName(t);if(uu(t)&&zt(t)&&this.component.scales[t]||this.scaleNameMap.has(this.getName(t)))return this.scaleNameMap.get(this.getName(t))}projectionName(t){if(t)return this.getName("projection");if(this.component.projection&&!this.component.projection.merged||this.projectionNameMap.has(this.getName("projection")))return this.projectionNameMap.get(this.getName("projection"))}getScaleComponent(t){if(!this.component.scales)throw new Error("getScaleComponent cannot be called before parseScale(). Make sure you have called parseScale or use parseUnitModelWithScale().");const n=this.component.scales[t];return n&&!n.merged?n:this.parent?this.parent.getScaleComponent(t):void 0}getSelectionComponent(t,n){let i=this.component.selection[t];if(!i&&this.parent&&(i=this.parent.getSelectionComponent(t,n)),!i)throw new Error(Kg(n));return i}hasAxisOrientSignalRef(){return this.component.axes.x?.some(t=>t.hasOrientSignalRef())||this.component.axes.y?.some(t=>t.hasOrientSignalRef())}}class lp extends Ga{vgField(t,n={}){const i=this.fieldDef(t);if(i)return w(i,n)}reduceFieldDef(t,n){return $y(this.getMapping(),(i,r,s)=>{const o=ht(r);return o?t(i,o,s):i},n)}forEachFieldDef(t,n){ma(this.getMapping(),(i,r)=>{const s=ht(i);s&&t(s,r)},n)}}class us extends K{clone(){return new us(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"value",i[1]??"density"],n.groupby&&n.minsteps==null&&n.maxsteps==null&&n.steps==null&&(this.transform.steps=200)}dependentFields(){return new Set([this.transform.density,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`DensityTransform ${W(this.transform)}`}assemble(){const{density:t,...n}=this.transform;return{type:"kde",field:t,...n}}}class fs extends K{clone(){return new fs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n)}dependentFields(){return new Set([this.transform.extent])}producedFields(){return new Set([])}hash(){return`ExtentTransform ${W(this.transform)}`}assemble(){const{extent:t,param:n}=this.transform;return{type:"extent",field:t,signal:n}}}class ji extends K{clone(){return new ji(null,{...this.filter})}constructor(t,n){super(t),this.filter=n}static make(t,n){const{config:i,mark:r,markDef:s}=n;if(X("invalid",s,i)!=="filter")return null;const a=n.reduceFieldDef((c,l,u)=>{const f=zt(u)&&n.getScaleComponent(u);if(f){const d=f.get("type");ze(d)&&l.aggregate!=="count"&&!rn(r)&&(c[l.field]=l)}return c},{});return v(a).length?new ji(t,a):null}dependentFields(){return new Set(v(this.filter))}producedFields(){return new Set}hash(){return`FilterInvalid ${W(this.filter)}`}assemble(){const t=v(this.filter).reduce((n,i)=>{const r=this.filter[i],s=w(r,{expr:"datum"});return r!==null&&(r.type==="temporal"?n.push(`(isDate(${s}) || (isValid(${s}) && isFinite(+${s})))`):r.type==="quantitative"&&(n.push(`isValid(${s})`),n.push(`isFinite(+${s})`))),n},[]);return t.length>0?{type:"filter",expr:t.join(" && ")}:null}}class ds extends K{clone(){return new ds(this.parent,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const{flatten:i,as:r=[]}=this.transform;this.transform.as=i.map((s,o)=>r[o]??s)}dependentFields(){return new Set(this.transform.flatten)}producedFields(){return new Set(this.transform.as)}hash(){return`FlattenTransform ${W(this.transform)}`}assemble(){const{flatten:t,as:n}=this.transform;return{type:"flatten",fields:t,as:n}}}class ps extends K{clone(){return new ps(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"key",i[1]??"value"]}dependentFields(){return new Set(this.transform.fold)}producedFields(){return new Set(this.transform.as)}hash(){return`FoldTransform ${W(this.transform)}`}assemble(){const{fold:t,as:n}=this.transform;return{type:"fold",fields:t,as:n}}}class Yn extends K{clone(){return new Yn(null,j(this.fields),this.geojson,this.signal)}static parseAll(t,n){if(n.component.projection&&!n.component.projection.isFit)return t;let i=0;for(const r of[[it,nt],[Ue,rt]]){const s=r.map(o=>{const a=fe(n.encoding[o]);return E(a)?a.field:Et(a)?{expr:`${a.datum}`}:Qe(a)?{expr:`${a.value}`}:void 0});(s[0]||s[1])&&(t=new Yn(t,s,null,n.getName(`geojson_${i++}`)))}if(n.channelHasField(Re)){const r=n.typedFieldDef(Re);r.type===mi&&(t=new Yn(t,null,r.field,n.getName(`geojson_${i++}`)))}return t}constructor(t,n,i,r){super(t),this.fields=n,this.geojson=i,this.signal=r}dependentFields(){const t=(this.fields??[]).filter(z);return new Set([...this.geojson?[this.geojson]:[],...t])}producedFields(){return new Set}hash(){return`GeoJSON ${this.geojson} ${this.signal} ${W(this.fields)}`}assemble(){return[...this.geojson?[{type:"filter",expr:`isValid(datum["${this.geojson}"])`}]:[],{type:"geojson",...this.fields?{fields:this.fields}:{},...this.geojson?{geojson:this.geojson}:{},signal:this.signal}]}}class Mi extends K{clone(){return new Mi(null,this.projection,j(this.fields),j(this.as))}constructor(t,n,i,r){super(t),this.projection=n,this.fields=i,this.as=r}static parseAll(t,n){if(!n.projectionName())return t;for(const i of[[it,nt],[Ue,rt]]){const r=i.map(o=>{const a=fe(n.encoding[o]);return E(a)?a.field:Et(a)?{expr:`${a.datum}`}:Qe(a)?{expr:`${a.value}`}:void 0}),s=i[0]===Ue?"2":"";(r[0]||r[1])&&(t=new Mi(t,n.projectionName(),r,[n.getName(`x${s}`),n.getName(`y${s}`)]))}return t}dependentFields(){return new Set(this.fields.filter(z))}producedFields(){return new Set(this.as)}hash(){return`Geopoint ${this.projection} ${W(this.fields)} ${W(this.as)}`}assemble(){return{type:"geopoint",projection:this.projection,fields:this.fields,as:this.as}}}class bn extends K{clone(){return new bn(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}dependentFields(){return new Set([this.transform.impute,this.transform.key,...this.transform.groupby??[]])}producedFields(){return new Set([this.transform.impute])}processSequence(t){const{start:n=0,stop:i,step:r}=t;return{signal:`sequence(${[n,i,...r?[r]:[]].join(",")})`}}static makeFromTransform(t,n){return new bn(t,n)}static makeFromEncoding(t,n){const i=n.encoding,r=i.x,s=i.y;if(E(r)&&E(s)){const o=r.impute?r:s.impute?s:void 0;if(o===void 0)return;const a=r.impute?s:s.impute?r:void 0,{method:c,value:l,frame:u,keyvals:f}=o.impute,d=_f(n.mark,i);return new bn(t,{impute:o.field,key:a.field,...c?{method:c}:{},...l!==void 0?{value:l}:{},...u?{frame:u}:{},...f!==void 0?{keyvals:f}:{},...d.length?{groupby:d}:{}})}return null}hash(){return`Impute ${W(this.transform)}`}assemble(){const{impute:t,key:n,keyvals:i,method:r,groupby:s,value:o,frame:a=[null,null]}=this.transform,c={type:"impute",field:t,key:n,...i?{keyvals:Sb(i)?this.processSequence(i):i}:{},method:"value",...s?{groupby:s}:{},value:!r||r==="value"?o:null};if(r&&r!=="value"){const l={type:"window",as:[`imputed_${t}_value`],ops:[r],fields:[t],frame:a,ignorePeers:!1,...s?{groupby:s}:{}},u={type:"formula",expr:`datum.${t} === null ? datum.imputed_${t}_value : datum.${t}`,as:t};return[c,l,u]}else return[c]}}class gs extends K{clone(){return new gs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.loess]}dependentFields(){return new Set([this.transform.loess,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`LoessTransform ${W(this.transform)}`}assemble(){const{loess:t,on:n,...i}=this.transform;return{type:"loess",x:n,y:t,...i}}}class Ui extends K{clone(){return new Ui(null,j(this.transform),this.secondary)}constructor(t,n,i){super(t),this.transform=n,this.secondary=i}static make(t,n,i,r){const s=n.component.data.sources,{from:o}=i;let a=null;if(Eb(o)){let c=dp(o.data,s);c||(c=new _n(o.data),s.push(c));const l=n.getName(`lookup_${r}`);a=new Ce(c,l,Z.Lookup,n.component.data.outputNodeRefCounts),n.component.data.outputNodes[l]=a}else if($b(o)){const c=o.param;i={as:c,...i};let l;try{l=n.getSelectionComponent(se(c),c)}catch{throw new Error(Zg(c))}if(a=l.materialized,!a)throw new Error(eh(c))}return new Ui(t,i,a.getSource())}dependentFields(){return new Set([this.transform.lookup])}producedFields(){return new Set(this.transform.as?ce(this.transform.as):this.transform.from.fields)}hash(){return`Lookup ${W({transform:this.transform,secondary:this.secondary})}`}assemble(){let t;if(this.transform.from.fields)t={values:this.transform.from.fields,...this.transform.as?{as:ce(this.transform.as)}:{}};else{let n=this.transform.as;z(n)||(S(ch),n="_lookup"),t={as:[n]}}return{type:"lookup",from:this.secondary,key:this.transform.from.key,fields:[this.transform.lookup],...t,...this.transform.default?{default:this.transform.default}:{}}}}class hs extends K{clone(){return new hs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"prob",i[1]??"value"]}dependentFields(){return new Set([this.transform.quantile,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`QuantileTransform ${W(this.transform)}`}assemble(){const{quantile:t,...n}=this.transform;return{type:"quantile",field:t,...n}}}class ms extends K{clone(){return new ms(null,j(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=j(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.regression]}dependentFields(){return new Set([this.transform.regression,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`RegressionTransform ${W(this.transform)}`}assemble(){const{regression:t,on:n,...i}=this.transform;return{type:"regression",x:n,y:t,...i}}}class ys extends K{clone(){return new ys(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut((this.transform.groupby??[]).concat(t),n=>n)}producedFields(){}dependentFields(){return new Set([this.transform.pivot,this.transform.value,...this.transform.groupby??[]])}hash(){return`PivotTransform ${W(this.transform)}`}assemble(){const{pivot:t,value:n,groupby:i,limit:r,op:s}=this.transform;return{type:"pivot",field:t,value:n,...r!==void 0?{limit:r}:{},...s!==void 0?{op:s}:{},...i!==void 0?{groupby:i}:{}}}}class bs extends K{clone(){return new bs(null,j(this.transform))}constructor(t,n){super(t),this.transform=n}dependentFields(){return new Set}producedFields(){return new Set}hash(){return`SampleTransform ${W(this.transform)}`}assemble(){return{type:"sample",size:this.transform.sample}}}function up(e){let t=0;function n(i,r){if(i instanceof _n&&!i.isGenerator&&!oi(i.data)&&(e.push(r),r={name:null,source:r.name,transform:[]}),i instanceof we&&(i.parent instanceof _n&&!r.source?(r.format={...r.format??{},parse:i.assembleFormatParse()},r.transform.push(...i.assembleTransforms(!0))):r.transform.push(...i.assembleTransforms())),i instanceof xi){r.name||(r.name=`data_${t++}`),!r.source||r.transform.length>0?(e.push(r),i.data=r.name):i.data=r.source,e.push(...i.assemble());return}switch((i instanceof er||i instanceof tr||i instanceof ji||i instanceof bi||i instanceof ci||i instanceof Mi||i instanceof Ke||i instanceof Ui||i instanceof vi||i instanceof zn||i instanceof ps||i instanceof ds||i instanceof us||i instanceof gs||i instanceof hs||i instanceof ms||i instanceof Yt||i instanceof bs||i instanceof ys||i instanceof fs)&&r.transform.push(i.assemble()),(i instanceof pt||i instanceof dt||i instanceof bn||i instanceof Tt||i instanceof Yn)&&r.transform.push(...i.assemble()),i instanceof Ce&&(r.source&&r.transform.length===0?i.setSource(r.source):i.parent instanceof Ce?i.setSource(r.name):(r.name||(r.name=`data_${t++}`),i.setSource(r.name),i.numChildren()===1&&(e.push(r),r={name:null,source:r.name,transform:[]}))),i.numChildren()){case 0:i instanceof Ce&&(!r.source||r.transform.length>0)&&e.push(r);break;case 1:n(i.children[0],r);break;default:{r.name||(r.name=`data_${t++}`);let s=r.name;!r.source||r.transform.length>0?e.push(r):s=r.source;for(const o of i.children)n(o,{name:null,source:s,transform:[]});break}}}return n}function IS(e){const t=[],n=up(t);for(const i of e.children)n(i,{source:e.name,name:null,transform:[]});return t}function LS(e,t){const n=[],i=up(n);let r=0;for(const o of e.sources){o.hasName()||(o.dataName=`source_${r++}`);const a=o.assemble();i(o,a)}for(const o of n)o.transform.length===0&&delete o.transform;let s=0;for(const[o,a]of n.entries())(a.transform??[]).length===0&&!a.source&&n.splice(s++,0,n.splice(o,1)[0]);for(const o of n)for(const a of o.transform??[])a.type==="lookup"&&(a.from=e.outputNodes[a.from].getSource());for(const o of n)o.name in t&&(o.values=t[o.name]);return n}function PS(e){return e==="top"||e==="left"||I(e)?"header":"footer"}function zS(e){for(const t of je)DS(e,t);dl(e,"x"),dl(e,"y")}function DS(e,t){const{facet:n,config:i,child:r,component:s}=e;if(e.channelHasField(t)){const o=n[t],a=ui("title",null,i,t);let c=Vn(o,i,{allowDisabling:!0,includeDefault:a===void 0||!!a});r.component.layoutHeaders[t].title&&(c=A(c)?c.join(", "):c,c+=` / ${r.component.layoutHeaders[t].title}`,r.component.layoutHeaders[t].title=null);const l=ui("labelOrient",o.header,i,t),u=o.header!==null?ue(o.header?.labels,i.header.labels,!0):!1,f=G(["bottom","right"],l)?"footer":"header";s.layoutHeaders[t]={title:o.header!==null?c:null,facetFieldDef:o,[f]:t==="facet"?[]:[fp(e,t,u)]}}}function fp(e,t,n){const i=t==="row"?"height":"width";return{labels:n,sizeSignal:e.child.component.layoutSize.get(i)?e.child.getSizeSignalRef(i):void 0,axes:[]}}function dl(e,t){const{child:n}=e;if(n.component.axes[t]){const{layoutHeaders:i,resolve:r}=e.component;if(r.axis[t]=za(r,t),r.axis[t]==="shared"){const s=t==="x"?"column":"row",o=i[s];for(const a of n.component.axes[t]){const c=PS(a.get("orient"));o[c]??(o[c]=[fp(e,s,!1)]);const l=ki(a,"main",e.config,{header:!0});l&&o[c][0].axes.push(l),a.mainExtracted=!0}}}}function jS(e){qa(e),kr(e,"width"),kr(e,"height")}function MS(e){qa(e);const t=e.layout.columns===1?"width":"childWidth",n=e.layout.columns===void 0?"height":"childHeight";kr(e,t),kr(e,n)}function qa(e){for(const t of e.children)t.parseLayoutSize()}function kr(e,t){const n=zd(t),i=jr(n),r=e.component.resolve,s=e.component.layoutSize;let o;for(const a of e.children){const c=a.component.layoutSize.getWithExplicit(n),l=r.scale[i]??jd(i,e);if(l==="independent"&&c.value==="step"){o=void 0;break}if(o){if(l==="independent"&&o.value!==c.value){o=void 0;break}o=Ht(o,c,n,"")}else o=c}if(o){for(const a of e.children)e.renameSignal(a.getName(n),e.getName(t)),a.component.layoutSize.set(n,"merged",!1);s.setWithExplicit(t,o)}else s.setWithExplicit(t,{explicit:!1,value:void 0})}function US(e){const{size:t,component:n}=e;for(const i of St){const r=Ie(i);if(t[r]){const s=t[r];n.layoutSize.set(r,mt(s)?"step":s,!0)}else{const s=BS(e,r);n.layoutSize.set(r,s,!1)}}}function BS(e,t){const n=t==="width"?"x":"y",i=e.config,r=e.getScaleComponent(n);if(r){const s=r.get("type"),o=r.get("range");if(be(s)){const a=Cr(i.view,t);return nn(o)||mt(a)?"step":a}else return no(i.view,t)}else{if(e.hasProjection||e.mark==="arc")return no(i.view,t);{const s=Cr(i.view,t);return mt(s)?s.step:s}}}function yo(e,t,n){return w(t,{suffix:`by_${w(e)}`,...n??{}})}class Ii extends lp{constructor(t,n,i,r){super(t,"facet",n,i,r,t.resolve),this.child=Ka(t.spec,this,this.getName("child"),void 0,r),this.children=[this.child],this.facet=this.initFacet(t.facet)}initFacet(t){if(!Yi(t))return{facet:this.initFacetFieldDef(t,"facet")};const n=v(t),i={};for(const r of n){if(![Nt,_t].includes(r)){S(Br(r,"facet"));break}const s=t[r];if(s.field===void 0){S(Ks(s,r));break}i[r]=this.initFacetFieldDef(s,r)}return i}initFacetFieldDef(t,n){const i=ga(t,n);return i.header?i.header=_e(i.header):i.header===null&&(i.header=null),i}channelHasField(t){return!!this.facet[t]}fieldDef(t){return this.facet[t]}parseData(){this.component.data=xs(this),this.child.parseData()}parseLayoutSize(){qa(this)}parseSelections(){this.child.parseSelections(),this.component.selection=this.child.component.selection}parseMarkGroup(){this.child.parseMarkGroup()}parseAxesAndHeaders(){this.child.parseAxesAndHeaders(),zS(this)}assembleSelectionTopLevelSignals(t){return this.child.assembleSelectionTopLevelSignals(t)}assembleSignals(){return this.child.assembleSignals(),[]}assembleSelectionData(t){return this.child.assembleSelectionData(t)}getHeaderLayoutMixins(){const t={};for(const n of je)for(const i of La){const r=this.component.layoutHeaders[n],s=r[i],{facetFieldDef:o}=r;if(o){const a=ui("titleOrient",o.header,this.config,n);if(["right","bottom"].includes(a)){const c=as(n,a);t.titleAnchor??(t.titleAnchor={}),t.titleAnchor[c]="end"}}if(s?.[0]){const a=n==="row"?"height":"width",c=i==="header"?"headerBand":"footerBand";n!=="facet"&&!this.child.component.layoutSize.get(a)&&(t[c]??(t[c]={}),t[c][n]=.5),r.title&&(t.offset??(t.offset={}),t.offset[n==="row"?"rowTitle":"columnTitle"]=10)}}return t}assembleDefaultLayout(){const{column:t,row:n}=this.facet,i=t?this.columnDistinctSignal():n?1:void 0;let r="all";return(!n&&this.component.resolve.scale.x==="independent"||!t&&this.component.resolve.scale.y==="independent")&&(r="none"),{...this.getHeaderLayoutMixins(),...i?{columns:i}:{},bounds:"full",align:r}}assembleLayoutSignals(){return this.child.assembleLayoutSignals()}columnDistinctSignal(){if(!(this.parent&&this.parent instanceof Ii))return{signal:`length(data('${this.getName("column_domain")}'))`}}assembleGroupStyle(){}assembleGroup(t){return this.parent&&this.parent instanceof Ii?{...this.channelHasField("column")?{encode:{update:{columns:{field:w(this.facet.column,{prefix:"distinct"})}}}}:{},...super.assembleGroup(t)}:super.assembleGroup(t)}getCardinalityAggregateForChild(){const t=[],n=[],i=[];if(this.child instanceof Ii){if(this.child.channelHasField("column")){const r=w(this.child.facet.column);t.push(r),n.push("distinct"),i.push(`distinct_${r}`)}}else for(const r of St){const s=this.child.component.scales[r];if(s&&!s.merged){const o=s.get("type"),a=s.get("range");if(be(o)&&nn(a)){const c=ls(this.child,r),l=Ba(c);l?(t.push(l),n.push("distinct"),i.push(`distinct_${l}`)):S(Bo(r))}}}return{fields:t,ops:n,as:i}}assembleFacet(){const{name:t,data:n}=this.component.data.facetRoot,{row:i,column:r}=this.facet,{fields:s,ops:o,as:a}=this.getCardinalityAggregateForChild(),c=[];for(const u of je){const f=this.facet[u];if(f){c.push(w(f));const{bin:d,sort:p}=f;if(te(d)&&c.push(w(f,{binSuffix:"end"})),ft(p)){const{field:g,op:h=Xr}=p,m=yo(f,p);i&&r?(s.push(m),o.push("max"),a.push(m)):(s.push(g),o.push(h),a.push(m))}else if(A(p)){const g=li(f,u);s.push(g),o.push("max"),a.push(g)}}}const l=!!i&&!!r;return{name:t,data:n,groupby:c,...l||s.length>0?{aggregate:{...l?{cross:l}:{},...s.length?{fields:s,ops:o,as:a}:{}}}:{}}}facetSortFields(t){const{facet:n}=this,i=n[t];return i?ft(i.sort)?[yo(i,i.sort,{expr:"datum"})]:A(i.sort)?[li(i,t,{expr:"datum"})]:[w(i,{expr:"datum"})]:[]}facetSortOrder(t){const{facet:n}=this,i=n[t];if(i){const{sort:r}=i;return[(ft(r)?r.order:!A(r)&&r)||"ascending"]}return[]}assembleLabelTitle(){const{facet:t,config:n}=this;if(t.facet)return uo(t.facet,"facet",n);const i={row:["top","bottom"],column:["left","right"]};for(const r of Ia)if(t[r]){const s=ui("labelOrient",t[r]?.header,n,r);if(i[r].includes(s))return uo(t[r],r,n)}}assembleMarks(){const{child:t}=this,n=this.component.data.facetRoot,i=IS(n),r=t.assembleGroupEncodeEntry(!1),s=this.assembleLabelTitle()||t.assembleTitle(),o=t.assembleGroupStyle();return[{name:this.getName("cell"),type:"group",...s?{title:s}:{},...o?{style:o}:{},from:{facet:this.assembleFacet()},sort:{field:je.map(c=>this.facetSortFields(c)).flat(),order:je.map(c=>this.facetSortOrder(c)).flat()},...i.length>0?{data:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup(Zb(this,[]))}]}getMapping(){return this.facet}}function WS(e,t){const{row:n,column:i}=t;if(n&&i){let r=null;for(const s of[n,i])if(ft(s.sort)){const{field:o,op:a=Xr}=s.sort;e=r=new zn(e,{joinaggregate:[{op:a,field:o,as:yo(s,s.sort,{forAs:!0})}],groupby:[w(s)]})}return r}return null}function dp(e,t){for(const n of t){const i=n.data;if(e.name&&n.hasName()&&e.name!==n.dataName)continue;const r=e.format?.mesh,s=i.format?.feature;if(r&&s)continue;const o=e.format?.feature;if((o||s)&&o!==s)continue;const a=i.format?.mesh;if(!((r||a)&&r!==a)){if(Di(e)&&Di(i)){if(lt(e.values,i.values))return n}else if(oi(e)&&oi(i)){if(e.url===i.url)return n}else if(id(e)&&e.name===n.dataName)return n}}return null}function GS(e,t){if(e.data||!e.parent){if(e.data===null){const i=new _n({values:[]});return t.push(i),i}const n=dp(e.data,t);if(n)return Wt(e.data)||(n.data.format=Ql({},e.data.format,n.data.format)),!n.hasName()&&e.data.name&&(n.dataName=e.data.name),n;{const i=new _n(e.data);return t.push(i),i}}else return e.parent.component.data.facetRoot?e.parent.component.data.facetRoot:e.parent.component.data.main}function qS(e,t,n){let i=0;for(const r of t.transforms){let s,o;if(Rb(r))o=e=new ci(e,r),s="derived";else if(Na(r)){const a=Pv(r);o=e=we.makeWithAncestors(e,{},a,n)??e,e=new bi(e,t,r.filter)}else if(Qf(r))o=e=pt.makeFromTransform(e,r,t),s="number";else if(Lb(r))s="date",n.getWithExplicit(r.field).value===void 0&&(e=new we(e,{[r.field]:s}),n.set(r.field,s,!1)),o=e=dt.makeFromTransform(e,r);else if(Pb(r))o=e=Ke.makeFromTransform(e,r),s="number",Ra(t)&&(e=new Yt(e));else if(Jf(r))o=e=Ui.make(e,t,r,i++),s="derived";else if(Tb(r))o=e=new vi(e,r),s="number";else if(Ob(r))o=e=new zn(e,r),s="number";else if(zb(r))o=e=Tt.makeFromTransform(e,r),s="derived";else if(Db(r))o=e=new ps(e,r),s="derived";else if(jb(r))o=e=new fs(e,r),s="derived";else if(Ab(r))o=e=new ds(e,r),s="derived";else if(wb(r))o=e=new ys(e,r),s="derived";else if(kb(r))e=new bs(e,r);else if(Ib(r))o=e=bn.makeFromTransform(e,r),s="derived";else if(Cb(r))o=e=new us(e,r),s="derived";else if(Fb(r))o=e=new hs(e,r),s="derived";else if(Nb(r))o=e=new ms(e,r),s="derived";else if(_b(r))o=e=new gs(e,r),s="derived";else{S(ah(r));continue}if(o&&s!==void 0)for(const a of o.producedFields()??[])n.set(a,s,!1)}return e}function xs(e){let t=GS(e,e.component.data.sources);const{outputNodes:n,outputNodeRefCounts:i}=e.component.data,r=e.data,o=!(r&&(Wt(r)||oi(r)||Di(r)))&&e.parent?e.parent.component.data.ancestorParse.clone():new Jb;Wt(r)?(rd(r)?t=new tr(t,r.sequence):_a(r)&&(t=new er(t,r.graticule)),o.parseNothing=!0):r?.format?.parse===null&&(o.parseNothing=!0),t=we.makeExplicit(t,e,o)??t,t=new Yt(t);const a=e.parent&&Si(e.parent);(ae(e)||Je(e))&&a&&(t=pt.makeFromEncoding(t,e)??t),e.transforms.length>0&&(t=qS(t,e,o));const c=Dv(e),l=zv(e);t=we.makeWithAncestors(t,{},{...c,...l},o)??t,ae(e)&&(t=Yn.parseAll(t,e),t=Mi.parseAll(t,e)),(ae(e)||Je(e))&&(a||(t=pt.makeFromEncoding(t,e)??t),t=dt.makeFromEncoding(t,e)??t,t=ci.parseAllForSortIndex(t,e));const u=e.getDataName(Z.Raw),f=new Ce(t,u,Z.Raw,i);if(n[u]=f,t=f,ae(e)){const h=Ke.makeFromEncoding(t,e);h&&(t=h,Ra(e)&&(t=new Yt(t))),t=bn.makeFromEncoding(t,e)??t,t=Tt.makeFromEncoding(t,e)??t}ae(e)&&(t=ji.make(t,e)??t);const d=e.getDataName(Z.Main),p=new Ce(t,d,Z.Main,i);n[d]=p,t=p,ae(e)&&Rx(e,p);let g=null;if(Je(e)){const h=e.getName("facet");t=WS(t,e.facet)??t,g=new xi(t,e,h,p.getSource()),n[h]=g}return{...e.component.data,outputNodes:n,outputNodeRefCounts:i,raw:f,main:p,facetRoot:g,ancestorParse:o}}class HS extends Ga{constructor(t,n,i,r){super(t,"concat",n,i,r,t.resolve),(t.resolve?.axis?.x==="shared"||t.resolve?.axis?.y==="shared")&&S(rh),this.children=this.getChildren(t).map((s,o)=>Ka(s,this,this.getName(`concat_${o}`),void 0,r))}parseData(){this.component.data=xs(this);for(const t of this.children)t.parseData()}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of v(t.component.selection))this.component.selection[n]=t.component.selection[n]}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup()}parseAxesAndHeaders(){for(const t of this.children)t.parseAxesAndHeaders()}getChildren(t){return ns(t)?t.vconcat:Ca(t)?t.hconcat:t.concat}parseLayoutSize(){MS(this)}parseAxisGroup(){return null}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.forEach(t=>t.assembleSignals()),[]}assembleLayoutSignals(){const t=Pa(this);for(const n of this.children)t.push(...n.assembleLayoutSignals());return t}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleMarks(){return this.children.map(t=>{const n=t.assembleTitle(),i=t.assembleGroupStyle(),r=t.assembleGroupEncodeEntry(!1);return{type:"group",name:t.getName("group"),...n?{title:n}:{},...i?{style:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup()}})}assembleGroupStyle(){}assembleDefaultLayout(){const t=this.layout.columns;return{...t!=null?{columns:t}:{},bounds:"full",align:"each"}}}function VS(e){return e===!1||e===null}const XS={disable:1,gridScale:1,scale:1,...wf,labelExpr:1,encode:1},pp=v(XS);class Ha extends jt{constructor(t={},n={},i=!1){super(),this.explicit=t,this.implicit=n,this.mainExtracted=i}clone(){return new Ha(j(this.explicit),j(this.implicit),this.mainExtracted)}hasAxisPart(t){return t==="axis"?!0:t==="grid"||t==="title"?!!this.get(t):!VS(this.get(t))}hasOrientSignalRef(){return I(this.explicit.orient)}}function YS(e,t,n){const{encoding:i,config:r}=e,s=fe(i[t])??fe(i[vt(t)]),o=e.axis(t)||{},{format:a,formatType:c}=o;if(Cn(c))return{text:Ye({fieldOrDatumDef:s,field:"datum.value",format:a,formatType:c,config:r}),...n};if(a===void 0&&c===void 0&&r.customFormatTypes){if(ii(s)==="quantitative"){if(ri(s)&&s.stack==="normalize"&&r.normalizedNumberFormatType)return{text:Ye({fieldOrDatumDef:s,field:"datum.value",format:r.normalizedNumberFormat,formatType:r.normalizedNumberFormatType,config:r}),...n};if(r.numberFormatType)return{text:Ye({fieldOrDatumDef:s,field:"datum.value",format:r.numberFormat,formatType:r.numberFormatType,config:r}),...n}}if(ii(s)==="temporal"&&r.timeFormatType&&E(s)&&!s.timeUnit)return{text:Ye({fieldOrDatumDef:s,field:"datum.value",format:r.timeFormat,formatType:r.timeFormatType,config:r}),...n}}return n}function KS(e){return St.reduce((t,n)=>(e.component.scales[n]&&(t[n]=[iE(n,e)]),t),{})}const JS={bottom:"top",top:"bottom",left:"right",right:"left"};function QS(e){const{axes:t,resolve:n}=e.component,i={top:0,bottom:0,right:0,left:0};for(const r of e.children){r.parseAxesAndHeaders();for(const s of v(r.component.axes))n.axis[s]=za(e.component.resolve,s),n.axis[s]==="shared"&&(t[s]=ZS(t[s],r.component.axes[s]),t[s]||(n.axis[s]="independent",delete t[s]))}for(const r of St){for(const s of e.children)if(s.component.axes[r]){if(n.axis[r]==="independent"){t[r]=(t[r]??[]).concat(s.component.axes[r]);for(const o of s.component.axes[r]){const{value:a,explicit:c}=o.getWithExplicit("orient");if(!I(a)){if(i[a]>0&&!c){const l=JS[a];i[a]>i[l]&&o.set("orient",l,!1)}i[a]++}}}delete s.component.axes[r]}if(n.axis[r]==="independent"&&t[r]&&t[r].length>1)for(const[s,o]of(t[r]||[]).entries())s>0&&o.get("grid")&&!o.explicit.grid&&(o.implicit.grid=!1)}}function ZS(e,t){if(e){if(e.length!==t.length)return;const n=e.length;for(let i=0;i<n;i++){const r=e[i],s=t[i];if(!!r!=!!s)return;if(r&&s){const o=r.getWithExplicit("orient"),a=s.getWithExplicit("orient");if(o.explicit&&a.explicit&&o.value!==a.value)return;e[i]=eE(r,s)}}}else return t.map(n=>n.clone());return e}function eE(e,t){for(const n of pp){const i=Ht(e.getWithExplicit(n),t.getWithExplicit(n),n,"axis",(r,s)=>{switch(n){case"title":return Nu(r,s);case"gridScale":return{explicit:r.explicit,value:ue(r.value,s.value)}}return rs(r,s,n,"axis")});e.setWithExplicit(n,i)}return e}function tE(e,t,n,i,r){if(t==="disable")return n!==void 0;switch(n=n||{},t){case"titleAngle":case"labelAngle":return e===(I(n.labelAngle)?n.labelAngle:zi(n.labelAngle));case"values":return!!n.values;case"encode":return!!n.encoding||!!n.labelAngle;case"title":if(e===Ad(i,r))return!0}return e===n[t]}const nE=new Set(["grid","translate","format","formatType","orient","labelExpr","tickCount","position","tickMinStep"]);function iE(e,t){let n=t.axis(e);const i=new Ha,r=fe(t.encoding[e]),{mark:s,config:o}=t,a=n?.orient||o[e==="x"?"axisX":"axisY"]?.orient||o.axis?.orient||Wx(e),c=t.getScaleComponent(e).get("type"),l=Px(e,c,a,t.config),u=n!==void 0?!n:co("disable",o.style,n?.style,l).configValue;if(i.set("disable",u,n!==void 0),u)return i;n=n||{};const f=Mx(r,n,e,o.style,l),d=cf(n.formatType,r,c),p=af(r,r.type,n.format,n.formatType,o,!0),g={fieldOrDatumDef:r,axis:n,channel:e,model:t,scaleType:c,orient:a,labelAngle:f,format:p,formatType:d,mark:s,config:o};for(const y of pp){const b=y in Jc?Jc[y](g):Fc(y)?n[y]:void 0,k=b!==void 0,T=tE(b,y,n,t,e);if(k&&T)i.set(y,b,T);else{const{configValue:x=void 0,configFrom:_=void 0}=Fc(y)&&y!=="values"?co(y,o.style,n.style,l):{},C=x!==void 0;k&&!C?i.set(y,b,T):(_!=="vgAxisConfig"||nE.has(y)&&C||Qi(x)||I(x))&&i.set(y,x,!1)}}const h=n.encoding??{},m=$f.reduce((y,b)=>{if(!i.hasAxisPart(b))return y;const k=Dd(h[b]??{},t),T=b==="labels"?YS(t,e,k):k;return T!==void 0&&!J(T)&&(y[b]={update:T}),y},{});return J(m)||i.set("encode",m,!!n.encoding||n.labelAngle!==void 0),i}function rE({encoding:e,size:t}){for(const n of St){const i=Ie(n);mt(t[i])&&Bt(e[n])&&(delete t[i],S(Au(i)))}return t}function sE(e,t,n){const i=_e(e),r=X("orient",i,n);if(i.orient=lE(i.type,t,r),r!==void 0&&r!==i.orient&&S(Ch(i.orient,r)),i.type==="bar"&&i.orient){const a=X("cornerRadiusEnd",i,n);if(a!==void 0){const c=i.orient==="horizontal"&&t.x2||i.orient==="vertical"&&t.y2?["cornerRadius"]:Gm[i.orient];for(const l of c)i[l]=a;i.cornerRadiusEnd!==void 0&&delete i.cornerRadiusEnd}}return X("opacity",i,n)===void 0&&(i.opacity=aE(i.type,t)),X("cursor",i,n)===void 0&&(i.cursor=oE(i,t,n)),i}function oE(e,t,n){return t.href||e.href||X("href",e,n)?"pointer":e.cursor}function aE(e,t){if(G([Vr,na,ia,ra],e)&&!ha(t))return .7}function cE(e,t,{graticule:n}){if(n)return!1;const i=At("filled",e,t),r=e.type;return ue(i,r!==Vr&&r!==Hr&&r!==yr)}function lE(e,t,n){switch(e){case Vr:case ia:case ra:case ef:case Im:case Rm:return}const{x:i,y:r,x2:s,y2:o}=t;switch(e){case qr:if(E(i)&&(xe(i.bin)||E(r)&&r.aggregate&&!i.aggregate))return"vertical";if(E(r)&&(xe(r.bin)||E(i)&&i.aggregate&&!r.aggregate))return"horizontal";if(o||s){if(n)return n;if(!s)return(E(i)&&i.type===$n&&!te(i.bin)||xr(i))&&E(r)&&xe(r.bin)?"horizontal":"vertical";if(!o)return(E(r)&&r.type===$n&&!te(r.bin)||xr(r))&&E(i)&&xe(i.bin)?"vertical":"horizontal"}case yr:if(s&&!(E(i)&&xe(i.bin))&&o&&!(E(r)&&xe(r.bin)))return;case Gr:if(o)return E(r)&&xe(r.bin)?"horizontal":"vertical";if(s)return E(i)&&xe(i.bin)?"vertical":"horizontal";if(e===yr){if(i&&!r)return"vertical";if(r&&!i)return"horizontal"}case Hr:case na:{const a=wc(i),c=wc(r);if(n)return n;if(a&&!c)return e!=="tick"?"horizontal":"vertical";if(!a&&c)return e!=="tick"?"vertical":"horizontal";if(a&&c)return"vertical";{const l=Fe(i)&&i.type===ti,u=Fe(r)&&r.type===ti;if(l&&!u)return"vertical";if(!l&&u)return"horizontal"}return}}return"vertical"}const uE={vgMark:"arc",encodeEntry:e=>({...We(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...Vt(e,"radius"),...Vt(e,"theta")})},fE={vgMark:"area",encodeEntry:e=>({...We(e,{align:"ignore",baseline:"ignore",color:"include",orient:"include",size:"ignore",theta:"ignore"}),...Fr("x",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="horizontal"}),...Fr("y",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="vertical"}),...Aa(e)})},dE={vgMark:"rect",encodeEntry:e=>({...We(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y")})},pE={vgMark:"shape",encodeEntry:e=>({...We(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"})}),postEncodingTransform:e=>{const{encoding:t}=e,n=t.shape;return[{type:"geoshape",projection:e.projectionName(),...n&&E(n)&&n.type===mi?{field:w(n,{expr:"datum"})}:{}}]}},gE={vgMark:"image",encodeEntry:e=>({...We(e,{align:"ignore",baseline:"ignore",color:"ignore",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y"),...Ta(e,"url")})},hE={vgMark:"line",encodeEntry:e=>({...We(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...ge("size",e,{vgChannel:"strokeWidth"}),...Aa(e)})},mE={vgMark:"trail",encodeEntry:e=>({...We(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...ge("size",e),...Aa(e)})};function Va(e,t){const{config:n}=e;return{...We(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...ge("size",e),...ge("angle",e),...yE(e,n,t)}}function yE(e,t,n){return n?{shape:{value:n}}:ge("shape",e)}const bE={vgMark:"symbol",encodeEntry:e=>Va(e)},xE={vgMark:"symbol",encodeEntry:e=>Va(e,"circle")},vE={vgMark:"symbol",encodeEntry:e=>Va(e,"square")},SE={vgMark:"rect",encodeEntry:e=>({...We(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Vt(e,"x"),...Vt(e,"y")})},EE={vgMark:"rule",encodeEntry:e=>{const{markDef:t}=e,n=t.orient;return!e.encoding.x&&!e.encoding.y&&!e.encoding.latitude&&!e.encoding.longitude?{}:{...We(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Fr("x",e,{defaultPos:n==="horizontal"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="vertical"}),...Fr("y",e,{defaultPos:n==="vertical"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="horizontal"}),...ge("size",e,{vgChannel:"strokeWidth"})}}},$E={vgMark:"text",encodeEntry:e=>{const{config:t,encoding:n}=e;return{...We(e,{align:"include",baseline:"include",color:"include",size:"ignore",orient:"ignore",theta:"include"}),...$e("x",e,{defaultPos:"mid"}),...$e("y",e,{defaultPos:"mid"}),...Ta(e),...ge("size",e,{vgChannel:"fontSize"}),...ge("angle",e),...Vc("align",wE(e.markDef,n,t)),...Vc("baseline",CE(e.markDef,n,t)),...$e("radius",e,{defaultPos:null}),...$e("theta",e,{defaultPos:null})}}};function wE(e,t,n){if(X("align",e,n)===void 0)return"center"}function CE(e,t,n){if(X("baseline",e,n)===void 0)return"middle"}const FE={vgMark:"rect",encodeEntry:e=>{const{config:t,markDef:n}=e,i=n.orient,r=i==="horizontal"?"width":"height",s=i==="horizontal"?"height":"width";return{...We(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...$e("x",e,{defaultPos:"mid",vgChannel:"xc"}),...$e("y",e,{defaultPos:"mid",vgChannel:"yc"}),...ge("size",e,{defaultValue:NE(e),vgChannel:r}),[s]:re(X("thickness",n,t))}}};function NE(e){const{config:t,markDef:n}=e,{orient:i}=n,r=i==="horizontal"?"width":"height",s=e.getScaleComponent(i==="horizontal"?"x":"y"),o=X("size",n,t,{vgChannel:r})??t.tick.bandSize;if(o!==void 0)return o;{const a=s?s.get("range"):void 0;return a&&nn(a)&&le(a.step)?a.step*3/4:wr(t.view,r)*3/4}}const ar={arc:uE,area:fE,bar:dE,circle:xE,geoshape:pE,image:gE,line:hE,point:bE,rect:SE,rule:EE,square:vE,text:$E,tick:FE,trail:mE};function _E(e){if(G([Hr,Gr,Lm],e.mark)){const t=_f(e.mark,e.encoding);if(t.length>0)return kE(e,t)}else if(e.mark===qr){const t=Ys.some(n=>X(n,e.markDef,e.config));if(e.stack&&!e.fieldDef("size")&&t)return TE(e)}return Xa(e)}const pl="faceted_path_";function kE(e,t){return[{name:e.getName("pathgroup"),type:"group",from:{facet:{name:pl+e.requestDataName(Z.Main),data:e.requestDataName(Z.Main),groupby:t}},encode:{update:{width:{field:{group:"width"}},height:{field:{group:"height"}}}},marks:Xa(e,{fromPrefix:pl})}]}const gl="stack_group_";function TE(e){const[t]=Xa(e,{fromPrefix:gl}),n=e.scaleName(e.stack.fieldChannel),i=(l={})=>e.vgField(e.stack.fieldChannel,l),r=(l,u)=>{const f=[i({prefix:"min",suffix:"start",expr:u}),i({prefix:"max",suffix:"start",expr:u}),i({prefix:"min",suffix:"end",expr:u}),i({prefix:"max",suffix:"end",expr:u})];return`${l}(${f.map(d=>`scale('${n}',${d})`).join(",")})`};let s,o;e.stack.fieldChannel==="x"?(s={...Jn(t.encode.update,["y","yc","y2","height",...Ys]),x:{signal:r("min","datum")},x2:{signal:r("max","datum")},clip:{value:!0}},o={x:{field:{group:"x"},mult:-1},height:{field:{group:"height"}}},t.encode.update={...ke(t.encode.update,["y","yc","y2"]),height:{field:{group:"height"}}}):(s={...Jn(t.encode.update,["x","xc","x2","width"]),y:{signal:r("min","datum")},y2:{signal:r("max","datum")},clip:{value:!0}},o={y:{field:{group:"y"},mult:-1},width:{field:{group:"width"}}},t.encode.update={...ke(t.encode.update,["x","xc","x2"]),width:{field:{group:"width"}}});for(const l of Ys){const u=At(l,e.markDef,e.config);t.encode.update[l]?(s[l]=t.encode.update[l],delete t.encode.update[l]):u&&(s[l]=re(u)),u&&(t.encode.update[l]={value:0})}const a=[];if(e.stack.groupbyChannels?.length>0)for(const l of e.stack.groupbyChannels){const u=e.fieldDef(l),f=w(u);f&&a.push(f),(u?.bin||u?.timeUnit)&&a.push(w(u,{binSuffix:"end"}))}return s=["stroke","strokeWidth","strokeJoin","strokeCap","strokeDash","strokeDashOffset","strokeMiterLimit","strokeOpacity"].reduce((l,u)=>{if(t.encode.update[u])return{...l,[u]:t.encode.update[u]};{const f=At(u,e.markDef,e.config);return f!==void 0?{...l,[u]:re(f)}:l}},s),s.stroke&&(s.strokeForeground={value:!0},s.strokeOffset={value:0}),[{type:"group",from:{facet:{data:e.requestDataName(Z.Main),name:gl+e.requestDataName(Z.Main),groupby:a,aggregate:{fields:[i({suffix:"start"}),i({suffix:"start"}),i({suffix:"end"}),i({suffix:"end"})],ops:["min","max","min","max"]}}},encode:{update:s},marks:[{type:"group",encode:{update:o},marks:[t]}]}]}function OE(e){const{encoding:t,stack:n,mark:i,markDef:r,config:s}=e,o=t.order;if(!(!A(o)&&Qe(o)&&Vs(o.value)||!o&&Vs(X("order",r,s)))){if((A(o)||E(o))&&!n)return wu(o,{expr:"datum"});if(rn(i)){const a=r.orient==="horizontal"?"y":"x",c=t[a];if(E(c)){const l=c.sort;if(A(l))return{field:w(c,{prefix:a,suffix:"sort_index",expr:"datum"})};if(ft(l))return{field:w({aggregate:ha(e.encoding)?l.op:void 0,field:l.field},{expr:"datum"})};if(uf(l)){const u=e.fieldDef(l.encoding);return{field:w(u,{expr:"datum"}),order:l.order}}else return l===null?void 0:{field:w(c,{binSuffix:e.stack?.impute?"mid":void 0,expr:"datum"})}}return}}}function Xa(e,t={fromPrefix:""}){const{mark:n,markDef:i,encoding:r,config:s}=e,o=ue(i.clip,AE(e),RE(e)),a=Eu(i),c=r.key,l=OE(e),u=IE(e),f=X("aria",i,s),d=ar[n].postEncodingTransform?ar[n].postEncodingTransform(e):null;return[{name:e.getName("marks"),type:ar[n].vgMark,...o?{clip:!0}:{},...a?{style:a}:{},...c?{key:c.field}:{},...l?{sort:l}:{},...u||{},...f===!1?{aria:f}:{},from:{data:t.fromPrefix+e.requestDataName(Z.Main)},encode:{update:ar[n].encodeEntry(e)},...d?{transform:d}:{}}]}function AE(e){const t=e.getScaleComponent("x"),n=e.getScaleComponent("y");return t?.get("selectionExtent")||n?.get("selectionExtent")?!0:void 0}function RE(e){const t=e.component.projection;return t&&!t.isFit?!0:void 0}function IE(e){if(!e.component.selection)return null;const t=v(e.component.selection).length;let n=t,i=e.parent;for(;i&&n===0;)n=v(i.component.selection).length,i=i.parent;return n?{interactive:t>0||e.mark==="geoshape"||!!e.encoding.tooltip}:null}class gp extends lp{constructor(t,n,i,r={},s){super(t,"unit",n,i,s,void 0,_c(t)?t.view:void 0),this.specifiedScales={},this.specifiedAxes={},this.specifiedLegends={},this.specifiedProjection={},this.selection=[],this.children=[];const o=gt(t.mark)?{...t.mark}:{type:t.mark},a=o.type;o.filled===void 0&&(o.filled=cE(o,s,{graticule:t.data&&_a(t.data)}));const c=this.encoding=Sy(t.encoding||{},a,o.filled,s);this.markDef=sE(o,c,s),this.size=rE({encoding:c,size:_c(t)?{...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}}:r}),this.stack=Xf(this.markDef,c),this.specifiedScales=this.initScales(a,c),this.specifiedAxes=this.initAxes(c),this.specifiedLegends=this.initLegends(c),this.specifiedProjection=t.projection,this.selection=(t.params??[]).filter(l=>$a(l))}get hasProjection(){const{encoding:t}=this,n=this.mark===tf,i=t&&xg.some(r=>M(t[r]));return n||i}scaleDomain(t){const n=this.specifiedScales[t];return n?n.domain:void 0}axis(t){return this.specifiedAxes[t]}legend(t){return this.specifiedLegends[t]}initScales(t,n){return Mr.reduce((i,r)=>{const s=fe(n[r]);return s&&(i[r]=this.initScale(s.scale??{})),i},{})}initScale(t){const{domain:n,range:i}=t,r=_e(t);return A(n)&&(r.domain=n.map(Pe)),A(i)&&(r.range=i.map(Pe)),r}initAxes(t){return St.reduce((n,i)=>{const r=t[i];if(M(r)||i===oe&&M(t.x2)||i===me&&M(t.y2)){const s=M(r)?r.axis:void 0;n[i]=s&&this.initAxis({...s})}return n},{})}initAxis(t){const n=v(t),i={};for(const r of n){const s=t[r];i[r]=Qi(s)?Su(s):Pe(s)}return i}initLegends(t){return _g.reduce((n,i)=>{const r=fe(t[i]);if(r&&Tg(i)){const s=r.legend;n[i]=s&&_e(s)}return n},{})}parseData(){this.component.data=xs(this)}parseLayoutSize(){US(this)}parseSelections(){this.component.selection=Ax(this,this.selection)}parseMarkGroup(){this.component.mark=_E(this)}parseAxesAndHeaders(){this.component.axes=KS(this)}assembleSelectionTopLevelSignals(t){return ex(this,t)}assembleSignals(){return[...kd(this),...Qb(this,[])]}assembleSelectionData(t){return tx(this,t)}assembleLayout(){return null}assembleLayoutSignals(){return Pa(this)}assembleMarks(){let t=this.component.mark??[];return(!this.parent||!Si(this.parent))&&(t=ad(this,t)),t.map(this.correctDataNames)}assembleGroupStyle(){const{style:t}=this.view||{};return t!==void 0?t:this.encoding.x||this.encoding.y?"cell":"view"}getMapping(){return this.encoding}get mark(){return this.markDef.type}channelHasField(t){return mn(this.encoding,t)}fieldDef(t){const n=this.encoding[t];return ht(n)}typedFieldDef(t){const n=this.fieldDef(t);return Fe(n)?n:null}}class Ya extends Ga{constructor(t,n,i,r,s){super(t,"layer",n,i,s,t.resolve,t.view);const o={...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}};this.children=t.layer.map((a,c)=>{if(is(a))return new Ya(a,this,this.getName(`layer_${c}`),o,s);if(Dt(a))return new gp(a,this,this.getName(`layer_${c}`),o,s);throw new Error(Uo(a))})}parseData(){this.component.data=xs(this);for(const t of this.children)t.parseData()}parseLayoutSize(){jS(this)}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of v(t.component.selection))this.component.selection[n]=t.component.selection[n]}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup()}parseAxesAndHeaders(){QS(this)}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleSignals()),kd(this))}assembleLayoutSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleLayoutSignals()),Pa(this))}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleGroupStyle(){const t=new Set;for(const i of this.children)for(const r of ce(i.assembleGroupStyle()))t.add(r);const n=Array.from(t);return n.length>1?n:n.length===1?n[0]:void 0}assembleTitle(){let t=super.assembleTitle();if(t)return t;for(const n of this.children)if(t=n.assembleTitle(),t)return t}assembleLayout(){return null}assembleMarks(){return nx(this,this.children.flatMap(t=>t.assembleMarks()))}assembleLegends(){return this.children.reduce((t,n)=>t.concat(n.assembleLegends()),Hd(this))}}function Ka(e,t,n,i,r){if(Yr(e))return new Ii(e,t,n,r);if(is(e))return new Ya(e,t,n,i,r);if(Dt(e))return new gp(e,t,n,i,r);if(qy(e))return new HS(e,t,n,r);throw new Error(Uo(e))}function LE(e,t={}){t.logger&&Kh(t.logger),t.fieldTitle&&vf(t.fieldTitle);try{const n=Vf(Ar(t.config,e.config)),i=td(e,n),r=Ka(i,null,"",void 0,n);return r.parse(),eS(r.component.data,r),{spec:zE(r,PE(e,i.autosize,n,r),e.datasets,e.usermeta),normalized:i}}finally{t.logger&&Jh(),t.fieldTitle&&dy()}}function PE(e,t,n,i){const r=i.component.layoutSize.get("width"),s=i.component.layoutSize.get("height");if(t===void 0?(t={type:"pad"},i.hasAxisOrientSignalRef()&&(t.resize=!0)):z(t)&&(t={type:t}),r&&s&&Xb(t.type)){if(r==="step"&&s==="step")S(dc()),t.type="pad";else if(r==="step"||s==="step"){const o=r==="step"?"width":"height";S(dc(jr(o)));const a=o==="width"?"height":"width";t.type=Yb(a)}}return{...v(t).length===1&&t.type?t.type==="pad"?{}:{autosize:t.type}:{autosize:t},...Mc(n,!1),...Mc(e,!0)}}function zE(e,t,n={},i){const r=e.config?sb(e.config):void 0,s=[].concat(e.assembleSelectionData([]),LS(e.component.data,n)),o=e.assembleProjections(),a=e.assembleTitle(),c=e.assembleGroupStyle(),l=e.assembleGroupEncodeEntry(!0);let u=e.assembleLayoutSignals();u=u.filter(p=>(p.name==="width"||p.name==="height")&&p.value!==void 0?(t[p.name]=+p.value,!1):!0);const{params:f,...d}=t;return{$schema:"https://vega.github.io/schema/vega/v5.json",...e.description?{description:e.description}:{},...d,...a?{title:a}:{},...c?{style:c}:{},...l?{encode:{update:l}}:{},data:s,...o.length>0?{projections:o}:{},...e.assembleGroup([...u,...e.assembleSelectionTopLevelSignals([]),...Wf(f)]),...r?{config:r}:{},...i?{usermeta:i}:{}}}const DE=cg.version,jE=Object.freeze(Object.defineProperty({__proto__:null,accessPathDepth:Qn,accessPathWithDatum:Oo,compile:LE,contains:G,deepEqual:lt,deleteNestedProperty:gr,duplicate:j,entries:Gt,every:_o,fieldIntersection:To,flatAccessWithDatum:eu,getFirstDefined:ue,hasIntersection:ko,hash:W,internalField:iu,isBoolean:Pi,isEmpty:J,isEqual:hg,isInternalField:ru,isNullOrFalse:Vs,isNumeric:Rr,keys:v,logicalExpr:Ai,mergeDeep:Ql,never:Jl,normalize:td,normalizeAngle:zi,omit:ke,pick:Jn,prefixGenerator:Xs,removePathFromField:Ao,replaceAll:vn,replacePathInField:Me,resetIdCounter:yg,setEqual:Zl,some:xn,stringify:ee,titleCase:Wi,unique:ut,uniqueId:nu,vals:ve,varName:se,version:DE},Symbol.toStringTag,{value:"Module"}));var ME="vega-themes",UE="2.14.0",BE="Themes for stylized Vega and Vega-Lite visualizations.",WE=["vega","vega-lite","themes","style"],GE="BSD-3-Clause",qE={name:"UW Interactive Data Lab",url:"https://idl.cs.washington.edu"},HE=[{name:"Emily Gu",url:"https://github.com/emilygu"},{name:"Arvind Satyanarayan",url:"http://arvindsatya.com"},{name:"Jeffrey Heer",url:"https://idl.cs.washington.edu"},{name:"Dominik Moritz",url:"https://www.domoritz.de"}],VE="build/vega-themes.js",XE="build/vega-themes.module.js",YE="build/vega-themes.min.js",KE="build/vega-themes.min.js",JE="build/vega-themes.module.d.ts",QE={type:"git",url:"https://github.com/vega/vega-themes.git"},ZE=["src","build"],e$={prebuild:"yarn clean",build:"rollup -c",clean:"rimraf build && rimraf examples/build","copy:data":"rsync -r node_modules/vega-datasets/data/* examples/data","copy:build":"rsync -r build/* examples/build","deploy:gh":"yarn build && mkdir -p examples/build && rsync -r build/* examples/build && gh-pages -d examples",preversion:"yarn lint",serve:"browser-sync start -s -f build examples --serveStatic examples",start:"yarn build && concurrently --kill-others -n Server,Rollup 'yarn serve' 'rollup -c -w'",format:"eslint . --fix",lint:"eslint .",release:"release-it"},t$={"@babel/core":"^7.22.9","@babel/plugin-proposal-async-generator-functions":"^7.20.7","@babel/plugin-proposal-json-strings":"^7.18.6","@babel/plugin-proposal-object-rest-spread":"^7.20.7","@babel/plugin-proposal-optional-catch-binding":"^7.18.6","@babel/plugin-transform-runtime":"^7.22.9","@babel/preset-env":"^7.22.9","@babel/preset-typescript":"^7.22.5","@release-it/conventional-changelog":"^7.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.1.0","@rollup/plugin-terser":"^0.4.3","@typescript-eslint/eslint-plugin":"^6.0.0","@typescript-eslint/parser":"^6.0.0","browser-sync":"^2.29.3",concurrently:"^8.2.0",eslint:"^8.45.0","eslint-config-prettier":"^8.8.0","eslint-plugin-prettier":"^5.0.0","gh-pages":"^5.0.0",prettier:"^3.0.0","release-it":"^16.1.0",rollup:"^3.26.2","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-ts":"^3.2.0",typescript:"^5.1.6",vega:"^5.25.0","vega-lite":"^5.9.3"},n$={vega:"*","vega-lite":"*"},i$={},r$={name:ME,version:UE,description:BE,keywords:WE,license:GE,author:qE,contributors:HE,main:VE,module:XE,unpkg:YE,jsdelivr:KE,types:JE,repository:QE,files:ZE,scripts:e$,devDependencies:t$,peerDependencies:n$,dependencies:i$};const Mn="#fff",hl="#888",s$={background:"#333",view:{stroke:hl},title:{color:Mn,subtitleColor:Mn},style:{"guide-label":{fill:Mn},"guide-title":{fill:Mn}},axis:{domainColor:Mn,gridColor:hl,tickColor:Mn}},on="#4572a7",o$={background:"#fff",arc:{fill:on},area:{fill:on},line:{stroke:on,strokeWidth:2},path:{stroke:on},rect:{fill:on},shape:{stroke:on},symbol:{fill:on,strokeWidth:1.5,size:50},axis:{bandPosition:.5,grid:!0,gridColor:"#000000",gridOpacity:1,gridWidth:.5,labelPadding:10,tickSize:5,tickWidth:.5},axisBand:{grid:!1,tickExtra:!0},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:50,symbolType:"square"},range:{category:["#4572a7","#aa4643","#8aa453","#71598e","#4598ae","#d98445","#94aace","#d09393","#b9cc98","#a99cbc"]}},an="#30a2da",zs="#cbcbcb",a$="#999",c$="#333",ml="#f0f0f0",yl="#333",l$={arc:{fill:an},area:{fill:an},axis:{domainColor:zs,grid:!0,gridColor:zs,gridWidth:1,labelColor:a$,labelFontSize:10,titleColor:c$,tickColor:zs,tickSize:10,titleFontSize:14,titlePadding:10,labelPadding:4},axisBand:{grid:!1},background:ml,group:{fill:ml},legend:{labelColor:yl,labelFontSize:11,padding:1,symbolSize:30,symbolType:"square",titleColor:yl,titleFontSize:14,titlePadding:10},line:{stroke:an,strokeWidth:2},path:{stroke:an,strokeWidth:.5},rect:{fill:an},range:{category:["#30a2da","#fc4f30","#e5ae38","#6d904f","#8b8b8b","#b96db8","#ff9e27","#56cc60","#52d2ca","#52689e","#545454","#9fe4f8"],diverging:["#cc0020","#e77866","#f6e7e1","#d6e8ed","#91bfd9","#1d78b5"],heatmap:["#d6e8ed","#cee0e5","#91bfd9","#549cc6","#1d78b5"]},point:{filled:!0,shape:"circle"},shape:{stroke:an},bar:{binSpacing:2,fill:an,stroke:null},title:{anchor:"start",fontSize:24,fontWeight:600,offset:20}},cn="#000",u$={group:{fill:"#e5e5e5"},arc:{fill:cn},area:{fill:cn},line:{stroke:cn},path:{stroke:cn},rect:{fill:cn},shape:{stroke:cn},symbol:{fill:cn,size:40},axis:{domain:!1,grid:!0,gridColor:"#FFFFFF",gridOpacity:1,labelColor:"#7F7F7F",labelPadding:4,tickColor:"#7F7F7F",tickSize:5.67,titleFontSize:16,titleFontWeight:"normal"},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:40},range:{category:["#000000","#7F7F7F","#1A1A1A","#999999","#333333","#B0B0B0","#4D4D4D","#C9C9C9","#666666","#DCDCDC"]}},f$=22,d$="normal",bl="Benton Gothic, sans-serif",xl=11.5,p$="normal",ln="#82c6df",Ds="Benton Gothic Bold, sans-serif",vl="normal",Sl=13,wi={"category-6":["#ec8431","#829eb1","#c89d29","#3580b1","#adc839","#ab7fb4"],"fire-7":["#fbf2c7","#f9e39c","#f8d36e","#f4bb6a","#e68a4f","#d15a40","#ab4232"],"fireandice-6":["#e68a4f","#f4bb6a","#f9e39c","#dadfe2","#a6b7c6","#849eae"],"ice-7":["#edefee","#dadfe2","#c4ccd2","#a6b7c6","#849eae","#607785","#47525d"]},g$={background:"#ffffff",title:{anchor:"start",color:"#000000",font:Ds,fontSize:f$,fontWeight:d$},arc:{fill:ln},area:{fill:ln},line:{stroke:ln,strokeWidth:2},path:{stroke:ln},rect:{fill:ln},shape:{stroke:ln},symbol:{fill:ln,size:30},axis:{labelFont:bl,labelFontSize:xl,labelFontWeight:p$,titleFont:Ds,titleFontSize:Sl,titleFontWeight:vl},axisX:{labelAngle:0,labelPadding:4,tickSize:3},axisY:{labelBaseline:"middle",maxExtent:45,minExtent:45,tickSize:2,titleAlign:"left",titleAngle:0,titleX:-45,titleY:-11},legend:{labelFont:bl,labelFontSize:xl,symbolType:"square",titleFont:Ds,titleFontSize:Sl,titleFontWeight:vl},range:{category:wi["category-6"],diverging:wi["fireandice-6"],heatmap:wi["fire-7"],ordinal:wi["fire-7"],ramp:wi["fire-7"]}},un="#ab5787",cr="#979797",h$={background:"#f9f9f9",arc:{fill:un},area:{fill:un},line:{stroke:un},path:{stroke:un},rect:{fill:un},shape:{stroke:un},symbol:{fill:un,size:30},axis:{domainColor:cr,domainWidth:.5,gridWidth:.2,labelColor:cr,tickColor:cr,tickWidth:.2,titleColor:cr},axisBand:{grid:!1},axisX:{grid:!0,tickSize:10},axisY:{domain:!1,grid:!0,tickSize:0},legend:{labelFontSize:11,padding:1,symbolSize:30,symbolType:"square"},range:{category:["#ab5787","#51b2e5","#703c5c","#168dd9","#d190b6","#00609f","#d365ba","#154866","#666666","#c4c4c4"]}},fn="#3e5c69",m$={background:"#fff",arc:{fill:fn},area:{fill:fn},line:{stroke:fn},path:{stroke:fn},rect:{fill:fn},shape:{stroke:fn},symbol:{fill:fn},axis:{domainWidth:.5,grid:!0,labelPadding:2,tickSize:5,tickWidth:.5,titleFontWeight:"normal"},axisBand:{grid:!1},axisX:{gridWidth:.2},axisY:{gridDash:[3],gridWidth:.4},legend:{labelFontSize:11,padding:1,symbolType:"square"},range:{category:["#3e5c69","#6793a6","#182429","#0570b0","#3690c0","#74a9cf","#a6bddb","#e2ddf2"]}},De="#1696d2",El="#000000",y$="#FFFFFF",lr="Lato",js="Lato",b$="Lato",x$="#DEDDDD",v$=18,Ci={"main-colors":["#1696d2","#d2d2d2","#000000","#fdbf11","#ec008b","#55b748","#5c5859","#db2b27"],"shades-blue":["#CFE8F3","#A2D4EC","#73BFE2","#46ABDB","#1696D2","#12719E","#0A4C6A","#062635"],"shades-gray":["#F5F5F5","#ECECEC","#E3E3E3","#DCDBDB","#D2D2D2","#9D9D9D","#696969","#353535"],"shades-yellow":["#FFF2CF","#FCE39E","#FDD870","#FCCB41","#FDBF11","#E88E2D","#CA5800","#843215"],"shades-magenta":["#F5CBDF","#EB99C2","#E46AA7","#E54096","#EC008B","#AF1F6B","#761548","#351123"],"shades-green":["#DCEDD9","#BCDEB4","#98CF90","#78C26D","#55B748","#408941","#2C5C2D","#1A2E19"],"shades-black":["#D5D5D4","#ADABAC","#848081","#5C5859","#332D2F","#262223","#1A1717","#0E0C0D"],"shades-red":["#F8D5D4","#F1AAA9","#E9807D","#E25552","#DB2B27","#A4201D","#6E1614","#370B0A"],"one-group":["#1696d2","#000000"],"two-groups-cat-1":["#1696d2","#000000"],"two-groups-cat-2":["#1696d2","#fdbf11"],"two-groups-cat-3":["#1696d2","#db2b27"],"two-groups-seq":["#a2d4ec","#1696d2"],"three-groups-cat":["#1696d2","#fdbf11","#000000"],"three-groups-seq":["#a2d4ec","#1696d2","#0a4c6a"],"four-groups-cat-1":["#000000","#d2d2d2","#fdbf11","#1696d2"],"four-groups-cat-2":["#1696d2","#ec0008b","#fdbf11","#5c5859"],"four-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a"],"five-groups-cat-1":["#1696d2","#fdbf11","#d2d2d2","#ec008b","#000000"],"five-groups-cat-2":["#1696d2","#0a4c6a","#d2d2d2","#fdbf11","#332d2f"],"five-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a","#000000"],"six-groups-cat-1":["#1696d2","#ec008b","#fdbf11","#000000","#d2d2d2","#55b748"],"six-groups-cat-2":["#1696d2","#d2d2d2","#ec008b","#fdbf11","#332d2f","#0a4c6a"],"six-groups-seq":["#cfe8f3","#a2d4ec","#73bfe2","#46abdb","#1696d2","#12719e"],"diverging-colors":["#ca5800","#fdbf11","#fdd870","#fff2cf","#cfe8f3","#73bfe2","#1696d2","#0a4c6a"]},S$={background:y$,title:{anchor:"start",fontSize:v$,font:lr},axisX:{domain:!0,domainColor:El,domainWidth:1,grid:!1,labelFontSize:12,labelFont:js,labelAngle:0,tickColor:El,tickSize:5,titleFontSize:12,titlePadding:10,titleFont:lr},axisY:{domain:!1,domainWidth:1,grid:!0,gridColor:x$,gridWidth:1,labelFontSize:12,labelFont:js,labelPadding:8,ticks:!1,titleFontSize:12,titlePadding:10,titleFont:lr,titleAngle:0,titleY:-10,titleX:18},legend:{labelFontSize:12,labelFont:js,symbolSize:100,titleFontSize:12,titlePadding:10,titleFont:lr,orient:"right",offset:10},view:{stroke:"transparent"},range:{category:Ci["six-groups-cat-1"],diverging:Ci["diverging-colors"],heatmap:Ci["diverging-colors"],ordinal:Ci["six-groups-seq"],ramp:Ci["shades-blue"]},area:{fill:De},rect:{fill:De},line:{color:De,stroke:De,strokeWidth:5},trail:{color:De,stroke:De,strokeWidth:0,size:1},path:{stroke:De,strokeWidth:.5},point:{filled:!0},text:{font:b$,color:De,fontSize:11,align:"center",fontWeight:400,size:11},style:{bar:{fill:De,stroke:null}},arc:{fill:De},shape:{stroke:De},symbol:{fill:De,size:30}},dn="#3366CC",$l="#ccc",ur="Arial, sans-serif",E$={arc:{fill:dn},area:{fill:dn},path:{stroke:dn},rect:{fill:dn},shape:{stroke:dn},symbol:{stroke:dn},circle:{fill:dn},background:"#fff",padding:{top:10,right:10,bottom:10,left:10},style:{"guide-label":{font:ur,fontSize:12},"guide-title":{font:ur,fontSize:12},"group-title":{font:ur,fontSize:12}},title:{font:ur,fontSize:14,fontWeight:"bold",dy:-3,anchor:"start"},axis:{gridColor:$l,tickColor:$l,domain:!1,grid:!0},range:{category:["#4285F4","#DB4437","#F4B400","#0F9D58","#AB47BC","#00ACC1","#FF7043","#9E9D24","#5C6BC0","#F06292","#00796B","#C2185B"],heatmap:["#c6dafc","#5e97f6","#2a56c6"]}},Ja=e=>e*(1/3+1),wl=Ja(9),Cl=Ja(10),Fl=Ja(12),Fi="Segoe UI",Nl="wf_standard-font, helvetica, arial, sans-serif",_l="#252423",Ni="#605E5C",kl="transparent",$$="#C8C6C4",qe="#118DFF",w$="#12239E",C$="#E66C37",F$="#6B007B",N$="#E044A7",_$="#744EC2",k$="#D9B300",T$="#D64550",hp=qe,mp="#DEEFFF",Tl=[mp,hp],O$=[mp,"#c7e4ff","#b0d9ff","#9aceff","#83c3ff","#6cb9ff","#55aeff","#3fa3ff","#2898ff",hp],A$={view:{stroke:kl},background:kl,font:Fi,header:{titleFont:Nl,titleFontSize:Fl,titleColor:_l,labelFont:Fi,labelFontSize:Cl,labelColor:Ni},axis:{ticks:!1,grid:!1,domain:!1,labelColor:Ni,labelFontSize:wl,titleFont:Nl,titleColor:_l,titleFontSize:Fl,titleFontWeight:"normal"},axisQuantitative:{tickCount:3,grid:!0,gridColor:$$,gridDash:[1,5],labelFlush:!1},axisBand:{tickExtra:!0},axisX:{labelPadding:5},axisY:{labelPadding:10},bar:{fill:qe},line:{stroke:qe,strokeWidth:3,strokeCap:"round",strokeJoin:"round"},text:{font:Fi,fontSize:wl,fill:Ni},arc:{fill:qe},area:{fill:qe,line:!0,opacity:.6},path:{stroke:qe},rect:{fill:qe},point:{fill:qe,filled:!0,size:75},shape:{stroke:qe},symbol:{fill:qe,strokeWidth:1.5,size:50},legend:{titleFont:Fi,titleFontWeight:"bold",titleColor:Ni,labelFont:Fi,labelFontSize:Cl,labelColor:Ni,symbolType:"circle",symbolSize:75},range:{category:[qe,w$,C$,F$,N$,_$,k$,T$],diverging:Tl,heatmap:Tl,ordinal:O$}},Ms='IBM Plex Sans,system-ui,-apple-system,BlinkMacSystemFont,".sfnstext-regular",sans-serif',Ol=400,R$=["#8a3ffc","#33b1ff","#007d79","#ff7eb6","#fa4d56","#fff1f1","#6fdc8c","#4589ff","#d12771","#d2a106","#08bdba","#bae6ff","#ba4e00","#d4bbff"],I$=["#6929c4","#1192e8","#005d5d","#9f1853","#fa4d56","#570408","#198038","#002d9c","#ee538b","#b28600","#009d9a","#012749","#8a3800","#a56eff"];function vs({type:e,background:t}){const n=e==="dark"?"#161616":"#ffffff",i=e==="dark"?"#f4f4f4":"#161616",r=e==="dark"?R$:I$,s=e==="dark"?"#d4bbff":"#6929c4";return{background:t,arc:{fill:s},area:{fill:s},path:{stroke:s},rect:{fill:s},shape:{stroke:s},symbol:{stroke:s},circle:{fill:s},view:{fill:n,stroke:n},group:{fill:n},title:{color:i,anchor:"start",dy:-15,fontSize:16,font:Ms,fontWeight:600},axis:{labelColor:i,labelFontSize:12,grid:!0,gridColor:"#525252",titleColor:i,labelAngle:0},style:{"guide-label":{font:Ms,fill:i,fontWeight:Ol},"guide-title":{font:Ms,fill:i,fontWeight:Ol}},range:{category:r,diverging:["#750e13","#a2191f","#da1e28","#fa4d56","#ff8389","#ffb3b8","#ffd7d9","#fff1f1","#e5f6ff","#bae6ff","#82cfff","#33b1ff","#1192e8","#0072c3","#00539a","#003a6d"],heatmap:["#f6f2ff","#e8daff","#d4bbff","#be95ff","#a56eff","#8a3ffc","#6929c4","#491d8b","#31135e","#1c0f30"]}}}const L$=vs({type:"light",background:"#ffffff"}),P$=vs({type:"light",background:"#f4f4f4"}),z$=vs({type:"dark",background:"#262626"}),D$=vs({type:"dark",background:"#161616"}),j$=r$.version,M$=Object.freeze(Object.defineProperty({__proto__:null,carbong10:P$,carbong100:D$,carbong90:z$,carbonwhite:L$,dark:s$,excel:o$,fivethirtyeight:l$,ggplot2:u$,googlecharts:E$,latimes:g$,powerbi:A$,quartz:h$,urbaninstitute:S$,version:j$,vox:m$},Symbol.toStringTag,{value:"Module"}));var Us={};function U$(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Bs,Al;function B$(){return Al||(Al=1,Bs=function(e){e.prototype[Symbol.iterator]=function*(){for(let t=this.head;t;t=t.next)yield t.value}}),Bs}var W$=H;H.Node=kn;H.create=H;function H(e){var t=this;if(t instanceof H||(t=new H),t.tail=null,t.head=null,t.length=0,e&&typeof e.forEach=="function")e.forEach(function(r){t.push(r)});else if(arguments.length>0)for(var n=0,i=arguments.length;n<i;n++)t.push(arguments[n]);return t}H.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,n=e.prev;return t&&(t.prev=n),n&&(n.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=n),e.list.length--,e.next=null,e.prev=null,e.list=null,t};H.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}};H.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}};H.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)q$(this,arguments[e]);return this.length};H.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)H$(this,arguments[e]);return this.length};H.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}};H.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}};H.prototype.forEach=function(e,t){t=t||this;for(var n=this.head,i=0;n!==null;i++)e.call(t,n.value,i,this),n=n.next};H.prototype.forEachReverse=function(e,t){t=t||this;for(var n=this.tail,i=this.length-1;n!==null;i--)e.call(t,n.value,i,this),n=n.prev};H.prototype.get=function(e){for(var t=0,n=this.head;n!==null&&t<e;t++)n=n.next;if(t===e&&n!==null)return n.value};H.prototype.getReverse=function(e){for(var t=0,n=this.tail;n!==null&&t<e;t++)n=n.prev;if(t===e&&n!==null)return n.value};H.prototype.map=function(e,t){t=t||this;for(var n=new H,i=this.head;i!==null;)n.push(e.call(t,i.value,this)),i=i.next;return n};H.prototype.mapReverse=function(e,t){t=t||this;for(var n=new H,i=this.tail;i!==null;)n.push(e.call(t,i.value,this)),i=i.prev;return n};H.prototype.reduce=function(e,t){var n,i=this.head;if(arguments.length>1)n=t;else if(this.head)i=this.head.next,n=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=0;i!==null;r++)n=e(n,i.value,r),i=i.next;return n};H.prototype.reduceReverse=function(e,t){var n,i=this.tail;if(arguments.length>1)n=t;else if(this.tail)i=this.tail.prev,n=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=this.length-1;i!==null;r--)n=e(n,i.value,r),i=i.prev;return n};H.prototype.toArray=function(){for(var e=new Array(this.length),t=0,n=this.head;n!==null;t++)e[t]=n.value,n=n.next;return e};H.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,n=this.tail;n!==null;t++)e[t]=n.value,n=n.prev;return e};H.prototype.slice=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new H;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(;r!==null&&i<t;i++,r=r.next)n.push(r.value);return n};H.prototype.sliceReverse=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new H;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=this.length,r=this.tail;r!==null&&i>t;i--)r=r.prev;for(;r!==null&&i>e;i--,r=r.prev)n.push(r.value);return n};H.prototype.splice=function(e,t,...n){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(var s=[],i=0;r&&i<t;i++)s.push(r.value),r=this.removeNode(r);r===null&&(r=this.tail),r!==this.head&&r!==this.tail&&(r=r.prev);for(var i=0;i<n.length;i++)r=G$(this,r,n[i]);return s};H.prototype.reverse=function(){for(var e=this.head,t=this.tail,n=e;n!==null;n=n.prev){var i=n.prev;n.prev=n.next,n.next=i}return this.head=t,this.tail=e,this};function G$(e,t,n){var i=t===e.head?new kn(n,null,t,e):new kn(n,t,t.next,e);return i.next===null&&(e.tail=i),i.prev===null&&(e.head=i),e.length++,i}function q$(e,t){e.tail=new kn(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function H$(e,t){e.head=new kn(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function kn(e,t,n,i){if(!(this instanceof kn))return new kn(e,t,n,i);this.list=i,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,n?(n.prev=this,this.next=n):this.next=null}try{B$()(H)}catch{}const V$=W$,pn=Symbol("max"),Ct=Symbol("length"),Un=Symbol("lengthCalculator"),Li=Symbol("allowStale"),gn=Symbol("maxAge"),wt=Symbol("dispose"),Rl=Symbol("noDisposeOnSet"),pe=Symbol("lruList"),He=Symbol("cache"),yp=Symbol("updateAgeOnGet"),Ws=()=>1;class X${constructor(t){if(typeof t=="number"&&(t={max:t}),t||(t={}),t.max&&(typeof t.max!="number"||t.max<0))throw new TypeError("max must be a non-negative number");this[pn]=t.max||1/0;const n=t.length||Ws;if(this[Un]=typeof n!="function"?Ws:n,this[Li]=t.stale||!1,t.maxAge&&typeof t.maxAge!="number")throw new TypeError("maxAge must be a number");this[gn]=t.maxAge||0,this[wt]=t.dispose,this[Rl]=t.noDisposeOnSet||!1,this[yp]=t.updateAgeOnGet||!1,this.reset()}set max(t){if(typeof t!="number"||t<0)throw new TypeError("max must be a non-negative number");this[pn]=t||1/0,_i(this)}get max(){return this[pn]}set allowStale(t){this[Li]=!!t}get allowStale(){return this[Li]}set maxAge(t){if(typeof t!="number")throw new TypeError("maxAge must be a non-negative number");this[gn]=t,_i(this)}get maxAge(){return this[gn]}set lengthCalculator(t){typeof t!="function"&&(t=Ws),t!==this[Un]&&(this[Un]=t,this[Ct]=0,this[pe].forEach(n=>{n.length=this[Un](n.value,n.key),this[Ct]+=n.length})),_i(this)}get lengthCalculator(){return this[Un]}get length(){return this[Ct]}get itemCount(){return this[pe].length}rforEach(t,n){n=n||this;for(let i=this[pe].tail;i!==null;){const r=i.prev;Il(this,t,i,n),i=r}}forEach(t,n){n=n||this;for(let i=this[pe].head;i!==null;){const r=i.next;Il(this,t,i,n),i=r}}keys(){return this[pe].toArray().map(t=>t.key)}values(){return this[pe].toArray().map(t=>t.value)}reset(){this[wt]&&this[pe]&&this[pe].length&&this[pe].forEach(t=>this[wt](t.key,t.value)),this[He]=new Map,this[pe]=new V$,this[Ct]=0}dump(){return this[pe].map(t=>Tr(this,t)?!1:{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}).toArray().filter(t=>t)}dumpLru(){return this[pe]}set(t,n,i){if(i=i||this[gn],i&&typeof i!="number")throw new TypeError("maxAge must be a number");const r=i?Date.now():0,s=this[Un](n,t);if(this[He].has(t)){if(s>this[pn])return Kn(this,this[He].get(t)),!1;const c=this[He].get(t).value;return this[wt]&&(this[Rl]||this[wt](t,c.value)),c.now=r,c.maxAge=i,c.value=n,this[Ct]+=s-c.length,c.length=s,this.get(t),_i(this),!0}const o=new Y$(t,n,s,r,i);return o.length>this[pn]?(this[wt]&&this[wt](t,n),!1):(this[Ct]+=o.length,this[pe].unshift(o),this[He].set(t,this[pe].head),_i(this),!0)}has(t){if(!this[He].has(t))return!1;const n=this[He].get(t).value;return!Tr(this,n)}get(t){return Gs(this,t,!0)}peek(t){return Gs(this,t,!1)}pop(){const t=this[pe].tail;return t?(Kn(this,t),t.value):null}del(t){Kn(this,this[He].get(t))}load(t){this.reset();const n=Date.now();for(let i=t.length-1;i>=0;i--){const r=t[i],s=r.e||0;if(s===0)this.set(r.k,r.v);else{const o=s-n;o>0&&this.set(r.k,r.v,o)}}}prune(){this[He].forEach((t,n)=>Gs(this,n,!1))}}const Gs=(e,t,n)=>{const i=e[He].get(t);if(i){const r=i.value;if(Tr(e,r)){if(Kn(e,i),!e[Li])return}else n&&(e[yp]&&(i.value.now=Date.now()),e[pe].unshiftNode(i));return r.value}},Tr=(e,t)=>{if(!t||!t.maxAge&&!e[gn])return!1;const n=Date.now()-t.now;return t.maxAge?n>t.maxAge:e[gn]&&n>e[gn]},_i=e=>{if(e[Ct]>e[pn])for(let t=e[pe].tail;e[Ct]>e[pn]&&t!==null;){const n=t.prev;Kn(e,t),t=n}},Kn=(e,t)=>{if(t){const n=t.value;e[wt]&&e[wt](n.key,n.value),e[Ct]-=n.length,e[He].delete(n.key),e[pe].removeNode(t)}};class Y${constructor(t,n,i,r,s){this.key=t,this.value=n,this.length=i,this.now=r,this.maxAge=s||0}}const Il=(e,t,n,i)=>{let r=n.value;Tr(e,r)&&(Kn(e,n),e[Li]||(r=void 0)),r&&t.call(i,r.value,r.key,e)};var K$=X$;const J$=Object.freeze({loose:!0}),Q$=Object.freeze({}),Z$=e=>e?typeof e!="object"?J$:e:Q$;var Qa=Z$,bo={exports:{}};const e1="2.0.0",bp=256,t1=Number.MAX_SAFE_INTEGER||9007199254740991,n1=16,i1=bp-6,r1=["major","premajor","minor","preminor","patch","prepatch","prerelease"];var Za={MAX_LENGTH:bp,MAX_SAFE_COMPONENT_LENGTH:n1,MAX_SAFE_BUILD_LENGTH:i1,MAX_SAFE_INTEGER:t1,RELEASE_TYPES:r1,SEMVER_SPEC_VERSION:e1,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2};const s1=typeof process=="object"&&Us&&Us.NODE_DEBUG&&/\bsemver\b/i.test(Us.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var Ss=s1;(function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:r}=Za,s=Ss;t=e.exports={};const o=t.re=[],a=t.safeRe=[],c=t.src=[],l=t.t={};let u=0;const f="[a-zA-Z0-9-]",d=[["\\s",1],["\\d",r],[f,i]],p=h=>{for(const[m,y]of d)h=h.split(`${m}*`).join(`${m}{0,${y}}`).split(`${m}+`).join(`${m}{1,${y}}`);return h},g=(h,m,y)=>{const b=p(m),k=u++;s(h,k,m),l[h]=k,c[k]=m,o[k]=new RegExp(m,y?"g":void 0),a[k]=new RegExp(b,y?"g":void 0)};g("NUMERICIDENTIFIER","0|[1-9]\\d*"),g("NUMERICIDENTIFIERLOOSE","\\d+"),g("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${f}*`),g("MAINVERSION",`(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})`),g("MAINVERSIONLOOSE",`(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASEIDENTIFIER",`(?:${c[l.NUMERICIDENTIFIER]}|${c[l.NONNUMERICIDENTIFIER]})`),g("PRERELEASEIDENTIFIERLOOSE",`(?:${c[l.NUMERICIDENTIFIERLOOSE]}|${c[l.NONNUMERICIDENTIFIER]})`),g("PRERELEASE",`(?:-(${c[l.PRERELEASEIDENTIFIER]}(?:\\.${c[l.PRERELEASEIDENTIFIER]})*))`),g("PRERELEASELOOSE",`(?:-?(${c[l.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[l.PRERELEASEIDENTIFIERLOOSE]})*))`),g("BUILDIDENTIFIER",`${f}+`),g("BUILD",`(?:\\+(${c[l.BUILDIDENTIFIER]}(?:\\.${c[l.BUILDIDENTIFIER]})*))`),g("FULLPLAIN",`v?${c[l.MAINVERSION]}${c[l.PRERELEASE]}?${c[l.BUILD]}?`),g("FULL",`^${c[l.FULLPLAIN]}$`),g("LOOSEPLAIN",`[v=\\s]*${c[l.MAINVERSIONLOOSE]}${c[l.PRERELEASELOOSE]}?${c[l.BUILD]}?`),g("LOOSE",`^${c[l.LOOSEPLAIN]}$`),g("GTLT","((?:<|>)?=?)"),g("XRANGEIDENTIFIERLOOSE",`${c[l.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),g("XRANGEIDENTIFIER",`${c[l.NUMERICIDENTIFIER]}|x|X|\\*`),g("XRANGEPLAIN",`[v=\\s]*(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:${c[l.PRERELEASE]})?${c[l.BUILD]}?)?)?`),g("XRANGEPLAINLOOSE",`[v=\\s]*(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:${c[l.PRERELEASELOOSE]})?${c[l.BUILD]}?)?)?`),g("XRANGE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAIN]}$`),g("XRANGELOOSE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAINLOOSE]}$`),g("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),g("COERCE",`${c[l.COERCEPLAIN]}(?:$|[^\\d])`),g("COERCEFULL",c[l.COERCEPLAIN]+`(?:${c[l.PRERELEASE]})?(?:${c[l.BUILD]})?(?:$|[^\\d])`),g("COERCERTL",c[l.COERCE],!0),g("COERCERTLFULL",c[l.COERCEFULL],!0),g("LONETILDE","(?:~>?)"),g("TILDETRIM",`(\\s*)${c[l.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",g("TILDE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAIN]}$`),g("TILDELOOSE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAINLOOSE]}$`),g("LONECARET","(?:\\^)"),g("CARETTRIM",`(\\s*)${c[l.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",g("CARET",`^${c[l.LONECARET]}${c[l.XRANGEPLAIN]}$`),g("CARETLOOSE",`^${c[l.LONECARET]}${c[l.XRANGEPLAINLOOSE]}$`),g("COMPARATORLOOSE",`^${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]})$|^$`),g("COMPARATOR",`^${c[l.GTLT]}\\s*(${c[l.FULLPLAIN]})$|^$`),g("COMPARATORTRIM",`(\\s*)${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]}|${c[l.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",g("HYPHENRANGE",`^\\s*(${c[l.XRANGEPLAIN]})\\s+-\\s+(${c[l.XRANGEPLAIN]})\\s*$`),g("HYPHENRANGELOOSE",`^\\s*(${c[l.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[l.XRANGEPLAINLOOSE]})\\s*$`),g("STAR","(<|>)?=?\\s*\\*"),g("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),g("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")})(bo,bo.exports);var ec=bo.exports;const Ll=/^[0-9]+$/,xp=(e,t)=>{const n=Ll.test(e),i=Ll.test(t);return n&&i&&(e=+e,t=+t),e===t?0:n&&!i?-1:i&&!n?1:e<t?-1:1},o1=(e,t)=>xp(t,e);var a1={compareIdentifiers:xp,rcompareIdentifiers:o1};const fr=Ss,{MAX_LENGTH:Pl,MAX_SAFE_INTEGER:dr}=Za,{safeRe:zl,t:Dl}=ec,c1=Qa,{compareIdentifiers:Bn}=a1;let l1=class ot{constructor(t,n){if(n=c1(n),t instanceof ot){if(t.loose===!!n.loose&&t.includePrerelease===!!n.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>Pl)throw new TypeError(`version is longer than ${Pl} characters`);fr("SemVer",t,n),this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;const i=t.trim().match(n.loose?zl[Dl.LOOSE]:zl[Dl.FULL]);if(!i)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+i[1],this.minor=+i[2],this.patch=+i[3],this.major>dr||this.major<0)throw new TypeError("Invalid major version");if(this.minor>dr||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>dr||this.patch<0)throw new TypeError("Invalid patch version");i[4]?this.prerelease=i[4].split(".").map(r=>{if(/^[0-9]+$/.test(r)){const s=+r;if(s>=0&&s<dr)return s}return r}):this.prerelease=[],this.build=i[5]?i[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(fr("SemVer.compare",this.version,this.options,t),!(t instanceof ot)){if(typeof t=="string"&&t===this.version)return 0;t=new ot(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof ot||(t=new ot(t,this.options)),Bn(this.major,t.major)||Bn(this.minor,t.minor)||Bn(this.patch,t.patch)}comparePre(t){if(t instanceof ot||(t=new ot(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let n=0;do{const i=this.prerelease[n],r=t.prerelease[n];if(fr("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return-1;if(i===r)continue;return Bn(i,r)}while(++n)}compareBuild(t){t instanceof ot||(t=new ot(t,this.options));let n=0;do{const i=this.build[n],r=t.build[n];if(fr("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return-1;if(i===r)continue;return Bn(i,r)}while(++n)}inc(t,n,i){switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n,i);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n,i);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n,i),this.inc("pre",n,i);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",n,i),this.inc("pre",n,i);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const r=Number(i)?1:0;if(!n&&i===!1)throw new Error("invalid increment argument: identifier is empty");if(this.prerelease.length===0)this.prerelease=[r];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(n===this.prerelease.join(".")&&i===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(r)}}if(n){let s=[n,r];i===!1&&(s=[n]),Bn(this.prerelease[0],n)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};var tc=l1;const jl=tc,u1=(e,t,n)=>new jl(e,n).compare(new jl(t,n));var Ei=u1;const f1=Ei,d1=(e,t,n)=>f1(e,t,n)===0;var p1=d1;const g1=Ei,h1=(e,t,n)=>g1(e,t,n)!==0;var m1=h1;const y1=Ei,b1=(e,t,n)=>y1(e,t,n)>0;var x1=b1;const v1=Ei,S1=(e,t,n)=>v1(e,t,n)>=0;var E1=S1;const $1=Ei,w1=(e,t,n)=>$1(e,t,n)<0;var C1=w1;const F1=Ei,N1=(e,t,n)=>F1(e,t,n)<=0;var _1=N1;const k1=p1,T1=m1,O1=x1,A1=E1,R1=C1,I1=_1,L1=(e,t,n,i)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e===n;case"!==":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e!==n;case"":case"=":case"==":return k1(e,n,i);case"!=":return T1(e,n,i);case">":return O1(e,n,i);case">=":return A1(e,n,i);case"<":return R1(e,n,i);case"<=":return I1(e,n,i);default:throw new TypeError(`Invalid operator: ${t}`)}};var P1=L1,qs,Ml;function z1(){if(Ml)return qs;Ml=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(u,f){if(f=n(f),u instanceof t){if(u.loose===!!f.loose)return u;u=u.value}u=u.trim().split(/\s+/).join(" "),o("comparator",u,f),this.options=f,this.loose=!!f.loose,this.parse(u),this.semver===e?this.value="":this.value=this.operator+this.semver.version,o("comp",this)}parse(u){const f=this.options.loose?i[r.COMPARATORLOOSE]:i[r.COMPARATOR],d=u.match(f);if(!d)throw new TypeError(`Invalid comparator: ${u}`);this.operator=d[1]!==void 0?d[1]:"",this.operator==="="&&(this.operator=""),d[2]?this.semver=new a(d[2],this.options.loose):this.semver=e}toString(){return this.value}test(u){if(o("Comparator.test",u,this.options.loose),this.semver===e||u===e)return!0;if(typeof u=="string")try{u=new a(u,this.options)}catch{return!1}return s(u,this.operator,this.semver,this.options)}intersects(u,f){if(!(u instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new c(u.value,f).test(this.value):u.operator===""?u.value===""?!0:new c(this.value,f).test(u.semver):(f=n(f),f.includePrerelease&&(this.value==="<0.0.0-0"||u.value==="<0.0.0-0")||!f.includePrerelease&&(this.value.startsWith("<0.0.0")||u.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&u.operator.startsWith(">")||this.operator.startsWith("<")&&u.operator.startsWith("<")||this.semver.version===u.semver.version&&this.operator.includes("=")&&u.operator.includes("=")||s(this.semver,"<",u.semver,f)&&this.operator.startsWith(">")&&u.operator.startsWith("<")||s(this.semver,">",u.semver,f)&&this.operator.startsWith("<")&&u.operator.startsWith(">")))}}qs=t;const n=Qa,{safeRe:i,t:r}=ec,s=P1,o=Ss,a=tc,c=vp();return qs}var Hs,Ul;function vp(){if(Ul)return Hs;Ul=1;class e{constructor($,L){if(L=i(L),$ instanceof e)return $.loose===!!L.loose&&$.includePrerelease===!!L.includePrerelease?$:new e($.raw,L);if($ instanceof r)return this.raw=$.value,this.set=[[$]],this.format(),this;if(this.options=L,this.loose=!!L.loose,this.includePrerelease=!!L.includePrerelease,this.raw=$.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(R=>this.parseRange(R.trim())).filter(R=>R.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const R=this.set[0];if(this.set=this.set.filter(P=>!g(P[0])),this.set.length===0)this.set=[R];else if(this.set.length>1){for(const P of this.set)if(P.length===1&&h(P[0])){this.set=[P];break}}}this.format()}format(){return this.range=this.set.map($=>$.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange($){const R=((this.options.includePrerelease&&d)|(this.options.loose&&p))+":"+$,P=n.get(R);if(P)return P;const O=this.options.loose,D=O?a[c.HYPHENRANGELOOSE]:a[c.HYPHENRANGE];$=$.replace(D,de(this.options.includePrerelease)),s("hyphen replace",$),$=$.replace(a[c.COMPARATORTRIM],l),s("comparator trim",$),$=$.replace(a[c.TILDETRIM],u),s("tilde trim",$),$=$.replace(a[c.CARETTRIM],f),s("caret trim",$);let q=$.split(" ").map(ie=>y(ie,this.options)).join(" ").split(/\s+/).map(ie=>ne(ie,this.options));O&&(q=q.filter(ie=>(s("loose invalid filter",ie,this.options),!!ie.match(a[c.COMPARATORLOOSE])))),s("range list",q);const U=new Map,Q=q.map(ie=>new r(ie,this.options));for(const ie of Q){if(g(ie))return[ie];U.set(ie.value,ie)}U.size>1&&U.has("")&&U.delete("");const Ee=[...U.values()];return n.set(R,Ee),Ee}intersects($,L){if(!($ instanceof e))throw new TypeError("a Range is required");return this.set.some(R=>m(R,L)&&$.set.some(P=>m(P,L)&&R.every(O=>P.every(D=>O.intersects(D,L)))))}test($){if(!$)return!1;if(typeof $=="string")try{$=new o($,this.options)}catch{return!1}for(let L=0;L<this.set.length;L++)if(Se(this.set[L],$,this.options))return!0;return!1}}Hs=e;const t=K$,n=new t({max:1e3}),i=Qa,r=z1(),s=Ss,o=tc,{safeRe:a,t:c,comparatorTrimReplace:l,tildeTrimReplace:u,caretTrimReplace:f}=ec,{FLAG_INCLUDE_PRERELEASE:d,FLAG_LOOSE:p}=Za,g=F=>F.value==="<0.0.0-0",h=F=>F.value==="",m=(F,$)=>{let L=!0;const R=F.slice();let P=R.pop();for(;L&&R.length;)L=R.every(O=>P.intersects(O,$)),P=R.pop();return L},y=(F,$)=>(s("comp",F,$),F=x(F,$),s("caret",F),F=k(F,$),s("tildes",F),F=C(F,$),s("xrange",F),F=Y(F,$),s("stars",F),F),b=F=>!F||F.toLowerCase()==="x"||F==="*",k=(F,$)=>F.trim().split(/\s+/).map(L=>T(L,$)).join(" "),T=(F,$)=>{const L=$.loose?a[c.TILDELOOSE]:a[c.TILDE];return F.replace(L,(R,P,O,D,q)=>{s("tilde",F,R,P,O,D,q);let U;return b(P)?U="":b(O)?U=`>=${P}.0.0 <${+P+1}.0.0-0`:b(D)?U=`>=${P}.${O}.0 <${P}.${+O+1}.0-0`:q?(s("replaceTilde pr",q),U=`>=${P}.${O}.${D}-${q} <${P}.${+O+1}.0-0`):U=`>=${P}.${O}.${D} <${P}.${+O+1}.0-0`,s("tilde return",U),U})},x=(F,$)=>F.trim().split(/\s+/).map(L=>_(L,$)).join(" "),_=(F,$)=>{s("caret",F,$);const L=$.loose?a[c.CARETLOOSE]:a[c.CARET],R=$.includePrerelease?"-0":"";return F.replace(L,(P,O,D,q,U)=>{s("caret",F,P,O,D,q,U);let Q;return b(O)?Q="":b(D)?Q=`>=${O}.0.0${R} <${+O+1}.0.0-0`:b(q)?O==="0"?Q=`>=${O}.${D}.0${R} <${O}.${+D+1}.0-0`:Q=`>=${O}.${D}.0${R} <${+O+1}.0.0-0`:U?(s("replaceCaret pr",U),O==="0"?D==="0"?Q=`>=${O}.${D}.${q}-${U} <${O}.${D}.${+q+1}-0`:Q=`>=${O}.${D}.${q}-${U} <${O}.${+D+1}.0-0`:Q=`>=${O}.${D}.${q}-${U} <${+O+1}.0.0-0`):(s("no pr"),O==="0"?D==="0"?Q=`>=${O}.${D}.${q}${R} <${O}.${D}.${+q+1}-0`:Q=`>=${O}.${D}.${q}${R} <${O}.${+D+1}.0-0`:Q=`>=${O}.${D}.${q} <${+O+1}.0.0-0`),s("caret return",Q),Q})},C=(F,$)=>(s("replaceXRanges",F,$),F.split(/\s+/).map(L=>N(L,$)).join(" ")),N=(F,$)=>{F=F.trim();const L=$.loose?a[c.XRANGELOOSE]:a[c.XRANGE];return F.replace(L,(R,P,O,D,q,U)=>{s("xRange",F,R,P,O,D,q,U);const Q=b(O),Ee=Q||b(D),ie=Ee||b(q),sn=ie;return P==="="&&sn&&(P=""),U=$.includePrerelease?"-0":"",Q?P===">"||P==="<"?R="<0.0.0-0":R="*":P&&sn?(Ee&&(D=0),q=0,P===">"?(P=">=",Ee?(O=+O+1,D=0,q=0):(D=+D+1,q=0)):P==="<="&&(P="<",Ee?O=+O+1:D=+D+1),P==="<"&&(U="-0"),R=`${P+O}.${D}.${q}${U}`):Ee?R=`>=${O}.0.0${U} <${+O+1}.0.0-0`:ie&&(R=`>=${O}.${D}.0${U} <${O}.${+D+1}.0-0`),s("xRange return",R),R})},Y=(F,$)=>(s("replaceStars",F,$),F.trim().replace(a[c.STAR],"")),ne=(F,$)=>(s("replaceGTE0",F,$),F.trim().replace(a[$.includePrerelease?c.GTE0PRE:c.GTE0],"")),de=F=>($,L,R,P,O,D,q,U,Q,Ee,ie,sn,Es)=>(b(R)?L="":b(P)?L=`>=${R}.0.0${F?"-0":""}`:b(O)?L=`>=${R}.${P}.0${F?"-0":""}`:D?L=`>=${L}`:L=`>=${L}${F?"-0":""}`,b(Q)?U="":b(Ee)?U=`<${+Q+1}.0.0-0`:b(ie)?U=`<${Q}.${+Ee+1}.0-0`:sn?U=`<=${Q}.${Ee}.${ie}-${sn}`:F?U=`<${Q}.${Ee}.${+ie+1}-0`:U=`<=${U}`,`${L} ${U}`.trim()),Se=(F,$,L)=>{for(let R=0;R<F.length;R++)if(!F[R].test($))return!1;if($.prerelease.length&&!L.includePrerelease){for(let R=0;R<F.length;R++)if(s(F[R].semver),F[R].semver!==r.ANY&&F[R].semver.prerelease.length>0){const P=F[R].semver;if(P.major===$.major&&P.minor===$.minor&&P.patch===$.patch)return!0}return!1}return!0};return Hs}const D1=vp(),j1=(e,t,n)=>{try{t=new D1(t,n)}catch{return!1}return t.test(e)};var M1=j1,Sp=U$(M1);function U1(e,t,n){const i=e.open(t),r=1e4,s=250,{origin:o}=new URL(t);let a=~~(r/s);function c(u){u.source===i&&(a=0,e.removeEventListener("message",c,!1))}e.addEventListener("message",c,!1);function l(){a<=0||(i.postMessage(n,o),setTimeout(l,s),a-=1)}setTimeout(l,s)}var B1=`.vega-embed {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
}
.vega-embed.has-actions {
  padding-right: 38px;
}
.vega-embed details:not([open]) > :not(summary) {
  display: none !important;
}
.vega-embed summary {
  list-style: none;
  position: absolute;
  top: 0;
  right: 0;
  padding: 6px;
  z-index: 1000;
  background: white;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
  color: #1b1e23;
  border: 1px solid #aaa;
  border-radius: 999px;
  opacity: 0.2;
  transition: opacity 0.4s ease-in;
  cursor: pointer;
  line-height: 0px;
}
.vega-embed summary::-webkit-details-marker {
  display: none;
}
.vega-embed summary:active {
  box-shadow: #aaa 0px 0px 0px 1px inset;
}
.vega-embed summary svg {
  width: 14px;
  height: 14px;
}
.vega-embed details[open] summary {
  opacity: 0.7;
}
.vega-embed:hover summary, .vega-embed:focus-within summary {
  opacity: 1 !important;
  transition: opacity 0.2s ease;
}
.vega-embed .vega-actions {
  position: absolute;
  z-index: 1001;
  top: 35px;
  right: -9px;
  display: flex;
  flex-direction: column;
  padding-bottom: 8px;
  padding-top: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #d9d9d9;
  background: white;
  animation-duration: 0.15s;
  animation-name: scale-in;
  animation-timing-function: cubic-bezier(0.2, 0, 0.13, 1.5);
  text-align: left;
}
.vega-embed .vega-actions a {
  padding: 8px 16px;
  font-family: sans-serif;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  color: #434a56;
  text-decoration: none;
}
.vega-embed .vega-actions a:hover, .vega-embed .vega-actions a:focus {
  background-color: #f7f7f9;
  color: black;
}
.vega-embed .vega-actions::before, .vega-embed .vega-actions::after {
  content: "";
  display: inline-block;
  position: absolute;
}
.vega-embed .vega-actions::before {
  left: auto;
  right: 14px;
  top: -16px;
  border: 8px solid rgba(0, 0, 0, 0);
  border-bottom-color: #d9d9d9;
}
.vega-embed .vega-actions::after {
  left: auto;
  right: 15px;
  top: -14px;
  border: 7px solid rgba(0, 0, 0, 0);
  border-bottom-color: #fff;
}
.vega-embed .chart-wrapper.fit-x {
  width: 100%;
}
.vega-embed .chart-wrapper.fit-y {
  height: 100%;
}

.vega-embed-wrapper {
  max-width: 100%;
  overflow: auto;
  padding-right: 14px;
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.6);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
`;function Ep(e,...t){for(const n of t)W1(e,n);return e}function W1(e,t){for(const n of Object.keys(t))$o(e,n,t[n],!0)}const at=Lp;let Bi=jE;const Bl=typeof window<"u"?window:void 0;Bi===void 0&&Bl?.vl?.compile&&(Bi=Bl.vl);const G1={export:{svg:!0,png:!0},source:!0,compiled:!0,editor:!0},q1={CLICK_TO_VIEW_ACTIONS:"Click to view actions",COMPILED_ACTION:"View Compiled Vega",EDITOR_ACTION:"Open in Vega Editor",PNG_ACTION:"Save as PNG",SOURCE_ACTION:"View Source",SVG_ACTION:"Save as SVG"},Oi={vega:"Vega","vega-lite":"Vega-Lite"},Or={vega:at.version,"vega-lite":Bi?Bi.version:"not available"},H1={vega:e=>e,"vega-lite":(e,t)=>Bi.compile(e,{config:t}).spec},V1=`
<svg viewBox="0 0 16 16" fill="currentColor" stroke="none" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
  <circle r="2" cy="8" cx="2"></circle>
  <circle r="2" cy="8" cx="8"></circle>
  <circle r="2" cy="8" cx="14"></circle>
</svg>`,X1="chart-wrapper";function Y1(e){return typeof e=="function"}function Wl(e,t,n,i){const r=`<html><head>${t}</head><body><pre><code class="json">`,s=`</code></pre>${n}</body></html>`,o=window.open("");o.document.write(r+e+s),o.document.title=`${Oi[i]} JSON Source`}function K1(e,t){if(e.$schema){const n=Yl(e.$schema);t&&t!==n.library&&console.warn(`The given visualization spec is written in ${Oi[n.library]}, but mode argument sets ${Oi[t]??t}.`);const i=n.library;return Sp(Or[i],`^${n.version.slice(1)}`)||console.warn(`The input spec uses ${Oi[i]} ${n.version}, but the current version of ${Oi[i]} is v${Or[i]}.`),i}return"mark"in e||"encoding"in e||"layer"in e||"hconcat"in e||"vconcat"in e||"facet"in e||"repeat"in e?"vega-lite":"marks"in e||"signals"in e||"scales"in e||"axes"in e?"vega":t??"vega"}function $p(e){return!!(e&&"load"in e)}function Gl(e){return $p(e)?e:at.loader(e)}function J1(e){const t=e.usermeta?.embedOptions??{};return z(t.defaultStyle)&&(t.defaultStyle=!1),t}async function Q1(e,t,n={}){let i,r;z(t)?(r=Gl(n.loader),i=JSON.parse(await r.load(t))):i=t;const s=J1(i),o=s.loader;(!r||o)&&(r=Gl(n.loader??o));const a=await ql(s,r),c=await ql(n,r),l={...Ep(c,a),config:Ar(c.config??{},a.config??{})};return await ew(e,i,l,r)}async function ql(e,t){const n=z(e.config)?JSON.parse(await t.load(e.config)):e.config??{},i=z(e.patch)?JSON.parse(await t.load(e.patch)):e.patch;return{...e,...i?{patch:i}:{},...n?{config:n}:{}}}function Z1(e){const t=e.getRootNode?e.getRootNode():document;return t instanceof ShadowRoot?{root:t,rootContainer:t}:{root:document,rootContainer:document.head??document.body}}async function ew(e,t,n={},i){const r=n.theme?Ar(M$[n.theme],n.config??{}):n.config,s=di(n.actions)?n.actions:Ep({},G1,n.actions??{}),o={...q1,...n.i18n},a=n.renderer??"canvas",c=n.logLevel??at.Warn,l=n.downloadFileName??"visualization",u=typeof e=="string"?document.querySelector(e):e;if(!u)throw new Error(`${e} does not exist`);if(n.defaultStyle!==!1){const x="vega-embed-style",{root:_,rootContainer:C}=Z1(u);if(!_.getElementById(x)){const N=document.createElement("style");N.id=x,N.innerHTML=n.defaultStyle===void 0||n.defaultStyle===!0?B1.toString():n.defaultStyle,C.appendChild(N)}}const f=K1(t,n.mode);let d=H1[f](t,r);if(f==="vega-lite"&&d.$schema){const x=Yl(d.$schema);Sp(Or.vega,`^${x.version.slice(1)}`)||console.warn(`The compiled spec uses Vega ${x.version}, but current version is v${Or.vega}.`)}u.classList.add("vega-embed"),s&&u.classList.add("has-actions"),u.innerHTML="";let p=u;if(s){const x=document.createElement("div");x.classList.add(X1),u.appendChild(x),p=x}const g=n.patch;if(g&&(d=g instanceof Function?g(d):Pp(d,g,!0,!1).newDocument),n.formatLocale&&at.formatLocale(n.formatLocale),n.timeFormatLocale&&at.timeFormatLocale(n.timeFormatLocale),n.expressionFunctions)for(const x in n.expressionFunctions){const _=n.expressionFunctions[x];"fn"in _?at.expressionFunction(x,_.fn,_.visitor):_ instanceof Function&&at.expressionFunction(x,_)}const{ast:h}=n,m=at.parse(d,f==="vega-lite"?{}:r,{ast:h}),y=new(n.viewClass||at.View)(m,{loader:i,logLevel:c,renderer:a,...h?{expr:at.expressionInterpreter??n.expr??zp}:{}});if(y.addSignalListener("autosize",(x,_)=>{const{type:C}=_;C=="fit-x"?(p.classList.add("fit-x"),p.classList.remove("fit-y")):C=="fit-y"?(p.classList.remove("fit-x"),p.classList.add("fit-y")):C=="fit"?p.classList.add("fit-x","fit-y"):p.classList.remove("fit-x","fit-y")}),n.tooltip!==!1){const{loader:x,tooltip:_}=n,C=x&&!$p(x)?x?.baseURL:void 0,N=Y1(_)?_:new Dp({baseURL:C,..._===!0?{}:_}).call;y.tooltip(N)}let{hover:b}=n;if(b===void 0&&(b=f==="vega"),b){const{hoverSet:x,updateSet:_}=typeof b=="boolean"?{}:b;y.hover(x,_)}n&&(n.width!=null&&y.width(n.width),n.height!=null&&y.height(n.height),n.padding!=null&&y.padding(n.padding)),await y.initialize(p,n.bind).runAsync();let k;if(s!==!1){let x=u;if(n.defaultStyle!==!1||n.forceActionsMenu){const C=document.createElement("details");C.title=o.CLICK_TO_VIEW_ACTIONS,u.append(C),x=C;const N=document.createElement("summary");N.innerHTML=V1,C.append(N),k=Y=>{C.contains(Y.target)||C.removeAttribute("open")},document.addEventListener("click",k)}const _=document.createElement("div");if(x.append(_),_.classList.add("vega-actions"),s===!0||s.export!==!1){for(const C of["svg","png"])if(s===!0||s.export===!0||s.export[C]){const N=o[`${C.toUpperCase()}_ACTION`],Y=document.createElement("a"),ne=V(n.scaleFactor)?n.scaleFactor[C]:n.scaleFactor;Y.text=N,Y.href="#",Y.target="_blank",Y.download=`${l}.${C}`,Y.addEventListener("mousedown",async function(de){de.preventDefault();const Se=await y.toImageURL(C,ne);this.href=Se}),_.append(Y)}}if(s===!0||s.source!==!1){const C=document.createElement("a");C.text=o.SOURCE_ACTION,C.href="#",C.addEventListener("click",function(N){Wl(ws(t),n.sourceHeader??"",n.sourceFooter??"",f),N.preventDefault()}),_.append(C)}if(f==="vega-lite"&&(s===!0||s.compiled!==!1)){const C=document.createElement("a");C.text=o.COMPILED_ACTION,C.href="#",C.addEventListener("click",function(N){Wl(ws(d),n.sourceHeader??"",n.sourceFooter??"","vega"),N.preventDefault()}),_.append(C)}if(s===!0||s.editor!==!1){const C=n.editorUrl??"https://vega.github.io/editor/",N=document.createElement("a");N.text=o.EDITOR_ACTION,N.href="#",N.addEventListener("click",function(Y){U1(window,C,{config:r,mode:f,renderer:a,spec:ws(t)}),Y.preventDefault()}),_.append(N)}}function T(){k&&document.removeEventListener("click",k),y.finalize()}return{view:y,spec:t,vgSpec:d,finalize:T,embedOptions:n}}const{SvelteComponent:tw,append:xo,attr:vo,binding_callbacks:Hl,detach:wp,element:So,flush:Wn,init:nw,insert:Cp,noop:Vl,safe_not_equal:iw,set_data:rw,space:sw,text:ow}=window.__gradio__svelte__internal,{onMount:aw,onDestroy:cw}=window.__gradio__svelte__internal;function Xl(e){let t,n;return{c(){t=So("div"),n=ow(e[0]),vo(t,"class","caption layout svelte-1qhqpn7")},m(i,r){Cp(i,t,r),xo(t,n)},p(i,r){r&1&&rw(n,i[0])},d(i){i&&wp(t)}}}function lw(e){let t,n,i,r=e[0]&&Xl(e);return{c(){t=So("div"),n=So("div"),i=sw(),r&&r.c(),vo(t,"data-testid","altair"),vo(t,"class","altair layout svelte-1qhqpn7")},m(s,o){Cp(s,t,o),xo(t,n),e[11](n),xo(t,i),r&&r.m(t,null),e[12](t)},p(s,[o]){s[0]?r?r.p(s,o):(r=Xl(s),r.c(),r.m(t,null)):r&&(r.d(1),r=null)},i:Vl,o:Vl,d(s){s&&wp(t),e[11](null),r&&r.d(),e[12](null)}}}function uw(e,t,n){let i,r,s,{value:o}=t,{colors:a=[]}=t,{caption:c}=t,{show_actions_button:l}=t,{gradio:u}=t,f,d,p,{_selectable:g}=t,h=window.getComputedStyle(document.body),m,y;const b=()=>Math.min(d.offsetWidth,y||d.offsetWidth);let k=()=>{};const T=()=>{s&&n(9,r.width=b(),r),Q1(f,r,{actions:l}).then(function(N){if(p=N.view,k=()=>{p.signal("width",b()).run()},!g)return;const Y=(ne,de)=>{const Se=p.signal("brush");if(Se)if(Object.keys(Se).length===0)u.dispatch("select",{value:null,index:null,selected:!1});else{const F=Object.keys(Se)[0];let $=Se[F].map(L=>L/1e3);u.dispatch("select",{value:Se,index:$,selected:!0})}};p.addEventListener("mouseup",Y),p.addEventListener("touchup",Y)})};let x=new ResizeObserver(()=>{s&&r.width!==d.offsetWidth&&k()});aw(()=>{T(),x.observe(d)}),cw(()=>{x.disconnect()});function _(N){Hl[N?"unshift":"push"](()=>{f=N,n(1,f)})}function C(N){Hl[N?"unshift":"push"](()=>{d=N,n(2,d)})}return e.$$set=N=>{"value"in N&&n(3,o=N.value),"colors"in N&&n(4,a=N.colors),"caption"in N&&n(0,c=N.caption),"show_actions_button"in N&&n(5,l=N.show_actions_button),"gradio"in N&&n(6,u=N.gradio),"_selectable"in N&&n(7,g=N._selectable)},e.$$.update=()=>{e.$$.dirty&8&&n(10,i=o?.plot),e.$$.dirty&1024&&n(9,r=JSON.parse(i)),e.$$.dirty&640&&r&&r.params&&!g&&n(9,r.params=r.params.filter(N=>N.name!=="brush"),r),e.$$.dirty&536&&o.chart&&n(9,r=jp(r,h,o.chart,a)),e.$$.dirty&768&&m!==r&&(n(8,m=r),y=r.width),e.$$.dirty&520&&(s=!(r.encoding?.column?.field||r.encoding?.row?.field||o.chart===void 0))},[c,f,d,o,a,l,u,g,m,r,i,_,C]}class o0 extends tw{constructor(t){super(),nw(this,t,uw,lw,iw,{value:3,colors:4,caption:0,show_actions_button:5,gradio:6,_selectable:7})}get value(){return this.$$.ctx[3]}set value(t){this.$$set({value:t}),Wn()}get colors(){return this.$$.ctx[4]}set colors(t){this.$$set({colors:t}),Wn()}get caption(){return this.$$.ctx[0]}set caption(t){this.$$set({caption:t}),Wn()}get show_actions_button(){return this.$$.ctx[5]}set show_actions_button(t){this.$$set({show_actions_button:t}),Wn()}get gradio(){return this.$$.ctx[6]}set gradio(t){this.$$set({gradio:t}),Wn()}get _selectable(){return this.$$.ctx[7]}set _selectable(t){this.$$set({_selectable:t}),Wn()}}export{o0 as default};
//# sourceMappingURL=AltairPlot-CrFfYV5E.js.map
