import { c as create_ssr_component, e as escape } from './ssr-C3HYbsxA.js';

const m=create_ssr_component((f,t,e,c)=>{let{title:l}=t,{x:o}=t,{y:x}=t;return t.title===void 0&&e.title&&l!==void 0&&e.title(l),t.x===void 0&&e.x&&o!==void 0&&e.x(o),t.y===void 0&&e.y&&x!==void 0&&e.y(x),`${l?`${escape(l)}`:`${escape(o)} x ${escape(x)}`}`});

export { m as default };
//# sourceMappingURL=Example2-Biy8EGGC.js.map
