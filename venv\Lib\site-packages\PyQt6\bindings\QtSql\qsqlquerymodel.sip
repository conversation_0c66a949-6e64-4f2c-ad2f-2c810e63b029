// qsqlquerymodel.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlQueryModel : public QAbstractTableModel
{
%TypeHeaderCode
#include <qsqlquerymodel.h>
%End

public:
    explicit QSqlQueryModel(QObject *parent /TransferThis/ = 0);
    virtual ~QSqlQueryModel();
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const;
    QSqlRecord record() const;
    QSqlRecord record(int row) const;
    virtual QVariant data(const QModelIndex &item, int role = Qt::DisplayRole) const;
    virtual QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const;
    virtual bool setHeaderData(int section, Qt::Orientation orientation, const QVariant &value, int role = Qt::EditRole);
    virtual bool insertColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    void setQuery(const QSqlQuery &query);
    void setQuery(const QString &query, const QSqlDatabase &db = QSqlDatabase());
%If (Qt_6_5_0 -)
    const QSqlQuery &query() const;
%MethodCode
        sipRes = const_cast<QSqlQuery *>(&sipCpp->query(Qt::Disambiguated));
%End

%End
%If (- Qt_6_5_0)
    QSqlQuery query() const;
%End
    virtual void clear();
    QSqlError lastError() const;
    virtual void fetchMore(const QModelIndex &parent = QModelIndex());
    virtual bool canFetchMore(const QModelIndex &parent = QModelIndex()) const;

protected:
    virtual void queryChange();
    virtual QModelIndex indexInQuery(const QModelIndex &item) const;
    void setLastError(const QSqlError &error);
    void beginResetModel();
    void endResetModel();
    void beginInsertRows(const QModelIndex &parent, int first, int last);
    void endInsertRows();
    void beginRemoveRows(const QModelIndex &parent, int first, int last);
    void endRemoveRows();
    void beginInsertColumns(const QModelIndex &parent, int first, int last);
    void endInsertColumns();
    void beginRemoveColumns(const QModelIndex &parent, int first, int last);
    void endRemoveColumns();

public:
    virtual QHash<int, QByteArray> roleNames() const;
%If (Qt_6_9_0 -)
    void refresh();
%End
};
