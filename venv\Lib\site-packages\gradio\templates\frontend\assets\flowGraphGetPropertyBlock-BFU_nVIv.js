import{R as s}from"./declarationMapper-BZjsjg7g.js";import{R as a}from"./index-Dpxo-yl_.js";import{F as i}from"./flowGraphCachedOperationBlock-DbEh2T66.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./KHR_interactivity-DTxiAnOo.js";import"./objectModelMapping-BR4RdEzn.js";class u extends i{constructor(t){super(s,t),this.config=t,this.object=this.registerDataInput("object",s,t.object),this.propertyName=this.registerDataInput("propertyName",s,t.propertyName),this.customGetFunction=this.registerDataInput("customGetFunction",s)}_doOperation(t){const o=this.customGetFunction.getValue(t);let r;if(o)r=o(this.object.getValue(t),this.propertyName.getValue(t),t);else{const e=this.object.getValue(t),p=this.propertyName.getValue(t);r=e&&p?this._getPropertyValue(e,p):void 0}return r}_getPropertyValue(t,o){const r=o.split(".");let e=t;for(const p of r)if(e=e[p],e===void 0)return;return e}getClassName(){return"FlowGraphGetPropertyBlock"}}a("FlowGraphGetPropertyBlock",u);export{u as FlowGraphGetPropertyBlock};
//# sourceMappingURL=flowGraphGetPropertyBlock-BFU_nVIv.js.map
