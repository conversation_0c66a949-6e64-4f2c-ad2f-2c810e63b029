import { c as create_ssr_component, o as onDestroy, d as add_attribute, e as escape$1 } from './ssr-C3HYbsxA.js';
import { T as Tr$1, am as ea$1 } from './2-DJbI4FWc.js';
import { t } from './color-C4FRpKJ1.js';
import { X as X$1, M as Mr$1, x as xt$1, f as fg$1, c as cc$1, Y as Y$1, U as U$1, v as v3, L as LH, e as et$1, a as x3, p as p3, A as Ag$1, H as H$1, t as t5, g as ge$1, b as pP, d as tt$1 } from './vega-interpreter.module-D8ONVnUK.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';
import './dispatch-DGVRt-1i.js';

function wp(e,t,n,i){let r=t.getPropertyValue("--color-accent"),s=t.getPropertyValue("--body-text-color"),o=t.getPropertyValue("--border-color-primary"),a=t.fontFamily,c=t.getPropertyValue("--block-title-text-weight");const l=h=>h.endsWith("px")?parseFloat(h.slice(0,-2)):12;let u=l(t.getPropertyValue("--text-md")),f=l(t.getPropertyValue("--text-sm")),d={autosize:{type:"fit",contains:"padding"},axis:{labelFont:a,labelColor:s,titleFont:a,titleColor:s,tickColor:o,labelFontSize:f,gridColor:o,titleFontWeight:"normal",titleFontSize:f,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:s,labelFont:a,titleColor:s,titleFont:a,titleFontWeight:"normal",titleFontSize:f,labelFontWeight:"normal",offset:2},title:{color:s,font:a,fontSize:u,fontWeight:c,anchor:"middle"},view:{stroke:o}};e.config=d;let g=e.encoding,p=e.layer;switch(n){case"scatter":e.config.mark={stroke:r},g.color&&g.color.type=="nominal"?g.color.scale.range=g.color.scale.range.map((h,m)=>Ss(i,m)):g.color&&g.color.type=="quantitative"&&(g.color.scale.range=["#eff6ff","#1e3a8a"],g.color.scale.range.interpolate="hsl");break;case"line":e.config.mark={stroke:r,cursor:"crosshair"},p.forEach(h=>{h.encoding.color&&(h.encoding.color.scale.range=h.encoding.color.scale.range.map((m,y)=>Ss(i,y)));});break;case"bar":e.config.mark={opacity:.8,fill:r},g.color&&(g.color.scale.range=g.color.scale.range.map((h,m)=>Ss(i,m)));break}return e}function Ss(e,t$1){let n=e[t$1%e.length];return n&&n in ea$1?ea$1[n]?.primary:n||ea$1[t(t$1)].primary}const Ap="vega-lite",Cp='Dominik Moritz, Kanit "Ham" Wongsuphasawat, Arvind Satyanarayan, Jeffrey Heer',Np="5.12.0",Fp=["Kanit Wongsuphasawat (http://kanitw.yellowpigz.com)","Dominik Moritz (https://www.domoritz.de)","Arvind Satyanarayan (https://arvindsatya.com)","Jeffrey Heer (https://jheer.org)"],_p="https://vega.github.io/vega-lite/",kp="Vega-Lite is a concise high-level language for interactive visualization.",Op=["vega","chart","visualization"],Tp="build/vega-lite.js",Ip="build/vega-lite.min.js",Rp="build/vega-lite.min.js",Pp="build/src/index",Lp="build/src/index.d.ts",zp={vl2pdf:"./bin/vl2pdf",vl2png:"./bin/vl2png",vl2svg:"./bin/vl2svg",vl2vg:"./bin/vl2vg"},Dp=["bin","build","src","vega-lite*","tsconfig.json"],Mp={changelog:"conventional-changelog -p angular -r 2",prebuild:"yarn clean:build",build:"yarn build:only","build:only":"tsc -p tsconfig.build.json && rollup -c","prebuild:examples":"yarn build:only","build:examples":"yarn data && TZ=America/Los_Angeles scripts/build-examples.sh","prebuild:examples-full":"yarn build:only","build:examples-full":"TZ=America/Los_Angeles scripts/build-examples.sh 1","build:example":"TZ=America/Los_Angeles scripts/build-example.sh","build:toc":"yarn build:jekyll && scripts/generate-toc","build:site":"rollup -c site/rollup.config.mjs","build:jekyll":"pushd site && bundle exec jekyll build -q && popd","build:versions":"scripts/update-version.sh",clean:"yarn clean:build && del-cli 'site/data/*' 'examples/compiled/*.png' && find site/examples ! -name 'index.md' ! -name 'data' -type f -delete","clean:build":"del-cli 'build/*' !build/vega-lite-schema.json",data:"rsync -r node_modules/vega-datasets/data/* site/data",schema:"mkdir -p build && ts-json-schema-generator -f tsconfig.json -p src/index.ts -t TopLevelSpec --no-type-check --no-ref-encode > build/vega-lite-schema.json && yarn renameschema && cp build/vega-lite-schema.json site/_data/",renameschema:"scripts/rename-schema.sh",presite:"yarn data && yarn schema && yarn build:site && yarn build:versions && scripts/create-example-pages.sh",site:"yarn site:only","site:only":"pushd site && bundle exec jekyll serve -I -l && popd",prettierbase:"prettier '**/*.{md,css,yml}'",format:"eslint . --fix && yarn prettierbase --write",lint:"eslint . && yarn prettierbase --check",jest:"NODE_OPTIONS=--experimental-vm-modules npx jest",test:"yarn jest test/ && yarn lint && yarn schema && yarn jest examples/ && yarn test:runtime","test:cover":"yarn jest --collectCoverage test/","test:inspect":"node --inspect-brk --experimental-vm-modules ./node_modules/.bin/jest --runInBand test","test:runtime":"NODE_OPTIONS=--experimental-vm-modules TZ=America/Los_Angeles npx jest test-runtime/ --config test-runtime/jest-config.json","test:runtime:generate":"yarn build:only && del-cli test-runtime/resources && VL_GENERATE_TESTS=true yarn test:runtime",watch:"tsc -p tsconfig.build.json -w","watch:site":"yarn build:site -w","watch:test":"yarn jest --watch test/","watch:test:runtime":"NODE_OPTIONS=--experimental-vm-modules TZ=America/Los_Angeles npx jest --watch test-runtime/ --config test-runtime/jest-config.json",release:"release-it"},jp={type:"git",url:"https://github.com/vega/vega-lite.git"},Bp="BSD-3-Clause",Up={url:"https://github.com/vega/vega-lite/issues"},Wp={"@babel/core":"^7.21.8","@babel/plugin-proposal-class-properties":"^7.18.6","@babel/preset-env":"^7.21.5","@babel/preset-typescript":"^7.21.5","@release-it/conventional-changelog":"^5.1.1","@rollup/plugin-alias":"^5.0.0","@rollup/plugin-babel":"^6.0.3","@rollup/plugin-commonjs":"^25.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.0.2","@rollup/plugin-terser":"^0.4.1","@types/chai":"^4.3.5","@types/d3":"^7.4.0","@types/jest":"^27.4.1","@types/pako":"^2.0.0","@typescript-eslint/eslint-plugin":"^5.59.5","@typescript-eslint/parser":"^5.59.5",ajv:"^8.12.0","ajv-formats":"^2.1.1",chai:"^4.3.7",cheerio:"^1.0.0-rc.12","conventional-changelog-cli":"^3.0.0",d3:"^7.8.4","del-cli":"^5.0.0",eslint:"^8.40.0","eslint-config-prettier":"^8.8.0","eslint-plugin-jest":"^27.2.1","eslint-plugin-prettier":"^4.2.1","highlight.js":"^11.8.0",jest:"^27.5.1","jest-dev-server":"^6.1.1",mkdirp:"^3.0.1",pako:"^2.1.0",prettier:"^2.8.8",puppeteer:"^15.0.0","release-it":"^15.10.3",rollup:"^3.21.6","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-sourcemaps":"^0.6.3",serve:"^14.2.0",terser:"^5.17.3","ts-jest":"^29.1.0","ts-json-schema-generator":"^1.2.0",typescript:"~4.9.5","vega-cli":"^5.25.0","vega-datasets":"^2.7.0","vega-embed":"^6.22.1","vega-tooltip":"^0.32.0","yaml-front-matter":"^4.1.1"},Gp={"@types/clone":"~2.1.1",clone:"~2.1.2","fast-deep-equal":"~3.1.3","fast-json-stable-stringify":"~2.1.0","json-stringify-pretty-compact":"~3.0.0",tslib:"~2.5.0","vega-event-selector":"~3.0.1","vega-expression":"~5.1.0","vega-util":"~1.17.2",yargs:"~17.7.2"},qp={vega:"^5.24.0"},Hp={node:">=16"},Vp={name:Ap,author:Cp,version:Np,collaborators:Fp,homepage:_p,description:kp,keywords:Op,main:Tp,unpkg:Ip,jsdelivr:Rp,module:Pp,types:Lp,bin:zp,files:Dp,scripts:Mp,repository:jp,license:Bp,bugs:Up,devDependencies:Wp,dependencies:Gp,peerDependencies:qp,engines:Hp};var Dl={exports:{}};(function(e){var t=function(){function n(d,g){return g!=null&&d instanceof g}var i;try{i=Map;}catch{i=function(){};}var r;try{r=Set;}catch{r=function(){};}var s;try{s=Promise;}catch{s=function(){};}function o(d,g,p,h,m){typeof g=="object"&&(p=g.depth,h=g.prototype,m=g.includeNonEnumerable,g=g.circular);var y=[],b=[],O=typeof Buffer<"u";typeof g>"u"&&(g=!0),typeof p>"u"&&(p=1/0);function C(w,M){if(w===null)return null;if(M===0)return w;var D,K;if(typeof w!="object")return w;if(n(w,i))D=new i;else if(n(w,r))D=new r;else if(n(w,s))D=new s(function(T,N){w.then(function(P){T(C(P,M-1));},function(P){N(C(P,M-1));});});else if(o.__isArray(w))D=[];else if(o.__isRegExp(w))D=new RegExp(w.source,f(w)),w.lastIndex&&(D.lastIndex=w.lastIndex);else if(o.__isDate(w))D=new Date(w.getTime());else {if(O&&Buffer.isBuffer(w))return Buffer.allocUnsafe?D=Buffer.allocUnsafe(w.length):D=new Buffer(w.length),w.copy(D),D;n(w,Error)?D=Object.create(w):typeof h>"u"?(K=Object.getPrototypeOf(w),D=Object.create(K)):(D=Object.create(h),K=h);}if(g){var ve=y.indexOf(w);if(ve!=-1)return b[ve];y.push(w),b.push(D);}n(w,i)&&w.forEach(function(T,N){var P=C(N,M-1),G=C(T,M-1);D.set(P,G);}),n(w,r)&&w.forEach(function(T){var N=C(T,M-1);D.add(N);});for(var se in w){var ce;K&&(ce=Object.getOwnPropertyDescriptor(K,se)),!(ce&&ce.set==null)&&(D[se]=C(w[se],M-1));}if(Object.getOwnPropertySymbols)for(var rt=Object.getOwnPropertySymbols(w),se=0;se<rt.length;se++){var A=rt[se],E=Object.getOwnPropertyDescriptor(w,A);E&&!E.enumerable&&!m||(D[A]=C(w[A],M-1),E.enumerable||Object.defineProperty(D,A,{enumerable:!1}));}if(m)for(var I=Object.getOwnPropertyNames(w),se=0;se<I.length;se++){var _=I[se],E=Object.getOwnPropertyDescriptor(w,_);E&&E.enumerable||(D[_]=C(w[_],M-1),Object.defineProperty(D,_,{enumerable:!1}));}return D}return C(d,p)}o.clonePrototype=function(g){if(g===null)return null;var p=function(){};return p.prototype=g,new p};function a(d){return Object.prototype.toString.call(d)}o.__objToStr=a;function c(d){return typeof d=="object"&&a(d)==="[object Date]"}o.__isDate=c;function l(d){return typeof d=="object"&&a(d)==="[object Array]"}o.__isArray=l;function u(d){return typeof d=="object"&&a(d)==="[object RegExp]"}o.__isRegExp=u;function f(d){var g="";return d.global&&(g+="g"),d.ignoreCase&&(g+="i"),d.multiline&&(g+="m"),g}return o.__getRegExpFlags=f,o}();e.exports&&(e.exports=t);})(Dl);var Xp=Dl.exports;const Yp=Tr$1(Xp);var Kp=function e(t,n){if(t===n)return !0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return !1;var i,r,s;if(Array.isArray(t)){if(i=t.length,i!=n.length)return !1;for(r=i;r--!==0;)if(!e(t[r],n[r]))return !1;return !0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(s=Object.keys(t),i=s.length,i!==Object.keys(n).length)return !1;for(r=i;r--!==0;)if(!Object.prototype.hasOwnProperty.call(n,s[r]))return !1;for(r=i;r--!==0;){var o=s[r];if(!e(t[o],n[o]))return !1}return !0}return t!==t&&n!==n};const Qp=Tr$1(Kp);var Jp=function(e,t){t||(t={}),typeof t=="function"&&(t={cmp:t});var n=typeof t.cycles=="boolean"?t.cycles:!1,i=t.cmp&&function(s){return function(o){return function(a,c){var l={key:a,value:o[a]},u={key:c,value:o[c]};return s(l,u)}}}(t.cmp),r=[];return function s(o){if(o&&o.toJSON&&typeof o.toJSON=="function"&&(o=o.toJSON()),o!==void 0){if(typeof o=="number")return isFinite(o)?""+o:"null";if(typeof o!="object")return JSON.stringify(o);var a,c;if(Array.isArray(o)){for(c="[",a=0;a<o.length;a++)a&&(c+=","),c+=s(o[a])||"null";return c+"]"}if(o===null)return "null";if(r.indexOf(o)!==-1){if(n)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var l=r.push(o)-1,u=Object.keys(o).sort(i&&i(o));for(c="",a=0;a<u.length;a++){var f=u[a],d=s(o[f]);d&&(c&&(c+=","),c+=JSON.stringify(f)+":"+d);}return r.splice(l,1),"{"+c+"}"}}(e)};const mo=Tr$1(Jp);function yo(e){return !!e.or}function bo(e){return !!e.and}function xo(e){return !!e.not}function lr(e,t){if(xo(e))lr(e.not,t);else if(bo(e))for(const n of e.and)lr(n,t);else if(yo(e))for(const n of e.or)lr(n,t);else t(e);}function Wn(e,t){return xo(e)?{not:Wn(e.not,t)}:bo(e)?{and:e.and.map(n=>Wn(n,t))}:yo(e)?{or:e.or.map(n=>Wn(n,t))}:t(e)}const ct=Qp,L=Yp;function Ml(e){throw new Error(e)}function Yn(e,t){const n={};for(const i of t)X$1(e,i)&&(n[i]=e[i]);return n}function Fe(e,t){const n={...e};for(const i of t)delete n[i];return n}Set.prototype.toJSON=function(){return `Set(${[...this].map(e=>mo(e)).join(",")})`};const Z=mo;function U(e){if(Mr$1(e))return e;const t=xt$1(e)?e:mo(e);if(t.length<250)return t;let n=0;for(let i=0;i<t.length;i++){const r=t.charCodeAt(i);n=(n<<5)-n+r,n=n&n;}return n}function Us(e){return e===!1||e===null}function W(e,t){return e.includes(t)}function bn(e,t){let n=0;for(const[i,r]of e.entries())if(t(r,i,n++))return !0;return !1}function vo(e,t){let n=0;for(const[i,r]of e.entries())if(!t(r,i,n++))return !1;return !0}function jl(e,...t){for(const n of t)Zp(e,n??{});return e}function Zp(e,t){for(const n of x(t))fg$1(e,n,t[n],!0);}function lt(e,t){const n=[],i={};let r;for(const s of e)r=t(s),!(r in i)&&(i[r]=1,n.push(s));return n}function eg(e,t){const n=x(e),i=x(t);if(n.length!==i.length)return !1;for(const r of n)if(e[r]!==t[r])return !1;return !0}function Bl(e,t){if(e.size!==t.size)return !1;for(const n of e)if(!t.has(n))return !1;return !0}function So(e,t){for(const n of e)if(t.has(n))return !0;return !1}function Ws(e){const t=new Set;for(const n of e){const r=cc$1(n).map((o,a)=>a===0?o:`[${o}]`),s=r.map((o,a)=>r.slice(0,a+1).join(""));for(const o of s)t.add(o);}return t}function Eo(e,t){return e===void 0||t===void 0?!0:So(Ws(e),Ws(t))}function Y(e){return x(e).length===0}const x=Object.keys,xe=Object.values,Wt=Object.entries;function Ii(e){return e===!0||e===!1}function ie(e){const t=e.replace(/\W/g,"_");return (e.match(/^\d+/)?"_":"")+t}function _i(e,t){return xo(e)?`!(${_i(e.not,t)})`:bo(e)?`(${e.and.map(n=>_i(n,t)).join(") && (")})`:yo(e)?`(${e.or.map(n=>_i(n,t)).join(") || (")})`:t(e)}function ur(e,t){if(t.length===0)return !0;const n=t.shift();return n in e&&ur(e[n],t)&&delete e[n],Y(e)}function Mi(e){return e.charAt(0).toUpperCase()+e.substr(1)}function $o(e,t="datum"){const n=cc$1(e),i=[];for(let r=1;r<=n.length;r++){const s=`[${n.slice(0,r).map(Y$1).join("][")}]`;i.push(`${t}${s}`);}return i.join(" && ")}function Ul(e,t="datum"){return `${t}[${Y$1(cc$1(e).join("."))}]`}function tg(e){return e.replace(/(\[|\]|\.|'|")/g,"\\$1")}function Me(e){return `${cc$1(e).map(tg).join("\\.")}`}function xn(e,t,n){return e.replace(new RegExp(t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"g"),n)}function wo(e){return `${cc$1(e).join(".")}`}function Kn(e){return e?cc$1(e).length:0}function ue(...e){for(const t of e)if(t!==void 0)return t}let Wl=42;function Gl(e){const t=++Wl;return e?String(e)+t:t}function ng(){Wl=42;}function ql(e){return Hl(e)?e:`__${e}`}function Hl(e){return e.startsWith("__")}function Ri(e){if(e!==void 0)return (e%360+360)%360}function _r(e){return Mr$1(e)?!0:!isNaN(e)&&!isNaN(parseFloat(e))}const Ct="row",Nt="column",kr="facet",re="x",he="y",Ze="x2",mt="y2",Yt="xOffset",di="yOffset",et="radius",Tt="radius2",Be="theta",It="theta2",tt="latitude",nt="longitude",it="latitude2",je="longitude2",Oe="color",yt="fill",bt="stroke",Te="shape",Rt="size",_n="angle",Pt="opacity",Kt="fillOpacity",Qt="strokeOpacity",Jt="strokeWidth",Zt="strokeDash",ji="text",Qn="order",Bi="detail",Or="key",vn="tooltip",Tr="href",Ir="url",Rr="description",ig={x:1,y:1,x2:1,y2:1},Vl={theta:1,theta2:1,radius:1,radius2:1};function Xl(e){return e in Vl}const Ao={longitude:1,longitude2:1,latitude:1,latitude2:1};function Yl(e){switch(e){case tt:return "y";case it:return "y2";case nt:return "x";case je:return "x2"}}function Kl(e){return e in Ao}const rg=x(Ao),Co={...ig,...Vl,...Ao,xOffset:1,yOffset:1,color:1,fill:1,stroke:1,opacity:1,fillOpacity:1,strokeOpacity:1,strokeWidth:1,strokeDash:1,size:1,angle:1,shape:1,order:1,text:1,detail:1,key:1,tooltip:1,href:1,url:1,description:1};function Gn(e){return e===Oe||e===yt||e===bt}const Ql={row:1,column:1,facet:1},De=x(Ql),No={...Co,...Ql},sg=x(No),{order:L1,detail:z1,tooltip:D1,...og}=No,{row:M1,column:j1,facet:B1,...ag}=og;function cg(e){return !!ag[e]}function Jl(e){return !!No[e]}const lg=[Ze,mt,it,je,It,Tt];function Zl(e){return kn(e)!==e}function kn(e){switch(e){case Ze:return re;case mt:return he;case it:return tt;case je:return nt;case It:return Be;case Tt:return et}return e}function Gt(e){if(Xl(e))switch(e){case Be:return "startAngle";case It:return "endAngle";case et:return "outerRadius";case Tt:return "innerRadius"}return e}function xt(e){switch(e){case re:return Ze;case he:return mt;case tt:return it;case nt:return je;case Be:return It;case et:return Tt}}function Ie(e){switch(e){case re:case Ze:return "width";case he:case mt:return "height"}}function eu(e){switch(e){case re:return "xOffset";case he:return "yOffset";case Ze:return "x2Offset";case mt:return "y2Offset";case Be:return "thetaOffset";case et:return "radiusOffset";case It:return "theta2Offset";case Tt:return "radius2Offset"}}function Fo(e){switch(e){case re:return "xOffset";case he:return "yOffset"}}function tu(e){switch(e){case"xOffset":return "x";case"yOffset":return "y"}}const ug=x(Co),{x:U1,y:W1,x2:G1,y2:q1,xOffset:H1,yOffset:V1,latitude:X1,longitude:Y1,latitude2:K1,longitude2:Q1,theta:J1,theta2:Z1,radius:ew,radius2:tw,..._o}=Co,fg=x(_o),ko={x:1,y:1},vt=x(ko);function me(e){return e in ko}const Oo={theta:1,radius:1},dg=x(Oo);function Pr(e){return e==="width"?re:he}const nu={xOffset:1,yOffset:1};function pi(e){return e in nu}const{text:nw,tooltip:iw,href:rw,url:sw,description:ow,detail:aw,key:cw,order:lw,...iu}=_o,pg=x(iu);function gg(e){return !!_o[e]}function hg(e){switch(e){case Oe:case yt:case bt:case Rt:case Te:case Pt:case Jt:case Zt:return !0;case Kt:case Qt:case _n:return !1}}const ru={...ko,...Oo,...nu,...iu},Lr=x(ru);function Lt(e){return !!ru[e]}function mg(e,t){return bg(e)[t]}const su={arc:"always",area:"always",bar:"always",circle:"always",geoshape:"always",image:"always",line:"always",rule:"always",point:"always",rect:"always",square:"always",trail:"always",text:"always",tick:"always"},{geoshape:uw,...yg}=su;function bg(e){switch(e){case Oe:case yt:case bt:case Rr:case Bi:case Or:case vn:case Tr:case Qn:case Pt:case Kt:case Qt:case Jt:case kr:case Ct:case Nt:return su;case re:case he:case Yt:case di:case tt:case nt:return yg;case Ze:case mt:case it:case je:return {area:"always",bar:"always",image:"always",rect:"always",rule:"always",circle:"binned",point:"binned",square:"binned",tick:"binned",line:"binned",trail:"binned"};case Rt:return {point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",text:"always",line:"always",trail:"always"};case Zt:return {line:"always",point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",geoshape:"always"};case Te:return {point:"always",geoshape:"always"};case ji:return {text:"always"};case _n:return {point:"always",square:"always",text:"always"};case Ir:return {image:"always"};case Be:return {text:"always",arc:"always"};case et:return {text:"always",arc:"always"};case It:case Tt:return {arc:"always"}}}function Es(e){switch(e){case re:case he:case Be:case et:case Yt:case di:case Rt:case _n:case Jt:case Pt:case Kt:case Qt:case Ze:case mt:case It:case Tt:return;case kr:case Ct:case Nt:case Te:case Zt:case ji:case vn:case Tr:case Ir:case Rr:return "discrete";case Oe:case yt:case bt:return "flexible";case tt:case nt:case it:case je:case Bi:case Or:case Qn:return}}const xg={argmax:1,argmin:1,average:1,count:1,distinct:1,product:1,max:1,mean:1,median:1,min:1,missing:1,q1:1,q3:1,ci0:1,ci1:1,stderr:1,stdev:1,stdevp:1,sum:1,valid:1,values:1,variance:1,variancep:1},vg={count:1,min:1,max:1};function kt(e){return !!e&&!!e.argmin}function en(e){return !!e&&!!e.argmax}function To(e){return xt$1(e)&&!!xg[e]}const Sg=new Set(["count","valid","missing","distinct"]);function ou(e){return xt$1(e)&&Sg.has(e)}function Eg(e){return xt$1(e)&&W(["min","max"],e)}const $g=new Set(["count","sum","distinct","valid","missing"]),wg=new Set(["mean","average","median","q1","q3","min","max"]);function au(e){return Ag$1(e)&&(e=Xr(e,void 0)),"bin"+x(e).map(t=>zr(e[t])?ie(`_${t}_${Wt(e[t])}`):ie(`_${t}_${e[t]}`)).join("")}function ee(e){return e===!0||On(e)&&!e.binned}function be(e){return e==="binned"||On(e)&&e.binned===!0}function On(e){return et$1(e)}function zr(e){return e?.param}function ec(e){switch(e){case Ct:case Nt:case Rt:case Oe:case yt:case bt:case Jt:case Pt:case Kt:case Qt:case Te:return 6;case Zt:return 4;default:return 10}}function Ui(e){return !!e?.expr}function Ne(e){const t=x(e||{}),n={};for(const i of t)n[i]=Pe(e[i]);return n}function cu(e){const{anchor:t,frame:n,offset:i,orient:r,angle:s,limit:o,color:a,subtitleColor:c,subtitleFont:l,subtitleFontSize:u,subtitleFontStyle:f,subtitleFontWeight:d,subtitleLineHeight:g,subtitlePadding:p,...h}=e,m={...h,...a?{fill:a}:{}},y={...t?{anchor:t}:{},...n?{frame:n}:{},...i?{offset:i}:{},...r?{orient:r}:{},...s!==void 0?{angle:s}:{},...o!==void 0?{limit:o}:{}},b={...c?{subtitleColor:c}:{},...l?{subtitleFont:l}:{},...u?{subtitleFontSize:u}:{},...f?{subtitleFontStyle:f}:{},...d?{subtitleFontWeight:d}:{},...g?{subtitleLineHeight:g}:{},...p?{subtitlePadding:p}:{}},O=Yn(e,["align","baseline","dx","dy","limit"]);return {titleMarkConfig:m,subtitleMarkConfig:O,nonMarkTitleProperties:y,subtitle:b}}function jt(e){return xt$1(e)||U$1(e)&&xt$1(e[0])}function k(e){return !!e?.signal}function tn(e){return !!e.step}function Ag(e){return U$1(e)?!1:"fields"in e&&!("data"in e)}function Cg(e){return U$1(e)?!1:"fields"in e&&"data"in e}function At(e){return U$1(e)?!1:"field"in e&&"data"in e}const Ng={aria:1,description:1,ariaRole:1,ariaRoleDescription:1,blend:1,opacity:1,fill:1,fillOpacity:1,stroke:1,strokeCap:1,strokeWidth:1,strokeOpacity:1,strokeDash:1,strokeDashOffset:1,strokeJoin:1,strokeOffset:1,strokeMiterLimit:1,startAngle:1,endAngle:1,padAngle:1,innerRadius:1,outerRadius:1,size:1,shape:1,interpolate:1,tension:1,orient:1,align:1,baseline:1,text:1,dir:1,dx:1,dy:1,ellipsis:1,limit:1,radius:1,theta:1,angle:1,font:1,fontSize:1,fontWeight:1,fontStyle:1,lineBreak:1,lineHeight:1,cursor:1,href:1,tooltip:1,cornerRadius:1,cornerRadiusTopLeft:1,cornerRadiusTopRight:1,cornerRadiusBottomLeft:1,cornerRadiusBottomRight:1,aspect:1,width:1,height:1,url:1,smooth:1},Fg=x(Ng),_g={arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1},Gs=["cornerRadius","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight"];function lu(e){const t=U$1(e.condition)?e.condition.map(tc):tc(e.condition);return {...Pe(e),condition:t}}function Pe(e){if(Ui(e)){const{expr:t,...n}=e;return {signal:t,...n}}return e}function tc(e){if(Ui(e)){const{expr:t,...n}=e;return {signal:t,...n}}return e}function ne(e){if(Ui(e)){const{expr:t,...n}=e;return {signal:t,...n}}return k(e)?e:e!==void 0?{value:e}:void 0}function kg(e){return k(e)?e.signal:Y$1(e)}function nc(e){return k(e)?e.signal:Y$1(e.value)}function He(e){return k(e)?e.signal:e==null?null:Y$1(e)}function Og(e,t,n){for(const i of n){const r=Ot(i,t.markDef,t.config);r!==void 0&&(e[i]=ne(r));}return e}function uu(e){return [].concat(e.type,e.style??[])}function H(e,t,n,i={}){const{vgChannel:r,ignoreVgConfig:s}=i;return r&&t[r]!==void 0?t[r]:t[e]!==void 0?t[e]:s&&(!r||r===e)?void 0:Ot(e,t,n,i)}function Ot(e,t,n,{vgChannel:i}={}){return ue(i?fr(e,t,n.style):void 0,fr(e,t,n.style),i?n[t.type][i]:void 0,n[t.type][e],i?n.mark[i]:n.mark[e])}function fr(e,t,n){return fu(e,uu(t),n)}function fu(e,t,n){t=H$1(t);let i;for(const r of t){const s=n[r];s&&s[e]!==void 0&&(i=s[e]);}return i}function du(e,t){return H$1(e).reduce((n,i)=>(n.field.push($(i,t)),n.order.push(i.sort??"ascending"),n),{field:[],order:[]})}function pu(e,t){const n=[...e];return t.forEach(i=>{for(const r of n)if(ct(r,i))return;n.push(i);}),n}function gu(e,t){return ct(e,t)||!t?e:e?[...H$1(e),...H$1(t)].join(", "):t}function hu(e,t){const n=e.value,i=t.value;if(n==null||i===null)return {explicit:e.explicit,value:null};if((jt(n)||k(n))&&(jt(i)||k(i)))return {explicit:e.explicit,value:gu(n,i)};if(jt(n)||k(n))return {explicit:e.explicit,value:n};if(jt(i)||k(i))return {explicit:e.explicit,value:i};if(!jt(n)&&!k(n)&&!jt(i)&&!k(i))return {explicit:e.explicit,value:pu(n,i)};throw new Error("It should never reach here")}function Io(e){return `Invalid specification ${Z(e)}. Make sure the specification includes at least one of the following properties: "mark", "layer", "facet", "hconcat", "vconcat", "concat", or "repeat".`}const Tg='Autosize "fit" only works for single views and layered views.';function ic(e){return `${e=="width"?"Width":"Height"} "container" only works for single views and layered views.`}function rc(e){const t=e=="width"?"Width":"Height",n=e=="width"?"x":"y";return `${t} "container" only works well with autosize "fit" or "fit-${n}".`}function sc(e){return e?`Dropping "fit-${e}" because spec has discrete ${Ie(e)}.`:'Dropping "fit" because spec has discrete size.'}function Ro(e){return `Unknown field for ${e}. Cannot calculate view size.`}function oc(e){return `Cannot project a selection on encoding channel "${e}", which has no field.`}function Ig(e,t){return `Cannot project a selection on encoding channel "${e}" as it uses an aggregate function ("${t}").`}function Rg(e){return `The "nearest" transform is not supported for ${e} marks.`}function mu(e){return `Selection not supported for ${e} yet.`}function Pg(e){return `Cannot find a selection named "${e}".`}const Lg="Scale bindings are currently only supported for scales with unbinned, continuous domains.",zg="Legend bindings are only supported for selections over an individual field or encoding channel.";function Dg(e){return `Lookups can only be performed on selection parameters. "${e}" is a variable parameter.`}function Mg(e){return `Cannot define and lookup the "${e}" selection in the same view. Try moving the lookup into a second, layered view?`}const jg="The same selection must be used to override scale domains in a layered view.",Bg='Interval selections should be initialized using "x", "y", "longitude", or "latitude" keys.';function Ug(e){return `Unknown repeated value "${e}".`}function ac(e){return `The "columns" property cannot be used when "${e}" has nested row/column.`}const Wg="Axes cannot be shared in concatenated or repeated views yet (https://github.com/vega/vega-lite/issues/2415).";function Gg(e){return `Unrecognized parse "${e}".`}function cc(e,t,n){return `An ancestor parsed field "${e}" as ${n} but a child wants to parse the field as ${t}.`}const qg="Attempt to add the same child twice.";function Hg(e){return `Ignoring an invalid transform: ${Z(e)}.`}const Vg='If "from.fields" is not specified, "as" has to be a string that specifies the key to be used for the data from the secondary source.';function lc(e){return `Config.customFormatTypes is not true, thus custom format type and format for channel ${e} are dropped.`}function Xg(e){const{parentProjection:t,projection:n}=e;return `Layer's shared projection ${Z(t)} is overridden by a child projection ${Z(n)}.`}const Yg="Arc marks uses theta channel rather than angle, replacing angle with theta.";function Kg(e){return `${e}Offset dropped because ${e} is continuous`}function Qg(e){return `There is no ${e} encoding. Replacing ${e}Offset encoding as ${e}.`}function Jg(e,t,n){return `Channel ${e} is a ${t}. Converted to {value: ${Z(n)}}.`}function yu(e){return `Invalid field type "${e}".`}function Zg(e,t){return `Invalid field type "${e}" for aggregate: "${t}", using "quantitative" instead.`}function eh(e){return `Invalid aggregation operator "${e}".`}function bu(e,t){const{fill:n,stroke:i}=t;return `Dropping color ${e} as the plot also has ${n&&i?"fill and stroke":n?"fill":"stroke"}.`}function th(e){return `Position range does not support relative band size for ${e}.`}function qs(e,t){return `Dropping ${Z(e)} from channel "${t}" since it does not contain any data field, datum, value, or signal.`}const nh="Line marks cannot encode size with a non-groupby field. You may want to use trail marks instead.";function Dr(e,t,n){return `${e} dropped as it is incompatible with "${t}".`}function ih(e){return `${e} encoding has no scale, so specified scale is ignored.`}function rh(e){return `${e}-encoding is dropped as ${e} is not a valid encoding channel.`}function sh(e){return `${e} encoding should be discrete (ordinal / nominal / binned).`}function oh(e){return `${e} encoding should be discrete (ordinal / nominal / binned) or use a discretizing scale (e.g. threshold).`}function ah(e){return `Facet encoding dropped as ${e.join(" and ")} ${e.length>1?"are":"is"} also specified.`}function $s(e,t){return `Using discrete channel "${e}" to encode "${t}" field can be misleading as it does not encode ${t==="ordinal"?"order":"magnitude"}.`}function ch(e){return `The ${e} for range marks cannot be an expression`}function lh(e,t){return `Line mark is for continuous lines and thus cannot be used with ${e&&t?"x2 and y2":e?"x2":"y2"}. We will use the rule mark (line segments) instead.`}function uh(e,t){return `Specified orient "${e}" overridden with "${t}".`}function fh(e){return `Cannot use the scale property "${e}" with non-color channel.`}function dh(e){return `Cannot use the relative band size with ${e} scale.`}function ph(e){return `Using unaggregated domain with raw field has no effect (${Z(e)}).`}function gh(e){return `Unaggregated domain not applicable for "${e}" since it produces values outside the origin domain of the source data.`}function hh(e){return `Unaggregated domain is currently unsupported for log scale (${Z(e)}).`}function mh(e){return `Cannot apply size to non-oriented mark "${e}".`}function yh(e,t,n){return `Channel "${e}" does not work with "${t}" scale. We are using "${n}" scale instead.`}function bh(e,t){return `FieldDef does not work with "${e}" scale. We are using "${t}" scale instead.`}function xu(e,t,n){return `${n}-scale's "${t}" is dropped as it does not work with ${e} scale.`}function vu(e){return `The step for "${e}" is dropped because the ${e==="width"?"x":"y"} is continuous.`}function xh(e,t,n,i){return `Conflicting ${t.toString()} property "${e.toString()}" (${Z(n)} and ${Z(i)}). Using ${Z(n)}.`}function vh(e,t,n,i){return `Conflicting ${t.toString()} property "${e.toString()}" (${Z(n)} and ${Z(i)}). Using the union of the two domains.`}function Sh(e){return `Setting the scale to be independent for "${e}" means we also have to set the guide (axis or legend) to be independent.`}function Eh(e){return `Dropping sort property ${Z(e)} as unioned domains only support boolean or op "count", "min", and "max".`}const uc="Domains that should be unioned has conflicting sort properties. Sort will be set to true.",$h="Detected faceted independent scales that union domain of multiple fields from different data sources. We will use the first field. The result view size may be incorrect.",wh="Detected faceted independent scales that union domain of the same fields from different source. We will assume that this is the same field from a different fork of the same data source. However, if this is not the case, the result view size may be incorrect.",Ah="Detected faceted independent scales that union domain of multiple fields from the same data source. We will use the first field. The result view size may be incorrect.";function Ch(e){return `Cannot stack "${e}" if there is already "${e}2".`}function Nh(e){return `Cannot stack non-linear scale (${e}).`}function Fh(e){return `Stacking is applied even though the aggregate function is non-summative ("${e}").`}function dr(e,t){return `Invalid ${e}: ${Z(t)}.`}function _h(e){return `Dropping day from datetime ${Z(e)} as day cannot be combined with other units.`}function kh(e,t){return `${t?"extent ":""}${t&&e?"and ":""}${e?"center ":""}${t&&e?"are ":"is "}not needed when data are aggregated.`}function Oh(e,t,n){return `${e} is not usually used with ${t} for ${n}.`}function Th(e,t){return `Continuous axis should not have customized aggregation function ${e}; ${t} already agregates the axis.`}function fc(e){return `1D error band does not support ${e}.`}function Su(e){return `Channel ${e} is required for "binned" bin.`}function Ih(e){return `Channel ${e} should not be used with "binned" bin.`}function Rh(e){return `Domain for ${e} is required for threshold scale.`}const Eu=x3(p3);let Jn=Eu;function Ph(e){return Jn=e,Jn}function Lh(){return Jn=Eu,Jn}function v(...e){Jn.warn(...e);}function zh(...e){Jn.debug(...e);}function Tn(e){if(e&&et$1(e)){for(const t of Lo)if(t in e)return !0}return !1}const $u=["january","february","march","april","may","june","july","august","september","october","november","december"],Dh=$u.map(e=>e.substr(0,3)),wu=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"],Mh=wu.map(e=>e.substr(0,3));function jh(e){if(_r(e)&&(e=+e),Mr$1(e))return e>4&&v(dr("quarter",e)),e-1;throw new Error(dr("quarter",e))}function Bh(e){if(_r(e)&&(e=+e),Mr$1(e))return e-1;{const t=e.toLowerCase(),n=$u.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=Dh.indexOf(i);if(r!==-1)return r;throw new Error(dr("month",e))}}function Uh(e){if(_r(e)&&(e=+e),Mr$1(e))return e%7;{const t=e.toLowerCase(),n=wu.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=Mh.indexOf(i);if(r!==-1)return r;throw new Error(dr("day",e))}}function Po(e,t){const n=[];if(t&&e.day!==void 0&&x(e).length>1&&(v(_h(e)),e=L(e),delete e.day),e.year!==void 0?n.push(e.year):n.push(2012),e.month!==void 0){const i=t?Bh(e.month):e.month;n.push(i);}else if(e.quarter!==void 0){const i=t?jh(e.quarter):e.quarter;n.push(Mr$1(i)?i*3:`${i}*3`);}else n.push(0);if(e.date!==void 0)n.push(e.date);else if(e.day!==void 0){const i=t?Uh(e.day):e.day;n.push(Mr$1(i)?i+1:`${i}+1`);}else n.push(1);for(const i of ["hours","minutes","seconds","milliseconds"]){const r=e[i];n.push(typeof r>"u"?0:r);}return n}function Sn(e){const n=Po(e,!0).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function Wh(e){const n=Po(e,!1).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function Gh(e){const t=Po(e,!0);return e.utc?+new Date(Date.UTC(...t)):+new Date(...t)}const Au={year:1,quarter:1,month:1,week:1,day:1,dayofyear:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1},Lo=x(Au);function qh(e){return !!Au[e]}function In(e){return et$1(e)?e.binned:Cu(e)}function Cu(e){return e&&e.startsWith("binned")}function zo(e){return e.startsWith("utc")}function Hh(e){return e.substring(3)}const Vh={"year-month":"%b %Y ","year-month-date":"%b %d, %Y "};function Mr(e){return Lo.filter(t=>Fu(e,t))}function Nu(e){const t=Mr(e);return t[t.length-1]}function Fu(e,t){const n=e.indexOf(t);return !(n<0||n>0&&t==="seconds"&&e.charAt(n-1)==="i"||e.length>n+3&&t==="day"&&e.charAt(n+3)==="o"||n>0&&t==="year"&&e.charAt(n-1)==="f")}function Xh(e,t,{end:n}={end:!1}){const i=$o(t),r=zo(e)?"utc":"";function s(c){return c==="quarter"?`(${r}quarter(${i})-1)`:`${r}${c}(${i})`}let o;const a={};for(const c of Lo)Fu(e,c)&&(a[c]=s(c),o=c);return n&&(a[o]+="+1"),Wh(a)}function _u(e){if(!e)return;const t=Mr(e);return `timeUnitSpecifier(${Z(t)}, ${Z(Vh)})`}function Yh(e,t,n){if(!e)return;const i=_u(e);return `${n||zo(e)?"utc":"time"}Format(${t}, ${i})`}function ge(e){if(!e)return;let t;return xt$1(e)?Cu(e)?t={unit:e.substring(6),binned:!0}:t={unit:e}:et$1(e)&&(t={...e,...e.unit?{unit:e.unit}:{}}),zo(t.unit)&&(t.utc=!0,t.unit=Hh(t.unit)),t}function Kh(e){const{utc:t,...n}=ge(e);return n.unit?(t?"utc":"")+x(n).map(i=>ie(`${i==="unit"?"":`_${i}_`}${n[i]}`)).join(""):(t?"utc":"")+"timeunit"+x(n).map(i=>ie(`_${i}_${n[i]}`)).join("")}function ku(e,t=n=>n){const n=ge(e),i=Nu(n.unit);if(i&&i!=="day"){const r={year:2001,month:1,date:1,hours:0,minutes:0,seconds:0,milliseconds:0},{step:s,part:o}=Ou(i,n.step),a={...r,[o]:+r[o]+s};return `${t(Sn(a))} - ${t(Sn(r))}`}}const Qh={year:1,month:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1};function Jh(e){return !!Qh[e]}function Ou(e,t=1){if(Jh(e))return {part:e,step:t};switch(e){case"day":case"dayofyear":return {part:"date",step:t};case"quarter":return {part:"month",step:t*3};case"week":return {part:"date",step:t*7}}}function Zh(e){return e?.param}function Do(e){return !!e?.field&&e.equal!==void 0}function Mo(e){return !!e?.field&&e.lt!==void 0}function jo(e){return !!e?.field&&e.lte!==void 0}function Bo(e){return !!e?.field&&e.gt!==void 0}function Uo(e){return !!e?.field&&e.gte!==void 0}function Wo(e){if(e?.field){if(U$1(e.range)&&e.range.length===2)return !0;if(k(e.range))return !0}return !1}function Go(e){return !!e?.field&&(U$1(e.oneOf)||U$1(e.in))}function em(e){return !!e?.field&&e.valid!==void 0}function Tu(e){return Go(e)||Do(e)||Wo(e)||Mo(e)||Bo(e)||jo(e)||Uo(e)}function st(e,t){return Yr(e,{timeUnit:t,wrapTime:!0})}function tm(e,t){return e.map(n=>st(n,t))}function Iu(e,t=!0){const{field:n}=e,i=ge(e.timeUnit),{unit:r,binned:s}=i||{},o=$(e,{expr:"datum"}),a=r?`time(${s?o:Xh(r,n)})`:o;if(Do(e))return `${a}===${st(e.equal,r)}`;if(Mo(e)){const c=e.lt;return `${a}<${st(c,r)}`}else if(Bo(e)){const c=e.gt;return `${a}>${st(c,r)}`}else if(jo(e)){const c=e.lte;return `${a}<=${st(c,r)}`}else if(Uo(e)){const c=e.gte;return `${a}>=${st(c,r)}`}else {if(Go(e))return `indexof([${tm(e.oneOf,r).join(",")}], ${a}) !== -1`;if(em(e))return qo(a,e.valid);if(Wo(e)){const{range:c}=e,l=k(c)?{signal:`${c.signal}[0]`}:c[0],u=k(c)?{signal:`${c.signal}[1]`}:c[1];if(l!==null&&u!==null&&t)return "inrange("+a+", ["+st(l,r)+", "+st(u,r)+"])";const f=[];return l!==null&&f.push(`${a} >= ${st(l,r)}`),u!==null&&f.push(`${a} <= ${st(u,r)}`),f.length>0?f.join(" && "):"true"}}throw new Error(`Invalid field predicate: ${Z(e)}`)}function qo(e,t=!0){return t?`isValid(${e}) && isFinite(+${e})`:`!isValid(${e}) || !isFinite(+${e})`}function nm(e){return Tu(e)&&e.timeUnit?{...e,timeUnit:ge(e.timeUnit)}:e}const Wi={quantitative:"quantitative",ordinal:"ordinal",temporal:"temporal",nominal:"nominal",geojson:"geojson"};function im(e){return e==="quantitative"||e==="temporal"}function Ru(e){return e==="ordinal"||e==="nominal"}const En=Wi.quantitative,Ho=Wi.ordinal,Zn=Wi.temporal,Vo=Wi.nominal,gi=Wi.geojson;function rm(e){if(e)switch(e=e.toLowerCase(),e){case"q":case En:return "quantitative";case"t":case Zn:return "temporal";case"o":case Ho:return "ordinal";case"n":case Vo:return "nominal";case gi:return "geojson"}}const _e={LINEAR:"linear",LOG:"log",POW:"pow",SQRT:"sqrt",SYMLOG:"symlog",IDENTITY:"identity",SEQUENTIAL:"sequential",TIME:"time",UTC:"utc",QUANTILE:"quantile",QUANTIZE:"quantize",THRESHOLD:"threshold",BIN_ORDINAL:"bin-ordinal",ORDINAL:"ordinal",POINT:"point",BAND:"band"},Hs={linear:"numeric",log:"numeric",pow:"numeric",sqrt:"numeric",symlog:"numeric",identity:"numeric",sequential:"numeric",time:"time",utc:"time",ordinal:"ordinal","bin-ordinal":"bin-ordinal",point:"ordinal-position",band:"ordinal-position",quantile:"discretizing",quantize:"discretizing",threshold:"discretizing"};function sm(e,t){const n=Hs[e],i=Hs[t];return n===i||n==="ordinal-position"&&i==="time"||i==="ordinal-position"&&n==="time"}const om={linear:0,log:1,pow:1,sqrt:1,symlog:1,identity:1,sequential:1,time:0,utc:0,point:10,band:11,ordinal:0,"bin-ordinal":0,quantile:0,quantize:0,threshold:0};function dc(e){return om[e]}const Pu=new Set(["linear","log","pow","sqrt","symlog"]),Lu=new Set([...Pu,"time","utc"]);function zu(e){return Pu.has(e)}const Du=new Set(["quantile","quantize","threshold"]),am=new Set([...Lu,...Du,"sequential","identity"]),cm=new Set(["ordinal","bin-ordinal","point","band"]);function ye(e){return cm.has(e)}function Le(e){return am.has(e)}function Ve(e){return Lu.has(e)}function ei(e){return Du.has(e)}const lm={pointPadding:.5,barBandPaddingInner:.1,rectBandPaddingInner:0,bandWithNestedOffsetPaddingInner:.2,bandWithNestedOffsetPaddingOuter:.2,minBandSize:2,minFontSize:8,maxFontSize:40,minOpacity:.3,maxOpacity:.8,minSize:9,minStrokeWidth:1,maxStrokeWidth:4,quantileCount:4,quantizeCount:4,zero:!0};function um(e){return !xt$1(e)&&!!e.name}function Mu(e){return e?.param}function fm(e){return e?.unionWith}function dm(e){return et$1(e)&&"field"in e}const pm={type:1,domain:1,domainMax:1,domainMin:1,domainMid:1,align:1,range:1,rangeMax:1,rangeMin:1,scheme:1,bins:1,reverse:1,round:1,clamp:1,nice:1,base:1,exponent:1,constant:1,interpolate:1,zero:1,padding:1,paddingInner:1,paddingOuter:1},{type:fw,domain:dw,range:pw,rangeMax:gw,rangeMin:hw,scheme:mw,...gm}=pm,hm=x(gm);function Vs(e,t){switch(t){case"type":case"domain":case"reverse":case"range":return !0;case"scheme":case"interpolate":return !["point","band","identity"].includes(e);case"bins":return !["point","band","identity","ordinal"].includes(e);case"round":return Ve(e)||e==="band"||e==="point";case"padding":case"rangeMin":case"rangeMax":return Ve(e)||["point","band"].includes(e);case"paddingOuter":case"align":return ["point","band"].includes(e);case"paddingInner":return e==="band";case"domainMax":case"domainMid":case"domainMin":case"clamp":return Ve(e);case"nice":return Ve(e)||e==="quantize"||e==="threshold";case"exponent":return e==="pow";case"base":return e==="log";case"constant":return e==="symlog";case"zero":return Le(e)&&!W(["log","time","utc","threshold","quantile"],e)}}function ju(e,t){switch(t){case"interpolate":case"scheme":case"domainMid":return Gn(e)?void 0:fh(t);case"align":case"type":case"bins":case"domain":case"domainMax":case"domainMin":case"range":case"base":case"exponent":case"constant":case"nice":case"padding":case"paddingInner":case"paddingOuter":case"rangeMax":case"rangeMin":case"reverse":case"round":case"clamp":case"zero":return}}function mm(e,t){return W([Ho,Vo],t)?e===void 0||ye(e):t===Zn?W([_e.TIME,_e.UTC,void 0],e):t===En?zu(e)||ei(e)||e===void 0:!0}function ym(e,t,n=!1){if(!Lt(e))return !1;switch(e){case re:case he:case Yt:case di:case Be:case et:return Ve(t)||t==="band"?!0:t==="point"?!n:!1;case Rt:case Jt:case Pt:case Kt:case Qt:case _n:return Ve(t)||ei(t)||W(["band","point","ordinal"],t);case Oe:case yt:case bt:return t!=="band";case Zt:case Te:return t==="ordinal"||ei(t)}}const Ce={arc:"arc",area:"area",bar:"bar",image:"image",line:"line",point:"point",rect:"rect",rule:"rule",text:"text",tick:"tick",trail:"trail",circle:"circle",square:"square",geoshape:"geoshape"},Bu=Ce.arc,jr=Ce.area,Br=Ce.bar,bm=Ce.image,Ur=Ce.line,Wr=Ce.point,xm=Ce.rect,pr=Ce.rule,Uu=Ce.text,Xo=Ce.tick,vm=Ce.trail,Yo=Ce.circle,Ko=Ce.square,Wu=Ce.geoshape;function nn(e){return ["line","area","trail"].includes(e)}function Qo(e){return ["rect","bar","image","arc"].includes(e)}const Sm=new Set(x(Ce));function pt(e){return e.type}const Em=["stroke","strokeWidth","strokeDash","strokeDashOffset","strokeOpacity","strokeJoin","strokeMiterLimit"],$m=["fill","fillOpacity"],wm=[...Em,...$m],Am={color:1,filled:1,invalid:1,order:1,radius2:1,theta2:1,timeUnitBandSize:1,timeUnitBandPosition:1},pc=x(Am),Cm={area:["line","point"],bar:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],rect:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],line:["point"],tick:["bandSize","thickness"]},Nm={color:"#4c78a8",invalid:"filter",timeUnitBandSize:1},Fm={mark:1,arc:1,area:1,bar:1,circle:1,image:1,line:1,point:1,rect:1,rule:1,square:1,text:1,tick:1,trail:1,geoshape:1},Gu=x(Fm);function $n(e){return e&&e.band!=null}const _m={horizontal:["cornerRadiusTopRight","cornerRadiusBottomRight"],vertical:["cornerRadiusTopLeft","cornerRadiusTopRight"]},qu=5,km={binSpacing:1,continuousBandSize:qu,minBandSize:.25,timeUnitBandPosition:.5},Om={binSpacing:0,continuousBandSize:qu,minBandSize:.25,timeUnitBandPosition:.5},Tm={thickness:1};function Im(e){return pt(e)?e.type:e}function Jo(e){const{channel:t,channelDef:n,markDef:i,scale:r,config:s}=e,o=ea(e);return S(n)&&!ou(n.aggregate)&&r&&Ve(r.get("type"))?Rm({fieldDef:n,channel:t,markDef:i,ref:o,config:s}):o}function Rm({fieldDef:e,channel:t,markDef:n,ref:i,config:r}){return nn(n.type)?i:H("invalid",n,r)===null?[Pm(e,t),i]:i}function Pm(e,t){const n=Zo(e,!0),r=kn(t)==="y"?{field:{group:"height"}}:{value:0};return {test:n,...r}}function Zo(e,t=!0){return qo(xt$1(e)?e:$(e,{expr:"datum"}),!t)}function Lm(e){const{datum:t}=e;return Tn(t)?Sn(t):`${Z(t)}`}function gn(e,t,n,i){const r={};if(t&&(r.scale=t),St(e)){const{datum:s}=e;Tn(s)?r.signal=Sn(s):k(s)?r.signal=s.signal:Ui(s)?r.signal=s.expr:r.value=s;}else r.field=$(e,n);if(i){const{offset:s,band:o}=i;s&&(r.offset=s),o&&(r.band=o);}return r}function gr({scaleName:e,fieldOrDatumDef:t,fieldOrDatumDef2:n,offset:i,startSuffix:r,bandPosition:s=.5}){const o=0<s&&s<1?"datum":void 0,a=$(t,{expr:o,suffix:r}),c=n!==void 0?$(n,{expr:o}):$(t,{suffix:"end",expr:o}),l={};if(s===0||s===1){l.scale=e;const u=s===0?a:c;l.field=u;}else {const u=k(s)?`${s.signal} * ${a} + (1-${s.signal}) * ${c}`:`${s} * ${a} + ${1-s} * ${c}`;l.signal=`scale("${e}", ${u})`;}return i&&(l.offset=i),l}function zm({scaleName:e,fieldDef:t}){const n=$(t,{expr:"datum"}),i=$(t,{expr:"datum",suffix:"end"});return `abs(scale("${e}", ${i}) - scale("${e}", ${n}))`}function ea({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l,bandPosition:u}){if(t){if(z(t)){const f=o?.get("type");if(Ae(t)){u??(u=Zu({fieldDef:t,fieldDef2:n,markDef:i,config:r}));const{bin:d,timeUnit:g,type:p}=t;if(ee(d)||u&&g&&p===Zn)return a?.impute?gn(t,s,{binSuffix:"mid"},{offset:c}):u&&!ye(f)?gr({scaleName:s,fieldOrDatumDef:t,bandPosition:u,offset:c}):gn(t,s,Vi(t,e)?{binSuffix:"range"}:{},{offset:c});if(be(d)){if(S(n))return gr({scaleName:s,fieldOrDatumDef:t,fieldOrDatumDef2:n,bandPosition:u,offset:c});v(Su(e===re?Ze:mt));}}return gn(t,s,ye(f)?{binSuffix:"range"}:{},{offset:c,band:f==="band"?u??t.bandPosition??.5:void 0})}else if(Qe(t)){const f=t.value,d=c?{offset:c}:{};return {...ki(e,f),...d}}}return tt$1(l)&&(l=l()),l&&{...l,...c?{offset:c}:{}}}function ki(e,t){return W(["x","x2"],e)&&t==="width"?{field:{group:"width"}}:W(["y","y2"],e)&&t==="height"?{field:{group:"height"}}:ne(t)}function wn(e){return e&&e!=="number"&&e!=="time"}function Hu(e,t,n){return `${e}(${t}${n?`, ${Z(n)}`:""})`}const Dm=" – ";function ta({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s}){if(wn(n))return Xe({fieldOrDatumDef:e,format:t,formatType:n,expr:i,config:s});const o=Vu(e,i,r),a=ti(e);if(t===void 0&&n===void 0&&s.customFormatTypes){if(a==="quantitative"){if(r&&s.normalizedNumberFormatType)return Xe({fieldOrDatumDef:e,format:s.normalizedNumberFormat,formatType:s.normalizedNumberFormatType,expr:i,config:s});if(s.numberFormatType)return Xe({fieldOrDatumDef:e,format:s.numberFormat,formatType:s.numberFormatType,expr:i,config:s})}if(a==="temporal"&&s.timeFormatType&&S(e)&&e.timeUnit===void 0)return Xe({fieldOrDatumDef:e,format:s.timeFormat,formatType:s.timeFormatType,expr:i,config:s})}if(ii(e)){const c=jm({field:o,timeUnit:S(e)?ge(e.timeUnit)?.unit:void 0,format:t,formatType:s.timeFormatType,rawTimeFormat:s.timeFormat,isUTCScale:Rn(e)&&e.scale?.type===_e.UTC});return c?{signal:c}:void 0}if(t=Xs({type:a,specifiedFormat:t,config:s,normalizeStack:r}),S(e)&&ee(e.bin)){const c=$(e,{expr:i,binSuffix:"end"});return {signal:Gi(o,c,t,n,s)}}else return t||ti(e)==="quantitative"?{signal:`${Ku(o,t)}`}:{signal:`isValid(${o}) ? ${o} : ""+${o}`}}function Vu(e,t,n){return S(e)?n?`${$(e,{expr:t,suffix:"end"})}-${$(e,{expr:t,suffix:"start"})}`:$(e,{expr:t}):Lm(e)}function Xe({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s,field:o}){if(o??(o=Vu(e,i,r)),o!=="datum.value"&&S(e)&&ee(e.bin)){const a=$(e,{expr:i,binSuffix:"end"});return {signal:Gi(o,a,t,n,s)}}return {signal:Hu(n,o,t)}}function Xu(e,t,n,i,r,s){if(!(xt$1(i)&&wn(i))&&!(n===void 0&&i===void 0&&r.customFormatTypes&&ti(e)==="quantitative"&&(r.normalizedNumberFormatType&&ni(e)&&e.stack==="normalize"||r.numberFormatType))){if(ni(e)&&e.stack==="normalize"&&r.normalizedNumberFormat)return Xs({type:"quantitative",config:r,normalizeStack:!0});if(ii(e)){const o=S(e)?ge(e.timeUnit)?.unit:void 0;return o===void 0&&r.customFormatTypes&&r.timeFormatType?void 0:Mm({specifiedFormat:n,timeUnit:o,config:r,omitTimeFormatConfig:s})}return Xs({type:t,specifiedFormat:n,config:r})}}function Yu(e,t,n){if(e&&(k(e)||e==="number"||e==="time"))return e;if(ii(t)&&n!=="time"&&n!=="utc")return S(t)&&ge(t?.timeUnit)?.utc?"utc":"time"}function Xs({type:e,specifiedFormat:t,config:n,normalizeStack:i}){if(xt$1(t))return t;if(e===En)return i?n.normalizedNumberFormat:n.numberFormat}function Mm({specifiedFormat:e,timeUnit:t,config:n,omitTimeFormatConfig:i}){return e||(t?{signal:_u(t)}:i?void 0:n.timeFormat)}function Ku(e,t){return `format(${e}, "${t||""}")`}function gc(e,t,n,i){return wn(n)?Hu(n,e,t):Ku(e,(xt$1(t)?t:void 0)??i.numberFormat)}function Gi(e,t,n,i,r){if(n===void 0&&i===void 0&&r.customFormatTypes&&r.numberFormatType)return Gi(e,t,r.numberFormat,r.numberFormatType,r);const s=gc(e,n,i,r),o=gc(t,n,i,r);return `${qo(e,!1)} ? "null" : ${s} + "${Dm}" + ${o}`}function jm({field:e,timeUnit:t,format:n,formatType:i,rawTimeFormat:r,isUTCScale:s}){return !t||n?!t&&i?`${i}(${e}, '${n}')`:(n=xt$1(n)?n:r,`${s?"utc":"time"}Format(${e}, '${n}')`):Yh(t,e,s)}const Gr="min",Bm={x:1,y:1,color:1,fill:1,stroke:1,strokeWidth:1,size:1,shape:1,fillOpacity:1,strokeOpacity:1,opacity:1,text:1};function hc(e){return e in Bm}function Qu(e){return !!e?.encoding}function ut(e){return e&&(e.op==="count"||!!e.field)}function Ju(e){return e&&U$1(e)}function qi(e){return "row"in e||"column"in e}function na(e){return !!e&&"header"in e}function qr(e){return "facet"in e}function Um(e){return e.param}function Wm(e){return e&&!xt$1(e)&&"repeat"in e}function mc(e){const{field:t,timeUnit:n,bin:i,aggregate:r}=e;return {...n?{timeUnit:n}:{},...i?{bin:i}:{},...r?{aggregate:r}:{},field:t}}function ia(e){return "sort"in e}function Zu({fieldDef:e,fieldDef2:t,markDef:n,config:i}){if(z(e)&&e.bandPosition!==void 0)return e.bandPosition;if(S(e)){const{timeUnit:r,bin:s}=e;if(r&&!t)return Qo(n.type)?0:Ot("timeUnitBandPosition",n,i);if(ee(s))return .5}}function ef({channel:e,fieldDef:t,fieldDef2:n,markDef:i,config:r,scaleType:s,useVlSizeChannel:o}){const a=Ie(e),c=H(o?"size":a,i,r,{vgChannel:a});if(c!==void 0)return c;if(S(t)){const{timeUnit:l,bin:u}=t;if(l&&!n)return {band:Ot("timeUnitBandSize",i,r)};if(ee(u)&&!ye(s))return {band:1}}if(Qo(i.type))return s?ye(s)?r[i.type]?.discreteBandSize||{band:1}:r[i.type]?.continuousBandSize:r[i.type]?.discreteBandSize}function tf(e,t,n,i){return ee(e.bin)||e.timeUnit&&Ae(e)&&e.type==="temporal"?Zu({fieldDef:e,fieldDef2:t,markDef:n,config:i})!==void 0:!1}function nf(e){return e&&!!e.sort&&!e.field}function Hr(e){return e&&"condition"in e}function Vr(e){const t=e?.condition;return !!t&&!U$1(t)&&S(t)}function Hi(e){const t=e?.condition;return !!t&&!U$1(t)&&z(t)}function Gm(e){const t=e?.condition;return !!t&&(U$1(t)||Qe(t))}function S(e){return e&&(!!e.field||e.aggregate==="count")}function ti(e){return e?.type}function St(e){return e&&"datum"in e}function Bt(e){return Ae(e)&&!mr(e)||hr(e)}function yc(e){return Ae(e)&&e.type==="quantitative"&&!e.bin||hr(e)}function hr(e){return St(e)&&Mr$1(e.datum)}function z(e){return S(e)||St(e)}function Ae(e){return e&&("field"in e||e.aggregate==="count")&&"type"in e}function Qe(e){return e&&"value"in e&&"value"in e}function Rn(e){return e&&("scale"in e||"sort"in e)}function ni(e){return e&&("axis"in e||"stack"in e||"impute"in e)}function rf(e){return e&&"legend"in e}function sf(e){return e&&("format"in e||"formatType"in e)}function qm(e){return Fe(e,["legend","axis","header","scale"])}function Hm(e){return "op"in e}function $(e,t={}){let n=e.field;const i=t.prefix;let r=t.suffix,s="";if(Xm(e))n=ql("count");else {let o;if(!t.nofn)if(Hm(e))o=e.op;else {const{bin:a,aggregate:c,timeUnit:l}=e;ee(a)?(o=au(a),r=(t.binSuffix??"")+(t.suffix??"")):c?en(c)?(s=`["${n}"]`,n=`argmax_${c.argmax}`):kt(c)?(s=`["${n}"]`,n=`argmin_${c.argmin}`):o=String(c):l&&!In(l)&&(o=Kh(l),r=(!["range","mid"].includes(t.binSuffix)&&t.binSuffix||"")+(t.suffix??""));}o&&(n=n?`${o}_${n}`:o);}return r&&(n=`${n}_${r}`),i&&(n=`${i}_${n}`),t.forAs?wo(n):t.expr?Ul(n,t.expr)+s:Me(n)+s}function mr(e){switch(e.type){case"nominal":case"ordinal":case"geojson":return !0;case"quantitative":return S(e)&&!!e.bin;case"temporal":return !1}throw new Error(yu(e.type))}function Vm(e){return Rn(e)&&ei(e.scale?.type)}function Xm(e){return e.aggregate==="count"}function Ym(e,t){const{field:n,bin:i,timeUnit:r,aggregate:s}=e;if(s==="count")return t.countTitle;if(ee(i))return `${n} (binned)`;if(r&&!In(r)){const o=ge(r)?.unit;if(o)return `${n} (${Mr(o).join("-")})`}else if(s)return en(s)?`${n} for max ${s.argmax}`:kt(s)?`${n} for min ${s.argmin}`:`${Mi(s)} of ${n}`;return n}function Km(e){const{aggregate:t,bin:n,timeUnit:i,field:r}=e;if(en(t))return `${r} for argmax(${t.argmax})`;if(kt(t))return `${r} for argmin(${t.argmin})`;const s=i&&!In(i)?ge(i):void 0,o=t||s?.unit||s?.maxbins&&"timeunit"||ee(n)&&"bin";return o?`${o.toUpperCase()}(${r})`:r}const of=(e,t)=>{switch(t.fieldTitle){case"plain":return e.field;case"functional":return Km(e);default:return Ym(e,t)}};let af=of;function cf(e){af=e;}function Qm(){cf(of);}function qn(e,t,{allowDisabling:n,includeDefault:i=!0}){const r=ra(e)?.title;if(!S(e))return r??e.title;const s=e,o=i?sa(s,t):void 0;return n?ue(r,s.title,o):r??s.title??o}function ra(e){if(ni(e)&&e.axis)return e.axis;if(rf(e)&&e.legend)return e.legend;if(na(e)&&e.header)return e.header}function sa(e,t){return af(e,t)}function yr(e){if(sf(e)){const{format:t,formatType:n}=e;return {format:t,formatType:n}}else {const t=ra(e)??{},{format:n,formatType:i}=t;return {format:n,formatType:i}}}function Jm(e,t){switch(t){case"latitude":case"longitude":return "quantitative";case"row":case"column":case"facet":case"shape":case"strokeDash":return "nominal";case"order":return "ordinal"}if(ia(e)&&U$1(e.sort))return "ordinal";const{aggregate:n,bin:i,timeUnit:r}=e;if(r)return "temporal";if(i||n&&!en(n)&&!kt(n))return "quantitative";if(Rn(e)&&e.scale?.type)switch(Hs[e.scale.type]){case"numeric":case"discretizing":return "quantitative";case"time":return "temporal"}return "nominal"}function gt(e){if(S(e))return e;if(Vr(e))return e.condition}function fe(e){if(z(e))return e;if(Hi(e))return e.condition}function lf(e,t,n,i={}){if(xt$1(e)||Mr$1(e)||Ag$1(e)){const r=xt$1(e)?"string":Mr$1(e)?"number":"boolean";return v(Jg(t,r,e)),{value:e}}return z(e)?br(e,t,n,i):Hi(e)?{...e,condition:br(e.condition,t,n,i)}:e}function br(e,t,n,i){if(sf(e)){const{format:r,formatType:s,...o}=e;if(wn(s)&&!n.customFormatTypes)return v(lc(t)),br(o,t,n,i)}else {const r=ni(e)?"axis":rf(e)?"legend":na(e)?"header":null;if(r&&e[r]){const{format:s,formatType:o,...a}=e[r];if(wn(o)&&!n.customFormatTypes)return v(lc(t)),br({...e,[r]:a},t,n,i)}}return S(e)?oa(e,t,i):Zm(e)}function Zm(e){let t=e.type;if(t)return e;const{datum:n}=e;return t=Mr$1(n)?"quantitative":xt$1(n)?"nominal":Tn(n)?"temporal":void 0,{...e,type:t}}function oa(e,t,{compositeMark:n=!1}={}){const{aggregate:i,timeUnit:r,bin:s,field:o}=e,a={...e};if(!n&&i&&!To(i)&&!en(i)&&!kt(i)&&(v(eh(i)),delete a.aggregate),r&&(a.timeUnit=ge(r)),o&&(a.field=`${o}`),ee(s)&&(a.bin=Xr(s,t)),be(s)&&!me(t)&&v(Ih(t)),Ae(a)){const{type:c}=a,l=rm(c);c!==l&&(a.type=l),c!=="quantitative"&&ou(i)&&(v(Zg(c,i)),a.type="quantitative");}else if(!Zl(t)){const c=Jm(a,t);a.type=c;}if(Ae(a)){const{compatible:c,warning:l}=ey(a,t)||{};c===!1&&v(l);}if(ia(a)&&xt$1(a.sort)){const{sort:c}=a;if(hc(c))return {...a,sort:{encoding:c}};const l=c.substr(1);if(c.charAt(0)==="-"&&hc(l))return {...a,sort:{encoding:l,order:"descending"}}}if(na(a)){const{header:c}=a;if(c){const{orient:l,...u}=c;if(l)return {...a,header:{...u,labelOrient:c.labelOrient||l,titleOrient:c.titleOrient||l}}}}return a}function Xr(e,t){return Ag$1(e)?{maxbins:ec(t)}:e==="binned"?{binned:!0}:!e.maxbins&&!e.step?{...e,maxbins:ec(t)}:e}const zn={compatible:!0};function ey(e,t){const n=e.type;if(n==="geojson"&&t!=="shape")return {compatible:!1,warning:`Channel ${t} should not be used with a geojson data.`};switch(t){case Ct:case Nt:case kr:return mr(e)?zn:{compatible:!1,warning:sh(t)};case re:case he:case Yt:case di:case Oe:case yt:case bt:case ji:case Bi:case Or:case vn:case Tr:case Ir:case _n:case Be:case et:case Rr:return zn;case nt:case je:case tt:case it:return n!==En?{compatible:!1,warning:`Channel ${t} should be used with a quantitative field only, not ${e.type} field.`}:zn;case Pt:case Kt:case Qt:case Jt:case Rt:case It:case Tt:case Ze:case mt:return n==="nominal"&&!e.sort?{compatible:!1,warning:`Channel ${t} should not be used with an unsorted discrete field.`}:zn;case Te:case Zt:return !mr(e)&&!Vm(e)?{compatible:!1,warning:oh(t)}:zn;case Qn:return e.type==="nominal"&&!("sort"in e)?{compatible:!1,warning:"Channel order is inappropriate for nominal field, which has no inherent order."}:zn}}function ii(e){const{formatType:t}=yr(e);return t==="time"||!t&&ty(e)}function ty(e){return e&&(e.type==="temporal"||S(e)&&!!e.timeUnit)}function Yr(e,{timeUnit:t,type:n,wrapTime:i,undefinedIfExprNotRequired:r}){const s=t&&ge(t)?.unit;let o=s||n==="temporal",a;return Ui(e)?a=e.expr:k(e)?a=e.signal:Tn(e)?(o=!0,a=Sn(e)):(xt$1(e)||Mr$1(e))&&o&&(a=`datetime(${Z(e)})`,qh(s)&&(Mr$1(e)&&e<1e4||xt$1(e)&&isNaN(Date.parse(e)))&&(a=Sn({[s]:e}))),a?i&&o?`time(${a})`:a:r?void 0:Z(e)}function uf(e,t){const{type:n}=e;return t.map(i=>{const r=S(e)&&!In(e.timeUnit)?e.timeUnit:void 0,s=Yr(i,{timeUnit:r,type:n,undefinedIfExprNotRequired:!0});return s!==void 0?{signal:s}:i})}function Vi(e,t){return ee(e.bin)?Lt(t)&&["ordinal","nominal"].includes(e.type):(console.warn("Only call this method for binned field defs."),!1)}const bc={labelAlign:{part:"labels",vgProp:"align"},labelBaseline:{part:"labels",vgProp:"baseline"},labelColor:{part:"labels",vgProp:"fill"},labelFont:{part:"labels",vgProp:"font"},labelFontSize:{part:"labels",vgProp:"fontSize"},labelFontStyle:{part:"labels",vgProp:"fontStyle"},labelFontWeight:{part:"labels",vgProp:"fontWeight"},labelOpacity:{part:"labels",vgProp:"opacity"},labelOffset:null,labelPadding:null,gridColor:{part:"grid",vgProp:"stroke"},gridDash:{part:"grid",vgProp:"strokeDash"},gridDashOffset:{part:"grid",vgProp:"strokeDashOffset"},gridOpacity:{part:"grid",vgProp:"opacity"},gridWidth:{part:"grid",vgProp:"strokeWidth"},tickColor:{part:"ticks",vgProp:"stroke"},tickDash:{part:"ticks",vgProp:"strokeDash"},tickDashOffset:{part:"ticks",vgProp:"strokeDashOffset"},tickOpacity:{part:"ticks",vgProp:"opacity"},tickSize:null,tickWidth:{part:"ticks",vgProp:"strokeWidth"}};function Xi(e){return e?.condition}const ff=["domain","grid","labels","ticks","title"],ny={grid:"grid",gridCap:"grid",gridColor:"grid",gridDash:"grid",gridDashOffset:"grid",gridOpacity:"grid",gridScale:"grid",gridWidth:"grid",orient:"main",bandPosition:"both",aria:"main",description:"main",domain:"main",domainCap:"main",domainColor:"main",domainDash:"main",domainDashOffset:"main",domainOpacity:"main",domainWidth:"main",format:"main",formatType:"main",labelAlign:"main",labelAngle:"main",labelBaseline:"main",labelBound:"main",labelColor:"main",labelFlush:"main",labelFlushOffset:"main",labelFont:"main",labelFontSize:"main",labelFontStyle:"main",labelFontWeight:"main",labelLimit:"main",labelLineHeight:"main",labelOffset:"main",labelOpacity:"main",labelOverlap:"main",labelPadding:"main",labels:"main",labelSeparation:"main",maxExtent:"main",minExtent:"main",offset:"both",position:"main",tickCap:"main",tickColor:"main",tickDash:"main",tickDashOffset:"main",tickMinStep:"both",tickOffset:"both",tickOpacity:"main",tickRound:"both",ticks:"main",tickSize:"main",tickWidth:"both",title:"main",titleAlign:"main",titleAnchor:"main",titleAngle:"main",titleBaseline:"main",titleColor:"main",titleFont:"main",titleFontSize:"main",titleFontStyle:"main",titleFontWeight:"main",titleLimit:"main",titleLineHeight:"main",titleOpacity:"main",titlePadding:"main",titleX:"main",titleY:"main",encode:"both",scale:"both",tickBand:"both",tickCount:"both",tickExtra:"both",translate:"both",values:"both",zindex:"both"},df={orient:1,aria:1,bandPosition:1,description:1,domain:1,domainCap:1,domainColor:1,domainDash:1,domainDashOffset:1,domainOpacity:1,domainWidth:1,format:1,formatType:1,grid:1,gridCap:1,gridColor:1,gridDash:1,gridDashOffset:1,gridOpacity:1,gridWidth:1,labelAlign:1,labelAngle:1,labelBaseline:1,labelBound:1,labelColor:1,labelFlush:1,labelFlushOffset:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelLineHeight:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labels:1,labelSeparation:1,maxExtent:1,minExtent:1,offset:1,position:1,tickBand:1,tickCap:1,tickColor:1,tickCount:1,tickDash:1,tickDashOffset:1,tickExtra:1,tickMinStep:1,tickOffset:1,tickOpacity:1,tickRound:1,ticks:1,tickSize:1,tickWidth:1,title:1,titleAlign:1,titleAnchor:1,titleAngle:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titlePadding:1,titleX:1,titleY:1,translate:1,values:1,zindex:1},iy={...df,style:1,labelExpr:1,encoding:1};function xc(e){return !!iy[e]}const ry={axis:1,axisBand:1,axisBottom:1,axisDiscrete:1,axisLeft:1,axisPoint:1,axisQuantitative:1,axisRight:1,axisTemporal:1,axisTop:1,axisX:1,axisXBand:1,axisXDiscrete:1,axisXPoint:1,axisXQuantitative:1,axisXTemporal:1,axisY:1,axisYBand:1,axisYDiscrete:1,axisYPoint:1,axisYQuantitative:1,axisYTemporal:1},pf=x(ry);function zt(e){return "mark"in e}class Kr{constructor(t,n){this.name=t,this.run=n;}hasMatchingType(t){return zt(t)?Im(t.mark)===this.name:!1}}function hn(e,t){const n=e&&e[t];return n?U$1(n)?bn(n,i=>!!i.field):S(n)||Vr(n):!1}function gf(e,t){const n=e&&e[t];return n?U$1(n)?bn(n,i=>!!i.field):S(n)||St(n)||Hi(n):!1}function Ys(e,t){if(me(t)){const n=e[t];if((S(n)||St(n))&&(Ru(n.type)||S(n)&&n.timeUnit)){const i=Fo(t);return gf(e,i)}}return !1}function aa(e){return bn(sg,t=>{if(hn(e,t)){const n=e[t];if(U$1(n))return bn(n,i=>!!i.aggregate);{const i=gt(n);return i&&!!i.aggregate}}return !1})}function hf(e,t){const n=[],i=[],r=[],s=[],o={};return ca(e,(a,c)=>{if(S(a)){const{field:l,aggregate:u,bin:f,timeUnit:d,...g}=a;if(u||d||f){const h=ra(a)?.title;let m=$(a,{forAs:!0});const y={...h?[]:{title:qn(a,t,{allowDisabling:!0})},...g,field:m};if(u){let b;if(en(u)?(b="argmax",m=$({op:"argmax",field:u.argmax},{forAs:!0}),y.field=`${m}.${l}`):kt(u)?(b="argmin",m=$({op:"argmin",field:u.argmin},{forAs:!0}),y.field=`${m}.${l}`):u!=="boxplot"&&u!=="errorbar"&&u!=="errorband"&&(b=u),b){const O={op:b,as:m};l&&(O.field=l),s.push(O);}}else if(n.push(m),Ae(a)&&ee(f)){if(i.push({bin:f,field:l,as:m}),n.push($(a,{binSuffix:"end"})),Vi(a,c)&&n.push($(a,{binSuffix:"range"})),me(c)){const b={field:`${m}_end`};o[`${c}2`]=b;}y.bin="binned",Zl(c)||(y.type=En);}else if(d&&!In(d)){r.push({timeUnit:d,field:l,as:m});const b=Ae(a)&&a.type!==Zn&&"time";b&&(c===ji||c===vn?y.formatType=b:gg(c)?y.legend={formatType:b,...y.legend}:me(c)&&(y.axis={formatType:b,...y.axis}));}o[c]=y;}else n.push(l),o[c]=e[c];}else o[c]=e[c];}),{bins:i,timeUnits:r,aggregate:s,groupby:n,encoding:o}}function sy(e,t,n){const i=mg(t,n);if(i){if(i==="binned"){const r=e[t===Ze?re:he];return !!(S(r)&&S(e[t])&&be(r.bin))}}else return !1;return !0}function oy(e,t,n,i){const r={};for(const s of x(e))Jl(s)||v(rh(s));for(let s of ug){if(!e[s])continue;const o=e[s];if(pi(s)){const a=tu(s),c=r[a];if(S(c)){if(im(c.type)&&S(o)&&!c.timeUnit){v(Kg(a));continue}}else s=a,v(Qg(a));}if(s==="angle"&&t==="arc"&&!e.theta&&(v(Yg),s=Be),!sy(e,s,t)){v(Dr(s,t));continue}if(s===Rt&&t==="line"&&gt(e[s])?.aggregate){v(nh);continue}if(s===Oe&&(n?"fill"in e:"stroke"in e)){v(bu("encoding",{fill:"fill"in e,stroke:"stroke"in e}));continue}if(s===Bi||s===Qn&&!U$1(o)&&!Qe(o)||s===vn&&U$1(o)){if(o){if(s===Qn){const a=e[s];if(nf(a)){r[s]=a;continue}}r[s]=H$1(o).reduce((a,c)=>(S(c)?a.push(oa(c,s)):v(qs(c,s)),a),[]);}}else {if(s===vn&&o===null)r[s]=null;else if(!S(o)&&!St(o)&&!Qe(o)&&!Hr(o)&&!k(o)){v(qs(o,s));continue}r[s]=lf(o,s,i);}}return r}function Qr(e,t){const n={};for(const i of x(e)){const r=lf(e[i],i,t,{compositeMark:!0});n[i]=r;}return n}function ay(e){const t=[];for(const n of x(e))if(hn(e,n)){const i=e[n],r=H$1(i);for(const s of r)S(s)?t.push(s):Vr(s)&&t.push(s.condition);}return t}function ca(e,t,n){if(e)for(const i of x(e)){const r=e[i];if(U$1(r))for(const s of r)t.call(n,s,i);else t.call(n,r,i);}}function cy(e,t,n,i){return e?x(e).reduce((r,s)=>{const o=e[s];return U$1(o)?o.reduce((a,c)=>t.call(i,a,c,s),r):t.call(i,r,o,s)},n):n}function mf(e,t){return x(t).reduce((n,i)=>{switch(i){case re:case he:case Tr:case Rr:case Ir:case Ze:case mt:case Yt:case di:case Be:case It:case et:case Tt:case tt:case nt:case it:case je:case ji:case Te:case _n:case vn:return n;case Qn:if(e==="line"||e==="trail")return n;case Bi:case Or:{const r=t[i];if(U$1(r)||S(r))for(const s of H$1(r))s.aggregate||n.push($(s,{}));return n}case Rt:if(e==="trail")return n;case Oe:case yt:case bt:case Pt:case Kt:case Qt:case Zt:case Jt:{const r=gt(t[i]);return r&&!r.aggregate&&n.push($(r,{})),n}}},[])}function ly(e){const{tooltip:t,...n}=e;if(!t)return {filteredEncoding:n};let i,r;if(U$1(t)){for(const s of t)s.aggregate?(i||(i=[]),i.push(s)):(r||(r=[]),r.push(s));i&&(n.tooltip=i);}else t.aggregate?n.tooltip=t:r=t;return U$1(r)&&r.length===1&&(r=r[0]),{customTooltipWithoutAggregatedField:r,filteredEncoding:n}}function Ks(e,t,n,i=!0){if("tooltip"in n)return {tooltip:n.tooltip};const r=e.map(({fieldPrefix:o,titlePrefix:a})=>{const c=i?` of ${la(t)}`:"";return {field:o+t.field,type:t.type,title:k(a)?{signal:`${a}"${escape(c)}"`}:a+c}}),s=ay(n).map(qm);return {tooltip:[...r,...lt(s,U)]}}function la(e){const{title:t,field:n}=e;return ue(t,n)}function ua(e,t,n,i,r){const{scale:s,axis:o}=n;return ({partName:a,mark:c,positionPrefix:l,endPositionPrefix:u=void 0,extraEncoding:f={}})=>{const d=la(n);return yf(e,a,r,{mark:c,encoding:{[t]:{field:`${l}_${n.field}`,type:n.type,...d!==void 0?{title:d}:{},...s!==void 0?{scale:s}:{},...o!==void 0?{axis:o}:{}},...xt$1(u)?{[`${t}2`]:{field:`${u}_${n.field}`}}:{},...i,...f}})}}function yf(e,t,n,i){const{clip:r,color:s,opacity:o}=e,a=e.type;return e[t]||e[t]===void 0&&n[t]?[{...i,mark:{...n[t],...r?{clip:r}:{},...s?{color:s}:{},...o?{opacity:o}:{},...pt(i.mark)?i.mark:{type:i.mark},style:`${a}-${String(t)}`,...Ag$1(e[t])?{}:e[t]}}]:[]}function bf(e,t,n){const{encoding:i}=e,r=t==="vertical"?"y":"x",s=i[r],o=i[`${r}2`],a=i[`${r}Error`],c=i[`${r}Error2`];return {continuousAxisChannelDef:Zi(s,n),continuousAxisChannelDef2:Zi(o,n),continuousAxisChannelDefError:Zi(a,n),continuousAxisChannelDefError2:Zi(c,n),continuousAxis:r}}function Zi(e,t){if(e?.aggregate){const{aggregate:n,...i}=e;return n!==t&&v(Th(n,t)),i}else return e}function xf(e,t){const{mark:n,encoding:i}=e,{x:r,y:s}=i;if(pt(n)&&n.orient)return n.orient;if(Bt(r)){if(Bt(s)){const o=S(r)&&r.aggregate,a=S(s)&&s.aggregate;if(!o&&a===t)return "vertical";if(!a&&o===t)return "horizontal";if(o===t&&a===t)throw new Error("Both x and y cannot have aggregate");return ii(s)&&!ii(r)?"horizontal":"vertical"}return "horizontal"}else {if(Bt(s))return "vertical";throw new Error(`Need a valid continuous axis for ${t}s`)}}const xr="boxplot",uy=["box","median","outliers","rule","ticks"],fy=new Kr(xr,Sf);function vf(e){return Mr$1(e)?"tukey":e}function Sf(e,{config:t}){e={...e,encoding:Qr(e.encoding,t)};const{mark:n,encoding:i,params:r,projection:s,...o}=e,a=pt(n)?n:{type:n};r&&v(mu("boxplot"));const c=a.extent??t.boxplot.extent,l=H("size",a,t),u=a.invalid,f=vf(c),{bins:d,timeUnits:g,transform:p,continuousAxisChannelDef:h,continuousAxis:m,groupby:y,aggregate:b,encodingWithoutContinuousAxis:O,ticksOrient:C,boxOrient:w,customTooltipWithoutAggregatedField:M}=dy(e,c,t),{color:D,size:K,...ve}=O,se=dp=>ua(a,m,h,dp,t.boxplot),ce=se(ve),rt=se(O),A=se({...ve,...K?{size:K}:{}}),E=Ks([{fieldPrefix:f==="min-max"?"upper_whisker_":"max_",titlePrefix:"Max"},{fieldPrefix:"upper_box_",titlePrefix:"Q3"},{fieldPrefix:"mid_box_",titlePrefix:"Median"},{fieldPrefix:"lower_box_",titlePrefix:"Q1"},{fieldPrefix:f==="min-max"?"lower_whisker_":"min_",titlePrefix:"Min"}],h,O),I={type:"tick",color:"black",opacity:1,orient:C,invalid:u,aria:!1},_=f==="min-max"?E:Ks([{fieldPrefix:"upper_whisker_",titlePrefix:"Upper Whisker"},{fieldPrefix:"lower_whisker_",titlePrefix:"Lower Whisker"}],h,O),T=[...ce({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"lower_whisker",endPositionPrefix:"lower_box",extraEncoding:_}),...ce({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"upper_box",endPositionPrefix:"upper_whisker",extraEncoding:_}),...ce({partName:"ticks",mark:I,positionPrefix:"lower_whisker",extraEncoding:_}),...ce({partName:"ticks",mark:I,positionPrefix:"upper_whisker",extraEncoding:_})],N=[...f!=="tukey"?T:[],...rt({partName:"box",mark:{type:"bar",...l?{size:l}:{},orient:w,invalid:u,ariaRoleDescription:"box"},positionPrefix:"lower_box",endPositionPrefix:"upper_box",extraEncoding:E}),...A({partName:"median",mark:{type:"tick",invalid:u,...et$1(t.boxplot.median)&&t.boxplot.median.color?{color:t.boxplot.median.color}:{},...l?{size:l}:{},orient:C,aria:!1},positionPrefix:"mid_box",extraEncoding:E})];if(f==="min-max")return {...o,transform:(o.transform??[]).concat(p),layer:N};const P=`datum["lower_box_${h.field}"]`,G=`datum["upper_box_${h.field}"]`,j=`(${G} - ${P})`,Q=`${P} - ${c} * ${j}`,Se=`${G} + ${c} * ${j}`,te=`datum["${h.field}"]`,rn={joinaggregate:Ef(h.field),groupby:y},bs={transform:[{filter:`(${Q} <= ${te}) && (${te} <= ${Se})`},{aggregate:[{op:"min",field:h.field,as:`lower_whisker_${h.field}`},{op:"max",field:h.field,as:`upper_whisker_${h.field}`},{op:"min",field:`lower_box_${h.field}`,as:`lower_box_${h.field}`},{op:"max",field:`upper_box_${h.field}`,as:`upper_box_${h.field}`},...b],groupby:y}],layer:T},{tooltip:k1,...up}=ve,{scale:Ya,axis:fp}=h,Ka=la(h),Qa=Fe(fp,["title"]),Ja=yf(a,"outliers",t.boxplot,{transform:[{filter:`(${te} < ${Q}) || (${te} > ${Se})`}],mark:"point",encoding:{[m]:{field:h.field,type:h.type,...Ka!==void 0?{title:Ka}:{},...Ya!==void 0?{scale:Ya}:{},...Y(Qa)?{}:{axis:Qa}},...up,...D?{color:D}:{},...M?{tooltip:M}:{}}})[0];let Ji;const Za=[...d,...g,rn];return Ja?Ji={transform:Za,layer:[Ja,bs]}:(Ji=bs,Ji.transform.unshift(...Za)),{...o,layer:[Ji,{transform:p,layer:N}]}}function Ef(e){return [{op:"q1",field:e,as:`lower_box_${e}`},{op:"q3",field:e,as:`upper_box_${e}`}]}function dy(e,t,n){const i=xf(e,xr),{continuousAxisChannelDef:r,continuousAxis:s}=bf(e,i,xr),o=r.field,a=vf(t),c=[...Ef(o),{op:"median",field:o,as:`mid_box_${o}`},{op:"min",field:o,as:(a==="min-max"?"lower_whisker_":"min_")+o},{op:"max",field:o,as:(a==="min-max"?"upper_whisker_":"max_")+o}],l=a==="min-max"||a==="tukey"?[]:[{calculate:`datum["upper_box_${o}"] - datum["lower_box_${o}"]`,as:`iqr_${o}`},{calculate:`min(datum["upper_box_${o}"] + datum["iqr_${o}"] * ${t}, datum["max_${o}"])`,as:`upper_whisker_${o}`},{calculate:`max(datum["lower_box_${o}"] - datum["iqr_${o}"] * ${t}, datum["min_${o}"])`,as:`lower_whisker_${o}`}],{[s]:u,...f}=e.encoding,{customTooltipWithoutAggregatedField:d,filteredEncoding:g}=ly(f),{bins:p,timeUnits:h,aggregate:m,groupby:y,encoding:b}=hf(g,n),O=i==="vertical"?"horizontal":"vertical",C=i,w=[...p,...h,{aggregate:[...m,...c],groupby:y},...l];return {bins:p,timeUnits:h,transform:w,groupby:y,aggregate:m,continuousAxisChannelDef:r,continuousAxis:s,encodingWithoutContinuousAxis:b,ticksOrient:O,boxOrient:C,customTooltipWithoutAggregatedField:d}}const fa="errorbar",py=["ticks","rule"],gy=new Kr(fa,$f);function $f(e,{config:t}){e={...e,encoding:Qr(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,ticksOrient:o,markDef:a,outerSpec:c,tooltipEncoding:l}=wf(e,fa,t);delete s.size;const u=ua(a,r,i,s,t.errorbar),f=a.thickness,d=a.size,g={type:"tick",orient:o,aria:!1,...f!==void 0?{thickness:f}:{},...d!==void 0?{size:d}:{}},p=[...u({partName:"ticks",mark:g,positionPrefix:"lower",extraEncoding:l}),...u({partName:"ticks",mark:g,positionPrefix:"upper",extraEncoding:l}),...u({partName:"rule",mark:{type:"rule",ariaRoleDescription:"errorbar",...f!==void 0?{size:f}:{}},positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:l})];return {...c,transform:n,...p.length>1?{layer:p}:{...p[0]}}}function hy(e,t){const{encoding:n}=e;if(my(n))return {orient:xf(e,t),inputType:"raw"};const i=yy(n),r=by(n),s=n.x,o=n.y;if(i){if(r)throw new Error(`${t} cannot be both type aggregated-upper-lower and aggregated-error`);const a=n.x2,c=n.y2;if(z(a)&&z(c))throw new Error(`${t} cannot have both x2 and y2`);if(z(a)){if(Bt(s))return {orient:"horizontal",inputType:"aggregated-upper-lower"};throw new Error(`Both x and x2 have to be quantitative in ${t}`)}else if(z(c)){if(Bt(o))return {orient:"vertical",inputType:"aggregated-upper-lower"};throw new Error(`Both y and y2 have to be quantitative in ${t}`)}throw new Error("No ranged axis")}else {const a=n.xError,c=n.xError2,l=n.yError,u=n.yError2;if(z(c)&&!z(a))throw new Error(`${t} cannot have xError2 without xError`);if(z(u)&&!z(l))throw new Error(`${t} cannot have yError2 without yError`);if(z(a)&&z(l))throw new Error(`${t} cannot have both xError and yError with both are quantiative`);if(z(a)){if(Bt(s))return {orient:"horizontal",inputType:"aggregated-error"};throw new Error("All x, xError, and xError2 (if exist) have to be quantitative")}else if(z(l)){if(Bt(o))return {orient:"vertical",inputType:"aggregated-error"};throw new Error("All y, yError, and yError2 (if exist) have to be quantitative")}throw new Error("No ranged axis")}}function my(e){return (z(e.x)||z(e.y))&&!z(e.x2)&&!z(e.y2)&&!z(e.xError)&&!z(e.xError2)&&!z(e.yError)&&!z(e.yError2)}function yy(e){return z(e.x2)||z(e.y2)}function by(e){return z(e.xError)||z(e.xError2)||z(e.yError)||z(e.yError2)}function wf(e,t,n){const{mark:i,encoding:r,params:s,projection:o,...a}=e,c=pt(i)?i:{type:i};s&&v(mu(t));const{orient:l,inputType:u}=hy(e,t),{continuousAxisChannelDef:f,continuousAxisChannelDef2:d,continuousAxisChannelDefError:g,continuousAxisChannelDefError2:p,continuousAxis:h}=bf(e,l,t),{errorBarSpecificAggregate:m,postAggregateCalculates:y,tooltipSummary:b,tooltipTitleWithFieldName:O}=xy(c,f,d,g,p,u,t,n),{[h]:C,[h==="x"?"x2":"y2"]:w,[h==="x"?"xError":"yError"]:M,[h==="x"?"xError2":"yError2"]:D,...K}=r,{bins:ve,timeUnits:se,aggregate:ce,groupby:rt,encoding:A}=hf(K,n),E=[...ce,...m],I=u!=="raw"?[]:rt,_=Ks(b,f,A,O);return {transform:[...a.transform??[],...ve,...se,...E.length===0?[]:[{aggregate:E,groupby:I}],...y],groupby:I,continuousAxisChannelDef:f,continuousAxis:h,encodingWithoutContinuousAxis:A,ticksOrient:l==="vertical"?"horizontal":"vertical",markDef:c,outerSpec:a,tooltipEncoding:_}}function xy(e,t,n,i,r,s,o,a){let c=[],l=[];const u=t.field;let f,d=!1;if(s==="raw"){const g=e.center?e.center:e.extent?e.extent==="iqr"?"median":"mean":a.errorbar.center,p=e.extent?e.extent:g==="mean"?"stderr":"iqr";if(g==="median"!=(p==="iqr")&&v(Oh(g,p,o)),p==="stderr"||p==="stdev")c=[{op:p,field:u,as:`extent_${u}`},{op:g,field:u,as:`center_${u}`}],l=[{calculate:`datum["center_${u}"] + datum["extent_${u}"]`,as:`upper_${u}`},{calculate:`datum["center_${u}"] - datum["extent_${u}"]`,as:`lower_${u}`}],f=[{fieldPrefix:"center_",titlePrefix:Mi(g)},{fieldPrefix:"upper_",titlePrefix:vc(g,p,"+")},{fieldPrefix:"lower_",titlePrefix:vc(g,p,"-")}],d=!0;else {let h,m,y;p==="ci"?(h="mean",m="ci0",y="ci1"):(h="median",m="q1",y="q3"),c=[{op:m,field:u,as:`lower_${u}`},{op:y,field:u,as:`upper_${u}`},{op:h,field:u,as:`center_${u}`}],f=[{fieldPrefix:"upper_",titlePrefix:qn({field:u,aggregate:y,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"lower_",titlePrefix:qn({field:u,aggregate:m,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"center_",titlePrefix:qn({field:u,aggregate:h,type:"quantitative"},a,{allowDisabling:!1})}];}}else {(e.center||e.extent)&&v(kh(e.center,e.extent)),s==="aggregated-upper-lower"?(f=[],l=[{calculate:`datum["${n.field}"]`,as:`upper_${u}`},{calculate:`datum["${u}"]`,as:`lower_${u}`}]):s==="aggregated-error"&&(f=[{fieldPrefix:"",titlePrefix:u}],l=[{calculate:`datum["${u}"] + datum["${i.field}"]`,as:`upper_${u}`}],r?l.push({calculate:`datum["${u}"] + datum["${r.field}"]`,as:`lower_${u}`}):l.push({calculate:`datum["${u}"] - datum["${i.field}"]`,as:`lower_${u}`}));for(const g of l)f.push({fieldPrefix:g.as.substring(0,6),titlePrefix:xn(xn(g.calculate,'datum["',""),'"]',"")});}return {postAggregateCalculates:l,errorBarSpecificAggregate:c,tooltipSummary:f,tooltipTitleWithFieldName:d}}function vc(e,t,n){return `${Mi(e)} ${n} ${t}`}const da="errorband",vy=["band","borders"],Sy=new Kr(da,Af);function Af(e,{config:t}){e={...e,encoding:Qr(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,markDef:o,outerSpec:a,tooltipEncoding:c}=wf(e,da,t),l=o,u=ua(l,r,i,s,t.errorband),f=e.encoding.x!==void 0&&e.encoding.y!==void 0;let d={type:f?"area":"rect"},g={type:f?"line":"rule"};const p={...l.interpolate?{interpolate:l.interpolate}:{},...l.tension&&l.interpolate?{tension:l.tension}:{}};return f?(d={...d,...p,ariaRoleDescription:"errorband"},g={...g,...p,aria:!1}):l.interpolate?v(fc("interpolate")):l.tension&&v(fc("tension")),{...a,transform:n,layer:[...u({partName:"band",mark:d,positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:c}),...u({partName:"borders",mark:g,positionPrefix:"lower",extraEncoding:c}),...u({partName:"borders",mark:g,positionPrefix:"upper",extraEncoding:c})]}}const Cf={};function pa(e,t,n){const i=new Kr(e,t);Cf[e]={normalizer:i,parts:n};}function Ey(){return x(Cf)}pa(xr,Sf,uy);pa(fa,$f,py);pa(da,Af,vy);const $y=["gradientHorizontalMaxLength","gradientHorizontalMinLength","gradientVerticalMaxLength","gradientVerticalMinLength","unselectedOpacity"],Nf={titleAlign:"align",titleAnchor:"anchor",titleAngle:"angle",titleBaseline:"baseline",titleColor:"color",titleFont:"font",titleFontSize:"fontSize",titleFontStyle:"fontStyle",titleFontWeight:"fontWeight",titleLimit:"limit",titleLineHeight:"lineHeight",titleOrient:"orient",titlePadding:"offset"},Ff={labelAlign:"align",labelAnchor:"anchor",labelAngle:"angle",labelBaseline:"baseline",labelColor:"color",labelFont:"font",labelFontSize:"fontSize",labelFontStyle:"fontStyle",labelFontWeight:"fontWeight",labelLimit:"limit",labelLineHeight:"lineHeight",labelOrient:"orient",labelPadding:"offset"},wy=x(Nf),Ay=x(Ff),Cy={header:1,headerRow:1,headerColumn:1,headerFacet:1},_f=x(Cy),kf=["size","shape","fill","stroke","strokeDash","strokeWidth","opacity"],Ny={gradientHorizontalMaxLength:200,gradientHorizontalMinLength:100,gradientVerticalMaxLength:200,gradientVerticalMinLength:64,unselectedOpacity:.35},Fy={aria:1,clipHeight:1,columnPadding:1,columns:1,cornerRadius:1,description:1,direction:1,fillColor:1,format:1,formatType:1,gradientLength:1,gradientOpacity:1,gradientStrokeColor:1,gradientStrokeWidth:1,gradientThickness:1,gridAlign:1,labelAlign:1,labelBaseline:1,labelColor:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labelSeparation:1,legendX:1,legendY:1,offset:1,orient:1,padding:1,rowPadding:1,strokeColor:1,symbolDash:1,symbolDashOffset:1,symbolFillColor:1,symbolLimit:1,symbolOffset:1,symbolOpacity:1,symbolSize:1,symbolStrokeColor:1,symbolStrokeWidth:1,symbolType:1,tickCount:1,tickMinStep:1,title:1,titleAlign:1,titleAnchor:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titleOrient:1,titlePadding:1,type:1,values:1,zindex:1},Je="_vgsid_",_y={point:{on:"click",fields:[Je],toggle:"event.shiftKey",resolve:"global",clear:"dblclick"},interval:{on:"[mousedown, window:mouseup] > window:mousemove!",encodings:["x","y"],translate:"[mousedown, window:mouseup] > window:mousemove!",zoom:"wheel!",mark:{fill:"#333",fillOpacity:.125,stroke:"white"},resolve:"global",clear:"dblclick"}};function ga(e){return e==="legend"||!!e?.legend}function ws(e){return ga(e)&&et$1(e)}function ha(e){return !!e?.select}function Of(e){const t=[];for(const n of e||[]){if(ha(n))continue;const{expr:i,bind:r,...s}=n;if(r&&i){const o={...s,bind:r,init:i};t.push(o);}else {const o={...s,...i?{update:i}:{},...r?{bind:r}:{}};t.push(o);}}return t}function ky(e){return Jr(e)||ya(e)||ma(e)}function ma(e){return "concat"in e}function Jr(e){return "vconcat"in e}function ya(e){return "hconcat"in e}function Tf({step:e,offsetIsDiscrete:t}){return t?e.for??"offset":"position"}function ht(e){return et$1(e)&&e.step!==void 0}function Sc(e){return e.view||e.width||e.height}const Ec=20,Oy={align:1,bounds:1,center:1,columns:1,spacing:1},Ty=x(Oy);function Iy(e,t,n){const i=n[t],r={},{spacing:s,columns:o}=i;s!==void 0&&(r.spacing=s),o!==void 0&&(qr(e)&&!qi(e.facet)||ma(e))&&(r.columns=o),Jr(e)&&(r.columns=1);for(const a of Ty)if(e[a]!==void 0)if(a==="spacing"){const c=e[a];r[a]=Mr$1(c)?c:{row:c.row??s,column:c.column??s};}else r[a]=e[a];return r}function Qs(e,t){return e[t]??e[t==="width"?"continuousWidth":"continuousHeight"]}function vr(e,t){const n=Sr(e,t);return ht(n)?n.step:If}function Sr(e,t){const n=e[t]??e[t==="width"?"discreteWidth":"discreteHeight"];return ue(n,{step:e.step})}const If=20,Ry={continuousWidth:200,continuousHeight:200,step:If},Py={background:"white",padding:5,timeFormat:"%b %d, %Y",countTitle:"Count of Records",view:Ry,mark:Nm,arc:{},area:{},bar:km,circle:{},geoshape:{},image:{},line:{},point:{},rect:Om,rule:{color:"black"},square:{},text:{color:"black"},tick:Tm,trail:{},boxplot:{size:14,extent:1.5,box:{},median:{color:"white"},outliers:{},rule:{},ticks:null},errorbar:{center:"mean",rule:!0,ticks:!1},errorband:{band:{opacity:.3},borders:!1},scale:lm,projection:{},legend:Ny,header:{titlePadding:10,labelPadding:10},headerColumn:{},headerRow:{},headerFacet:{},selection:_y,style:{},title:{},facet:{spacing:Ec},concat:{spacing:Ec},normalizedNumberFormat:".0%"},Et=["#4c78a8","#f58518","#e45756","#72b7b2","#54a24b","#eeca3b","#b279a2","#ff9da6","#9d755d","#bab0ac"],$c={text:11,guideLabel:10,guideTitle:11,groupTitle:13,groupSubtitle:12},wc={blue:Et[0],orange:Et[1],red:Et[2],teal:Et[3],green:Et[4],yellow:Et[5],purple:Et[6],pink:Et[7],brown:Et[8],gray0:"#000",gray1:"#111",gray2:"#222",gray3:"#333",gray4:"#444",gray5:"#555",gray6:"#666",gray7:"#777",gray8:"#888",gray9:"#999",gray10:"#aaa",gray11:"#bbb",gray12:"#ccc",gray13:"#ddd",gray14:"#eee",gray15:"#fff"};function Ly(e={}){return {signals:[{name:"color",value:et$1(e)?{...wc,...e}:wc}],mark:{color:{signal:"color.blue"}},rule:{color:{signal:"color.gray0"}},text:{color:{signal:"color.gray0"}},style:{"guide-label":{fill:{signal:"color.gray0"}},"guide-title":{fill:{signal:"color.gray0"}},"group-title":{fill:{signal:"color.gray0"}},"group-subtitle":{fill:{signal:"color.gray0"}},cell:{stroke:{signal:"color.gray8"}}},axis:{domainColor:{signal:"color.gray13"},gridColor:{signal:"color.gray8"},tickColor:{signal:"color.gray13"}},range:{category:[{signal:"color.blue"},{signal:"color.orange"},{signal:"color.red"},{signal:"color.teal"},{signal:"color.green"},{signal:"color.yellow"},{signal:"color.purple"},{signal:"color.pink"},{signal:"color.brown"},{signal:"color.grey8"}]}}}function zy(e){return {signals:[{name:"fontSize",value:et$1(e)?{...$c,...e}:$c}],text:{fontSize:{signal:"fontSize.text"}},style:{"guide-label":{fontSize:{signal:"fontSize.guideLabel"}},"guide-title":{fontSize:{signal:"fontSize.guideTitle"}},"group-title":{fontSize:{signal:"fontSize.groupTitle"}},"group-subtitle":{fontSize:{signal:"fontSize.groupSubtitle"}}}}}function Dy(e){return {text:{font:e},style:{"guide-label":{font:e},"guide-title":{font:e},"group-title":{font:e},"group-subtitle":{font:e}}}}function Rf(e){const t=x(e||{}),n={};for(const i of t){const r=e[i];n[i]=Xi(r)?lu(r):Pe(r);}return n}function My(e){const t=x(e),n={};for(const i of t)n[i]=Rf(e[i]);return n}const jy=[...Gu,...pf,..._f,"background","padding","legend","lineBreak","scale","style","title","view"];function Pf(e={}){const{color:t,font:n,fontSize:i,selection:r,...s}=e,o=v3({},L(Py),n?Dy(n):{},t?Ly(t):{},i?zy(i):{},s||{});r&&fg$1(o,"selection",r,!0);const a=Fe(o,jy);for(const c of ["background","lineBreak","padding"])o[c]&&(a[c]=Pe(o[c]));for(const c of Gu)o[c]&&(a[c]=Ne(o[c]));for(const c of pf)o[c]&&(a[c]=Rf(o[c]));for(const c of _f)o[c]&&(a[c]=Ne(o[c]));return o.legend&&(a.legend=Ne(o.legend)),o.scale&&(a.scale=Ne(o.scale)),o.style&&(a.style=My(o.style)),o.title&&(a.title=Ne(o.title)),o.view&&(a.view=Ne(o.view)),a}const By=new Set(["view",...Sm]),Uy=["color","fontSize","background","padding","facet","concat","numberFormat","numberFormatType","normalizedNumberFormat","normalizedNumberFormatType","timeFormat","countTitle","header","axisQuantitative","axisTemporal","axisDiscrete","axisPoint","axisXBand","axisXPoint","axisXDiscrete","axisXQuantitative","axisXTemporal","axisYBand","axisYPoint","axisYDiscrete","axisYQuantitative","axisYTemporal","scale","selection","overlay"],Wy={view:["continuousWidth","continuousHeight","discreteWidth","discreteHeight","step"],...Cm};function Gy(e){e=L(e);for(const t of Uy)delete e[t];if(e.axis)for(const t in e.axis)Xi(e.axis[t])&&delete e.axis[t];if(e.legend)for(const t of $y)delete e.legend[t];if(e.mark){for(const t of pc)delete e.mark[t];e.mark.tooltip&&et$1(e.mark.tooltip)&&delete e.mark.tooltip;}e.params&&(e.signals=(e.signals||[]).concat(Of(e.params)),delete e.params);for(const t of By){for(const i of pc)delete e[t][i];const n=Wy[t];if(n)for(const i of n)delete e[t][i];Hy(e,t);}for(const t of Ey())delete e[t];qy(e);for(const t in e)et$1(e[t])&&Y(e[t])&&delete e[t];return Y(e)?void 0:e}function qy(e){const{titleMarkConfig:t,subtitleMarkConfig:n,subtitle:i}=cu(e.title);Y(t)||(e.style["group-title"]={...e.style["group-title"],...t}),Y(n)||(e.style["group-subtitle"]={...e.style["group-subtitle"],...n}),Y(i)?delete e.title:e.title=i;}function Hy(e,t,n,i){const r=e[t];t==="view"&&(n="cell");const s={...r,...e.style[n??t]};Y(s)||(e.style[n??t]=s),delete e[t];}function Zr(e){return "layer"in e}function Vy(e){return "repeat"in e}function Xy(e){return !U$1(e.repeat)&&e.repeat.layer}class ba{map(t,n){return qr(t)?this.mapFacet(t,n):Vy(t)?this.mapRepeat(t,n):ya(t)?this.mapHConcat(t,n):Jr(t)?this.mapVConcat(t,n):ma(t)?this.mapConcat(t,n):this.mapLayerOrUnit(t,n)}mapLayerOrUnit(t,n){if(Zr(t))return this.mapLayer(t,n);if(zt(t))return this.mapUnit(t,n);throw new Error(Io(t))}mapLayer(t,n){return {...t,layer:t.layer.map(i=>this.mapLayerOrUnit(i,n))}}mapHConcat(t,n){return {...t,hconcat:t.hconcat.map(i=>this.map(i,n))}}mapVConcat(t,n){return {...t,vconcat:t.vconcat.map(i=>this.map(i,n))}}mapConcat(t,n){const{concat:i,...r}=t;return {...r,concat:i.map(s=>this.map(s,n))}}mapFacet(t,n){return {...t,spec:this.map(t.spec,n)}}mapRepeat(t,n){return {...t,spec:this.map(t.spec,n)}}}const Yy={zero:1,center:1,normalize:1};function Ky(e){return e in Yy}const Qy=new Set([Bu,Br,jr,pr,Wr,Yo,Ko,Ur,Uu,Xo]),Jy=new Set([Br,jr,Bu]);function Dn(e){return S(e)&&ti(e)==="quantitative"&&!e.bin}function Ac(e,t,{orient:n,type:i}){const r=t==="x"?"y":"radius",s=t==="x",o=e[t],a=e[r];if(S(o)&&S(a))if(Dn(o)&&Dn(a)){if(o.stack)return t;if(a.stack)return r;const c=S(o)&&!!o.aggregate,l=S(a)&&!!a.aggregate;if(c!==l)return c?t:r;if(s&&i==="bar"){if(n==="vertical")return r;if(n==="horizontal")return t}}else {if(Dn(o))return t;if(Dn(a))return r}else {if(Dn(o))return t;if(Dn(a))return r}}function Zy(e){switch(e){case"x":return "y";case"y":return "x";case"theta":return "radius";case"radius":return "theta"}}function Lf(e,t){const n=pt(e)?e:{type:e},i=n.type;if(!Qy.has(i))return null;const r=Ac(t,"x",n)||Ac(t,"theta",n);if(!r)return null;const s=t[r],o=S(s)?$(s,{}):void 0,a=Zy(r),c=[],l=new Set;if(t[a]){const d=t[a],g=S(d)?$(d,{}):void 0;g&&g!==o&&(c.push(a),l.add(g));const p=a==="x"?"xOffset":"yOffset",h=t[p],m=S(h)?$(h,{}):void 0;m&&m!==o&&(c.push(p),l.add(m));}const u=fg.reduce((d,g)=>{if(g!=="tooltip"&&hn(t,g)){const p=t[g];for(const h of H$1(p)){const m=gt(h);if(m.aggregate)continue;const y=$(m,{});(!y||!l.has(y))&&d.push({channel:g,fieldDef:m});}}return d},[]);let f;return s.stack!==void 0?Ag$1(s.stack)?f=s.stack?"zero":null:f=s.stack:Jy.has(i)&&(f="zero"),!f||!Ky(f)||aa(t)&&u.length===0?null:s?.scale?.type&&s?.scale?.type!==_e.LINEAR?(s?.stack&&v(Nh(s.scale.type)),null):z(t[xt(r)])?(s.stack!==void 0&&v(Ch(r)),null):(S(s)&&s.aggregate&&!$g.has(s.aggregate)&&v(Fh(s.aggregate)),{groupbyChannels:c,groupbyFields:l,fieldChannel:r,impute:s.impute===null?!1:nn(i),stackBy:u,offset:f})}function eb(e){const{point:t,line:n,...i}=e;return x(i).length>1?i:i.type}function tb(e){for(const t of ["line","area","rule","trail"])e[t]&&(e={...e,[t]:Fe(e[t],["point","line"])});return e}function As(e,t={},n){return e.point==="transparent"?{opacity:0}:e.point?et$1(e.point)?e.point:{}:e.point!==void 0?null:t.point||n.shape?et$1(t.point)?t.point:{}:void 0}function Cc(e,t={}){return e.line?e.line===!0?{}:e.line:e.line!==void 0?null:t.line?t.line===!0?{}:t.line:void 0}class nb{constructor(){this.name="path-overlay";}hasMatchingType(t,n){if(zt(t)){const{mark:i,encoding:r}=t,s=pt(i)?i:{type:i};switch(s.type){case"line":case"rule":case"trail":return !!As(s,n[s.type],r);case"area":return !!As(s,n[s.type],r)||!!Cc(s,n[s.type])}}return !1}run(t,n,i){const{config:r}=n,{params:s,projection:o,mark:a,name:c,encoding:l,...u}=t,f=Qr(l,r),d=pt(a)?a:{type:a},g=As(d,r[d.type],f),p=d.type==="area"&&Cc(d,r[d.type]),h=[{name:c,...s?{params:s}:{},mark:eb({...d.type==="area"&&d.opacity===void 0&&d.fillOpacity===void 0?{opacity:.7}:{},...d}),encoding:Fe(f,["shape"])}],m=Lf(d,f);let y=f;if(m){const{fieldChannel:b,offset:O}=m;y={...f,[b]:{...f[b],...O?{stack:O}:{}}};}return y=Fe(y,["y2","x2"]),p&&h.push({...o?{projection:o}:{},mark:{type:"line",...Yn(d,["clip","interpolate","tension","tooltip"]),...p},encoding:y}),g&&h.push({...o?{projection:o}:{},mark:{type:"point",opacity:1,filled:!0,...Yn(d,["clip","tooltip"]),...g},encoding:y}),i({...u,layer:h},{...n,config:tb(r)})}}function ib(e,t){return t?qi(e)?Df(e,t):zf(e,t):e}function Cs(e,t){return t?Df(e,t):e}function Js(e,t,n){const i=t[e];if(Wm(i)){if(i.repeat in n)return {...t,[e]:n[i.repeat]};v(Ug(i.repeat));return}return t}function zf(e,t){if(e=Js("field",e,t),e!==void 0){if(e===null)return null;if(ia(e)&&ut(e.sort)){const n=Js("field",e.sort,t);e={...e,...n?{sort:n}:{}};}return e}}function Nc(e,t){if(S(e))return zf(e,t);{const n=Js("datum",e,t);return n!==e&&!n.type&&(n.type="nominal"),n}}function Fc(e,t){if(z(e)){const n=Nc(e,t);if(n)return n;if(Hr(e))return {condition:e.condition}}else {if(Hi(e)){const n=Nc(e.condition,t);if(n)return {...e,condition:n};{const{condition:i,...r}=e;return r}}return e}}function Df(e,t){const n={};for(const i in e)if(X$1(e,i)){const r=e[i];if(U$1(r))n[i]=r.map(s=>Fc(s,t)).filter(s=>s);else {const s=Fc(r,t);s!==void 0&&(n[i]=s);}}return n}class rb{constructor(){this.name="RuleForRangedLine";}hasMatchingType(t){if(zt(t)){const{encoding:n,mark:i}=t;if(i==="line"||pt(i)&&i.type==="line")for(const r of lg){const s=kn(r),o=n[s];if(n[r]&&(S(o)&&!be(o.bin)||St(o)))return !0}}return !1}run(t,n,i){const{encoding:r,mark:s}=t;return v(lh(!!r.x2,!!r.y2)),i({...t,mark:et$1(s)?{...s,type:"rule"}:"rule"},n)}}class sb extends ba{constructor(){super(...arguments),this.nonFacetUnitNormalizers=[fy,gy,Sy,new nb,new rb];}map(t,n){if(zt(t)){const i=hn(t.encoding,Ct),r=hn(t.encoding,Nt),s=hn(t.encoding,kr);if(i||r||s)return this.mapFacetedUnit(t,n)}return super.map(t,n)}mapUnit(t,n){const{parentEncoding:i,parentProjection:r}=n,s=Cs(t.encoding,n.repeater),o={...t,...t.name?{name:[n.repeaterPrefix,t.name].filter(c=>c).join("_")}:{},...s?{encoding:s}:{}};if(i||r)return this.mapUnitWithParentEncodingOrProjection(o,n);const a=this.mapLayerOrUnit.bind(this);for(const c of this.nonFacetUnitNormalizers)if(c.hasMatchingType(o,n.config))return c.run(o,n,a);return o}mapRepeat(t,n){return Xy(t)?this.mapLayerRepeat(t,n):this.mapNonLayerRepeat(t,n)}mapLayerRepeat(t,n){const{repeat:i,spec:r,...s}=t,{row:o,column:a,layer:c}=i,{repeater:l={},repeaterPrefix:u=""}=n;return o||a?this.mapRepeat({...t,repeat:{...o?{row:o}:{},...a?{column:a}:{}},spec:{repeat:{layer:c},spec:r}},n):{...s,layer:c.map(f=>{const d={...l,layer:f},g=`${(r.name?`${r.name}_`:"")+u}child__layer_${ie(f)}`,p=this.mapLayerOrUnit(r,{...n,repeater:d,repeaterPrefix:g});return p.name=g,p})}}mapNonLayerRepeat(t,n){const{repeat:i,spec:r,data:s,...o}=t;!U$1(i)&&t.columns&&(t=Fe(t,["columns"]),v(ac("repeat")));const a=[],{repeater:c={},repeaterPrefix:l=""}=n,u=!U$1(i)&&i.row||[c?c.row:null],f=!U$1(i)&&i.column||[c?c.column:null],d=U$1(i)&&i||[c?c.repeat:null];for(const p of d)for(const h of u)for(const m of f){const y={repeat:p,row:h,column:m,layer:c.layer},b=(r.name?`${r.name}_`:"")+l+"child__"+(U$1(i)?`${ie(p)}`:(i.row?`row_${ie(h)}`:"")+(i.column?`column_${ie(m)}`:"")),O=this.map(r,{...n,repeater:y,repeaterPrefix:b});O.name=b,a.push(Fe(O,["data"]));}const g=U$1(i)?t.columns:i.column?i.column.length:1;return {data:r.data??s,align:"all",...o,columns:g,concat:a}}mapFacet(t,n){const{facet:i}=t;return qi(i)&&t.columns&&(t=Fe(t,["columns"]),v(ac("facet"))),super.mapFacet(t,n)}mapUnitWithParentEncodingOrProjection(t,n){const{encoding:i,projection:r}=t,{parentEncoding:s,parentProjection:o,config:a}=n,c=kc({parentProjection:o,projection:r}),l=_c({parentEncoding:s,encoding:Cs(i,n.repeater)});return this.mapUnit({...t,...c?{projection:c}:{},...l?{encoding:l}:{}},{config:a})}mapFacetedUnit(t,n){const{row:i,column:r,facet:s,...o}=t.encoding,{mark:a,width:c,projection:l,height:u,view:f,params:d,encoding:g,...p}=t,{facetMapping:h,layout:m}=this.getFacetMappingAndLayout({row:i,column:r,facet:s},n),y=Cs(o,n.repeater);return this.mapFacet({...p,...m,facet:h,spec:{...c?{width:c}:{},...u?{height:u}:{},...f?{view:f}:{},...l?{projection:l}:{},mark:a,encoding:y,...d?{params:d}:{}}},n)}getFacetMappingAndLayout(t,n){const{row:i,column:r,facet:s}=t;if(i||r){s&&v(ah([...i?[Ct]:[],...r?[Nt]:[]]));const o={},a={};for(const c of [Ct,Nt]){const l=t[c];if(l){const{align:u,center:f,spacing:d,columns:g,...p}=l;o[c]=p;for(const h of ["align","center","spacing"])l[h]!==void 0&&(a[h]??(a[h]={}),a[h][c]=l[h]);}}return {facetMapping:o,layout:a}}else {const{align:o,center:a,spacing:c,columns:l,...u}=s;return {facetMapping:ib(u,n.repeater),layout:{...o?{align:o}:{},...a?{center:a}:{},...c?{spacing:c}:{},...l?{columns:l}:{}}}}}mapLayer(t,{parentEncoding:n,parentProjection:i,...r}){const{encoding:s,projection:o,...a}=t,c={...r,parentEncoding:_c({parentEncoding:n,encoding:s,layer:!0}),parentProjection:kc({parentProjection:i,projection:o})};return super.mapLayer({...a,...t.name?{name:[c.repeaterPrefix,t.name].filter(l=>l).join("_")}:{}},c)}}function _c({parentEncoding:e,encoding:t={},layer:n}){let i={};if(e){const r=new Set([...x(e),...x(t)]);for(const s of r){const o=t[s],a=e[s];if(z(o)){const c={...a,...o};i[s]=c;}else Hi(o)?i[s]={...o,condition:{...a,...o.condition}}:o||o===null?i[s]=o:(n||Qe(a)||k(a)||z(a)||U$1(a))&&(i[s]=a);}}else i=t;return !i||Y(i)?void 0:i}function kc(e){const{parentProjection:t,projection:n}=e;return t&&n&&v(Xg({parentProjection:t,projection:n})),n??t}function xa(e){return "filter"in e}function ob(e){return e?.stop!==void 0}function Mf(e){return "lookup"in e}function ab(e){return "data"in e}function cb(e){return "param"in e}function lb(e){return "pivot"in e}function ub(e){return "density"in e}function fb(e){return "quantile"in e}function db(e){return "regression"in e}function pb(e){return "loess"in e}function gb(e){return "sample"in e}function hb(e){return "window"in e}function mb(e){return "joinaggregate"in e}function yb(e){return "flatten"in e}function bb(e){return "calculate"in e}function jf(e){return "bin"in e}function xb(e){return "impute"in e}function vb(e){return "timeUnit"in e}function Sb(e){return "aggregate"in e}function Eb(e){return "stack"in e}function $b(e){return "fold"in e}function wb(e){return "extent"in e&&!("density"in e)}function Ab(e){return e.map(t=>xa(t)?{filter:Wn(t.filter,nm)}:t)}class Cb extends ba{map(t,n){return n.emptySelections??(n.emptySelections={}),n.selectionPredicates??(n.selectionPredicates={}),t=Oc(t,n),super.map(t,n)}mapLayerOrUnit(t,n){if(t=Oc(t,n),t.encoding){const i={};for(const[r,s]of Wt(t.encoding))i[r]=Bf(s,n);t={...t,encoding:i};}return super.mapLayerOrUnit(t,n)}mapUnit(t,n){const{selection:i,...r}=t;return i?{...r,params:Wt(i).map(([s,o])=>{const{init:a,bind:c,empty:l,...u}=o;u.type==="single"?(u.type="point",u.toggle=!1):u.type==="multi"&&(u.type="point"),n.emptySelections[s]=l!=="none";for(const f of xe(n.selectionPredicates[s]??{}))f.empty=l!=="none";return {name:s,value:a,select:u,bind:c}})}:t}}function Oc(e,t){const{transform:n,...i}=e;if(n){const r=n.map(s=>{if(xa(s))return {filter:Zs(s,t)};if(jf(s)&&On(s.bin))return {...s,bin:Uf(s.bin)};if(Mf(s)){const{selection:o,...a}=s.from;return o?{...s,from:{param:o,...a}}:s}return s});return {...i,transform:r}}return e}function Bf(e,t){const n=L(e);if(S(n)&&On(n.bin)&&(n.bin=Uf(n.bin)),Rn(n)&&n.scale?.domain?.selection){const{selection:i,...r}=n.scale.domain;n.scale.domain={...r,...i?{param:i}:{}};}if(Hr(n))if(U$1(n.condition))n.condition=n.condition.map(i=>{const{selection:r,param:s,test:o,...a}=i;return s?i:{...a,test:Zs(i,t)}});else {const{selection:i,param:r,test:s,...o}=Bf(n.condition,t);n.condition=r?n.condition:{...o,test:Zs(n.condition,t)};}return n}function Uf(e){const t=e.extent;if(t?.selection){const{selection:n,...i}=t;return {...e,extent:{...i,param:n}}}return e}function Zs(e,t){const n=i=>Wn(i,r=>{var s;const o=t.emptySelections[r]??!0,a={param:r,empty:o};return (s=t.selectionPredicates)[r]??(s[r]=[]),t.selectionPredicates[r].push(a),a});return e.selection?n(e.selection):Wn(e.test||e.filter,i=>i.selection?n(i.selection):i)}class eo extends ba{map(t,n){const i=n.selections??[];if(t.params&&!zt(t)){const r=[];for(const s of t.params)ha(s)?i.push(s):r.push(s);t.params=r;}return n.selections=i,super.map(t,n)}mapUnit(t,n){const i=n.selections;if(!i||!i.length)return t;const r=(n.path??[]).concat(t.name),s=[];for(const o of i)if(!o.views||!o.views.length)s.push(o);else for(const a of o.views)(xt$1(a)&&(a===t.name||r.includes(a))||U$1(a)&&a.map(c=>r.indexOf(c)).every((c,l,u)=>c!==-1&&(l===0||c>u[l-1])))&&s.push(o);return s.length&&(t.params=s),t}}for(const e of ["mapFacet","mapRepeat","mapHConcat","mapVConcat","mapLayer"]){const t=eo.prototype[e];eo.prototype[e]=function(n,i){return t.call(this,n,Nb(n,i))};}function Nb(e,t){return e.name?{...t,path:(t.path??[]).concat(e.name)}:t}function Wf(e,t){t===void 0&&(t=Pf(e.config));const n=Ob(e,t),{width:i,height:r}=e,s=Tb(n,{width:i,height:r,autosize:e.autosize},t);return {...n,...s?{autosize:s}:{}}}const Fb=new sb,_b=new Cb,kb=new eo;function Ob(e,t={}){const n={config:t};return kb.map(Fb.map(_b.map(e,n),n),n)}function Tc(e){return xt$1(e)?{type:e}:e??{}}function Tb(e,t,n){let{width:i,height:r}=t;const s=zt(e)||Zr(e),o={};s?i=="container"&&r=="container"?(o.type="fit",o.contains="padding"):i=="container"?(o.type="fit-x",o.contains="padding"):r=="container"&&(o.type="fit-y",o.contains="padding"):(i=="container"&&(v(ic("width")),i=void 0),r=="container"&&(v(ic("height")),r=void 0));const a={type:"pad",...o,...n?Tc(n.autosize):{},...Tc(e.autosize)};if(a.type==="fit"&&!s&&(v(Tg),a.type="pad"),i=="container"&&!(a.type=="fit"||a.type=="fit-x")&&v(rc("width")),r=="container"&&!(a.type=="fit"||a.type=="fit-y")&&v(rc("height")),!ct(a,{type:"pad"}))return a}function Ib(e){return e==="fit"||e==="fit-x"||e==="fit-y"}function Rb(e){return e?`fit-${Pr(e)}`:"fit"}const Pb=["background","padding"];function Ic(e,t){const n={};for(const i of Pb)e&&e[i]!==void 0&&(n[i]=Pe(e[i]));return t&&(n.params=e.params),n}class Dt{constructor(t={},n={}){this.explicit=t,this.implicit=n;}clone(){return new Dt(L(this.explicit),L(this.implicit))}combine(){return {...this.explicit,...this.implicit}}get(t){return ue(this.explicit[t],this.implicit[t])}getWithExplicit(t){return this.explicit[t]!==void 0?{explicit:!0,value:this.explicit[t]}:this.implicit[t]!==void 0?{explicit:!1,value:this.implicit[t]}:{explicit:!1,value:void 0}}setWithExplicit(t,{value:n,explicit:i}){n!==void 0&&this.set(t,n,i);}set(t,n,i){return delete this[i?"implicit":"explicit"][t],this[i?"explicit":"implicit"][t]=n,this}copyKeyFromSplit(t,{explicit:n,implicit:i}){n[t]!==void 0?this.set(t,n[t],!0):i[t]!==void 0&&this.set(t,i[t],!1);}copyKeyFromObject(t,n){n[t]!==void 0&&this.set(t,n[t],!0);}copyAll(t){for(const n of x(t.combine())){const i=t.getWithExplicit(n);this.setWithExplicit(n,i);}}}function at(e){return {explicit:!0,value:e}}function Re(e){return {explicit:!1,value:e}}function Gf(e){return (t,n,i,r)=>{const s=e(t.value,n.value);return s>0?t:s<0?n:es(t,n,i,r)}}function es(e,t,n,i){return e.explicit&&t.explicit&&v(xh(n,i,e.value,t.value)),e}function qt(e,t,n,i,r=es){return e===void 0||e.value===void 0?t:e.explicit&&!t.explicit?e:t.explicit&&!e.explicit?t:ct(e.value,t.value)?e:r(e,t,n,i)}class Lb extends Dt{constructor(t={},n={},i=!1){super(t,n),this.explicit=t,this.implicit=n,this.parseNothing=i;}clone(){const t=super.clone();return t.parseNothing=this.parseNothing,t}}function ri(e){return "url"in e}function Pi(e){return "values"in e}function qf(e){return "name"in e&&!ri(e)&&!Pi(e)&&!Ut(e)}function Ut(e){return e&&(Hf(e)||Vf(e)||va(e))}function Hf(e){return "sequence"in e}function Vf(e){return "sphere"in e}function va(e){return "graticule"in e}var J;(function(e){e[e.Raw=0]="Raw",e[e.Main=1]="Main",e[e.Row=2]="Row",e[e.Column=3]="Column",e[e.Lookup=4]="Lookup";})(J||(J={}));function Xf(e){const{signals:t,hasLegend:n,index:i,...r}=e;return r.field=Me(r.field),r}function An(e,t=!0,n=ge$1){if(U$1(e)){const i=e.map(r=>An(r,t,n));return t?`[${i.join(", ")}]`:i}else if(Tn(e))return n(t?Sn(e):Gh(e));return t?n(Z(e)):e}function zb(e,t){for(const n of xe(e.component.selection??{})){const i=n.name;let r=`${i}${Vt}, ${n.resolve==="global"?"true":`{unit: ${mn(e)}}`}`;for(const s of ns)s.defined(n)&&(s.signals&&(t=s.signals(e,n,t)),s.modifyExpr&&(r=s.modifyExpr(e,n,r)));t.push({name:i+hx,on:[{events:{signal:n.name+Vt},update:`modify(${Y$1(n.name+Cn)}, ${r})`}]});}return Sa(t)}function Db(e,t){if(e.component.selection&&x(e.component.selection).length){const n=Y$1(e.getName("cell"));t.unshift({name:"facet",value:{},on:[{events:t5("mousemove","scope"),update:`isTuple(facet) ? facet : group(${n}).datum`}]});}return Sa(t)}function Mb(e,t){let n=!1;for(const i of xe(e.component.selection??{})){const r=i.name,s=Y$1(r+Cn);if(t.filter(a=>a.name===r).length===0){const a=i.resolve==="global"?"union":i.resolve,c=i.type==="point"?", true, true)":")";t.push({name:i.name,update:`${fd}(${s}, ${Y$1(a)}${c}`});}n=!0;for(const a of ns)a.defined(i)&&a.topLevelSignals&&(t=a.topLevelSignals(e,i,t));}return n&&t.filter(r=>r.name==="unit").length===0&&t.unshift({name:"unit",value:{},on:[{events:"mousemove",update:"isTuple(group()) ? group() : unit"}]}),Sa(t)}function jb(e,t){const n=[...t],i=mn(e,{escape:!1});for(const r of xe(e.component.selection??{})){const s={name:r.name+Cn};if(r.project.hasSelectionId&&(s.transform=[{type:"collect",sort:{field:Je}}]),r.init){const a=r.project.items.map(Xf);s.values=r.project.hasSelectionId?r.init.map(c=>({unit:i,[Je]:An(c,!1)[0]})):r.init.map(c=>({unit:i,fields:a,values:An(c,!1)}));}n.filter(a=>a.name===r.name+Cn).length||n.push(s);}return n}function Yf(e,t){for(const n of xe(e.component.selection??{}))for(const i of ns)i.defined(n)&&i.marks&&(t=i.marks(e,n,t));return t}function Bb(e,t){for(const n of e.children)oe(n)&&(t=Yf(n,t));return t}function Ub(e,t,n,i){const r=md(e,t.param,t);return {signal:Le(n.get("type"))&&U$1(i)&&i[0]>i[1]?`isValid(${r}) && reverse(${r})`:r}}function Sa(e){return e.map(t=>(t.on&&!t.on.length&&delete t.on,t))}class X{constructor(t,n){this.debugName=n,this._children=[],this._parent=null,t&&(this.parent=t);}clone(){throw new Error("Cannot clone node")}get parent(){return this._parent}set parent(t){this._parent=t,t&&t.addChild(this);}get children(){return this._children}numChildren(){return this._children.length}addChild(t,n){if(this._children.includes(t)){v(qg);return}n!==void 0?this._children.splice(n,0,t):this._children.push(t);}removeChild(t){const n=this._children.indexOf(t);return this._children.splice(n,1),n}remove(){let t=this._parent.removeChild(this);for(const n of this._children)n._parent=this._parent,this._parent.addChild(n,t++);}insertAsParentOf(t){const n=t.parent;n.removeChild(this),this.parent=n,t.parent=this;}swapWithParent(){const t=this._parent,n=t.parent;for(const r of this._children)r.parent=t;this._children=[],t.removeChild(this);const i=t.parent.removeChild(t);this._parent=n,n.addChild(this,i),t.parent=this;}}class we extends X{clone(){const t=new this.constructor;return t.debugName=`clone_${this.debugName}`,t._source=this._source,t._name=`clone_${this._name}`,t.type=this.type,t.refCounts=this.refCounts,t.refCounts[t._name]=0,t}constructor(t,n,i,r){super(t,n),this.type=i,this.refCounts=r,this._source=this._name=n,this.refCounts&&!(this._name in this.refCounts)&&(this.refCounts[this._name]=0);}dependentFields(){return new Set}producedFields(){return new Set}hash(){return this._hash===void 0&&(this._hash=`Output ${Gl()}`),this._hash}getSource(){return this.refCounts[this._name]++,this._source}isRequired(){return !!this.refCounts[this._name]}setSource(t){this._source=t;}}function Ns(e){return e.as!==void 0}function Rc(e){return `${e}_end`}class ft extends X{clone(){return new ft(null,L(this.formula))}constructor(t,n){super(t),this.formula=n;}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s)=>{const{field:o,timeUnit:a}=s;if(a){let c;if(In(a)){if(oe(n)){const{mark:l}=n;(Qo(l)||s.bandPosition)&&(c={timeUnit:ge(a),field:o});}}else c={as:$(s,{forAs:!0}),field:o,timeUnit:a};c&&(r[U(c)]=c);}return r},{});return Y(i)?null:new ft(t,i)}static makeFromTransform(t,n){const{timeUnit:i,...r}={...n},s=ge(i),o={...r,timeUnit:s};return new ft(t,{[U(o)]:o})}merge(t){this.formula={...this.formula};for(const n in t.formula)this.formula[n]||(this.formula[n]=t.formula[n]);for(const n of t.children)t.removeChild(n),n.parent=this;t.remove();}removeFormulas(t){const n={};for(const[i,r]of Wt(this.formula)){const s=Ns(r)?r.as:`${r.field}_end`;t.has(s)||(n[i]=r);}this.formula=n;}producedFields(){return new Set(xe(this.formula).map(t=>Ns(t)?t.as:Rc(t.field)))}dependentFields(){return new Set(xe(this.formula).map(t=>t.field))}hash(){return `TimeUnit ${U(this.formula)}`}assemble(){const t=[];for(const n of xe(this.formula))if(Ns(n)){const{field:i,as:r,timeUnit:s}=n,{unit:o,utc:a,...c}=ge(s);t.push({field:Me(i),type:"timeunit",...o?{units:Mr(o)}:{},...a?{timezone:"utc"}:{},...c,as:[r,`${r}_end`]});}else if(n){const{field:i,timeUnit:r}=n,s=Nu(r?.unit),{part:o,step:a}=Ou(s,r.step);t.push({type:"formula",expr:`timeOffset('${o}', datum['${i}'], ${a})`,as:Rc(i)});}return t}}const Yi="_tuple_fields";class Wb{constructor(...t){this.items=t,this.hasChannel={},this.hasField={},this.hasSelectionId=!1;}}const Gb={defined:()=>!0,parse:(e,t,n)=>{const i=t.name,r=t.project??(t.project=new Wb),s={},o={},a=new Set,c=(p,h)=>{const m=h==="visual"?p.channel:p.field;let y=ie(`${i}_${m}`);for(let b=1;a.has(y);b++)y=ie(`${i}_${m}_${b}`);return a.add(y),{[h]:y}},l=t.type,u=e.config.selection[l],f=n.value!==void 0?H$1(n.value):null;let{fields:d,encodings:g}=et$1(n.select)?n.select:{};if(!d&&!g&&f){for(const p of f)if(et$1(p))for(const h of x(p))cg(h)?(g||(g=[])).push(h):l==="interval"?(v(Bg),g=u.encodings):(d??(d=[])).push(h);}!d&&!g&&(g=u.encodings,"fields"in u&&(d=u.fields));for(const p of g??[]){const h=e.fieldDef(p);if(h){let m=h.field;if(h.aggregate){v(Ig(p,h.aggregate));continue}else if(!m){v(oc(p));continue}if(h.timeUnit&&!In(h.timeUnit)){m=e.vgField(p);const y={timeUnit:h.timeUnit,as:m,field:h.field};o[U(y)]=y;}if(!s[m]){const y=l==="interval"&&Lt(p)&&Le(e.getScaleComponent(p).get("type"))?"R":h.bin?"R-RE":"E",b={field:m,channel:p,type:y,index:r.items.length};b.signals={...c(b,"data"),...c(b,"visual")},r.items.push(s[m]=b),r.hasField[m]=s[m],r.hasSelectionId=r.hasSelectionId||m===Je,Kl(p)?(b.geoChannel=p,b.channel=Yl(p),r.hasChannel[b.channel]=s[m]):r.hasChannel[p]=s[m];}}else v(oc(p));}for(const p of d??[]){if(r.hasField[p])continue;const h={type:"E",field:p,index:r.items.length};h.signals={...c(h,"data")},r.items.push(h),r.hasField[p]=h,r.hasSelectionId=r.hasSelectionId||p===Je;}f&&(t.init=f.map(p=>r.items.map(h=>et$1(p)?p[h.geoChannel||h.channel]!==void 0?p[h.geoChannel||h.channel]:p[h.field]:p))),Y(o)||(r.timeUnit=new ft(null,o));},signals:(e,t,n)=>{const i=t.name+Yi;return n.filter(s=>s.name===i).length>0||t.project.hasSelectionId?n:n.concat({name:i,value:t.project.items.map(Xf)})}},Ft={defined:e=>e.type==="interval"&&e.resolve==="global"&&e.bind&&e.bind==="scales",parse:(e,t)=>{const n=t.scales=[];for(const i of t.project.items){const r=i.channel;if(!Lt(r))continue;const s=e.getScaleComponent(r),o=s?s.get("type"):void 0;if(!s||!Le(o)){v(Lg);continue}s.set("selectionExtent",{param:t.name,field:i.field},!0),n.push(i);}},topLevelSignals:(e,t,n)=>{const i=t.scales.filter(o=>n.filter(a=>a.name===o.signals.data).length===0);if(!e.parent||Pc(e)||i.length===0)return n;const r=n.filter(o=>o.name===t.name)[0];let s=r.update;if(s.indexOf(fd)>=0)r.update=`{${i.map(o=>`${Y$1(Me(o.field))}: ${o.signals.data}`).join(", ")}}`;else {for(const o of i){const a=`${Y$1(Me(o.field))}: ${o.signals.data}`;s.includes(a)||(s=`${s.substring(0,s.length-1)}, ${a}}`);}r.update=s;}return n.concat(i.map(o=>({name:o.signals.data})))},signals:(e,t,n)=>{if(e.parent&&!Pc(e))for(const i of t.scales){const r=n.filter(s=>s.name===i.signals.data)[0];r.push="outer",delete r.value,delete r.update;}return n}};function to(e,t){return `domain(${Y$1(e.scaleName(t))})`}function Pc(e){return e.parent&&xi(e.parent)&&!e.parent.parent}const Hn="_brush",Kf="_scale_trigger",Si="geo_interval_init_tick",Qf="_init",qb="_center",Hb={defined:e=>e.type==="interval",parse:(e,t,n)=>{var i;if(e.hasProjection){const r={...et$1(n.select)?n.select:{}};r.fields=[Je],r.encodings||(r.encodings=n.value?x(n.value):[nt,tt]),n.select={type:"interval",...r};}if(t.translate&&!Ft.defined(t)){const r=`!event.item || event.item.mark.name !== ${Y$1(t.name+Hn)}`;for(const s of t.events){if(!s.between){v(`${s} is not an ordered event stream for interval selections.`);continue}const o=H$1((i=s.between[0]).filter??(i.filter=[]));o.indexOf(r)<0&&o.push(r);}}},signals:(e,t,n)=>{const i=t.name,r=i+Vt,s=xe(t.project.hasChannel).filter(a=>a.channel===re||a.channel===he),o=t.init?t.init[0]:null;if(n.push(...s.reduce((a,c)=>a.concat(Vb(e,t,c,o&&o[c.index])),[])),e.hasProjection){const a=Y$1(e.projectionName()),c=e.projectionName()+qb,{x:l,y:u}=t.project.hasChannel,f=l&&l.signals.visual,d=u&&u.signals.visual,g=l?o&&o[l.index]:`${c}[0]`,p=u?o&&o[u.index]:`${c}[1]`,h=w=>e.getSizeSignalRef(w).signal,m=`[[${f?f+"[0]":"0"}, ${d?d+"[0]":"0"}],[${f?f+"[1]":h("width")}, ${d?d+"[1]":h("height")}]]`;o&&(n.unshift({name:i+Qf,init:`[scale(${a}, [${l?g[0]:g}, ${u?p[0]:p}]), scale(${a}, [${l?g[1]:g}, ${u?p[1]:p}])]`}),(!l||!u)&&(n.find(M=>M.name===c)||n.unshift({name:c,update:`invert(${a}, [${h("width")}/2, ${h("height")}/2])`})));const y=`intersect(${m}, {markname: ${Y$1(e.getName("marks"))}}, unit.mark)`,b=`{unit: ${mn(e)}}`,O=`vlSelectionTuples(${y}, ${b})`,C=s.map(w=>w.signals.visual);return n.concat({name:r,on:[{events:[...C.length?[{signal:C.join(" || ")}]:[],...o?[{signal:Si}]:[]],update:O}]})}else {if(!Ft.defined(t)){const l=i+Kf,u=s.map(f=>{const d=f.channel,{data:g,visual:p}=f.signals,h=Y$1(e.scaleName(d)),m=e.getScaleComponent(d).get("type"),y=Le(m)?"+":"";return `(!isArray(${g}) || (${y}invert(${h}, ${p})[0] === ${y}${g}[0] && ${y}invert(${h}, ${p})[1] === ${y}${g}[1]))`});u.length&&n.push({name:l,value:{},on:[{events:s.map(f=>({scale:e.scaleName(f.channel)})),update:u.join(" && ")+` ? ${l} : {}`}]});}const a=s.map(l=>l.signals.data),c=`unit: ${mn(e)}, fields: ${i+Yi}, values`;return n.concat({name:r,...o?{init:`{${c}: ${An(o)}}`}:{},...a.length?{on:[{events:[{signal:a.join(" || ")}],update:`${a.join(" && ")} ? {${c}: [${a}]} : null`}]}:{}})}},topLevelSignals:(e,t,n)=>(oe(e)&&e.hasProjection&&t.init&&(n.filter(r=>r.name===Si).length||n.unshift({name:Si,value:null,on:[{events:"timer{1}",update:`${Si} === null ? {} : ${Si}`}]})),n),marks:(e,t,n)=>{const i=t.name,{x:r,y:s}=t.project.hasChannel,o=r?.signals.visual,a=s?.signals.visual,c=`data(${Y$1(t.name+Cn)})`;if(Ft.defined(t)||!r&&!s)return n;const l={x:r!==void 0?{signal:`${o}[0]`}:{value:0},y:s!==void 0?{signal:`${a}[0]`}:{value:0},x2:r!==void 0?{signal:`${o}[1]`}:{field:{group:"width"}},y2:s!==void 0?{signal:`${a}[1]`}:{field:{group:"height"}}};if(t.resolve==="global")for(const h of x(l))l[h]=[{test:`${c}.length && ${c}[0].unit === ${mn(e)}`,...l[h]},{value:0}];const{fill:u,fillOpacity:f,cursor:d,...g}=t.mark,p=x(g).reduce((h,m)=>(h[m]=[{test:[r!==void 0&&`${o}[0] !== ${o}[1]`,s!==void 0&&`${a}[0] !== ${a}[1]`].filter(y=>y).join(" && "),value:g[m]},{value:null}],h),{});return [{name:`${i+Hn}_bg`,type:"rect",clip:!0,encode:{enter:{fill:{value:u},fillOpacity:{value:f}},update:l}},...n,{name:i+Hn,type:"rect",clip:!0,encode:{enter:{...d?{cursor:{value:d}}:{},fill:{value:"transparent"}},update:{...l,...p}}}]}};function Vb(e,t,n,i){const r=!e.hasProjection,s=n.channel,o=n.signals.visual,a=Y$1(r?e.scaleName(s):e.projectionName()),c=d=>`scale(${a}, ${d})`,l=e.getSizeSignalRef(s===re?"width":"height").signal,u=`${s}(unit)`,f=t.events.reduce((d,g)=>[...d,{events:g.between[0],update:`[${u}, ${u}]`},{events:g,update:`[${o}[0], clamp(${u}, 0, ${l})]`}],[]);if(r){const d=n.signals.data,g=Ft.defined(t),p=e.getScaleComponent(s),h=p?p.get("type"):void 0,m=i?{init:An(i,!0,c)}:{value:[]};return f.push({events:{signal:t.name+Kf},update:Le(h)?`[${c(`${d}[0]`)}, ${c(`${d}[1]`)}]`:"[0, 0]"}),g?[{name:d,on:[]}]:[{name:o,...m,on:f},{name:d,...i?{init:An(i)}:{},on:[{events:{signal:o},update:`${o}[0] === ${o}[1] ? null : invert(${a}, ${o})`}]}]}else {const d=s===re?0:1,g=t.name+Qf,p=i?{init:`[${g}[0][${d}], ${g}[1][${d}]]`}:{value:[]};return [{name:o,...p,on:f}]}}const Xb={defined:e=>e.type==="point",signals:(e,t,n)=>{const i=t.name,r=i+Yi,s=t.project,o="(item().isVoronoi ? datum.datum : datum)",a=xe(e.component.selection??{}).reduce((f,d)=>d.type==="interval"?f.concat(d.name+Hn):f,[]).map(f=>`indexof(item().mark.name, '${f}') < 0`).join(" && "),c=`datum && item().mark.marktype !== 'group' && indexof(item().mark.role, 'legend') < 0${a?` && ${a}`:""}`;let l=`unit: ${mn(e)}, `;if(t.project.hasSelectionId)l+=`${Je}: ${o}[${Y$1(Je)}]`;else {const f=s.items.map(d=>e.fieldDef(d.channel)?.bin?`[${o}[${Y$1(e.vgField(d.channel,{}))}], ${o}[${Y$1(e.vgField(d.channel,{binSuffix:"end"}))}]]`:`${o}[${Y$1(d.field)}]`).join(", ");l+=`fields: ${r}, values: [${f}]`;}const u=t.events;return n.concat([{name:i+Vt,on:u?[{events:u,update:`${c} ? {${l}} : null`,force:!0}]:[]}])}};function hi(e,t,n,i){const r=Hr(t)&&t.condition,s=i(t);if(r){const a=H$1(r).map(c=>{const l=i(c);if(Um(c)){const{param:u,empty:f}=c;return {test:hd(e,{param:u,empty:f}),...l}}else return {test:wr(e,c.test),...l}});return {[n]:[...a,...s!==void 0?[s]:[]]}}else return s!==void 0?{[n]:s}:{}}function Ea(e,t="text"){const n=e.encoding[t];return hi(e,n,t,i=>ts(i,e.config))}function ts(e,t,n="datum"){if(e){if(Qe(e))return ne(e.value);if(z(e)){const{format:i,formatType:r}=yr(e);return ta({fieldOrDatumDef:e,format:i,formatType:r,expr:n,config:t})}}}function Jf(e,t={}){const{encoding:n,markDef:i,config:r,stack:s}=e,o=n.tooltip;if(U$1(o))return {tooltip:Lc({tooltip:o},s,r,t)};{const a=t.reactiveGeom?"datum.datum":"datum";return hi(e,o,"tooltip",c=>{const l=ts(c,r,a);if(l)return l;if(c===null)return;let u=H("tooltip",i,r);if(u===!0&&(u={content:"encoding"}),xt$1(u))return {value:u};if(et$1(u))return k(u)?u:u.content==="encoding"?Lc(n,s,r,t):{signal:a}})}}function Zf(e,t,n,{reactiveGeom:i}={}){const r={...n,...n.tooltipFormat},s={},o=i?"datum.datum":"datum",a=[];function c(u,f){const d=kn(f),g=Ae(u)?u:{...u,type:e[d].type},p=g.title||sa(g,r),h=H$1(p).join(", ");let m;if(me(f)){const y=f==="x"?"x2":"y2",b=gt(e[y]);if(be(g.bin)&&b){const O=$(g,{expr:o}),C=$(b,{expr:o}),{format:w,formatType:M}=yr(g);m=Gi(O,C,w,M,r),s[y]=!0;}}if((me(f)||f===Be||f===et)&&t&&t.fieldChannel===f&&t.offset==="normalize"){const{format:y,formatType:b}=yr(g);m=ta({fieldOrDatumDef:g,format:y,formatType:b,expr:o,config:r,normalizeStack:!0}).signal;}m??(m=ts(g,r,o).signal),a.push({channel:f,key:h,value:m});}ca(e,(u,f)=>{S(u)?c(u,f):Vr(u)&&c(u.condition,f);});const l={};for(const{channel:u,key:f,value:d}of a)!s[u]&&!l[f]&&(l[f]=d);return l}function Lc(e,t,n,{reactiveGeom:i}={}){const r=Zf(e,t,n,{reactiveGeom:i}),s=Wt(r).map(([o,a])=>`"${o}": ${a}`);return s.length>0?{signal:`{${s.join(", ")}}`}:void 0}function Yb(e){const{markDef:t,config:n}=e,i=H("aria",t,n);return i===!1?{}:{...i?{aria:i}:{},...Kb(e),...Qb(e)}}function Kb(e){const{mark:t,markDef:n,config:i}=e;if(i.aria===!1)return {};const r=H("ariaRoleDescription",n,i);return r!=null?{ariaRoleDescription:{value:r}}:t in _g?{}:{ariaRoleDescription:{value:t}}}function Qb(e){const{encoding:t,markDef:n,config:i,stack:r}=e,s=t.description;if(s)return hi(e,s,"description",c=>ts(c,e.config));const o=H("description",n,i);if(o!=null)return {description:ne(o)};if(i.aria===!1)return {};const a=Zf(t,r,i);if(!Y(a))return {description:{signal:Wt(a).map(([c,l],u)=>`"${u>0?"; ":""}${c}: " + (${l})`).join(" + ")}}}function pe(e,t,n={}){const{markDef:i,encoding:r,config:s}=t,{vgChannel:o}=n;let{defaultRef:a,defaultValue:c}=n;a===void 0&&(c??(c=H(e,i,s,{vgChannel:o,ignoreVgConfig:!0})),c!==void 0&&(a=ne(c)));const l=r[e];return hi(t,l,o??e,u=>ea({channel:e,channelDef:u,markDef:i,config:s,scaleName:t.scaleName(e),scale:t.getScaleComponent(e),stack:null,defaultRef:a}))}function ed(e,t={filled:void 0}){const{markDef:n,encoding:i,config:r}=e,{type:s}=n,o=t.filled??H("filled",n,r),a=W(["bar","point","circle","square","geoshape"],s)?"transparent":void 0,c=H(o===!0?"color":void 0,n,r,{vgChannel:"fill"})??r.mark[o===!0&&"color"]??a,l=H(o===!1?"color":void 0,n,r,{vgChannel:"stroke"})??r.mark[o===!1&&"color"],u=o?"fill":"stroke",f={...c?{fill:ne(c)}:{},...l?{stroke:ne(l)}:{}};return n.color&&(o?n.fill:n.stroke)&&v(bu("property",{fill:"fill"in n,stroke:"stroke"in n})),{...f,...pe("color",e,{vgChannel:u,defaultValue:o?c:l}),...pe("fill",e,{defaultValue:i.fill?c:void 0}),...pe("stroke",e,{defaultValue:i.stroke?l:void 0})}}function Jb(e){const{encoding:t,mark:n}=e,i=t.order;return !nn(n)&&Qe(i)?hi(e,i,"zindex",r=>ne(r.value)):{}}function si({channel:e,markDef:t,encoding:n={},model:i,bandPosition:r}){const s=`${e}Offset`,o=t[s],a=n[s];if((s==="xOffset"||s==="yOffset")&&a)return {offsetType:"encoding",offset:ea({channel:s,channelDef:a,markDef:t,config:i?.config,scaleName:i.scaleName(s),scale:i.getScaleComponent(s),stack:null,defaultRef:ne(o),bandPosition:r})};const c=t[s];return c?{offsetType:"visual",offset:c}:{}}function Ee(e,t,{defaultPos:n,vgChannel:i}){const{encoding:r,markDef:s,config:o,stack:a}=t,c=r[e],l=r[xt(e)],u=t.scaleName(e),f=t.getScaleComponent(e),{offset:d,offsetType:g}=si({channel:e,markDef:s,encoding:r,model:t,bandPosition:.5}),p=$a({model:t,defaultPos:n,channel:e,scaleName:u,scale:f}),h=!c&&me(e)&&(r.latitude||r.longitude)?{field:t.getName(e)}:Zb({channel:e,channelDef:c,channel2Def:l,markDef:s,config:o,scaleName:u,scale:f,stack:a,offset:d,defaultRef:p,bandPosition:g==="encoding"?0:void 0});return h?{[i||e]:h}:void 0}function Zb(e){const{channel:t,channelDef:n,scaleName:i,stack:r,offset:s,markDef:o}=e;if(z(n)&&r&&t===r.fieldChannel){if(S(n)){let a=n.bandPosition;if(a===void 0&&o.type==="text"&&(t==="radius"||t==="theta")&&(a=.5),a!==void 0)return gr({scaleName:i,fieldOrDatumDef:n,startSuffix:"start",bandPosition:a,offset:s})}return gn(n,i,{suffix:"end"},{offset:s})}return Jo(e)}function $a({model:e,defaultPos:t,channel:n,scaleName:i,scale:r}){const{markDef:s,config:o}=e;return ()=>{const a=kn(n),c=Gt(n),l=H(n,s,o,{vgChannel:c});if(l!==void 0)return ki(n,l);switch(t){case"zeroOrMin":case"zeroOrMax":if(i){const u=r.get("type");if(!W([_e.LOG,_e.TIME,_e.UTC],u)){if(r.domainDefinitelyIncludesZero())return {scale:i,value:0}}}if(t==="zeroOrMin")return a==="y"?{field:{group:"height"}}:{value:0};switch(a){case"radius":return {signal:`min(${e.width.signal},${e.height.signal})/2`};case"theta":return {signal:"2*PI"};case"x":return {field:{group:"width"}};case"y":return {value:0}}break;case"mid":return {...e[Ie(n)],mult:.5}}}}const ex={left:"x",center:"xc",right:"x2"},tx={top:"y",middle:"yc",bottom:"y2"};function td(e,t,n,i="middle"){if(e==="radius"||e==="theta")return Gt(e);const r=e==="x"?"align":"baseline",s=H(r,t,n);let o;return k(s)?(v(ch(r)),o=void 0):o=s,e==="x"?ex[o||(i==="top"?"left":"center")]:tx[o||i]}function Er(e,t,{defaultPos:n,defaultPos2:i,range:r}){return r?nd(e,t,{defaultPos:n,defaultPos2:i}):Ee(e,t,{defaultPos:n})}function nd(e,t,{defaultPos:n,defaultPos2:i}){const{markDef:r,config:s}=t,o=xt(e),a=Ie(e),c=nx(t,i,o),l=c[a]?td(e,r,s):Gt(e);return {...Ee(e,t,{defaultPos:n,vgChannel:l}),...c}}function nx(e,t,n){const{encoding:i,mark:r,markDef:s,stack:o,config:a}=e,c=kn(n),l=Ie(n),u=Gt(n),f=i[c],d=e.scaleName(c),g=e.getScaleComponent(c),{offset:p}=n in i||n in s?si({channel:n,markDef:s,encoding:i,model:e}):si({channel:c,markDef:s,encoding:i,model:e});if(!f&&(n==="x2"||n==="y2")&&(i.latitude||i.longitude)){const m=Ie(n),y=e.markDef[m];return y!=null?{[m]:{value:y}}:{[u]:{field:e.getName(n)}}}const h=ix({channel:n,channelDef:f,channel2Def:i[n],markDef:s,config:a,scaleName:d,scale:g,stack:o,offset:p,defaultRef:void 0});return h!==void 0?{[u]:h}:er(n,s)||er(n,{[n]:fr(n,s,a.style),[l]:fr(l,s,a.style)})||er(n,a[r])||er(n,a.mark)||{[u]:$a({model:e,defaultPos:t,channel:n,scaleName:d,scale:g})()}}function ix({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l}){return z(t)&&a&&e.charAt(0)===a.fieldChannel.charAt(0)?gn(t,s,{suffix:"start"},{offset:c}):Jo({channel:e,channelDef:n,scaleName:s,scale:o,stack:a,markDef:i,config:r,offset:c,defaultRef:l})}function er(e,t){const n=Ie(e),i=Gt(e);if(t[i]!==void 0)return {[i]:ki(e,t[i])};if(t[e]!==void 0)return {[i]:ki(e,t[e])};if(t[n]){const r=t[n];if($n(r))v(th(n));else return {[n]:ki(e,r)}}}function Ht(e,t){const{config:n,encoding:i,markDef:r}=e,s=r.type,o=xt(t),a=Ie(t),c=i[t],l=i[o],u=e.getScaleComponent(t),f=u?u.get("type"):void 0,d=r.orient,g=i[a]??i.size??H("size",r,n,{vgChannel:a}),p=eu(t),h=s==="bar"&&(t==="x"?d==="vertical":d==="horizontal");return S(c)&&(ee(c.bin)||be(c.bin)||c.timeUnit&&!l)&&!(g&&!$n(g))&&!i[p]&&!ye(f)?ox({fieldDef:c,fieldDef2:l,channel:t,model:e}):(z(c)&&ye(f)||h)&&!l?sx(c,t,e):nd(t,e,{defaultPos:"zeroOrMax",defaultPos2:"zeroOrMin"})}function rx(e,t,n,i,r,s,o){if($n(r))if(n){const c=n.get("type");if(c==="band"){let l=`bandwidth('${t}')`;r.band!==1&&(l=`${r.band} * ${l}`);const u=Ot("minBandSize",{type:o},i);return {signal:u?`max(${He(u)}, ${l})`:l}}else r.band!==1&&(v(dh(c)),r=void 0);}else return {mult:r.band,field:{group:e}};else {if(k(r))return r;if(r)return {value:r}}if(n){const c=n.get("range");if(tn(c)&&Mr$1(c.step))return {value:c.step-2}}if(!s){const{bandPaddingInner:c,barBandPaddingInner:l,rectBandPaddingInner:u}=i.scale,f=ue(c,o==="bar"?l:u);if(k(f))return {signal:`(1 - (${f.signal})) * ${e}`};if(Mr$1(f))return {signal:`${1-f} * ${e}`}}return {value:vr(i.view,e)-2}}function sx(e,t,n){const{markDef:i,encoding:r,config:s,stack:o}=n,a=i.orient,c=n.scaleName(t),l=n.getScaleComponent(t),u=Ie(t),f=xt(t),d=eu(t),g=n.scaleName(d),p=n.getScaleComponent(Fo(t)),h=a==="horizontal"&&t==="y"||a==="vertical"&&t==="x";let m;(r.size||i.size)&&(h?m=pe("size",n,{vgChannel:u,defaultRef:ne(i.size)}):v(mh(i.type)));const y=!!m,b=ef({channel:t,fieldDef:e,markDef:i,config:s,scaleType:l?.get("type"),useVlSizeChannel:h});m=m||{[u]:rx(u,g||c,p||l,s,b,!!e,i.type)};const O=l?.get("type")==="band"&&$n(b)&&!y?"top":"middle",C=td(t,i,s,O),w=C==="xc"||C==="yc",{offset:M,offsetType:D}=si({channel:t,markDef:i,encoding:r,model:n,bandPosition:w?.5:0}),K=Jo({channel:t,channelDef:e,markDef:i,config:s,scaleName:c,scale:l,stack:o,offset:M,defaultRef:$a({model:n,defaultPos:"mid",channel:t,scaleName:c,scale:l}),bandPosition:w?D==="encoding"?0:.5:k(b)?{signal:`(1-${b})/2`}:$n(b)?(1-b.band)/2:0});if(u)return {[C]:K,...m};{const ve=Gt(f),se=m[u],ce=M?{...se,offset:M}:se;return {[C]:K,[ve]:U$1(K)?[K[0],{...K[1],offset:ce}]:{...K,offset:ce}}}}function zc(e,t,n,i,r,s,o){if(Xl(e))return 0;const a=e==="x"||e==="y2",c=a?-t/2:t/2;if(k(n)||k(r)||k(i)||s){const l=He(n),u=He(r),f=He(i),d=He(s),p=s?`(${o} < ${d} ? ${a?"":"-"}0.5 * (${d} - (${o})) : ${c})`:c,h=f?`${f} + `:"",m=l?`(${l} ? -1 : 1) * `:"",y=u?`(${u} + ${p})`:p;return {signal:h+m+y}}else return r=r||0,i+(n?-r-c:+r+c)}function ox({fieldDef:e,fieldDef2:t,channel:n,model:i}){const{config:r,markDef:s,encoding:o}=i,a=i.getScaleComponent(n),c=i.scaleName(n),l=a?a.get("type"):void 0,u=a.get("reverse"),f=ef({channel:n,fieldDef:e,markDef:s,config:r,scaleType:l}),g=i.component.axes[n]?.[0]?.get("translate")??.5,p=me(n)?H("binSpacing",s,r)??0:0,h=xt(n),m=Gt(n),y=Gt(h),b=Ot("minBandSize",s,r),{offset:O}=si({channel:n,markDef:s,encoding:o,model:i,bandPosition:0}),{offset:C}=si({channel:h,markDef:s,encoding:o,model:i,bandPosition:0}),w=zm({fieldDef:e,scaleName:c}),M=zc(n,p,u,g,O,b,w),D=zc(h,p,u,g,C??O,b,w),K=k(f)?{signal:`(1-${f.signal})/2`}:$n(f)?(1-f.band)/2:.5;if(ee(e.bin)||e.timeUnit)return {[y]:Dc({fieldDef:e,scaleName:c,bandPosition:K,offset:D}),[m]:Dc({fieldDef:e,scaleName:c,bandPosition:k(K)?{signal:`1-${K.signal}`}:1-K,offset:M})};if(be(e.bin)){const ve=gn(e,c,{},{offset:D});if(S(t))return {[y]:ve,[m]:gn(t,c,{},{offset:M})};if(On(e.bin)&&e.bin.step)return {[y]:ve,[m]:{signal:`scale("${c}", ${$(e,{expr:"datum"})} + ${e.bin.step})`,offset:M}}}v(Su(h));}function Dc({fieldDef:e,scaleName:t,bandPosition:n,offset:i}){return gr({scaleName:t,fieldOrDatumDef:e,bandPosition:n,offset:i})}const ax=new Set(["aria","width","height"]);function Ue(e,t){const{fill:n=void 0,stroke:i=void 0}=t.color==="include"?ed(e):{};return {...cx(e.markDef,t),...Mc(e,"fill",n),...Mc(e,"stroke",i),...pe("opacity",e),...pe("fillOpacity",e),...pe("strokeOpacity",e),...pe("strokeWidth",e),...pe("strokeDash",e),...Jb(e),...Jf(e),...Ea(e,"href"),...Yb(e)}}function Mc(e,t,n){const{config:i,mark:r,markDef:s}=e;if(H("invalid",s,i)==="hide"&&n&&!nn(r)){const a=lx(e,{invalid:!0,channels:Lr});if(a)return {[t]:[{test:a,value:null},...H$1(n)]}}return n?{[t]:n}:{}}function cx(e,t){return Fg.reduce((n,i)=>(!ax.has(i)&&e[i]!==void 0&&t[i]!=="ignore"&&(n[i]=ne(e[i])),n),{})}function lx(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{const a=e.getScaleComponent(o);if(a){const c=a.get("type"),l=e.vgField(o,{expr:"datum"});l&&Le(c)&&(s[l]=!0);}return s},{}),r=x(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>Zo(o,t)).join(` ${s} `)}}function wa(e){const{config:t,markDef:n}=e;if(H("invalid",n,t)){const r=ux(e,{channels:vt});if(r)return {defined:{signal:r}}}return {}}function ux(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{const a=e.getScaleComponent(o);if(a){const c=a.get("type"),l=e.vgField(o,{expr:"datum",binSuffix:e.stack?.impute?"mid":void 0});l&&Le(c)&&(s[l]=!0);}return s},{}),r=x(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>Zo(o,t)).join(` ${s} `)}}function jc(e,t){if(t!==void 0)return {[e]:ne(t)}}const Fs="voronoi",id={defined:e=>e.type==="point"&&e.nearest,parse:(e,t)=>{if(t.events)for(const n of t.events)n.markname=e.getName(Fs);},marks:(e,t,n)=>{const{x:i,y:r}=t.project.hasChannel,s=e.mark;if(nn(s))return v(Rg(s)),n;const o={name:e.getName(Fs),type:"path",interactive:!0,from:{data:e.getName("marks")},encode:{update:{fill:{value:"transparent"},strokeWidth:{value:.35},stroke:{value:"transparent"},isVoronoi:{value:!0},...Jf(e,{reactiveGeom:!0})}},transform:[{type:"voronoi",x:{expr:i||!r?"datum.datum.x || 0":"0"},y:{expr:r||!i?"datum.datum.y || 0":"0"},size:[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]}]};let a=0,c=!1;return n.forEach((l,u)=>{const f=l.name??"";f===e.component.mark[0].name?a=u:f.indexOf(Fs)>=0&&(c=!0);}),c||n.splice(a+1,0,o),n}},rd={defined:e=>e.type==="point"&&e.resolve==="global"&&e.bind&&e.bind!=="scales"&&!ga(e.bind),parse:(e,t,n)=>dd(t,n),topLevelSignals:(e,t,n)=>{const i=t.name,r=t.project,s=t.bind,o=t.init&&t.init[0],a=id.defined(t)?"(item().isVoronoi ? datum.datum : datum)":"datum";return r.items.forEach((c,l)=>{const u=ie(`${i}_${c.field}`);n.filter(d=>d.name===u).length||n.unshift({name:u,...o?{init:An(o[l])}:{value:null},on:t.events?[{events:t.events,update:`datum && item().mark.marktype !== 'group' ? ${a}[${Y$1(c.field)}] : null`}]:[],bind:s[c.field]??s[c.channel]??s});}),n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.filter(l=>l.name===i+Vt)[0],o=i+Yi,a=r.items.map(l=>ie(`${i}_${l.field}`)),c=a.map(l=>`${l} !== null`).join(" && ");return a.length&&(s.update=`${c} ? {fields: ${o}, values: [${a.join(", ")}]} : null`),delete s.value,delete s.on,n}},$r="_toggle",sd={defined:e=>e.type==="point"&&!!e.toggle,signals:(e,t,n)=>n.concat({name:t.name+$r,value:!1,on:[{events:t.events,update:t.toggle}]}),modifyExpr:(e,t)=>{const n=t.name+Vt,i=t.name+$r;return `${i} ? null : ${n}, `+(t.resolve==="global"?`${i} ? null : true, `:`${i} ? null : {unit: ${mn(e)}}, `)+`${i} ? ${n} : null`}},fx={defined:e=>e.clear!==void 0&&e.clear!==!1,parse:(e,t)=>{t.clear&&(t.clear=xt$1(t.clear)?t5(t.clear,"view"):t.clear);},topLevelSignals:(e,t,n)=>{if(rd.defined(t))for(const i of t.project.items){const r=n.findIndex(s=>s.name===ie(`${t.name}_${i.field}`));r!==-1&&n[r].on.push({events:t.clear,update:"null"});}return n},signals:(e,t,n)=>{function i(r,s){r!==-1&&n[r].on&&n[r].on.push({events:t.clear,update:s});}if(t.type==="interval")for(const r of t.project.items){const s=n.findIndex(o=>o.name===r.signals.visual);if(i(s,"[0, 0]"),s===-1){const o=n.findIndex(a=>a.name===r.signals.data);i(o,"null");}}else {let r=n.findIndex(s=>s.name===t.name+Vt);i(r,"null"),sd.defined(t)&&(r=n.findIndex(s=>s.name===t.name+$r),i(r,"false"));}return n}},od={defined:e=>{const t=e.resolve==="global"&&e.bind&&ga(e.bind),n=e.project.items.length===1&&e.project.items[0].field!==Je;return t&&!n&&v(zg),t&&n},parse:(e,t,n)=>{const i=L(n);if(i.select=xt$1(i.select)?{type:i.select,toggle:t.toggle}:{...i.select,toggle:t.toggle},dd(t,i),et$1(n.select)&&(n.select.on||n.select.clear)){const o='event.item && indexof(event.item.mark.role, "legend") < 0';for(const a of t.events)a.filter=H$1(a.filter??[]),a.filter.includes(o)||a.filter.push(o);}const r=ws(t.bind)?t.bind.legend:"click",s=xt$1(r)?t5(r,"view"):H$1(r);t.bind={legend:{merge:s}};},topLevelSignals:(e,t,n)=>{const i=t.name,r=ws(t.bind)&&t.bind.legend,s=o=>a=>{const c=L(a);return c.markname=o,c};for(const o of t.project.items){if(!o.hasLegend)continue;const a=`${ie(o.field)}_legend`,c=`${i}_${a}`;if(n.filter(u=>u.name===c).length===0){const u=r.merge.map(s(`${a}_symbols`)).concat(r.merge.map(s(`${a}_labels`))).concat(r.merge.map(s(`${a}_entries`)));n.unshift({name:c,...t.init?{}:{value:null},on:[{events:u,update:"isDefined(datum.value) ? datum.value : item().items[0].items[0].datum.value",force:!0},{events:r.merge,update:`!event.item || !datum ? null : ${c}`,force:!0}]});}}return n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.find(d=>d.name===i+Vt),o=i+Yi,a=r.items.filter(d=>d.hasLegend).map(d=>ie(`${i}_${ie(d.field)}_legend`)),l=`${a.map(d=>`${d} !== null`).join(" && ")} ? {fields: ${o}, values: [${a.join(", ")}]} : null`;t.events&&a.length>0?s.on.push({events:a.map(d=>({signal:d})),update:l}):a.length>0&&(s.update=l,delete s.value,delete s.on);const u=n.find(d=>d.name===i+$r),f=ws(t.bind)&&t.bind.legend;return u&&(t.events?u.on.push({...u.on[0],events:f}):u.on[0].events=f),n}};function dx(e,t,n){const i=e.fieldDef(t)?.field;for(const r of xe(e.component.selection??{})){const s=r.project.hasField[i]??r.project.hasChannel[t];if(s&&od.defined(r)){const o=n.get("selections")??[];o.push(r.name),n.set("selections",o,!1),s.hasLegend=!0;}}}const ad="_translate_anchor",cd="_translate_delta",px={defined:e=>e.type==="interval"&&e.translate,signals:(e,t,n)=>{const i=t.name,r=Ft.defined(t),s=i+ad,{x:o,y:a}=t.project.hasChannel;let c=t5(t.translate,"scope");return r||(c=c.map(l=>(l.between[0].markname=i+Hn,l))),n.push({name:s,value:{},on:[{events:c.map(l=>l.between[0]),update:"{x: x(unit), y: y(unit)"+(o!==void 0?`, extent_x: ${r?to(e,re):`slice(${o.signals.visual})`}`:"")+(a!==void 0?`, extent_y: ${r?to(e,he):`slice(${a.signals.visual})`}`:"")+"}"}]},{name:i+cd,value:{},on:[{events:c,update:`{x: ${s}.x - x(unit), y: ${s}.y - y(unit)}`}]}),o!==void 0&&Bc(e,t,o,"width",n),a!==void 0&&Bc(e,t,a,"height",n),n}};function Bc(e,t,n,i,r){const s=t.name,o=s+ad,a=s+cd,c=n.channel,l=Ft.defined(t),u=r.filter(w=>w.name===n.signals[l?"data":"visual"])[0],f=e.getSizeSignalRef(i).signal,d=e.getScaleComponent(c),g=d&&d.get("type"),p=d&&d.get("reverse"),h=l?c===re?p?"":"-":p?"-":"":"",m=`${o}.extent_${c}`,y=`${h}${a}.${c} / ${l?`${f}`:`span(${m})`}`,b=!l||!d?"panLinear":g==="log"?"panLog":g==="symlog"?"panSymlog":g==="pow"?"panPow":"panLinear",O=l?g==="pow"?`, ${d.get("exponent")??1}`:g==="symlog"?`, ${d.get("constant")??1}`:"":"",C=`${b}(${m}, ${y}${O})`;u.on.push({events:{signal:a},update:l?C:`clampRange(${C}, 0, ${f})`});}const ld="_zoom_anchor",ud="_zoom_delta",gx={defined:e=>e.type==="interval"&&e.zoom,signals:(e,t,n)=>{const i=t.name,r=Ft.defined(t),s=i+ud,{x:o,y:a}=t.project.hasChannel,c=Y$1(e.scaleName(re)),l=Y$1(e.scaleName(he));let u=t5(t.zoom,"scope");return r||(u=u.map(f=>(f.markname=i+Hn,f))),n.push({name:i+ld,on:[{events:u,update:r?"{"+[c?`x: invert(${c}, x(unit))`:"",l?`y: invert(${l}, y(unit))`:""].filter(f=>f).join(", ")+"}":"{x: x(unit), y: y(unit)}"}]},{name:s,on:[{events:u,force:!0,update:"pow(1.001, event.deltaY * pow(16, event.deltaMode))"}]}),o!==void 0&&Uc(e,t,o,"width",n),a!==void 0&&Uc(e,t,a,"height",n),n}};function Uc(e,t,n,i,r){const s=t.name,o=n.channel,a=Ft.defined(t),c=r.filter(b=>b.name===n.signals[a?"data":"visual"])[0],l=e.getSizeSignalRef(i).signal,u=e.getScaleComponent(o),f=u&&u.get("type"),d=a?to(e,o):c.name,g=s+ud,p=`${s}${ld}.${o}`,h=!a||!u?"zoomLinear":f==="log"?"zoomLog":f==="symlog"?"zoomSymlog":f==="pow"?"zoomPow":"zoomLinear",m=a?f==="pow"?`, ${u.get("exponent")??1}`:f==="symlog"?`, ${u.get("constant")??1}`:"":"",y=`${h}(${d}, ${p}, ${g}${m})`;c.on.push({events:{signal:g},update:a?y:`clampRange(${y}, 0, ${l})`});}const Cn="_store",Vt="_tuple",hx="_modify",fd="vlSelectionResolve",ns=[Xb,Hb,Gb,sd,rd,Ft,od,fx,px,gx,id];function mx(e){let t=e.parent;for(;t&&!Ke(t);)t=t.parent;return t}function mn(e,{escape:t}={escape:!0}){let n=t?Y$1(e.name):e.name;const i=mx(e);if(i){const{facet:r}=i;for(const s of De)r[s]&&(n+=` + '__facet_${s}_' + (facet[${Y$1(i.vgField(s))}])`);}return n}function Aa(e){return xe(e.component.selection??{}).reduce((t,n)=>t||n.project.hasSelectionId,!1)}function dd(e,t){(xt$1(t.select)||!t.select.on)&&delete e.events,(xt$1(t.select)||!t.select.clear)&&delete e.clear,(xt$1(t.select)||!t.select.toggle)&&delete e.toggle;}function no(e){const t=[];return e.type==="Identifier"?[e.name]:e.type==="Literal"?[e.value]:(e.type==="MemberExpression"&&(t.push(...no(e.object)),t.push(...no(e.property))),t)}function pd(e){return e.object.type==="MemberExpression"?pd(e.object):e.object.name==="datum"}function gd(e){const t=pP(e),n=new Set;return t.visit(i=>{i.type==="MemberExpression"&&pd(i)&&n.add(no(i).slice(1).join("."));}),n}class mi extends X{clone(){return new mi(null,this.model,L(this.filter))}constructor(t,n,i){super(t),this.model=n,this.filter=i,this.expr=wr(this.model,this.filter,this),this._dependentFields=gd(this.expr);}dependentFields(){return this._dependentFields}producedFields(){return new Set}assemble(){return {type:"filter",expr:this.expr}}hash(){return `Filter ${this.expr}`}}function yx(e,t){const n={},i=e.config.selection;if(!t||!t.length)return n;for(const r of t){const s=ie(r.name),o=r.select,a=xt$1(o)?o:o.type,c=et$1(o)?L(o):{type:a},l=i[a];for(const d in l)d==="fields"||d==="encodings"||(d==="mark"&&(c[d]={...l[d],...c[d]}),(c[d]===void 0||c[d]===!0)&&(c[d]=L(l[d]??c[d])));const u=n[s]={...c,name:s,type:a,init:r.value,bind:r.bind,events:xt$1(c.on)?t5(c.on,"scope"):H$1(L(c.on))},f=L(r);for(const d of ns)d.defined(u)&&d.parse&&d.parse(e,u,f);}return n}function hd(e,t,n,i="datum"){const r=xt$1(t)?t:t.param,s=ie(r),o=Y$1(s+Cn);let a;try{a=e.getSelectionComponent(s,r);}catch{return `!!${s}`}if(a.project.timeUnit){const d=n??e.component.data.raw,g=a.project.timeUnit.clone();d.parent?g.insertAsParentOf(d):d.parent=g;}const c=a.project.hasSelectionId?"vlSelectionIdTest(":"vlSelectionTest(",l=a.resolve==="global"?")":`, ${Y$1(a.resolve)})`,u=`${c}${o}, ${i}${l}`,f=`length(data(${o}))`;return t.empty===!1?`${f} && ${u}`:`!${f} || ${u}`}function md(e,t,n){const i=ie(t),r=n.encoding;let s=n.field,o;try{o=e.getSelectionComponent(i,t);}catch{return i}if(!r&&!s)s=o.project.items[0].field,o.project.items.length>1&&v(`A "field" or "encoding" must be specified when using a selection as a scale domain. Using "field": ${Y$1(s)}.`);else if(r&&!s){const a=o.project.items.filter(c=>c.channel===r);!a.length||a.length>1?(s=o.project.items[0].field,v((a.length?"Multiple ":"No ")+`matching ${Y$1(r)} encoding found for selection ${Y$1(n.param)}. Using "field": ${Y$1(s)}.`)):s=a[0].field;}return `${o.name}[${Y$1(Me(s))}]`}function bx(e,t){for(const[n,i]of Wt(e.component.selection??{})){const r=e.getName(`lookup_${n}`);e.component.data.outputNodes[r]=i.materialized=new we(new mi(t,e,{param:n}),r,J.Lookup,e.component.data.outputNodeRefCounts);}}function wr(e,t,n){return _i(t,i=>xt$1(i)?i:Zh(i)?hd(e,i,n):Iu(i))}function xx(e,t){if(e)return U$1(e)&&!jt(e)?e.map(n=>sa(n,t)).join(", "):e}function _s(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i;}function Ni(e,t,n,i={header:!1}){const{disable:r,orient:s,scale:o,labelExpr:a,title:c,zindex:l,...u}=e.combine();if(!r){for(const f in u){const d=ny[f],g=u[f];if(d&&d!==t&&d!=="both")delete u[f];else if(Xi(g)){const{condition:p,...h}=g,m=H$1(p),y=bc[f];if(y){const{vgProp:b,part:O}=y,C=[...m.map(w=>{const{test:M,...D}=w;return {test:wr(null,M),...D}}),h];_s(u,O,b,C),delete u[f];}else if(y===null){const b={signal:m.map(O=>{const{test:C,...w}=O;return `${wr(null,C)} ? ${nc(w)} : `}).join("")+nc(h)};u[f]=b;}}else if(k(g)){const p=bc[f];if(p){const{vgProp:h,part:m}=p;_s(u,m,h,g),delete u[f];}}W(["labelAlign","labelBaseline"],f)&&u[f]===null&&delete u[f];}if(t==="grid"){if(!u.grid)return;if(u.encode){const{grid:f}=u.encode;u.encode={...f?{grid:f}:{}},Y(u.encode)&&delete u.encode;}return {scale:o,orient:s,...u,domain:!1,labels:!1,aria:!1,maxExtent:0,minExtent:0,ticks:!1,zindex:ue(l,0)}}else {if(!i.header&&e.mainExtracted)return;if(a!==void 0){let d=a;u.encode?.labels?.update&&k(u.encode.labels.update.text)&&(d=xn(a,"datum.label",u.encode.labels.update.text.signal)),_s(u,"labels","text",{signal:d});}if(u.labelAlign===null&&delete u.labelAlign,u.encode){for(const d of ff)e.hasAxisPart(d)||delete u.encode[d];Y(u.encode)&&delete u.encode;}const f=xx(c,n);return {scale:o,orient:s,grid:!1,...f?{title:f}:{},...u,...n.aria===!1?{aria:!1}:{},zindex:ue(l,0)}}}}function yd(e){const{axes:t}=e.component,n=[];for(const i of vt)if(t[i]){for(const r of t[i])if(!r.get("disable")&&!r.get("gridScale")){const s=i==="x"?"height":"width",o=e.getSizeSignalRef(s).signal;s!==o&&n.push({name:s,update:o});}}return n}function vx(e,t){const{x:n=[],y:i=[]}=e;return [...n.map(r=>Ni(r,"grid",t)),...i.map(r=>Ni(r,"grid",t)),...n.map(r=>Ni(r,"main",t)),...i.map(r=>Ni(r,"main",t))].filter(r=>r)}function Wc(e,t,n,i){return Object.assign.apply(null,[{},...e.map(r=>{if(r==="axisOrient"){const s=n==="x"?"bottom":"left",o=t[n==="x"?"axisBottom":"axisLeft"]||{},a=t[n==="x"?"axisTop":"axisRight"]||{},c=new Set([...x(o),...x(a)]),l={};for(const u of c.values())l[u]={signal:`${i.signal} === "${s}" ? ${He(o[u])} : ${He(a[u])}`};return l}return t[r]})])}function Sx(e,t,n,i){const r=t==="band"?["axisDiscrete","axisBand"]:t==="point"?["axisDiscrete","axisPoint"]:zu(t)?["axisQuantitative"]:t==="time"||t==="utc"?["axisTemporal"]:[],s=e==="x"?"axisX":"axisY",o=k(n)?"axisOrient":`axis${Mi(n)}`,a=[...r,...r.map(l=>s+l.substr(4))],c=["axis",o,s];return {vlOnlyAxisConfig:Wc(a,i,e,n),vgAxisConfig:Wc(c,i,e,n),axisConfigStyle:Ex([...c,...a],i)}}function Ex(e,t){const n=[{}];for(const i of e){let r=t[i]?.style;if(r){r=H$1(r);for(const s of r)n.push(t.style[s]);}}return Object.assign.apply(null,n)}function io(e,t,n,i={}){const r=fu(e,n,t);if(r!==void 0)return {configFrom:"style",configValue:r};for(const s of ["vlOnlyAxisConfig","vgAxisConfig","axisConfigStyle"])if(i[s]?.[e]!==void 0)return {configFrom:s,configValue:i[s][e]};return {}}const Gc={scale:({model:e,channel:t})=>e.scaleName(t),format:({format:e})=>e,formatType:({formatType:e})=>e,grid:({fieldOrDatumDef:e,axis:t,scaleType:n})=>t.grid??$x(n,e),gridScale:({model:e,channel:t})=>wx(e,t),labelAlign:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelAlign||xd(t,n,i),labelAngle:({labelAngle:e})=>e,labelBaseline:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelBaseline||bd(t,n,i),labelFlush:({axis:e,fieldOrDatumDef:t,channel:n})=>e.labelFlush??Cx(t.type,n),labelOverlap:({axis:e,fieldOrDatumDef:t,scaleType:n})=>e.labelOverlap??Nx(t.type,n,S(t)&&!!t.timeUnit,S(t)?t.sort:void 0),orient:({orient:e})=>e,tickCount:({channel:e,model:t,axis:n,fieldOrDatumDef:i,scaleType:r})=>{const s=e==="x"?"width":e==="y"?"height":void 0,o=s?t.getSizeSignalRef(s):void 0;return n.tickCount??_x({fieldOrDatumDef:i,scaleType:r,size:o,values:n.values})},tickMinStep:kx,title:({axis:e,model:t,channel:n})=>{if(e.title!==void 0)return e.title;const i=vd(t,n);if(i!==void 0)return i;const r=t.typedFieldDef(n),s=n==="x"?"x2":"y2",o=t.fieldDef(s);return pu(r?[mc(r)]:[],S(o)?[mc(o)]:[])},values:({axis:e,fieldOrDatumDef:t})=>Ox(e,t),zindex:({axis:e,fieldOrDatumDef:t,mark:n})=>e.zindex??Tx(n,t)};function $x(e,t){return !ye(e)&&S(t)&&!ee(t?.bin)&&!be(t?.bin)}function wx(e,t){const n=t==="x"?"y":"x";if(e.getScaleComponent(n))return e.scaleName(n)}function Ax(e,t,n,i,r){const s=t?.labelAngle;if(s!==void 0)return k(s)?s:Ri(s);{const{configValue:o}=io("labelAngle",i,t?.style,r);return o!==void 0?Ri(o):n===re&&W([Vo,Ho],e.type)&&!(S(e)&&e.timeUnit)?270:void 0}}function ro(e){return `(((${e.signal} % 360) + 360) % 360)`}function bd(e,t,n,i){if(e!==void 0)if(n==="x"){if(k(e)){const r=ro(e),s=k(t)?`(${t.signal} === "top")`:t==="top";return {signal:`(45 < ${r} && ${r} < 135) || (225 < ${r} && ${r} < 315) ? "middle" :(${r} <= 45 || 315 <= ${r}) === ${s} ? "bottom" : "top"`}}if(45<e&&e<135||225<e&&e<315)return "middle";if(k(t)){const r=e<=45||315<=e?"===":"!==";return {signal:`${t.signal} ${r} "top" ? "bottom" : "top"`}}return (e<=45||315<=e)==(t==="top")?"bottom":"top"}else {if(k(e)){const r=ro(e),s=k(t)?`(${t.signal} === "left")`:t==="left";return {signal:`${r} <= 45 || 315 <= ${r} || (135 <= ${r} && ${r} <= 225) ? ${i?'"middle"':"null"} : (45 <= ${r} && ${r} <= 135) === ${s} ? "top" : "bottom"`}}if(e<=45||315<=e||135<=e&&e<=225)return i?"middle":null;if(k(t)){const r=45<=e&&e<=135?"===":"!==";return {signal:`${t.signal} ${r} "left" ? "top" : "bottom"`}}return (45<=e&&e<=135)==(t==="left")?"top":"bottom"}}function xd(e,t,n){if(e===void 0)return;const i=n==="x",r=i?0:90,s=i?"bottom":"left";if(k(e)){const o=ro(e),a=k(t)?`(${t.signal} === "${s}")`:t===s;return {signal:`(${r?`(${o} + 90)`:o} % 180 === 0) ? ${i?null:'"center"'} :(${r} < ${o} && ${o} < ${180+r}) === ${a} ? "left" : "right"`}}if((e+r)%180===0)return i?null:"center";if(k(t)){const o=r<e&&e<180+r?"===":"!==";return {signal:`${`${t.signal} ${o} "${s}"`} ? "left" : "right"`}}return (r<e&&e<180+r)==(t===s)?"left":"right"}function Cx(e,t){if(t==="x"&&W(["quantitative","temporal"],e))return !0}function Nx(e,t,n,i){if(n&&!et$1(i)||e!=="nominal"&&e!=="ordinal")return t==="log"||t==="symlog"?"greedy":!0}function Fx(e){return e==="x"?"bottom":"left"}function _x({fieldOrDatumDef:e,scaleType:t,size:n,values:i}){if(!i&&!ye(t)&&t!=="log"){if(S(e)){if(ee(e.bin))return {signal:`ceil(${n.signal}/10)`};if(e.timeUnit&&W(["month","hours","day","quarter"],ge(e.timeUnit)?.unit))return}return {signal:`ceil(${n.signal}/40)`}}}function kx({format:e,fieldOrDatumDef:t}){if(e==="d")return 1;if(S(t)){const{timeUnit:n}=t;if(n){const i=ku(n);if(i)return {signal:i}}}}function vd(e,t){const n=t==="x"?"x2":"y2",i=e.fieldDef(t),r=e.fieldDef(n),s=i?i.title:void 0,o=r?r.title:void 0;if(s&&o)return gu(s,o);if(s)return s;if(o)return o;if(s!==void 0)return s;if(o!==void 0)return o}function Ox(e,t){const n=e.values;if(U$1(n))return uf(t,n);if(k(n))return n}function Tx(e,t){return e==="rect"&&mr(t)?1:0}class oi extends X{clone(){return new oi(null,L(this.transform))}constructor(t,n){super(t),this.transform=n,this._dependentFields=gd(this.transform.calculate);}static parseAllForSortIndex(t,n){return n.forEachFieldDef((i,r)=>{if(Rn(i)&&Ju(i.sort)){const{field:s,timeUnit:o}=i,a=i.sort,c=a.map((l,u)=>`${Iu({field:s,timeUnit:o,equal:l})} ? ${u} : `).join("")+a.length;t=new oi(t,{calculate:c,as:ai(i,r,{forAs:!0})});}}),t}producedFields(){return new Set([this.transform.as])}dependentFields(){return this._dependentFields}assemble(){return {type:"formula",expr:this.transform.calculate,as:this.transform.as}}hash(){return `Calculate ${U(this.transform)}`}}function ai(e,t,n){return $(e,{prefix:t,suffix:"sort_index",...n??{}})}function is(e,t){return W(["top","bottom"],t)?"column":W(["left","right"],t)||e==="row"?"row":"column"}function ci(e,t,n,i){const r=i==="row"?n.headerRow:i==="column"?n.headerColumn:n.headerFacet;return ue((t||{})[e],r[e],n.header[e])}function rs(e,t,n,i){const r={};for(const s of e){const o=ci(s,t||{},n,i);o!==void 0&&(r[s]=o);}return r}const Ca=["row","column"],Na=["header","footer"];function Ix(e,t){const n=e.component.layoutHeaders[t].title,i=e.config?e.config:void 0,r=e.component.layoutHeaders[t].facetFieldDef?e.component.layoutHeaders[t].facetFieldDef:void 0,{titleAnchor:s,titleAngle:o,titleOrient:a}=rs(["titleAnchor","titleAngle","titleOrient"],r.header,i,t),c=is(t,a),l=Ri(o);return {name:`${t}-title`,type:"group",role:`${c}-title`,title:{text:n,...t==="row"?{orient:"left"}:{},style:"guide-title",...Ed(l,c),...Sd(c,l,s),...$d(i,r,t,wy,Nf)}}}function Sd(e,t,n="middle"){switch(n){case"start":return {align:"left"};case"end":return {align:"right"}}const i=xd(t,e==="row"?"left":"top",e==="row"?"y":"x");return i?{align:i}:{}}function Ed(e,t){const n=bd(e,t==="row"?"left":"top",t==="row"?"y":"x",!0);return n?{baseline:n}:{}}function Rx(e,t){const n=e.component.layoutHeaders[t],i=[];for(const r of Na)if(n[r])for(const s of n[r]){const o=Lx(e,t,r,n,s);o!=null&&i.push(o);}return i}function Px(e,t){const{sort:n}=e;return ut(n)?{field:$(n,{expr:"datum"}),order:n.order??"ascending"}:U$1(n)?{field:ai(e,t,{expr:"datum"}),order:"ascending"}:{field:$(e,{expr:"datum"}),order:n??"ascending"}}function so(e,t,n){const{format:i,formatType:r,labelAngle:s,labelAnchor:o,labelOrient:a,labelExpr:c}=rs(["format","formatType","labelAngle","labelAnchor","labelOrient","labelExpr"],e.header,n,t),l=ta({fieldOrDatumDef:e,format:i,formatType:r,expr:"parent",config:n}).signal,u=is(t,a);return {text:{signal:c?xn(xn(c,"datum.label",l),"datum.value",$(e,{expr:"parent"})):l},...t==="row"?{orient:"left"}:{},style:"guide-label",frame:"group",...Ed(s,u),...Sd(u,s,o),...$d(n,e,t,Ay,Ff)}}function Lx(e,t,n,i,r){if(r){let s=null;const{facetFieldDef:o}=i,a=e.config?e.config:void 0;if(o&&r.labels){const{labelOrient:f}=rs(["labelOrient"],o.header,a,t);(t==="row"&&!W(["top","bottom"],f)||t==="column"&&!W(["left","right"],f))&&(s=so(o,t,a));}const c=Ke(e)&&!qi(e.facet),l=r.axes,u=l?.length>0;if(s||u){const f=t==="row"?"height":"width";return {name:e.getName(`${t}_${n}`),type:"group",role:`${t}-${n}`,...i.facetFieldDef?{from:{data:e.getName(`${t}_domain`)},sort:Px(o,t)}:{},...u&&c?{from:{data:e.getName(`facet_domain_${t}`)}}:{},...s?{title:s}:{},...r.sizeSignal?{encode:{update:{[f]:r.sizeSignal}}}:{},...u?{axes:l}:{}}}}return null}const zx={column:{start:0,end:1},row:{start:1,end:0}};function Dx(e,t){return zx[t][e]}function Mx(e,t){const n={};for(const i of De){const r=e[i];if(r?.facetFieldDef){const{titleAnchor:s,titleOrient:o}=rs(["titleAnchor","titleOrient"],r.facetFieldDef.header,t,i),a=is(i,o),c=Dx(s,a);c!==void 0&&(n[a]=c);}}return Y(n)?void 0:n}function $d(e,t,n,i,r){const s={};for(const o of i){if(!r[o])continue;const a=ci(o,t?.header,e,n);a!==void 0&&(s[r[o]]=a);}return s}function Fa(e){return [...tr(e,"width"),...tr(e,"height"),...tr(e,"childWidth"),...tr(e,"childHeight")]}function tr(e,t){const n=t==="width"?"x":"y",i=e.component.layoutSize.get(t);if(!i||i==="merged")return [];const r=e.getSizeSignalRef(t).signal;if(i==="step"){const s=e.getScaleComponent(n);if(s){const o=s.get("type"),a=s.get("range");if(ye(o)&&tn(a)){const c=e.scaleName(n);return Ke(e.parent)&&e.parent.component.resolve.scale[n]==="independent"?[qc(c,a)]:[qc(c,a),{name:r,update:wd(c,s,`domain('${c}').length`)}]}}throw new Error("layout size is step although width/height is not step.")}else if(i=="container"){const s=r.endsWith("width"),o=s?"containerSize()[0]":"containerSize()[1]",a=Qs(e.config.view,s?"width":"height"),c=`isFinite(${o}) ? ${o} : ${a}`;return [{name:r,init:c,on:[{update:c,events:"window:resize"}]}]}else return [{name:r,value:i}]}function qc(e,t){const n=`${e}_step`;return k(t.step)?{name:n,update:t.step.signal}:{name:n,value:t.step}}function wd(e,t,n){const i=t.get("type"),r=t.get("padding"),s=ue(t.get("paddingOuter"),r);let o=t.get("paddingInner");return o=i==="band"?o!==void 0?o:r:1,`bandspace(${n}, ${He(o)}, ${He(s)}) * ${e}_step`}function Ad(e){return e==="childWidth"?"width":e==="childHeight"?"height":e}function Cd(e,t){return x(e).reduce((n,i)=>{const r=e[i];return {...n,...hi(t,r,i,s=>ne(s.value))}},{})}function Nd(e,t){if(Ke(t))return e==="theta"?"independent":"shared";if(xi(t))return "shared";if(Pa(t))return me(e)||e==="theta"||e==="radius"?"independent":"shared";throw new Error("invalid model type for resolve")}function _a(e,t){const n=e.scale[t],i=me(t)?"axis":"legend";return n==="independent"?(e[i][t]==="shared"&&v(Sh(t)),"independent"):e[i][t]||"shared"}const jx={...Fy,disable:1,labelExpr:1,selections:1,opacity:1,shape:1,stroke:1,fill:1,size:1,strokeWidth:1,strokeDash:1,encode:1},Fd=x(jx);class Bx extends Dt{}const Hc={symbols:Ux,gradient:Wx,labels:Gx,entries:qx};function Ux(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r,legendType:s}){if(s!=="symbol")return;const{markDef:o,encoding:a,config:c,mark:l}=n,u=o.filled&&l!=="trail";let f={...Og({},n,wm),...ed(n,{filled:u})};const d=r.get("symbolOpacity")??c.legend.symbolOpacity,g=r.get("symbolFillColor")??c.legend.symbolFillColor,p=r.get("symbolStrokeColor")??c.legend.symbolStrokeColor,h=d===void 0?_d(a.opacity)??o.opacity:void 0;if(f.fill){if(i==="fill"||u&&i===Oe)delete f.fill;else if(f.fill.field)g?delete f.fill:(f.fill=ne(c.legend.symbolBaseFillColor??"black"),f.fillOpacity=ne(h??1));else if(U$1(f.fill)){const m=oo(a.fill??a.color)??o.fill??(u&&o.color);m&&(f.fill=ne(m));}}if(f.stroke){if(i==="stroke"||!u&&i===Oe)delete f.stroke;else if(f.stroke.field||p)delete f.stroke;else if(U$1(f.stroke)){const m=ue(oo(a.stroke||a.color),o.stroke,u?o.color:void 0);m&&(f.stroke={value:m});}}if(i!==Pt){const m=S(t)&&Od(n,r,t);m?f.opacity=[{test:m,...ne(h??1)},ne(c.legend.unselectedOpacity)]:h&&(f.opacity=ne(h));}return f={...f,...e},Y(f)?void 0:f}function Wx(e,{model:t,legendType:n,legendCmpt:i}){if(n!=="gradient")return;const{config:r,markDef:s,encoding:o}=t;let a={};const l=(i.get("gradientOpacity")??r.legend.gradientOpacity)===void 0?_d(o.opacity)||s.opacity:void 0;return l&&(a.opacity=ne(l)),a={...a,...e},Y(a)?void 0:a}function Gx(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r}){const s=n.legend(i)||{},o=n.config,a=S(t)?Od(n,r,t):void 0,c=a?[{test:a,value:1},{value:o.legend.unselectedOpacity}]:void 0,{format:l,formatType:u}=s;let f;wn(u)?f=Xe({fieldOrDatumDef:t,field:"datum.value",format:l,formatType:u,config:o}):l===void 0&&u===void 0&&o.customFormatTypes&&(t.type==="quantitative"&&o.numberFormatType?f=Xe({fieldOrDatumDef:t,field:"datum.value",format:o.numberFormat,formatType:o.numberFormatType,config:o}):t.type==="temporal"&&o.timeFormatType&&S(t)&&t.timeUnit===void 0&&(f=Xe({fieldOrDatumDef:t,field:"datum.value",format:o.timeFormat,formatType:o.timeFormatType,config:o})));const d={...c?{opacity:c}:{},...f?{text:f}:{},...e};return Y(d)?void 0:d}function qx(e,{legendCmpt:t}){return t.get("selections")?.length?{...e,fill:{value:"transparent"}}:e}function _d(e){return kd(e,(t,n)=>Math.max(t,n.value))}function oo(e){return kd(e,(t,n)=>ue(t,n.value))}function kd(e,t){if(Gm(e))return H$1(e.condition).reduce(t,e.value);if(Qe(e))return e.value}function Od(e,t,n){const i=t.get("selections");if(!i?.length)return;const r=Y$1(n.field);return i.map(s=>`(!length(data(${Y$1(ie(s)+Cn)})) || (${s}[${r}] && indexof(${s}[${r}], datum.value) >= 0))`).join(" || ")}const Vc={direction:({direction:e})=>e,format:({fieldOrDatumDef:e,legend:t,config:n})=>{const{format:i,formatType:r}=t;return Xu(e,e.type,i,r,n,!1)},formatType:({legend:e,fieldOrDatumDef:t,scaleType:n})=>{const{formatType:i}=e;return Yu(i,t,n)},gradientLength:e=>{const{legend:t,legendConfig:n}=e;return t.gradientLength??n.gradientLength??Jx(e)},labelOverlap:({legend:e,legendConfig:t,scaleType:n})=>e.labelOverlap??t.labelOverlap??Zx(n),symbolType:({legend:e,markDef:t,channel:n,encoding:i})=>e.symbolType??Vx(t.type,n,i.shape,t.shape),title:({fieldOrDatumDef:e,config:t})=>qn(e,t,{allowDisabling:!0}),type:({legendType:e,scaleType:t,channel:n})=>{if(Gn(n)&&Ve(t)){if(e==="gradient")return}else if(e==="symbol")return;return e},values:({fieldOrDatumDef:e,legend:t})=>Hx(t,e)};function Hx(e,t){const n=e.values;if(U$1(n))return uf(t,n);if(k(n))return n}function Vx(e,t,n,i){if(t!=="shape"){const r=oo(n)??i;if(r)return r}switch(e){case"bar":case"rect":case"image":case"square":return "square";case"line":case"trail":case"rule":return "stroke";case"arc":case"point":case"circle":case"tick":case"geoshape":case"area":case"text":return "circle"}}function Xx(e){const{legend:t}=e;return ue(t.type,Yx(e))}function Yx({channel:e,timeUnit:t,scaleType:n}){if(Gn(e)){if(W(["quarter","month","day"],t))return "symbol";if(Ve(n))return "gradient"}return "symbol"}function Kx({legendConfig:e,legendType:t,orient:n,legend:i}){return i.direction??e[t?"gradientDirection":"symbolDirection"]??Qx(n,t)}function Qx(e,t){switch(e){case"top":case"bottom":return "horizontal";case"left":case"right":case"none":case void 0:return;default:return t==="gradient"?"horizontal":void 0}}function Jx({legendConfig:e,model:t,direction:n,orient:i,scaleType:r}){const{gradientHorizontalMaxLength:s,gradientHorizontalMinLength:o,gradientVerticalMaxLength:a,gradientVerticalMinLength:c}=e;if(Ve(r))return n==="horizontal"?i==="top"||i==="bottom"?Xc(t,"width",o,s):o:Xc(t,"height",c,a)}function Xc(e,t,n,i){return {signal:`clamp(${e.getSizeSignalRef(t).signal}, ${n}, ${i})`}}function Zx(e){if(W(["quantile","threshold","log","symlog"],e))return "greedy"}function Td(e){const t=oe(e)?ev(e):rv(e);return e.component.legends=t,t}function ev(e){const{encoding:t}=e,n={};for(const i of [Oe,...kf]){const r=fe(t[i]);!r||!e.getScaleComponent(i)||i===Te&&S(r)&&r.type===gi||(n[i]=iv(e,i));}return n}function tv(e,t){const n=e.scaleName(t);if(e.mark==="trail"){if(t==="color")return {stroke:n};if(t==="size")return {strokeWidth:n}}return t==="color"?e.markDef.filled?{fill:n}:{stroke:n}:{[t]:n}}function nv(e,t,n,i){switch(t){case"disable":return n!==void 0;case"values":return !!n?.values;case"title":if(t==="title"&&e===i?.title)return !0}return e===(n||{})[t]}function iv(e,t){let n=e.legend(t);const{markDef:i,encoding:r,config:s}=e,o=s.legend,a=new Bx({},tv(e,t));dx(e,t,a);const c=n!==void 0?!n:o.disable;if(a.set("disable",c,n!==void 0),c)return a;n=n||{};const l=e.getScaleComponent(t).get("type"),u=fe(r[t]),f=S(u)?ge(u.timeUnit)?.unit:void 0,d=n.orient||s.legend.orient||"right",g=Xx({legend:n,channel:t,timeUnit:f,scaleType:l}),p=Kx({legend:n,legendType:g,orient:d,legendConfig:o}),h={legend:n,channel:t,model:e,markDef:i,encoding:r,fieldOrDatumDef:u,legendConfig:o,config:s,scaleType:l,orient:d,legendType:g,direction:p};for(const C of Fd){if(g==="gradient"&&C.startsWith("symbol")||g==="symbol"&&C.startsWith("gradient"))continue;const w=C in Vc?Vc[C](h):n[C];if(w!==void 0){const M=nv(w,C,n,e.fieldDef(t));(M||s.legend[C]===void 0)&&a.set(C,w,M);}}const m=n?.encoding??{},y=a.get("selections"),b={},O={fieldOrDatumDef:u,model:e,channel:t,legendCmpt:a,legendType:g};for(const C of ["labels","legend","title","symbols","gradient","entries"]){const w=Cd(m[C]??{},e),M=C in Hc?Hc[C](w,O):w;M!==void 0&&!Y(M)&&(b[C]={...y?.length&&S(u)?{name:`${ie(u.field)}_legend_${C}`}:{},...y?.length?{interactive:!!y}:{},update:M});}return Y(b)||a.set("encode",b,!!n?.encoding),a}function rv(e){const{legends:t,resolve:n}=e.component;for(const i of e.children){Td(i);for(const r of x(i.component.legends))n.legend[r]=_a(e.component.resolve,r),n.legend[r]==="shared"&&(t[r]=Id(t[r],i.component.legends[r]),t[r]||(n.legend[r]="independent",delete t[r]));}for(const i of x(t))for(const r of e.children)r.component.legends[i]&&n.legend[i]==="shared"&&delete r.component.legends[i];return t}function Id(e,t){if(!e)return t.clone();const n=e.getWithExplicit("orient"),i=t.getWithExplicit("orient");if(n.explicit&&i.explicit&&n.value!==i.value)return;let r=!1;for(const s of Fd){const o=qt(e.getWithExplicit(s),t.getWithExplicit(s),s,"legend",(a,c)=>{switch(s){case"symbolType":return sv(a,c);case"title":return hu(a,c);case"type":return r=!0,Re("symbol")}return es(a,c,s,"legend")});e.setWithExplicit(s,o);}return r&&(e.implicit?.encode?.gradient&&ur(e.implicit,["encode","gradient"]),e.explicit?.encode?.gradient&&ur(e.explicit,["encode","gradient"])),e}function sv(e,t){return t.value==="circle"?t:e}function ov(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i;}function Rd(e){const t=e.component.legends,n={};for(const r of x(t)){const s=e.getScaleComponent(r),o=Z(s.get("domains"));if(n[o])for(const a of n[o])Id(a,t[r])||n[o].push(t[r]);else n[o]=[t[r].clone()];}return xe(n).flat().map(r=>av(r,e.config)).filter(r=>r!==void 0)}function av(e,t){const{disable:n,labelExpr:i,selections:r,...s}=e.combine();if(!n){if(t.aria===!1&&s.aria==null&&(s.aria=!1),s.encode?.symbols){const o=s.encode.symbols.update;o.fill&&o.fill.value!=="transparent"&&!o.stroke&&!s.stroke&&(o.stroke={value:"transparent"});for(const a of kf)s[a]&&delete o[a];}if(s.title||delete s.title,i!==void 0){let o=i;s.encode?.labels?.update&&k(s.encode.labels.update.text)&&(o=xn(i,"datum.label",s.encode.labels.update.text.signal)),ov(s,"labels","text",{signal:o});}return s}}function cv(e){return xi(e)||Pa(e)?lv(e):Pd(e)}function lv(e){return e.children.reduce((t,n)=>t.concat(n.assembleProjections()),Pd(e))}function Pd(e){const t=e.component.projection;if(!t||t.merged)return [];const n=t.combine(),{name:i}=n;if(t.data){const r={signal:`[${t.size.map(o=>o.signal).join(", ")}]`},s=t.data.reduce((o,a)=>{const c=k(a)?a.signal:`data('${e.lookupDataSource(a)}')`;return W(o,c)||o.push(c),o},[]);if(s.length<=0)throw new Error("Projection's fit didn't find any data sources");return [{name:i,size:r,fit:{signal:s.length>1?`[${s.join(", ")}]`:s[0]},...n}]}else return [{name:i,translate:{signal:"[width / 2, height / 2]"},...n}]}const uv=["type","clipAngle","clipExtent","center","rotate","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];class Ld extends Dt{constructor(t,n,i,r){super({...n},{name:t}),this.specifiedProjection=n,this.size=i,this.data=r,this.merged=!1;}get isFit(){return !!this.data}}function zd(e){e.component.projection=oe(e)?fv(e):gv(e);}function fv(e){if(e.hasProjection){const t=Ne(e.specifiedProjection),n=!(t&&(t.scale!=null||t.translate!=null)),i=n?[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]:void 0,r=n?dv(e):void 0,s=new Ld(e.projectionName(!0),{...Ne(e.config.projection)??{},...t??{}},i,r);return s.get("type")||s.set("type","equalEarth",!1),s}}function dv(e){const t=[],{encoding:n}=e;for(const i of [[nt,tt],[je,it]])(fe(n[i[0]])||fe(n[i[1]]))&&t.push({signal:e.getName(`geojson_${t.length}`)});return e.channelHasField(Te)&&e.typedFieldDef(Te).type===gi&&t.push({signal:e.getName(`geojson_${t.length}`)}),t.length===0&&t.push(e.requestDataName(J.Main)),t}function pv(e,t){const n=vo(uv,r=>!!(!X$1(e.explicit,r)&&!X$1(t.explicit,r)||X$1(e.explicit,r)&&X$1(t.explicit,r)&&ct(e.get(r),t.get(r))));if(ct(e.size,t.size)){if(n)return e;if(ct(e.explicit,{}))return t;if(ct(t.explicit,{}))return e}return null}function gv(e){if(e.children.length===0)return;let t;for(const i of e.children)zd(i);const n=vo(e.children,i=>{const r=i.component.projection;if(r)if(t){const s=pv(t,r);return s&&(t=s),!!s}else return t=r,!0;else return !0});if(t&&n){const i=e.projectionName(!0),r=new Ld(i,t.specifiedProjection,t.size,L(t.data));for(const s of e.children){const o=s.component.projection;o&&(o.isFit&&r.data.push(...s.component.projection.data),s.renameProjection(o.get("name"),i),o.merged=!0);}return r}}function hv(e,t,n,i){if(Vi(t,n)){const r=oe(e)?e.axis(n)??e.legend(n)??{}:{},s=$(t,{expr:"datum"}),o=$(t,{expr:"datum",binSuffix:"end"});return {formulaAs:$(t,{binSuffix:"range",forAs:!0}),formula:Gi(s,o,r.format,r.formatType,i)}}return {}}function Dd(e,t){return `${au(e)}_${t}`}function mv(e,t){return {signal:e.getName(`${t}_bins`),extentSignal:e.getName(`${t}_extent`)}}function ka(e,t,n){const i=Xr(n,void 0)??{},r=Dd(i,t);return e.getName(`${r}_bins`)}function yv(e){return "as"in e}function Yc(e,t,n){let i,r;yv(e)?i=xt$1(e.as)?[e.as,`${e.as}_end`]:[e.as[0],e.as[1]]:i=[$(e,{forAs:!0}),$(e,{binSuffix:"end",forAs:!0})];const s={...Xr(t,void 0)},o=Dd(s,e.field),{signal:a,extentSignal:c}=mv(n,o);if(zr(s.extent)){const u=s.extent;r=md(n,u.param,u),delete s.extent;}const l={bin:s,field:e.field,as:[i],...a?{signal:a}:{},...c?{extentSignal:c}:{},...r?{span:r}:{}};return {key:o,binComponent:l}}class dt extends X{clone(){return new dt(null,L(this.bins))}constructor(t,n){super(t),this.bins=n;}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s,o)=>{if(Ae(s)&&ee(s.bin)){const{key:a,binComponent:c}=Yc(s,s.bin,n);r[a]={...c,...r[a],...hv(n,s,o,n.config)};}return r},{});return Y(i)?null:new dt(t,i)}static makeFromTransform(t,n,i){const{key:r,binComponent:s}=Yc(n,n.bin,i);return new dt(t,{[r]:s})}merge(t,n){for(const i of x(t.bins))i in this.bins?(n(t.bins[i].signal,this.bins[i].signal),this.bins[i].as=lt([...this.bins[i].as,...t.bins[i].as],U)):this.bins[i]=t.bins[i];for(const i of t.children)t.removeChild(i),i.parent=this;t.remove();}producedFields(){return new Set(xe(this.bins).map(t=>t.as).flat(2))}dependentFields(){return new Set(xe(this.bins).map(t=>t.field))}hash(){return `Bin ${U(this.bins)}`}assemble(){return xe(this.bins).flatMap(t=>{const n=[],[i,...r]=t.as,{extent:s,...o}=t.bin,a={type:"bin",field:Me(t.field),as:i,signal:t.signal,...zr(s)?{extent:null}:{extent:s},...t.span?{span:{signal:`span(${t.span})`}}:{},...o};!s&&t.extentSignal&&(n.push({type:"extent",field:Me(t.field),signal:t.extentSignal}),a.extent={signal:t.extentSignal}),n.push(a);for(const c of r)for(let l=0;l<2;l++)n.push({type:"formula",expr:$({field:i[l]},{expr:"datum"}),as:c[l]});return t.formula&&n.push({type:"formula",expr:t.formula,as:t.formulaAs}),n})}}function bv(e,t,n,i){const r=oe(i)?i.encoding[xt(t)]:void 0;if(Ae(n)&&oe(i)&&tf(n,r,i.markDef,i.config))e.add($(n,{})),e.add($(n,{suffix:"end"})),n.bin&&Vi(n,t)&&e.add($(n,{binSuffix:"range"}));else if(Kl(t)){const s=Yl(t);e.add(i.getName(s));}else e.add($(n));return Rn(n)&&dm(n.scale?.range)&&e.add(n.scale.range.field),e}function xv(e,t){for(const n of x(t)){const i=t[n];for(const r of x(i))n in e?e[n][r]=new Set([...e[n][r]??[],...i[r]]):e[n]={[r]:i[r]};}}class Ye extends X{clone(){return new Ye(null,new Set(this.dimensions),L(this.measures))}constructor(t,n,i){super(t),this.dimensions=n,this.measures=i;}get groupBy(){return this.dimensions}static makeFromEncoding(t,n){let i=!1;n.forEachFieldDef(o=>{o.aggregate&&(i=!0);});const r={},s=new Set;return !i||(n.forEachFieldDef((o,a)=>{const{aggregate:c,field:l}=o;if(c)if(c==="count")r["*"]??(r["*"]={}),r["*"].count=new Set([$(o,{forAs:!0})]);else {if(kt(c)||en(c)){const u=kt(c)?"argmin":"argmax",f=c[u];r[f]??(r[f]={}),r[f][u]=new Set([$({op:u,field:f},{forAs:!0})]);}else r[l]??(r[l]={}),r[l][c]=new Set([$(o,{forAs:!0})]);Lt(a)&&n.scaleDomain(a)==="unaggregated"&&(r[l]??(r[l]={}),r[l].min=new Set([$({field:l,aggregate:"min"},{forAs:!0})]),r[l].max=new Set([$({field:l,aggregate:"max"},{forAs:!0})]));}else bv(s,a,o,n);}),s.size+x(r).length===0)?null:new Ye(t,s,r)}static makeFromTransform(t,n){const i=new Set,r={};for(const s of n.aggregate){const{op:o,field:a,as:c}=s;o&&(o==="count"?(r["*"]??(r["*"]={}),r["*"].count=new Set([c||$(s,{forAs:!0})])):(r[a]??(r[a]={}),r[a][o]=new Set([c||$(s,{forAs:!0})])));}for(const s of n.groupby??[])i.add(s);return i.size+x(r).length===0?null:new Ye(t,i,r)}merge(t){return Bl(this.dimensions,t.dimensions)?(xv(this.measures,t.measures),!0):(zh("different dimensions, cannot merge"),!1)}addDimensions(t){t.forEach(this.dimensions.add,this.dimensions);}dependentFields(){return new Set([...this.dimensions,...x(this.measures)])}producedFields(){const t=new Set;for(const n of x(this.measures))for(const i of x(this.measures[n])){const r=this.measures[n][i];r.size===0?t.add(`${i}_${n}`):r.forEach(t.add,t);}return t}hash(){return `Aggregate ${U({dimensions:this.dimensions,measures:this.measures})}`}assemble(){const t=[],n=[],i=[];for(const s of x(this.measures))for(const o of x(this.measures[s]))for(const a of this.measures[s][o])i.push(a),t.push(o),n.push(s==="*"?null:Me(s));return {type:"aggregate",groupby:[...this.dimensions].map(Me),ops:t,fields:n,as:i}}}class yi extends X{constructor(t,n,i,r){super(t),this.model=n,this.name=i,this.data=r;for(const s of De){const o=n.facet[s];if(o){const{bin:a,sort:c}=o;this[s]={name:n.getName(`${s}_domain`),fields:[$(o),...ee(a)?[$(o,{binSuffix:"end"})]:[]],...ut(c)?{sortField:c}:U$1(c)?{sortIndexField:ai(o,s)}:{}};}}this.childModel=n.child;}hash(){let t="Facet";for(const n of De)this[n]&&(t+=` ${n.charAt(0)}:${U(this[n])}`);return t}get fields(){const t=[];for(const n of De)this[n]?.fields&&t.push(...this[n].fields);return t}dependentFields(){const t=new Set(this.fields);for(const n of De)this[n]&&(this[n].sortField&&t.add(this[n].sortField.field),this[n].sortIndexField&&t.add(this[n].sortIndexField));return t}producedFields(){return new Set}getSource(){return this.name}getChildIndependentFieldsWithStep(){const t={};for(const n of vt){const i=this.childModel.component.scales[n];if(i&&!i.merged){const r=i.get("type"),s=i.get("range");if(ye(r)&&tn(s)){const o=ss(this.childModel,n),a=Ra(o);a?t[n]=a:v(Ro(n));}}}return t}assembleRowColumnHeaderData(t,n,i){const r={row:"y",column:"x",facet:void 0}[t],s=[],o=[],a=[];r&&i&&i[r]&&(n?(s.push(`distinct_${i[r]}`),o.push("max")):(s.push(i[r]),o.push("distinct")),a.push(`distinct_${i[r]}`));const{sortField:c,sortIndexField:l}=this[t];if(c){const{op:u=Gr,field:f}=c;s.push(f),o.push(u),a.push($(c,{forAs:!0}));}else l&&(s.push(l),o.push("max"),a.push(l));return {name:this[t].name,source:n??this.data,transform:[{type:"aggregate",groupby:this[t].fields,...s.length?{fields:s,ops:o,as:a}:{}}]}}assembleFacetHeaderData(t){const{columns:n}=this.model.layout,{layoutHeaders:i}=this.model.component,r=[],s={};for(const c of Ca){for(const l of Na){const u=(i[c]&&i[c][l])??[];for(const f of u)if(f.axes?.length>0){s[c]=!0;break}}if(s[c]){const l=`length(data("${this.facet.name}"))`,u=c==="row"?n?{signal:`ceil(${l} / ${n})`}:1:n?{signal:`min(${l}, ${n})`}:{signal:l};r.push({name:`${this.facet.name}_${c}`,transform:[{type:"sequence",start:0,stop:u}]});}}const{row:o,column:a}=s;return (o||a)&&r.unshift(this.assembleRowColumnHeaderData("facet",null,t)),r}assemble(){const t=[];let n=null;const i=this.getChildIndependentFieldsWithStep(),{column:r,row:s,facet:o}=this;if(r&&s&&(i.x||i.y)){n=`cross_${this.column.name}_${this.row.name}`;const a=[].concat(i.x??[],i.y??[]),c=a.map(()=>"distinct");t.push({name:n,source:this.data,transform:[{type:"aggregate",groupby:this.fields,fields:a,ops:c}]});}for(const a of [Nt,Ct])this[a]&&t.push(this.assembleRowColumnHeaderData(a,n,i));if(o){const a=this.assembleFacetHeaderData(i);a&&t.push(...a);}return t}}function Kc(e){return e.startsWith("'")&&e.endsWith("'")||e.startsWith('"')&&e.endsWith('"')?e.slice(1,-1):e}function vv(e,t){const n=$o(e);if(t==="number")return `toNumber(${n})`;if(t==="boolean")return `toBoolean(${n})`;if(t==="string")return `toString(${n})`;if(t==="date")return `toDate(${n})`;if(t==="flatten")return n;if(t.startsWith("date:")){const i=Kc(t.slice(5,t.length));return `timeParse(${n},'${i}')`}else if(t.startsWith("utc:")){const i=Kc(t.slice(4,t.length));return `utcParse(${n},'${i}')`}else return v(Gg(t)),null}function Sv(e){const t={};return lr(e.filter,n=>{if(Tu(n)){let i=null;Do(n)?i=Pe(n.equal):jo(n)?i=Pe(n.lte):Mo(n)?i=Pe(n.lt):Bo(n)?i=Pe(n.gt):Uo(n)?i=Pe(n.gte):Wo(n)?i=n.range[0]:Go(n)&&(i=(n.oneOf??n.in)[0]),i&&(Tn(i)?t[n.field]="date":Mr$1(i)?t[n.field]="number":xt$1(i)&&(t[n.field]="string")),n.timeUnit&&(t[n.field]="date");}}),t}function Ev(e){const t={};function n(i){ii(i)?t[i.field]="date":i.type==="quantitative"&&Eg(i.aggregate)?t[i.field]="number":Kn(i.field)>1?i.field in t||(t[i.field]="flatten"):Rn(i)&&ut(i.sort)&&Kn(i.sort.field)>1&&(i.sort.field in t||(t[i.sort.field]="flatten"));}if((oe(e)||Ke(e))&&e.forEachFieldDef((i,r)=>{if(Ae(i))n(i);else {const s=kn(r),o=e.fieldDef(s);n({...i,type:o.type});}}),oe(e)){const{mark:i,markDef:r,encoding:s}=e;if(nn(i)&&!e.encoding.order){const o=r.orient==="horizontal"?"y":"x",a=s[o];S(a)&&a.type==="quantitative"&&!(a.field in t)&&(t[a.field]="number");}}return t}function $v(e){const t={};if(oe(e)&&e.component.selection)for(const n of x(e.component.selection)){const i=e.component.selection[n];for(const r of i.project.items)!r.channel&&Kn(r.field)>1&&(t[r.field]="flatten");}return t}class $e extends X{clone(){return new $e(null,L(this._parse))}constructor(t,n){super(t),this._parse=n;}hash(){return `Parse ${U(this._parse)}`}static makeExplicit(t,n,i){let r={};const s=n.data;return !Ut(s)&&s?.format?.parse&&(r=s.format.parse),this.makeWithAncestors(t,r,{},i)}static makeWithAncestors(t,n,i,r){for(const a of x(i)){const c=r.getWithExplicit(a);c.value!==void 0&&(c.explicit||c.value===i[a]||c.value==="derived"||i[a]==="flatten"?delete i[a]:v(cc(a,i[a],c.value)));}for(const a of x(n)){const c=r.get(a);c!==void 0&&(c===n[a]?delete n[a]:v(cc(a,n[a],c)));}const s=new Dt(n,i);r.copyAll(s);const o={};for(const a of x(s.combine())){const c=s.get(a);c!==null&&(o[a]=c);}return x(o).length===0||r.parseNothing?null:new $e(t,o)}get parse(){return this._parse}merge(t){this._parse={...this._parse,...t.parse},t.remove();}assembleFormatParse(){const t={};for(const n of x(this._parse)){const i=this._parse[n];Kn(n)===1&&(t[n]=i);}return t}producedFields(){return new Set(x(this._parse))}dependentFields(){return new Set(x(this._parse))}assembleTransforms(t=!1){return x(this._parse).filter(n=>t?Kn(n)>1:!0).map(n=>{const i=vv(n,this._parse[n]);return i?{type:"formula",expr:i,as:wo(n)}:null}).filter(n=>n!==null)}}class Xt extends X{clone(){return new Xt(null)}constructor(t){super(t);}dependentFields(){return new Set}producedFields(){return new Set([Je])}hash(){return "Identifier"}assemble(){return {type:"identifier",as:Je}}}class Ki extends X{clone(){return new Ki(null,this.params)}constructor(t,n){super(t),this.params=n;}dependentFields(){return new Set}producedFields(){}hash(){return `Graticule ${U(this.params)}`}assemble(){return {type:"graticule",...this.params===!0?{}:this.params}}}class Qi extends X{clone(){return new Qi(null,this.params)}constructor(t,n){super(t),this.params=n;}dependentFields(){return new Set}producedFields(){return new Set([this.params.as??"data"])}hash(){return `Hash ${U(this.params)}`}assemble(){return {type:"sequence",...this.params}}}class Nn extends X{constructor(t){super(null),t??(t={name:"source"});let n;if(Ut(t)||(n=t.format?{...Fe(t.format,["parse"])}:{}),Pi(t))this._data={values:t.values};else if(ri(t)){if(this._data={url:t.url},!n.type){let i=/(?:\.([^.]+))?$/.exec(t.url)[1];W(["json","csv","tsv","dsv","topojson"],i)||(i="json"),n.type=i;}}else Vf(t)?this._data={values:[{type:"Sphere"}]}:(qf(t)||Ut(t))&&(this._data={});this._generator=Ut(t),t.name&&(this._name=t.name),n&&!Y(n)&&(this._data.format=n);}dependentFields(){return new Set}producedFields(){}get data(){return this._data}hasName(){return !!this._name}get isGenerator(){return this._generator}get dataName(){return this._name}set dataName(t){this._name=t;}set parent(t){throw new Error("Source nodes have to be roots.")}remove(){throw new Error("Source nodes are roots and cannot be removed.")}hash(){throw new Error("Cannot hash sources")}assemble(){return {name:this._name,...this._data,transform:[]}}}var Qc=function(e,t,n,i,r){if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n},wv=function(e,t,n,i){if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?i:n==="a"?i.call(e):i?i.value:t.get(e)},Fi;function Oa(e){return e instanceof Nn||e instanceof Ki||e instanceof Qi}class Ta{constructor(){Fi.set(this,void 0),Qc(this,Fi,!1);}setModified(){Qc(this,Fi,!0);}get modifiedFlag(){return wv(this,Fi,"f")}}Fi=new WeakMap;class Pn extends Ta{getNodeDepths(t,n,i){i.set(t,n);for(const r of t.children)this.getNodeDepths(r,n+1,i);return i}optimize(t){const i=[...this.getNodeDepths(t,0,new Map).entries()].sort((r,s)=>s[1]-r[1]);for(const r of i)this.run(r[0]);return this.modifiedFlag}}class Ia extends Ta{optimize(t){this.run(t);for(const n of t.children)this.optimize(n);return this.modifiedFlag}}class Av extends Ia{mergeNodes(t,n){const i=n.shift();for(const r of n)t.removeChild(r),r.parent=i,r.remove();}run(t){const n=t.children.map(r=>r.hash()),i={};for(let r=0;r<n.length;r++)i[n[r]]===void 0?i[n[r]]=[t.children[r]]:i[n[r]].push(t.children[r]);for(const r of x(i))i[r].length>1&&(this.setModified(),this.mergeNodes(t,i[r]));}}class Cv extends Ia{constructor(t){super(),this.requiresSelectionId=t&&Aa(t);}run(t){t instanceof Xt&&(this.requiresSelectionId&&(Oa(t.parent)||t.parent instanceof Ye||t.parent instanceof $e)||(this.setModified(),t.remove()));}}class Nv extends Ta{optimize(t){return this.run(t,new Set),this.modifiedFlag}run(t,n){let i=new Set;t instanceof ft&&(i=t.producedFields(),So(i,n)&&(this.setModified(),t.removeFormulas(n),t.producedFields.length===0&&t.remove()));for(const r of t.children)this.run(r,new Set([...n,...i]));}}class Fv extends Ia{constructor(){super();}run(t){t instanceof we&&!t.isRequired()&&(this.setModified(),t.remove());}}class _v extends Pn{run(t){if(!Oa(t)&&!(t.numChildren()>1)){for(const n of t.children)if(n instanceof $e)if(t instanceof $e)this.setModified(),t.merge(n);else {if(Eo(t.producedFields(),n.dependentFields()))continue;this.setModified(),n.swapWithParent();}}}}class kv extends Pn{run(t){const n=[...t.children],i=t.children.filter(r=>r instanceof $e);if(t.numChildren()>1&&i.length>=1){const r={},s=new Set;for(const o of i){const a=o.parse;for(const c of x(a))c in r?r[c]!==a[c]&&s.add(c):r[c]=a[c];}for(const o of s)delete r[o];if(!Y(r)){this.setModified();const o=new $e(t,r);for(const a of n){if(a instanceof $e)for(const c of x(r))delete a.parse[c];t.removeChild(a),a.parent=o,a instanceof $e&&x(a.parse).length===0&&a.remove();}}}}}class Ov extends Pn{run(t){t instanceof we||t.numChildren()>0||t instanceof yi||t instanceof Nn||(this.setModified(),t.remove());}}class Tv extends Pn{run(t){const n=t.children.filter(r=>r instanceof ft),i=n.pop();for(const r of n)this.setModified(),i.merge(r);}}class Iv extends Pn{run(t){const n=t.children.filter(r=>r instanceof Ye),i={};for(const r of n){const s=U(r.groupBy);s in i||(i[s]=[]),i[s].push(r);}for(const r of x(i)){const s=i[r];if(s.length>1){const o=s.pop();for(const a of s)o.merge(a)&&(t.removeChild(a),a.parent=o,a.remove(),this.setModified());}}}}class Rv extends Pn{constructor(t){super(),this.model=t;}run(t){const n=!(Oa(t)||t instanceof mi||t instanceof $e||t instanceof Xt),i=[],r=[];for(const s of t.children)s instanceof dt&&(n&&!Eo(t.producedFields(),s.dependentFields())?i.push(s):r.push(s));if(i.length>0){const s=i.pop();for(const o of i)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified(),t instanceof dt?t.merge(s,this.model.renameSignal.bind(this.model)):s.swapWithParent();}if(r.length>1){const s=r.pop();for(const o of r)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified();}}}class Pv extends Pn{run(t){const n=[...t.children];if(!bn(n,o=>o instanceof we)||t.numChildren()<=1)return;const r=[];let s;for(const o of n)if(o instanceof we){let a=o;for(;a.numChildren()===1;){const[c]=a.children;if(c instanceof we)a=c;else break}r.push(...a.children),s?(t.removeChild(o),o.parent=s.parent,s.parent.removeChild(s),s.parent=a,this.setModified()):s=a;}else r.push(o);if(r.length){this.setModified();for(const o of r)o.parent.removeChild(o),o.parent=s;}}}class Ln extends X{clone(){return new Ln(null,L(this.transform))}constructor(t,n){super(t),this.transform=n;}addDimensions(t){this.transform.groupby=lt(this.transform.groupby.concat(t),n=>n);}dependentFields(){const t=new Set;return this.transform.groupby&&this.transform.groupby.forEach(t.add,t),this.transform.joinaggregate.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.joinaggregate.map(this.getDefaultName))}getDefaultName(t){return t.as??$(t)}hash(){return `JoinAggregateTransform ${U(this.transform)}`}assemble(){const t=[],n=[],i=[];for(const s of this.transform.joinaggregate)n.push(s.op),i.push(this.getDefaultName(s)),t.push(s.field===void 0?null:s.field);const r=this.transform.groupby;return {type:"joinaggregate",as:i,ops:n,fields:t,...r!==void 0?{groupby:r}:{}}}}function Lv(e){return e.stack.stackBy.reduce((t,n)=>{const i=n.fieldDef,r=$(i);return r&&t.push(r),t},[])}function zv(e){return U$1(e)&&e.every(t=>xt$1(t))&&e.length>1}class _t extends X{clone(){return new _t(null,L(this._stack))}constructor(t,n){super(t),this._stack=n;}static makeFromTransform(t,n){const{stack:i,groupby:r,as:s,offset:o="zero"}=n,a=[],c=[];if(n.sort!==void 0)for(const f of n.sort)a.push(f.field),c.push(ue(f.order,"ascending"));const l={field:a,order:c};let u;return zv(s)?u=s:xt$1(s)?u=[s,`${s}_end`]:u=[`${n.stack}_start`,`${n.stack}_end`],new _t(t,{dimensionFieldDefs:[],stackField:i,groupby:r,offset:o,sort:l,facetby:[],as:u})}static makeFromEncoding(t,n){const i=n.stack,{encoding:r}=n;if(!i)return null;const{groupbyChannels:s,fieldChannel:o,offset:a,impute:c}=i,l=s.map(g=>{const p=r[g];return gt(p)}).filter(g=>!!g),u=Lv(n),f=n.encoding.order;let d;if(U$1(f)||S(f))d=du(f);else {const g=nf(f)?f.sort:o==="y"?"descending":"ascending";d=u.reduce((p,h)=>(p.field.push(h),p.order.push(g),p),{field:[],order:[]});}return new _t(t,{dimensionFieldDefs:l,stackField:n.vgField(o),facetby:[],stackby:u,sort:d,offset:a,impute:c,as:[n.vgField(o,{suffix:"start",forAs:!0}),n.vgField(o,{suffix:"end",forAs:!0})]})}get stack(){return this._stack}addDimensions(t){this._stack.facetby.push(...t);}dependentFields(){const t=new Set;return t.add(this._stack.stackField),this.getGroupbyFields().forEach(t.add,t),this._stack.facetby.forEach(t.add,t),this._stack.sort.field.forEach(t.add,t),t}producedFields(){return new Set(this._stack.as)}hash(){return `Stack ${U(this._stack)}`}getGroupbyFields(){const{dimensionFieldDefs:t,impute:n,groupby:i}=this._stack;return t.length>0?t.map(r=>r.bin?n?[$(r,{binSuffix:"mid"})]:[$(r,{}),$(r,{binSuffix:"end"})]:[$(r)]).flat():i??[]}assemble(){const t=[],{facetby:n,dimensionFieldDefs:i,stackField:r,stackby:s,sort:o,offset:a,impute:c,as:l}=this._stack;if(c)for(const u of i){const{bandPosition:f=.5,bin:d}=u;if(d){const g=$(u,{expr:"datum"}),p=$(u,{expr:"datum",binSuffix:"end"});t.push({type:"formula",expr:`${f}*${g}+${1-f}*${p}`,as:$(u,{binSuffix:"mid",forAs:!0})});}t.push({type:"impute",field:r,groupby:[...s,...n],key:$(u,{binSuffix:"mid"}),method:"value",value:0});}return t.push({type:"stack",groupby:[...this.getGroupbyFields(),...n],field:r,sort:o,as:l,offset:a}),t}}class bi extends X{clone(){return new bi(null,L(this.transform))}constructor(t,n){super(t),this.transform=n;}addDimensions(t){this.transform.groupby=lt(this.transform.groupby.concat(t),n=>n);}dependentFields(){const t=new Set;return (this.transform.groupby??[]).forEach(t.add,t),(this.transform.sort??[]).forEach(n=>t.add(n.field)),this.transform.window.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.window.map(this.getDefaultName))}getDefaultName(t){return t.as??$(t)}hash(){return `WindowTransform ${U(this.transform)}`}assemble(){const t=[],n=[],i=[],r=[];for(const f of this.transform.window)n.push(f.op),i.push(this.getDefaultName(f)),r.push(f.param===void 0?null:f.param),t.push(f.field===void 0?null:f.field);const s=this.transform.frame,o=this.transform.groupby;if(s&&s[0]===null&&s[1]===null&&n.every(f=>To(f)))return {type:"joinaggregate",as:i,ops:n,fields:t,...o!==void 0?{groupby:o}:{}};const a=[],c=[];if(this.transform.sort!==void 0)for(const f of this.transform.sort)a.push(f.field),c.push(f.order??"ascending");const l={field:a,order:c},u=this.transform.ignorePeers;return {type:"window",params:r,as:i,ops:n,fields:t,sort:l,...u!==void 0?{ignorePeers:u}:{},...o!==void 0?{groupby:o}:{},...s!==void 0?{frame:s}:{}}}}function Dv(e){function t(n){if(!(n instanceof yi)){const i=n.clone();if(i instanceof we){const r=co+i.getSource();i.setSource(r),e.model.component.data.outputNodes[r]=i;}else (i instanceof Ye||i instanceof _t||i instanceof bi||i instanceof Ln)&&i.addDimensions(e.fields);for(const r of n.children.flatMap(t))r.parent=i;return [i]}return n.children.flatMap(t)}return t}function ao(e){if(e instanceof yi)if(e.numChildren()===1&&!(e.children[0]instanceof we)){const t=e.children[0];(t instanceof Ye||t instanceof _t||t instanceof bi||t instanceof Ln)&&t.addDimensions(e.fields),t.swapWithParent(),ao(e);}else {const t=e.model.component.data.main;Md(t);const n=Dv(e),i=e.children.map(n).flat();for(const r of i)r.parent=t;}else e.children.map(ao);}function Md(e){if(e instanceof we&&e.type===J.Main&&e.numChildren()===1){const t=e.children[0];t instanceof yi||(t.swapWithParent(),Md(e));}}const co="scale_",nr=5;function lo(e){for(const t of e){for(const n of t.children)if(n.parent!==t)return !1;if(!lo(t.children))return !1}return !0}function We(e,t){let n=!1;for(const i of t)n=e.optimize(i)||n;return n}function Jc(e,t,n){let i=e.sources,r=!1;return r=We(new Fv,i)||r,r=We(new Cv(t),i)||r,i=i.filter(s=>s.numChildren()>0),r=We(new Ov,i)||r,i=i.filter(s=>s.numChildren()>0),n||(r=We(new _v,i)||r,r=We(new Rv(t),i)||r,r=We(new Nv,i)||r,r=We(new kv,i)||r,r=We(new Iv,i)||r,r=We(new Tv,i)||r,r=We(new Av,i)||r,r=We(new Pv,i)||r),e.sources=i,r}function Mv(e,t){lo(e.sources);let n=0,i=0;for(let r=0;r<nr&&Jc(e,t,!0);r++)n++;e.sources.map(ao);for(let r=0;r<nr&&Jc(e,t,!1);r++)i++;lo(e.sources),Math.max(n,i)===nr&&v(`Maximum optimization runs(${nr}) reached.`);}class ke{constructor(t){Object.defineProperty(this,"signal",{enumerable:!0,get:t});}static fromName(t,n){return new ke(()=>t(n))}}function jd(e){oe(e)?jv(e):Bv(e);}function jv(e){const t=e.component.scales;for(const n of x(t)){const i=Wv(e,n);if(t[n].setWithExplicit("domains",i),qv(e,n),e.component.data.isFaceted){let s=e;for(;!Ke(s)&&s.parent;)s=s.parent;if(s.component.resolve.scale[n]==="shared")for(const a of i.value)At(a)&&(a.data=co+a.data.replace(co,""));}}}function Bv(e){for(const n of e.children)jd(n);const t=e.component.scales;for(const n of x(t)){let i,r=null;for(const s of e.children){const o=s.component.scales[n];if(o){i===void 0?i=o.getWithExplicit("domains"):i=qt(i,o.getWithExplicit("domains"),"domains","scale",uo);const a=o.get("selectionExtent");r&&a&&r.param!==a.param&&v(jg),r=a;}}t[n].setWithExplicit("domains",i),r&&t[n].set("selectionExtent",r,!0);}}function Uv(e,t,n,i){if(e==="unaggregated"){const{valid:r,reason:s}=Zc(t,n);if(!r){v(s);return}}else if(e===void 0&&i.useUnaggregatedDomain){const{valid:r}=Zc(t,n);if(r)return "unaggregated"}return e}function Wv(e,t){const n=e.getScaleComponent(t).get("type"),{encoding:i}=e,r=Uv(e.scaleDomain(t),e.typedFieldDef(t),n,e.config.scale);return r!==e.scaleDomain(t)&&(e.specifiedScales[t]={...e.specifiedScales[t],domain:r}),t==="x"&&fe(i.x2)?fe(i.x)?qt(Mt(n,r,e,"x"),Mt(n,r,e,"x2"),"domain","scale",uo):Mt(n,r,e,"x2"):t==="y"&&fe(i.y2)?fe(i.y)?qt(Mt(n,r,e,"y"),Mt(n,r,e,"y2"),"domain","scale",uo):Mt(n,r,e,"y2"):Mt(n,r,e,t)}function Gv(e,t,n){return e.map(i=>({signal:`{data: ${Yr(i,{timeUnit:n,type:t})}}`}))}function ks(e,t,n){const i=ge(n)?.unit;return t==="temporal"||i?Gv(e,t,i):[e]}function Mt(e,t,n,i){const{encoding:r}=n,s=fe(r[i]),{type:o}=s,a=s.timeUnit;if(fm(t)){const f=Mt(e,void 0,n,i),d=ks(t.unionWith,o,a);return at([...d,...f.value])}else {if(k(t))return at([t]);if(t&&t!=="unaggregated"&&!Mu(t))return at(ks(t,o,a))}const c=n.stack;if(c&&i===c.fieldChannel){if(c.offset==="normalize")return Re([[0,1]]);const f=n.requestDataName(J.Main);return Re([{data:f,field:n.vgField(i,{suffix:"start"})},{data:f,field:n.vgField(i,{suffix:"end"})}])}const l=Lt(i)&&S(s)?Hv(n,i,e):void 0;if(St(s)){const f=ks([s.datum],o,a);return Re(f)}const u=s;if(t==="unaggregated"){const f=n.requestDataName(J.Main),{field:d}=s;return Re([{data:f,field:$({field:d,aggregate:"min"})},{data:f,field:$({field:d,aggregate:"max"})}])}else if(ee(u.bin)){if(ye(e))return Re(e==="bin-ordinal"?[]:[{data:Ii(l)?n.requestDataName(J.Main):n.requestDataName(J.Raw),field:n.vgField(i,Vi(u,i)?{binSuffix:"range"}:{}),sort:l===!0||!et$1(l)?{field:n.vgField(i,{}),op:"min"}:l}]);{const{bin:f}=u;if(ee(f)){const d=ka(n,u.field,f);return Re([new ke(()=>{const g=n.getSignalName(d);return `[${g}.start, ${g}.stop]`})])}else return Re([{data:n.requestDataName(J.Main),field:n.vgField(i,{})}])}}else if(u.timeUnit&&W(["time","utc"],e)&&tf(u,oe(n)?n.encoding[xt(i)]:void 0,n.markDef,n.config)){const f=n.requestDataName(J.Main);return Re([{data:f,field:n.vgField(i)},{data:f,field:n.vgField(i,{suffix:"end"})}])}else return Re(l?[{data:Ii(l)?n.requestDataName(J.Main):n.requestDataName(J.Raw),field:n.vgField(i),sort:l}]:[{data:n.requestDataName(J.Main),field:n.vgField(i)}])}function Os(e,t){const{op:n,field:i,order:r}=e;return {op:n??(t?"sum":Gr),...i?{field:Me(i)}:{},...r?{order:r}:{}}}function qv(e,t){const n=e.component.scales[t],i=e.specifiedScales[t].domain,r=e.fieldDef(t)?.bin,s=Mu(i)&&i,o=On(r)&&zr(r.extent)&&r.extent;(s||o)&&n.set("selectionExtent",s??o,!0);}function Hv(e,t,n){if(!ye(n))return;const i=e.fieldDef(t),r=i.sort;if(Ju(r))return {op:"min",field:ai(i,t),order:"ascending"};const{stack:s}=e,o=s?new Set([...s.groupbyFields,...s.stackBy.map(a=>a.fieldDef.field)]):void 0;if(ut(r)){const a=s&&!o.has(r.field);return Os(r,a)}else if(Qu(r)){const{encoding:a,order:c}=r,l=e.fieldDef(a),{aggregate:u,field:f}=l,d=s&&!o.has(f);if(kt(u)||en(u))return Os({field:$(l),order:c},d);if(To(u)||!u)return Os({op:u,field:f,order:c},d)}else {if(r==="descending")return {op:"min",field:e.vgField(t),order:"descending"};if(W(["ascending",void 0],r))return !0}}function Zc(e,t){const{aggregate:n,type:i}=e;return n?xt$1(n)&&!wg.has(n)?{valid:!1,reason:gh(n)}:i==="quantitative"&&t==="log"?{valid:!1,reason:hh(e)}:{valid:!0}:{valid:!1,reason:ph(e)}}function uo(e,t,n,i){return e.explicit&&t.explicit&&v(vh(n,i,e.value,t.value)),{explicit:e.explicit,value:[...e.value,...t.value]}}function Vv(e){const t=lt(e.map(o=>{if(At(o)){const{sort:a,...c}=o;return c}return o}),U),n=lt(e.map(o=>{if(At(o)){const a=o.sort;return a!==void 0&&!Ii(a)&&("op"in a&&a.op==="count"&&delete a.field,a.order==="ascending"&&delete a.order),a}}).filter(o=>o!==void 0),U);if(t.length===0)return;if(t.length===1){const o=e[0];if(At(o)&&n.length>0){let a=n[0];if(n.length>1){v(uc);const c=n.filter(l=>et$1(l)&&"op"in l&&l.op!=="min");n.every(l=>et$1(l)&&"op"in l)&&c.length===1?a=c[0]:a=!0;}else if(et$1(a)&&"field"in a){const c=a.field;o.field===c&&(a=a.order?{order:a.order}:!0);}return {...o,sort:a}}return o}const i=lt(n.map(o=>Ii(o)||!("op"in o)||xt$1(o.op)&&o.op in vg?o:(v(Eh(o)),!0)),U);let r;i.length===1?r=i[0]:i.length>1&&(v(uc),r=!0);const s=lt(e.map(o=>At(o)?o.data:null),o=>o);return s.length===1&&s[0]!==null?{data:s[0],fields:t.map(a=>a.field),...r?{sort:r}:{}}:{fields:t,...r?{sort:r}:{}}}function Ra(e){if(At(e)&&xt$1(e.field))return e.field;if(Ag(e)){let t;for(const n of e.fields)if(At(n)&&xt$1(n.field)){if(!t)t=n.field;else if(t!==n.field)return v($h),t}return v(wh),t}else if(Cg(e)){v(Ah);const t=e.fields[0];return xt$1(t)?t:void 0}}function ss(e,t){const i=e.component.scales[t].get("domains").map(r=>(At(r)&&(r.data=e.lookupDataSource(r.data)),r));return Vv(i)}function Bd(e){return xi(e)||Pa(e)?e.children.reduce((t,n)=>t.concat(Bd(n)),el(e)):el(e)}function el(e){return x(e.component.scales).reduce((t,n)=>{const i=e.component.scales[n];if(i.merged)return t;const r=i.combine(),{name:s,type:o,selectionExtent:a,domains:c,range:l,reverse:u,...f}=r,d=Xv(r.range,s,n,e),g=ss(e,n),p=a?Ub(e,a,i,g):null;return t.push({name:s,type:o,...g?{domain:g}:{},...p?{domainRaw:p}:{},range:d,...u!==void 0?{reverse:u}:{},...f}),t},[])}function Xv(e,t,n,i){if(me(n)){if(tn(e))return {step:{signal:`${t}_step`}}}else if(et$1(e)&&At(e))return {...e,data:i.lookupDataSource(e.data)};return e}class Ud extends Dt{constructor(t,n){super({},{name:t}),this.merged=!1,this.setWithExplicit("type",n);}domainDefinitelyIncludesZero(){return this.get("zero")!==!1?!0:bn(this.get("domains"),t=>U$1(t)&&t.length===2&&t[0]<=0&&t[1]>=0)}}const Yv=["range","scheme"];function Kv(e){const t=e.component.scales;for(const n of Lr){const i=t[n];if(!i)continue;const r=Qv(n,e);i.setWithExplicit("range",r);}}function tl(e,t){const n=e.fieldDef(t);if(n?.bin){const{bin:i,field:r}=n,s=Ie(t),o=e.getName(s);if(et$1(i)&&i.binned&&i.step!==void 0)return new ke(()=>{const a=e.scaleName(t),c=`(domain("${a}")[1] - domain("${a}")[0]) / ${i.step}`;return `${e.getSignalName(o)} / (${c})`});if(ee(i)){const a=ka(e,r,i);return new ke(()=>{const c=e.getSignalName(a),l=`(${c}.stop - ${c}.start) / ${c}.step`;return `${e.getSignalName(o)} / (${l})`})}}}function Qv(e,t){const n=t.specifiedScales[e],{size:i}=t,s=t.getScaleComponent(e).get("type");for(const f of Yv)if(n[f]!==void 0){const d=Vs(s,f),g=ju(e,f);if(!d)v(xu(s,f,e));else if(g)v(g);else switch(f){case"range":{const p=n.range;if(U$1(p)){if(me(e))return at(p.map(h=>{if(h==="width"||h==="height"){const m=t.getName(h),y=t.getSignalName.bind(t);return ke.fromName(y,m)}return h}))}else if(et$1(p))return at({data:t.requestDataName(J.Main),field:p.field,sort:{op:"min",field:t.vgField(e)}});return at(p)}case"scheme":return at(Jv(n[f]))}}const o=e===re||e==="xOffset"?"width":"height",a=i[o];if(ht(a)){if(me(e))if(ye(s)){const f=Wd(a,t,e);if(f)return at({step:f})}else v(vu(o));else if(pi(e)){const f=e===Yt?"x":"y";if(t.getScaleComponent(f).get("type")==="band"){const p=Gd(a,s);if(p)return at(p)}}}const{rangeMin:c,rangeMax:l}=n,u=Zv(e,t);return (c!==void 0||l!==void 0)&&Vs(s,"rangeMin")&&U$1(u)&&u.length===2?at([c??u[0],l??u[1]]):Re(u)}function Jv(e){return um(e)?{scheme:e.name,...Fe(e,["name"])}:{scheme:e}}function Zv(e,t){const{size:n,config:i,mark:r,encoding:s}=t,o=t.getSignalName.bind(t),{type:a}=fe(s[e]),l=t.getScaleComponent(e).get("type"),{domain:u,domainMid:f}=t.specifiedScales[e];switch(e){case re:case he:{if(W(["point","band"],l)){const p=qd(e,n,i.view);if(ht(p))return {step:Wd(p,t,e)}}const d=Ie(e),g=t.getName(d);return e===he&&Le(l)?[ke.fromName(o,g),0]:[0,ke.fromName(o,g)]}case Yt:case di:return eS(e,t,l);case Rt:{const d=t.component.scales[e].get("zero"),g=Hd(r,d,i),p=iS(r,n,t,i);return ei(l)?nS(g,p,tS(l,i,u,e)):[g,p]}case Be:return [0,Math.PI*2];case _n:return [0,360];case et:return [0,new ke(()=>{const d=t.getSignalName("width"),g=t.getSignalName("height");return `min(${d},${g})/2`})];case Jt:return [i.scale.minStrokeWidth,i.scale.maxStrokeWidth];case Zt:return [[1,0],[4,2],[2,1],[1,1],[1,2,4,2]];case Te:return "symbol";case Oe:case yt:case bt:return l==="ordinal"?a==="nominal"?"category":"ordinal":f!==void 0?"diverging":r==="rect"||r==="geoshape"?"heatmap":"ramp";case Pt:case Kt:case Qt:return [i.scale.minOpacity,i.scale.maxOpacity]}}function Wd(e,t,n){const{encoding:i}=t,r=t.getScaleComponent(n),s=Fo(n),o=i[s];if(Tf({step:e,offsetIsDiscrete:z(o)&&Ru(o.type)})==="offset"&&gf(i,s)){const c=t.getScaleComponent(s);let u=`domain('${t.scaleName(s)}').length`;if(c.get("type")==="band"){const d=c.get("paddingInner")??c.get("padding")??0,g=c.get("paddingOuter")??c.get("padding")??0;u=`bandspace(${u}, ${d}, ${g})`;}const f=r.get("paddingInner")??r.get("padding");return {signal:`${e.step} * ${u} / (1-${kg(f)})`}}else return e.step}function Gd(e,t){if(Tf({step:e,offsetIsDiscrete:ye(t)})==="offset")return {step:e.step}}function eS(e,t,n){const i=e===Yt?"x":"y",s=t.getScaleComponent(i).get("type"),o=t.scaleName(i);if(s==="band"){const a=qd(i,t.size,t.config.view);if(ht(a)){const c=Gd(a,n);if(c)return c}return [0,{signal:`bandwidth('${o}')`}]}else {const a=t.encoding[i];if(S(a)&&a.timeUnit){const c=ku(a.timeUnit,u=>`scale('${o}', ${u})`),l=t.config.scale.bandWithNestedOffsetPaddingInner;if(l){const u=k(l)?`${l.signal}/2`:`${l/2}`,f=k(l)?`(1 - ${l.signal}/2)`:`${1-l/2}`;return [{signal:`${u} * (${c})`},{signal:`${f} * (${c})`}]}return [0,{signal:c}]}return Ml(`Cannot use ${e} scale if ${i} scale is not discrete.`)}}function qd(e,t,n){const i=e===re?"width":"height",r=t[i];return r||Sr(n,i)}function tS(e,t,n,i){switch(e){case"quantile":return t.scale.quantileCount;case"quantize":return t.scale.quantizeCount;case"threshold":return n!==void 0&&U$1(n)?n.length+1:(v(Rh(i)),3)}}function nS(e,t,n){const i=()=>{const r=He(t),s=He(e),o=`(${r} - ${s}) / (${n} - 1)`;return `sequence(${s}, ${r} + ${o}, ${o})`};return k(t)?new ke(i):{signal:i()}}function Hd(e,t,n){if(t)return k(t)?{signal:`${t.signal} ? 0 : ${Hd(e,!1,n)}`}:0;switch(e){case"bar":case"tick":return n.scale.minBandSize;case"line":case"trail":case"rule":return n.scale.minStrokeWidth;case"text":return n.scale.minFontSize;case"point":case"square":case"circle":return n.scale.minSize}throw new Error(Dr("size",e))}const nl=.95;function iS(e,t,n,i){const r={x:tl(n,"x"),y:tl(n,"y")};switch(e){case"bar":case"tick":{if(i.scale.maxBandSize!==void 0)return i.scale.maxBandSize;const s=il(t,r,i.view);return Mr$1(s)?s-1:new ke(()=>`${s.signal} - 1`)}case"line":case"trail":case"rule":return i.scale.maxStrokeWidth;case"text":return i.scale.maxFontSize;case"point":case"square":case"circle":{if(i.scale.maxSize)return i.scale.maxSize;const s=il(t,r,i.view);return Mr$1(s)?Math.pow(nl*s,2):new ke(()=>`pow(${nl} * ${s.signal}, 2)`)}}throw new Error(Dr("size",e))}function il(e,t,n){const i=ht(e.width)?e.width.step:vr(n,"width"),r=ht(e.height)?e.height.step:vr(n,"height");return t.x||t.y?new ke(()=>`min(${[t.x?t.x.signal:i,t.y?t.y.signal:r].join(", ")})`):Math.min(i,r)}function Vd(e,t){oe(e)?rS(e,t):Yd(e,t);}function rS(e,t){const n=e.component.scales,{config:i,encoding:r,markDef:s,specifiedScales:o}=e;for(const a of x(n)){const c=o[a],l=n[a],u=e.getScaleComponent(a),f=fe(r[a]),d=c[t],g=u.get("type"),p=u.get("padding"),h=u.get("paddingInner"),m=Vs(g,t),y=ju(a,t);if(d!==void 0&&(m?y&&v(y):v(xu(g,t,a))),m&&y===void 0)if(d!==void 0){const b=f.timeUnit,O=f.type;switch(t){case"domainMax":case"domainMin":Tn(c[t])||O==="temporal"||b?l.set(t,{signal:Yr(c[t],{type:O,timeUnit:b})},!0):l.set(t,c[t],!0);break;default:l.copyKeyFromObject(t,c);}}else {const b=t in rl?rl[t]({model:e,channel:a,fieldOrDatumDef:f,scaleType:g,scalePadding:p,scalePaddingInner:h,domain:c.domain,domainMin:c.domainMin,domainMax:c.domainMax,markDef:s,config:i,hasNestedOffsetScale:Ys(r,a),hasSecondaryRangeChannel:!!r[xt(a)]}):i.scale[t];b!==void 0&&l.set(t,b,!1);}}}const rl={bins:({model:e,fieldOrDatumDef:t})=>S(t)?sS(e,t):void 0,interpolate:({channel:e,fieldOrDatumDef:t})=>oS(e,t.type),nice:({scaleType:e,channel:t,domain:n,domainMin:i,domainMax:r,fieldOrDatumDef:s})=>aS(e,t,n,i,r,s),padding:({channel:e,scaleType:t,fieldOrDatumDef:n,markDef:i,config:r})=>cS(e,t,r.scale,n,i,r.bar),paddingInner:({scalePadding:e,channel:t,markDef:n,scaleType:i,config:r,hasNestedOffsetScale:s})=>lS(e,t,n.type,i,r.scale,s),paddingOuter:({scalePadding:e,channel:t,scaleType:n,scalePaddingInner:i,config:r,hasNestedOffsetScale:s})=>uS(e,t,n,i,r.scale,s),reverse:({fieldOrDatumDef:e,scaleType:t,channel:n,config:i})=>{const r=S(e)?e.sort:void 0;return fS(t,r,n,i.scale)},zero:({channel:e,fieldOrDatumDef:t,domain:n,markDef:i,scaleType:r,config:s,hasSecondaryRangeChannel:o})=>dS(e,t,n,i,r,s.scale,o)};function Xd(e){oe(e)?Kv(e):Yd(e,"range");}function Yd(e,t){const n=e.component.scales;for(const i of e.children)t==="range"?Xd(i):Vd(i,t);for(const i of x(n)){let r;for(const s of e.children){const o=s.component.scales[i];if(o){const a=o.getWithExplicit(t);r=qt(r,a,t,"scale",Gf((c,l)=>{switch(t){case"range":return c.step&&l.step?c.step-l.step:0}return 0}));}}n[i].setWithExplicit(t,r);}}function sS(e,t){const n=t.bin;if(ee(n)){const i=ka(e,t.field,n);return new ke(()=>e.getSignalName(i))}else if(be(n)&&On(n)&&n.step!==void 0)return {step:n.step}}function oS(e,t){if(W([Oe,yt,bt],e)&&t!=="nominal")return "hcl"}function aS(e,t,n,i,r,s){if(!(gt(s)?.bin||U$1(n)||r!=null||i!=null||W([_e.TIME,_e.UTC],e)))return me(t)?!0:void 0}function cS(e,t,n,i,r,s){if(me(e)){if(Ve(t)){if(n.continuousPadding!==void 0)return n.continuousPadding;const{type:o,orient:a}=r;if(o==="bar"&&!(S(i)&&(i.bin||i.timeUnit))&&(a==="vertical"&&e==="x"||a==="horizontal"&&e==="y"))return s.continuousBandSize}if(t===_e.POINT)return n.pointPadding}}function lS(e,t,n,i,r,s=!1){if(e===void 0){if(me(t)){const{bandPaddingInner:o,barBandPaddingInner:a,rectBandPaddingInner:c,bandWithNestedOffsetPaddingInner:l}=r;return s?l:ue(o,n==="bar"?a:c)}else if(pi(t)&&i===_e.BAND)return r.offsetBandPaddingInner}}function uS(e,t,n,i,r,s=!1){if(e===void 0){if(me(t)){const{bandPaddingOuter:o,bandWithNestedOffsetPaddingOuter:a}=r;if(s)return a;if(n===_e.BAND)return ue(o,k(i)?{signal:`${i.signal}/2`}:i/2)}else if(pi(t)){if(n===_e.POINT)return .5;if(n===_e.BAND)return r.offsetBandPaddingOuter}}}function fS(e,t,n,i){if(n==="x"&&i.xReverse!==void 0)return Le(e)&&t==="descending"?k(i.xReverse)?{signal:`!${i.xReverse.signal}`}:!i.xReverse:i.xReverse;if(Le(e)&&t==="descending")return !0}function dS(e,t,n,i,r,s,o){if(!!n&&n!=="unaggregated"&&Le(r)){if(U$1(n)){const c=n[0],l=n[n.length-1];if(c<=0&&l>=0)return !0}return !1}if(e==="size"&&t.type==="quantitative"&&!ei(r))return !0;if(!(S(t)&&t.bin)&&W([...vt,...dg],e)){const{orient:c,type:l}=i;return W(["bar","area","line","trail"],l)&&(c==="horizontal"&&e==="y"||c==="vertical"&&e==="x")?!1:W(["bar","area"],l)&&!o?!0:s?.zero}return !1}function pS(e,t,n,i,r=!1){const s=gS(t,n,i,r),{type:o}=e;return Lt(t)?o!==void 0?ym(t,o)?S(n)&&!mm(o,n.type)?(v(bh(o,s)),s):o:(v(yh(t,o,s)),s):s:null}function gS(e,t,n,i){switch(t.type){case"nominal":case"ordinal":{if(Gn(e)||Es(e)==="discrete")return e==="shape"&&t.type==="ordinal"&&v($s(e,"ordinal")),"ordinal";if(me(e)||pi(e)){if(W(["rect","bar","image","rule"],n.type)||i)return "band"}else if(n.type==="arc"&&e in Oo)return "band";const r=n[Ie(e)];return $n(r)||ni(t)&&t.axis?.tickBand?"band":"point"}case"temporal":return Gn(e)?"time":Es(e)==="discrete"?(v($s(e,"temporal")),"ordinal"):S(t)&&t.timeUnit&&ge(t.timeUnit).utc?"utc":"time";case"quantitative":return Gn(e)?S(t)&&ee(t.bin)?"bin-ordinal":"linear":Es(e)==="discrete"?(v($s(e,"quantitative")),"ordinal"):"linear";case"geojson":return}throw new Error(yu(t.type))}function hS(e,{ignoreRange:t}={}){Kd(e),jd(e);for(const n of hm)Vd(e,n);t||Xd(e);}function Kd(e){oe(e)?e.component.scales=mS(e):e.component.scales=bS(e);}function mS(e){const{encoding:t,mark:n,markDef:i}=e,r={};for(const s of Lr){const o=fe(t[s]);if(o&&n===Wu&&s===Te&&o.type===gi)continue;let a=o&&o.scale;if(pi(s)){const c=tu(s);if(!Ys(t,c)){a&&v(ih(s));continue}}if(o&&a!==null&&a!==!1){a??(a={});const c=Ys(t,s),l=pS(a,s,o,i,c);r[s]=new Ud(e.scaleName(`${s}`,!0),{value:l,explicit:a.type===l});}}return r}const yS=Gf((e,t)=>dc(e)-dc(t));function bS(e){var t;const n=e.component.scales={},i={},r=e.component.resolve;for(const s of e.children){Kd(s);for(const o of x(s.component.scales))if((t=r.scale)[o]??(t[o]=Nd(o,e)),r.scale[o]==="shared"){const a=i[o],c=s.component.scales[o].getWithExplicit("type");a?sm(a.value,c.value)?i[o]=qt(a,c,"type","scale",yS):(r.scale[o]="independent",delete i[o]):i[o]=c;}}for(const s of x(i)){const o=e.scaleName(s,!0),a=i[s];n[s]=new Ud(o,a);for(const c of e.children){const l=c.component.scales[s];l&&(c.renameScale(l.get("name"),o),l.merged=!0);}}return n}class Ts{constructor(){this.nameMap={};}rename(t,n){this.nameMap[t]=n;}has(t){return this.nameMap[t]!==void 0}get(t){for(;this.nameMap[t]&&t!==this.nameMap[t];)t=this.nameMap[t];return t}}function oe(e){return e?.type==="unit"}function Ke(e){return e?.type==="facet"}function Pa(e){return e?.type==="concat"}function xi(e){return e?.type==="layer"}class La{constructor(t,n,i,r,s,o,a){this.type=n,this.parent=i,this.config=s,this.correctDataNames=c=>(c.from?.data&&(c.from.data=this.lookupDataSource(c.from.data)),c.from?.facet?.data&&(c.from.facet.data=this.lookupDataSource(c.from.facet.data)),c),this.parent=i,this.config=s,this.view=Ne(a),this.name=t.name??r,this.title=jt(t.title)?{text:t.title}:t.title?Ne(t.title):void 0,this.scaleNameMap=i?i.scaleNameMap:new Ts,this.projectionNameMap=i?i.projectionNameMap:new Ts,this.signalNameMap=i?i.signalNameMap:new Ts,this.data=t.data,this.description=t.description,this.transforms=Ab(t.transform??[]),this.layout=n==="layer"||n==="unit"?{}:Iy(t,n,s),this.component={data:{sources:i?i.component.data.sources:[],outputNodes:i?i.component.data.outputNodes:{},outputNodeRefCounts:i?i.component.data.outputNodeRefCounts:{},isFaceted:qr(t)||i?.component.data.isFaceted&&t.data===void 0},layoutSize:new Dt,layoutHeaders:{row:{},column:{},facet:{}},mark:null,resolve:{scale:{},axis:{},legend:{},...o?L(o):{}},selection:null,scales:null,projection:null,axes:{},legends:{}};}get width(){return this.getSizeSignalRef("width")}get height(){return this.getSizeSignalRef("height")}parse(){this.parseScale(),this.parseLayoutSize(),this.renameTopLevelLayoutSizeSignal(),this.parseSelections(),this.parseProjection(),this.parseData(),this.parseAxesAndHeaders(),this.parseLegends(),this.parseMarkGroup();}parseScale(){hS(this);}parseProjection(){zd(this);}renameTopLevelLayoutSizeSignal(){this.getName("width")!=="width"&&this.renameSignal(this.getName("width"),"width"),this.getName("height")!=="height"&&this.renameSignal(this.getName("height"),"height");}parseLegends(){Td(this);}assembleEncodeFromView(t){const{style:n,...i}=t,r={};for(const s of x(i)){const o=i[s];o!==void 0&&(r[s]=ne(o));}return r}assembleGroupEncodeEntry(t){let n={};return this.view&&(n=this.assembleEncodeFromView(this.view)),!t&&(this.description&&(n.description=ne(this.description)),this.type==="unit"||this.type==="layer")?{width:this.getSizeSignalRef("width"),height:this.getSizeSignalRef("height"),...n??{}}:Y(n)?void 0:n}assembleLayout(){if(!this.layout)return;const{spacing:t,...n}=this.layout,{component:i,config:r}=this,s=Mx(i.layoutHeaders,r);return {padding:t,...this.assembleDefaultLayout(),...n,...s?{titleBand:s}:{}}}assembleDefaultLayout(){return {}}assembleHeaderMarks(){const{layoutHeaders:t}=this.component;let n=[];for(const i of De)t[i].title&&n.push(Ix(this,i));for(const i of Ca)n=n.concat(Rx(this,i));return n}assembleAxes(){return vx(this.component.axes,this.config)}assembleLegends(){return Rd(this)}assembleProjections(){return cv(this)}assembleTitle(){const{encoding:t,...n}=this.title??{},i={...cu(this.config.title).nonMarkTitleProperties,...n,...t?{encode:{update:t}}:{}};if(i.text)return W(["unit","layer"],this.type)?W(["middle",void 0],i.anchor)&&(i.frame??(i.frame="group")):i.anchor??(i.anchor="start"),Y(i)?void 0:i}assembleGroup(t=[]){const n={};t=t.concat(this.assembleSignals()),t.length>0&&(n.signals=t);const i=this.assembleLayout();i&&(n.layout=i),n.marks=[].concat(this.assembleHeaderMarks(),this.assembleMarks());const r=!this.parent||Ke(this.parent)?Bd(this):[];r.length>0&&(n.scales=r);const s=this.assembleAxes();s.length>0&&(n.axes=s);const o=this.assembleLegends();return o.length>0&&(n.legends=o),n}getName(t){return ie((this.name?`${this.name}_`:"")+t)}getDataName(t){return this.getName(J[t].toLowerCase())}requestDataName(t){const n=this.getDataName(t),i=this.component.data.outputNodeRefCounts;return i[n]=(i[n]||0)+1,n}getSizeSignalRef(t){if(Ke(this.parent)){const n=Ad(t),i=Pr(n),r=this.component.scales[i];if(r&&!r.merged){const s=r.get("type"),o=r.get("range");if(ye(s)&&tn(o)){const a=r.get("name"),c=ss(this,i),l=Ra(c);if(l){const u=$({aggregate:"distinct",field:l},{expr:"datum"});return {signal:wd(a,r,u)}}else return v(Ro(i)),null}}}return {signal:this.signalNameMap.get(this.getName(t))}}lookupDataSource(t){const n=this.component.data.outputNodes[t];return n?n.getSource():t}getSignalName(t){return this.signalNameMap.get(t)}renameSignal(t,n){this.signalNameMap.rename(t,n);}renameScale(t,n){this.scaleNameMap.rename(t,n);}renameProjection(t,n){this.projectionNameMap.rename(t,n);}scaleName(t,n){if(n)return this.getName(t);if(Jl(t)&&Lt(t)&&this.component.scales[t]||this.scaleNameMap.has(this.getName(t)))return this.scaleNameMap.get(this.getName(t))}projectionName(t){if(t)return this.getName("projection");if(this.component.projection&&!this.component.projection.merged||this.projectionNameMap.has(this.getName("projection")))return this.projectionNameMap.get(this.getName("projection"))}getScaleComponent(t){if(!this.component.scales)throw new Error("getScaleComponent cannot be called before parseScale(). Make sure you have called parseScale or use parseUnitModelWithScale().");const n=this.component.scales[t];return n&&!n.merged?n:this.parent?this.parent.getScaleComponent(t):void 0}getSelectionComponent(t,n){let i=this.component.selection[t];if(!i&&this.parent&&(i=this.parent.getSelectionComponent(t,n)),!i)throw new Error(Pg(n));return i}hasAxisOrientSignalRef(){return this.component.axes.x?.some(t=>t.hasOrientSignalRef())||this.component.axes.y?.some(t=>t.hasOrientSignalRef())}}class Qd extends La{vgField(t,n={}){const i=this.fieldDef(t);if(i)return $(i,n)}reduceFieldDef(t,n){return cy(this.getMapping(),(i,r,s)=>{const o=gt(r);return o?t(i,o,s):i},n)}forEachFieldDef(t,n){ca(this.getMapping(),(i,r)=>{const s=gt(i);s&&t(s,r);},n);}}class os extends X{clone(){return new os(null,L(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=L(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"value",i[1]??"density"],n.groupby&&n.minsteps==null&&n.maxsteps==null&&n.steps==null&&(this.transform.steps=200);}dependentFields(){return new Set([this.transform.density,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return `DensityTransform ${U(this.transform)}`}assemble(){const{density:t,...n}=this.transform;return {type:"kde",field:t,...n}}}class as extends X{clone(){return new as(null,L(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=L(n);}dependentFields(){return new Set([this.transform.extent])}producedFields(){return new Set([])}hash(){return `ExtentTransform ${U(this.transform)}`}assemble(){const{extent:t,param:n}=this.transform;return {type:"extent",field:t,signal:n}}}class Li extends X{clone(){return new Li(null,{...this.filter})}constructor(t,n){super(t),this.filter=n;}static make(t,n){const{config:i,mark:r,markDef:s}=n;if(H("invalid",s,i)!=="filter")return null;const a=n.reduceFieldDef((c,l,u)=>{const f=Lt(u)&&n.getScaleComponent(u);if(f){const d=f.get("type");Le(d)&&l.aggregate!=="count"&&!nn(r)&&(c[l.field]=l);}return c},{});return x(a).length?new Li(t,a):null}dependentFields(){return new Set(x(this.filter))}producedFields(){return new Set}hash(){return `FilterInvalid ${U(this.filter)}`}assemble(){const t=x(this.filter).reduce((n,i)=>{const r=this.filter[i],s=$(r,{expr:"datum"});return r!==null&&(r.type==="temporal"?n.push(`(isDate(${s}) || (isValid(${s}) && isFinite(+${s})))`):r.type==="quantitative"&&(n.push(`isValid(${s})`),n.push(`isFinite(+${s})`))),n},[]);return t.length>0?{type:"filter",expr:t.join(" && ")}:null}}class cs extends X{clone(){return new cs(this.parent,L(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=L(n);const{flatten:i,as:r=[]}=this.transform;this.transform.as=i.map((s,o)=>r[o]??s);}dependentFields(){return new Set(this.transform.flatten)}producedFields(){return new Set(this.transform.as)}hash(){return `FlattenTransform ${U(this.transform)}`}assemble(){const{flatten:t,as:n}=this.transform;return {type:"flatten",fields:t,as:n}}}class ls extends X{clone(){return new ls(null,L(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=L(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"key",i[1]??"value"];}dependentFields(){return new Set(this.transform.fold)}producedFields(){return new Set(this.transform.as)}hash(){return `FoldTransform ${U(this.transform)}`}assemble(){const{fold:t,as:n}=this.transform;return {type:"fold",fields:t,as:n}}}class Vn extends X{clone(){return new Vn(null,L(this.fields),this.geojson,this.signal)}static parseAll(t,n){if(n.component.projection&&!n.component.projection.isFit)return t;let i=0;for(const r of [[nt,tt],[je,it]]){const s=r.map(o=>{const a=fe(n.encoding[o]);return S(a)?a.field:St(a)?{expr:`${a.datum}`}:Qe(a)?{expr:`${a.value}`}:void 0});(s[0]||s[1])&&(t=new Vn(t,s,null,n.getName(`geojson_${i++}`)));}if(n.channelHasField(Te)){const r=n.typedFieldDef(Te);r.type===gi&&(t=new Vn(t,null,r.field,n.getName(`geojson_${i++}`)));}return t}constructor(t,n,i,r){super(t),this.fields=n,this.geojson=i,this.signal=r;}dependentFields(){const t=(this.fields??[]).filter(xt$1);return new Set([...this.geojson?[this.geojson]:[],...t])}producedFields(){return new Set}hash(){return `GeoJSON ${this.geojson} ${this.signal} ${U(this.fields)}`}assemble(){return [...this.geojson?[{type:"filter",expr:`isValid(datum["${this.geojson}"])`}]:[],{type:"geojson",...this.fields?{fields:this.fields}:{},...this.geojson?{geojson:this.geojson}:{},signal:this.signal}]}}class zi extends X{clone(){return new zi(null,this.projection,L(this.fields),L(this.as))}constructor(t,n,i,r){super(t),this.projection=n,this.fields=i,this.as=r;}static parseAll(t,n){if(!n.projectionName())return t;for(const i of [[nt,tt],[je,it]]){const r=i.map(o=>{const a=fe(n.encoding[o]);return S(a)?a.field:St(a)?{expr:`${a.datum}`}:Qe(a)?{expr:`${a.value}`}:void 0}),s=i[0]===je?"2":"";(r[0]||r[1])&&(t=new zi(t,n.projectionName(),r,[n.getName(`x${s}`),n.getName(`y${s}`)]));}return t}dependentFields(){return new Set(this.fields.filter(xt$1))}producedFields(){return new Set(this.as)}hash(){return `Geopoint ${this.projection} ${U(this.fields)} ${U(this.as)}`}assemble(){return {type:"geopoint",projection:this.projection,fields:this.fields,as:this.as}}}class yn extends X{clone(){return new yn(null,L(this.transform))}constructor(t,n){super(t),this.transform=n;}dependentFields(){return new Set([this.transform.impute,this.transform.key,...this.transform.groupby??[]])}producedFields(){return new Set([this.transform.impute])}processSequence(t){const{start:n=0,stop:i,step:r}=t;return {signal:`sequence(${[n,i,...r?[r]:[]].join(",")})`}}static makeFromTransform(t,n){return new yn(t,n)}static makeFromEncoding(t,n){const i=n.encoding,r=i.x,s=i.y;if(S(r)&&S(s)){const o=r.impute?r:s.impute?s:void 0;if(o===void 0)return;const a=r.impute?s:s.impute?r:void 0,{method:c,value:l,frame:u,keyvals:f}=o.impute,d=mf(n.mark,i);return new yn(t,{impute:o.field,key:a.field,...c?{method:c}:{},...l!==void 0?{value:l}:{},...u?{frame:u}:{},...f!==void 0?{keyvals:f}:{},...d.length?{groupby:d}:{}})}return null}hash(){return `Impute ${U(this.transform)}`}assemble(){const{impute:t,key:n,keyvals:i,method:r,groupby:s,value:o,frame:a=[null,null]}=this.transform,c={type:"impute",field:t,key:n,...i?{keyvals:ob(i)?this.processSequence(i):i}:{},method:"value",...s?{groupby:s}:{},value:!r||r==="value"?o:null};if(r&&r!=="value"){const l={type:"window",as:[`imputed_${t}_value`],ops:[r],fields:[t],frame:a,ignorePeers:!1,...s?{groupby:s}:{}},u={type:"formula",expr:`datum.${t} === null ? datum.imputed_${t}_value : datum.${t}`,as:t};return [c,l,u]}else return [c]}}class us extends X{clone(){return new us(null,L(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=L(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.loess];}dependentFields(){return new Set([this.transform.loess,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return `LoessTransform ${U(this.transform)}`}assemble(){const{loess:t,on:n,...i}=this.transform;return {type:"loess",x:n,y:t,...i}}}class Di extends X{clone(){return new Di(null,L(this.transform),this.secondary)}constructor(t,n,i){super(t),this.transform=n,this.secondary=i;}static make(t,n,i,r){const s=n.component.data.sources,{from:o}=i;let a=null;if(ab(o)){let c=ep(o.data,s);c||(c=new Nn(o.data),s.push(c));const l=n.getName(`lookup_${r}`);a=new we(c,l,J.Lookup,n.component.data.outputNodeRefCounts),n.component.data.outputNodes[l]=a;}else if(cb(o)){const c=o.param;i={as:c,...i};let l;try{l=n.getSelectionComponent(ie(c),c);}catch{throw new Error(Dg(c))}if(a=l.materialized,!a)throw new Error(Mg(c))}return new Di(t,i,a.getSource())}dependentFields(){return new Set([this.transform.lookup])}producedFields(){return new Set(this.transform.as?H$1(this.transform.as):this.transform.from.fields)}hash(){return `Lookup ${U({transform:this.transform,secondary:this.secondary})}`}assemble(){let t;if(this.transform.from.fields)t={values:this.transform.from.fields,...this.transform.as?{as:H$1(this.transform.as)}:{}};else {let n=this.transform.as;xt$1(n)||(v(Vg),n="_lookup"),t={as:[n]};}return {type:"lookup",from:this.secondary,key:this.transform.from.key,fields:[this.transform.lookup],...t,...this.transform.default?{default:this.transform.default}:{}}}}class fs extends X{clone(){return new fs(null,L(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=L(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"prob",i[1]??"value"];}dependentFields(){return new Set([this.transform.quantile,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return `QuantileTransform ${U(this.transform)}`}assemble(){const{quantile:t,...n}=this.transform;return {type:"quantile",field:t,...n}}}class ds extends X{clone(){return new ds(null,L(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=L(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.regression];}dependentFields(){return new Set([this.transform.regression,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return `RegressionTransform ${U(this.transform)}`}assemble(){const{regression:t,on:n,...i}=this.transform;return {type:"regression",x:n,y:t,...i}}}class ps extends X{clone(){return new ps(null,L(this.transform))}constructor(t,n){super(t),this.transform=n;}addDimensions(t){this.transform.groupby=lt((this.transform.groupby??[]).concat(t),n=>n);}producedFields(){}dependentFields(){return new Set([this.transform.pivot,this.transform.value,...this.transform.groupby??[]])}hash(){return `PivotTransform ${U(this.transform)}`}assemble(){const{pivot:t,value:n,groupby:i,limit:r,op:s}=this.transform;return {type:"pivot",field:t,value:n,...r!==void 0?{limit:r}:{},...s!==void 0?{op:s}:{},...i!==void 0?{groupby:i}:{}}}}class gs extends X{clone(){return new gs(null,L(this.transform))}constructor(t,n){super(t),this.transform=n;}dependentFields(){return new Set}producedFields(){return new Set}hash(){return `SampleTransform ${U(this.transform)}`}assemble(){return {type:"sample",size:this.transform.sample}}}function Jd(e){let t=0;function n(i,r){if(i instanceof Nn&&!i.isGenerator&&!ri(i.data)&&(e.push(r),r={name:null,source:r.name,transform:[]}),i instanceof $e&&(i.parent instanceof Nn&&!r.source?(r.format={...r.format??{},parse:i.assembleFormatParse()},r.transform.push(...i.assembleTransforms(!0))):r.transform.push(...i.assembleTransforms())),i instanceof yi){r.name||(r.name=`data_${t++}`),!r.source||r.transform.length>0?(e.push(r),i.data=r.name):i.data=r.source,e.push(...i.assemble());return}switch((i instanceof Ki||i instanceof Qi||i instanceof Li||i instanceof mi||i instanceof oi||i instanceof zi||i instanceof Ye||i instanceof Di||i instanceof bi||i instanceof Ln||i instanceof ls||i instanceof cs||i instanceof os||i instanceof us||i instanceof fs||i instanceof ds||i instanceof Xt||i instanceof gs||i instanceof ps||i instanceof as)&&r.transform.push(i.assemble()),(i instanceof dt||i instanceof ft||i instanceof yn||i instanceof _t||i instanceof Vn)&&r.transform.push(...i.assemble()),i instanceof we&&(r.source&&r.transform.length===0?i.setSource(r.source):i.parent instanceof we?i.setSource(r.name):(r.name||(r.name=`data_${t++}`),i.setSource(r.name),i.numChildren()===1&&(e.push(r),r={name:null,source:r.name,transform:[]}))),i.numChildren()){case 0:i instanceof we&&(!r.source||r.transform.length>0)&&e.push(r);break;case 1:n(i.children[0],r);break;default:{r.name||(r.name=`data_${t++}`);let s=r.name;!r.source||r.transform.length>0?e.push(r):s=r.source;for(const o of i.children)n(o,{name:null,source:s,transform:[]});break}}}return n}function xS(e){const t=[],n=Jd(t);for(const i of e.children)n(i,{source:e.name,name:null,transform:[]});return t}function vS(e,t){const n=[],i=Jd(n);let r=0;for(const o of e.sources){o.hasName()||(o.dataName=`source_${r++}`);const a=o.assemble();i(o,a);}for(const o of n)o.transform.length===0&&delete o.transform;let s=0;for(const[o,a]of n.entries())(a.transform??[]).length===0&&!a.source&&n.splice(s++,0,n.splice(o,1)[0]);for(const o of n)for(const a of o.transform??[])a.type==="lookup"&&(a.from=e.outputNodes[a.from].getSource());for(const o of n)o.name in t&&(o.values=t[o.name]);return n}function SS(e){return e==="top"||e==="left"||k(e)?"header":"footer"}function ES(e){for(const t of De)$S(e,t);sl(e,"x"),sl(e,"y");}function $S(e,t){const{facet:n,config:i,child:r,component:s}=e;if(e.channelHasField(t)){const o=n[t],a=ci("title",null,i,t);let c=qn(o,i,{allowDisabling:!0,includeDefault:a===void 0||!!a});r.component.layoutHeaders[t].title&&(c=U$1(c)?c.join(", "):c,c+=` / ${r.component.layoutHeaders[t].title}`,r.component.layoutHeaders[t].title=null);const l=ci("labelOrient",o.header,i,t),u=o.header!==null?ue(o.header?.labels,i.header.labels,!0):!1,f=W(["bottom","right"],l)?"footer":"header";s.layoutHeaders[t]={title:o.header!==null?c:null,facetFieldDef:o,[f]:t==="facet"?[]:[Zd(e,t,u)]};}}function Zd(e,t,n){const i=t==="row"?"height":"width";return {labels:n,sizeSignal:e.child.component.layoutSize.get(i)?e.child.getSizeSignalRef(i):void 0,axes:[]}}function sl(e,t){const{child:n}=e;if(n.component.axes[t]){const{layoutHeaders:i,resolve:r}=e.component;if(r.axis[t]=_a(r,t),r.axis[t]==="shared"){const s=t==="x"?"column":"row",o=i[s];for(const a of n.component.axes[t]){const c=SS(a.get("orient"));o[c]??(o[c]=[Zd(e,s,!1)]);const l=Ni(a,"main",e.config,{header:!0});l&&o[c][0].axes.push(l),a.mainExtracted=!0;}}}}function wS(e){za(e),Ar(e,"width"),Ar(e,"height");}function AS(e){za(e);const t=e.layout.columns===1?"width":"childWidth",n=e.layout.columns===void 0?"height":"childHeight";Ar(e,t),Ar(e,n);}function za(e){for(const t of e.children)t.parseLayoutSize();}function Ar(e,t){const n=Ad(t),i=Pr(n),r=e.component.resolve,s=e.component.layoutSize;let o;for(const a of e.children){const c=a.component.layoutSize.getWithExplicit(n),l=r.scale[i]??Nd(i,e);if(l==="independent"&&c.value==="step"){o=void 0;break}if(o){if(l==="independent"&&o.value!==c.value){o=void 0;break}o=qt(o,c,n,"");}else o=c;}if(o){for(const a of e.children)e.renameSignal(a.getName(n),e.getName(t)),a.component.layoutSize.set(n,"merged",!1);s.setWithExplicit(t,o);}else s.setWithExplicit(t,{explicit:!1,value:void 0});}function CS(e){const{size:t,component:n}=e;for(const i of vt){const r=Ie(i);if(t[r]){const s=t[r];n.layoutSize.set(r,ht(s)?"step":s,!0);}else {const s=NS(e,r);n.layoutSize.set(r,s,!1);}}}function NS(e,t){const n=t==="width"?"x":"y",i=e.config,r=e.getScaleComponent(n);if(r){const s=r.get("type"),o=r.get("range");if(ye(s)){const a=Sr(i.view,t);return tn(o)||ht(a)?"step":a}else return Qs(i.view,t)}else {if(e.hasProjection||e.mark==="arc")return Qs(i.view,t);{const s=Sr(i.view,t);return ht(s)?s.step:s}}}function fo(e,t,n){return $(t,{suffix:`by_${$(e)}`,...n??{}})}class Oi extends Qd{constructor(t,n,i,r){super(t,"facet",n,i,r,t.resolve),this.child=Ua(t.spec,this,this.getName("child"),void 0,r),this.children=[this.child],this.facet=this.initFacet(t.facet);}initFacet(t){if(!qi(t))return {facet:this.initFacetFieldDef(t,"facet")};const n=x(t),i={};for(const r of n){if(![Ct,Nt].includes(r)){v(Dr(r,"facet"));break}const s=t[r];if(s.field===void 0){v(qs(s,r));break}i[r]=this.initFacetFieldDef(s,r);}return i}initFacetFieldDef(t,n){const i=oa(t,n);return i.header?i.header=Ne(i.header):i.header===null&&(i.header=null),i}channelHasField(t){return !!this.facet[t]}fieldDef(t){return this.facet[t]}parseData(){this.component.data=hs(this),this.child.parseData();}parseLayoutSize(){za(this);}parseSelections(){this.child.parseSelections(),this.component.selection=this.child.component.selection;}parseMarkGroup(){this.child.parseMarkGroup();}parseAxesAndHeaders(){this.child.parseAxesAndHeaders(),ES(this);}assembleSelectionTopLevelSignals(t){return this.child.assembleSelectionTopLevelSignals(t)}assembleSignals(){return this.child.assembleSignals(),[]}assembleSelectionData(t){return this.child.assembleSelectionData(t)}getHeaderLayoutMixins(){const t={};for(const n of De)for(const i of Na){const r=this.component.layoutHeaders[n],s=r[i],{facetFieldDef:o}=r;if(o){const a=ci("titleOrient",o.header,this.config,n);if(["right","bottom"].includes(a)){const c=is(n,a);t.titleAnchor??(t.titleAnchor={}),t.titleAnchor[c]="end";}}if(s?.[0]){const a=n==="row"?"height":"width",c=i==="header"?"headerBand":"footerBand";n!=="facet"&&!this.child.component.layoutSize.get(a)&&(t[c]??(t[c]={}),t[c][n]=.5),r.title&&(t.offset??(t.offset={}),t.offset[n==="row"?"rowTitle":"columnTitle"]=10);}}return t}assembleDefaultLayout(){const{column:t,row:n}=this.facet,i=t?this.columnDistinctSignal():n?1:void 0;let r="all";return (!n&&this.component.resolve.scale.x==="independent"||!t&&this.component.resolve.scale.y==="independent")&&(r="none"),{...this.getHeaderLayoutMixins(),...i?{columns:i}:{},bounds:"full",align:r}}assembleLayoutSignals(){return this.child.assembleLayoutSignals()}columnDistinctSignal(){if(!(this.parent&&this.parent instanceof Oi))return {signal:`length(data('${this.getName("column_domain")}'))`}}assembleGroupStyle(){}assembleGroup(t){return this.parent&&this.parent instanceof Oi?{...this.channelHasField("column")?{encode:{update:{columns:{field:$(this.facet.column,{prefix:"distinct"})}}}}:{},...super.assembleGroup(t)}:super.assembleGroup(t)}getCardinalityAggregateForChild(){const t=[],n=[],i=[];if(this.child instanceof Oi){if(this.child.channelHasField("column")){const r=$(this.child.facet.column);t.push(r),n.push("distinct"),i.push(`distinct_${r}`);}}else for(const r of vt){const s=this.child.component.scales[r];if(s&&!s.merged){const o=s.get("type"),a=s.get("range");if(ye(o)&&tn(a)){const c=ss(this.child,r),l=Ra(c);l?(t.push(l),n.push("distinct"),i.push(`distinct_${l}`)):v(Ro(r));}}}return {fields:t,ops:n,as:i}}assembleFacet(){const{name:t,data:n}=this.component.data.facetRoot,{row:i,column:r}=this.facet,{fields:s,ops:o,as:a}=this.getCardinalityAggregateForChild(),c=[];for(const u of De){const f=this.facet[u];if(f){c.push($(f));const{bin:d,sort:g}=f;if(ee(d)&&c.push($(f,{binSuffix:"end"})),ut(g)){const{field:p,op:h=Gr}=g,m=fo(f,g);i&&r?(s.push(m),o.push("max"),a.push(m)):(s.push(p),o.push(h),a.push(m));}else if(U$1(g)){const p=ai(f,u);s.push(p),o.push("max"),a.push(p);}}}const l=!!i&&!!r;return {name:t,data:n,groupby:c,...l||s.length>0?{aggregate:{...l?{cross:l}:{},...s.length?{fields:s,ops:o,as:a}:{}}}:{}}}facetSortFields(t){const{facet:n}=this,i=n[t];return i?ut(i.sort)?[fo(i,i.sort,{expr:"datum"})]:U$1(i.sort)?[ai(i,t,{expr:"datum"})]:[$(i,{expr:"datum"})]:[]}facetSortOrder(t){const{facet:n}=this,i=n[t];if(i){const{sort:r}=i;return [(ut(r)?r.order:!U$1(r)&&r)||"ascending"]}return []}assembleLabelTitle(){const{facet:t,config:n}=this;if(t.facet)return so(t.facet,"facet",n);const i={row:["top","bottom"],column:["left","right"]};for(const r of Ca)if(t[r]){const s=ci("labelOrient",t[r]?.header,n,r);if(i[r].includes(s))return so(t[r],r,n)}}assembleMarks(){const{child:t}=this,n=this.component.data.facetRoot,i=xS(n),r=t.assembleGroupEncodeEntry(!1),s=this.assembleLabelTitle()||t.assembleTitle(),o=t.assembleGroupStyle();return [{name:this.getName("cell"),type:"group",...s?{title:s}:{},...o?{style:o}:{},from:{facet:this.assembleFacet()},sort:{field:De.map(c=>this.facetSortFields(c)).flat(),order:De.map(c=>this.facetSortOrder(c)).flat()},...i.length>0?{data:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup(Db(this,[]))}]}getMapping(){return this.facet}}function FS(e,t){const{row:n,column:i}=t;if(n&&i){let r=null;for(const s of [n,i])if(ut(s.sort)){const{field:o,op:a=Gr}=s.sort;e=r=new Ln(e,{joinaggregate:[{op:a,field:o,as:fo(s,s.sort,{forAs:!0})}],groupby:[$(s)]});}return r}return null}function ep(e,t){for(const n of t){const i=n.data;if(e.name&&n.hasName()&&e.name!==n.dataName)continue;const r=e.format?.mesh,s=i.format?.feature;if(r&&s)continue;const o=e.format?.feature;if((o||s)&&o!==s)continue;const a=i.format?.mesh;if(!((r||a)&&r!==a)){if(Pi(e)&&Pi(i)){if(ct(e.values,i.values))return n}else if(ri(e)&&ri(i)){if(e.url===i.url)return n}else if(qf(e)&&e.name===n.dataName)return n}}return null}function _S(e,t){if(e.data||!e.parent){if(e.data===null){const i=new Nn({values:[]});return t.push(i),i}const n=ep(e.data,t);if(n)return Ut(e.data)||(n.data.format=jl({},e.data.format,n.data.format)),!n.hasName()&&e.data.name&&(n.dataName=e.data.name),n;{const i=new Nn(e.data);return t.push(i),i}}else return e.parent.component.data.facetRoot?e.parent.component.data.facetRoot:e.parent.component.data.main}function kS(e,t,n){let i=0;for(const r of t.transforms){let s,o;if(bb(r))o=e=new oi(e,r),s="derived";else if(xa(r)){const a=Sv(r);o=e=$e.makeWithAncestors(e,{},a,n)??e,e=new mi(e,t,r.filter);}else if(jf(r))o=e=dt.makeFromTransform(e,r,t),s="number";else if(vb(r))s="date",n.getWithExplicit(r.field).value===void 0&&(e=new $e(e,{[r.field]:s}),n.set(r.field,s,!1)),o=e=ft.makeFromTransform(e,r);else if(Sb(r))o=e=Ye.makeFromTransform(e,r),s="number",Aa(t)&&(e=new Xt(e));else if(Mf(r))o=e=Di.make(e,t,r,i++),s="derived";else if(hb(r))o=e=new bi(e,r),s="number";else if(mb(r))o=e=new Ln(e,r),s="number";else if(Eb(r))o=e=_t.makeFromTransform(e,r),s="derived";else if($b(r))o=e=new ls(e,r),s="derived";else if(wb(r))o=e=new as(e,r),s="derived";else if(yb(r))o=e=new cs(e,r),s="derived";else if(lb(r))o=e=new ps(e,r),s="derived";else if(gb(r))e=new gs(e,r);else if(xb(r))o=e=yn.makeFromTransform(e,r),s="derived";else if(ub(r))o=e=new os(e,r),s="derived";else if(fb(r))o=e=new fs(e,r),s="derived";else if(db(r))o=e=new ds(e,r),s="derived";else if(pb(r))o=e=new us(e,r),s="derived";else {v(Hg(r));continue}if(o&&s!==void 0)for(const a of o.producedFields()??[])n.set(a,s,!1);}return e}function hs(e){let t=_S(e,e.component.data.sources);const{outputNodes:n,outputNodeRefCounts:i}=e.component.data,r=e.data,o=!(r&&(Ut(r)||ri(r)||Pi(r)))&&e.parent?e.parent.component.data.ancestorParse.clone():new Lb;Ut(r)?(Hf(r)?t=new Qi(t,r.sequence):va(r)&&(t=new Ki(t,r.graticule)),o.parseNothing=!0):r?.format?.parse===null&&(o.parseNothing=!0),t=$e.makeExplicit(t,e,o)??t,t=new Xt(t);const a=e.parent&&xi(e.parent);(oe(e)||Ke(e))&&a&&(t=dt.makeFromEncoding(t,e)??t),e.transforms.length>0&&(t=kS(t,e,o));const c=$v(e),l=Ev(e);t=$e.makeWithAncestors(t,{},{...c,...l},o)??t,oe(e)&&(t=Vn.parseAll(t,e),t=zi.parseAll(t,e)),(oe(e)||Ke(e))&&(a||(t=dt.makeFromEncoding(t,e)??t),t=ft.makeFromEncoding(t,e)??t,t=oi.parseAllForSortIndex(t,e));const u=e.getDataName(J.Raw),f=new we(t,u,J.Raw,i);if(n[u]=f,t=f,oe(e)){const h=Ye.makeFromEncoding(t,e);h&&(t=h,Aa(e)&&(t=new Xt(t))),t=yn.makeFromEncoding(t,e)??t,t=_t.makeFromEncoding(t,e)??t;}oe(e)&&(t=Li.make(t,e)??t);const d=e.getDataName(J.Main),g=new we(t,d,J.Main,i);n[d]=g,t=g,oe(e)&&bx(e,g);let p=null;if(Ke(e)){const h=e.getName("facet");t=FS(t,e.facet)??t,p=new yi(t,e,h,g.getSource()),n[h]=p;}return {...e.component.data,outputNodes:n,outputNodeRefCounts:i,raw:f,main:g,facetRoot:p,ancestorParse:o}}class OS extends La{constructor(t,n,i,r){super(t,"concat",n,i,r,t.resolve),(t.resolve?.axis?.x==="shared"||t.resolve?.axis?.y==="shared")&&v(Wg),this.children=this.getChildren(t).map((s,o)=>Ua(s,this,this.getName(`concat_${o}`),void 0,r));}parseData(){this.component.data=hs(this);for(const t of this.children)t.parseData();}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of x(t.component.selection))this.component.selection[n]=t.component.selection[n];}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup();}parseAxesAndHeaders(){for(const t of this.children)t.parseAxesAndHeaders();}getChildren(t){return Jr(t)?t.vconcat:ya(t)?t.hconcat:t.concat}parseLayoutSize(){AS(this);}parseAxisGroup(){return null}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.forEach(t=>t.assembleSignals()),[]}assembleLayoutSignals(){const t=Fa(this);for(const n of this.children)t.push(...n.assembleLayoutSignals());return t}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleMarks(){return this.children.map(t=>{const n=t.assembleTitle(),i=t.assembleGroupStyle(),r=t.assembleGroupEncodeEntry(!1);return {type:"group",name:t.getName("group"),...n?{title:n}:{},...i?{style:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup()}})}assembleGroupStyle(){}assembleDefaultLayout(){const t=this.layout.columns;return {...t!=null?{columns:t}:{},bounds:"full",align:"each"}}}function TS(e){return e===!1||e===null}const IS={disable:1,gridScale:1,scale:1,...df,labelExpr:1,encode:1},tp=x(IS);class Da extends Dt{constructor(t={},n={},i=!1){super(),this.explicit=t,this.implicit=n,this.mainExtracted=i;}clone(){return new Da(L(this.explicit),L(this.implicit),this.mainExtracted)}hasAxisPart(t){return t==="axis"?!0:t==="grid"||t==="title"?!!this.get(t):!TS(this.get(t))}hasOrientSignalRef(){return k(this.explicit.orient)}}function RS(e,t,n){const{encoding:i,config:r}=e,s=fe(i[t])??fe(i[xt(t)]),o=e.axis(t)||{},{format:a,formatType:c}=o;if(wn(c))return {text:Xe({fieldOrDatumDef:s,field:"datum.value",format:a,formatType:c,config:r}),...n};if(a===void 0&&c===void 0&&r.customFormatTypes){if(ti(s)==="quantitative"){if(ni(s)&&s.stack==="normalize"&&r.normalizedNumberFormatType)return {text:Xe({fieldOrDatumDef:s,field:"datum.value",format:r.normalizedNumberFormat,formatType:r.normalizedNumberFormatType,config:r}),...n};if(r.numberFormatType)return {text:Xe({fieldOrDatumDef:s,field:"datum.value",format:r.numberFormat,formatType:r.numberFormatType,config:r}),...n}}if(ti(s)==="temporal"&&r.timeFormatType&&S(s)&&!s.timeUnit)return {text:Xe({fieldOrDatumDef:s,field:"datum.value",format:r.timeFormat,formatType:r.timeFormatType,config:r}),...n}}return n}function PS(e){return vt.reduce((t,n)=>(e.component.scales[n]&&(t[n]=[US(n,e)]),t),{})}const LS={bottom:"top",top:"bottom",left:"right",right:"left"};function zS(e){const{axes:t,resolve:n}=e.component,i={top:0,bottom:0,right:0,left:0};for(const r of e.children){r.parseAxesAndHeaders();for(const s of x(r.component.axes))n.axis[s]=_a(e.component.resolve,s),n.axis[s]==="shared"&&(t[s]=DS(t[s],r.component.axes[s]),t[s]||(n.axis[s]="independent",delete t[s]));}for(const r of vt){for(const s of e.children)if(s.component.axes[r]){if(n.axis[r]==="independent"){t[r]=(t[r]??[]).concat(s.component.axes[r]);for(const o of s.component.axes[r]){const{value:a,explicit:c}=o.getWithExplicit("orient");if(!k(a)){if(i[a]>0&&!c){const l=LS[a];i[a]>i[l]&&o.set("orient",l,!1);}i[a]++;}}}delete s.component.axes[r];}if(n.axis[r]==="independent"&&t[r]&&t[r].length>1)for(const[s,o]of (t[r]||[]).entries())s>0&&o.get("grid")&&!o.explicit.grid&&(o.implicit.grid=!1);}}function DS(e,t){if(e){if(e.length!==t.length)return;const n=e.length;for(let i=0;i<n;i++){const r=e[i],s=t[i];if(!!r!=!!s)return;if(r&&s){const o=r.getWithExplicit("orient"),a=s.getWithExplicit("orient");if(o.explicit&&a.explicit&&o.value!==a.value)return;e[i]=MS(r,s);}}}else return t.map(n=>n.clone());return e}function MS(e,t){for(const n of tp){const i=qt(e.getWithExplicit(n),t.getWithExplicit(n),n,"axis",(r,s)=>{switch(n){case"title":return hu(r,s);case"gridScale":return {explicit:r.explicit,value:ue(r.value,s.value)}}return es(r,s,n,"axis")});e.setWithExplicit(n,i);}return e}function jS(e,t,n,i,r){if(t==="disable")return n!==void 0;switch(n=n||{},t){case"titleAngle":case"labelAngle":return e===(k(n.labelAngle)?n.labelAngle:Ri(n.labelAngle));case"values":return !!n.values;case"encode":return !!n.encoding||!!n.labelAngle;case"title":if(e===vd(i,r))return !0}return e===n[t]}const BS=new Set(["grid","translate","format","formatType","orient","labelExpr","tickCount","position","tickMinStep"]);function US(e,t){let n=t.axis(e);const i=new Da,r=fe(t.encoding[e]),{mark:s,config:o}=t,a=n?.orient||o[e==="x"?"axisX":"axisY"]?.orient||o.axis?.orient||Fx(e),c=t.getScaleComponent(e).get("type"),l=Sx(e,c,a,t.config),u=n!==void 0?!n:io("disable",o.style,n?.style,l).configValue;if(i.set("disable",u,n!==void 0),u)return i;n=n||{};const f=Ax(r,n,e,o.style,l),d=Yu(n.formatType,r,c),g=Xu(r,r.type,n.format,n.formatType,o,!0),p={fieldOrDatumDef:r,axis:n,channel:e,model:t,scaleType:c,orient:a,labelAngle:f,format:g,formatType:d,mark:s,config:o};for(const y of tp){const b=y in Gc?Gc[y](p):xc(y)?n[y]:void 0,O=b!==void 0,C=jS(b,y,n,t,e);if(O&&C)i.set(y,b,C);else {const{configValue:w=void 0,configFrom:M=void 0}=xc(y)&&y!=="values"?io(y,o.style,n.style,l):{},D=w!==void 0;O&&!D?i.set(y,b,C):(M!=="vgAxisConfig"||BS.has(y)&&D||Xi(w)||k(w))&&i.set(y,w,!1);}}const h=n.encoding??{},m=ff.reduce((y,b)=>{if(!i.hasAxisPart(b))return y;const O=Cd(h[b]??{},t),C=b==="labels"?RS(t,e,O):O;return C!==void 0&&!Y(C)&&(y[b]={update:C}),y},{});return Y(m)||i.set("encode",m,!!n.encoding||n.labelAngle!==void 0),i}function WS({encoding:e,size:t}){for(const n of vt){const i=Ie(n);ht(t[i])&&Bt(e[n])&&(delete t[i],v(vu(i)));}return t}function GS(e,t,n){const i=Ne(e),r=H("orient",i,n);if(i.orient=XS(i.type,t,r),r!==void 0&&r!==i.orient&&v(uh(i.orient,r)),i.type==="bar"&&i.orient){const a=H("cornerRadiusEnd",i,n);if(a!==void 0){const c=i.orient==="horizontal"&&t.x2||i.orient==="vertical"&&t.y2?["cornerRadius"]:_m[i.orient];for(const l of c)i[l]=a;i.cornerRadiusEnd!==void 0&&delete i.cornerRadiusEnd;}}return H("opacity",i,n)===void 0&&(i.opacity=HS(i.type,t)),H("cursor",i,n)===void 0&&(i.cursor=qS(i,t,n)),i}function qS(e,t,n){return t.href||e.href||H("href",e,n)?"pointer":e.cursor}function HS(e,t){if(W([Wr,Xo,Yo,Ko],e)&&!aa(t))return .7}function VS(e,t,{graticule:n}){if(n)return !1;const i=Ot("filled",e,t),r=e.type;return ue(i,r!==Wr&&r!==Ur&&r!==pr)}function XS(e,t,n){switch(e){case Wr:case Yo:case Ko:case Uu:case xm:case bm:return}const{x:i,y:r,x2:s,y2:o}=t;switch(e){case Br:if(S(i)&&(be(i.bin)||S(r)&&r.aggregate&&!i.aggregate))return "vertical";if(S(r)&&(be(r.bin)||S(i)&&i.aggregate&&!r.aggregate))return "horizontal";if(o||s){if(n)return n;if(!s)return (S(i)&&i.type===En&&!ee(i.bin)||hr(i))&&S(r)&&be(r.bin)?"horizontal":"vertical";if(!o)return (S(r)&&r.type===En&&!ee(r.bin)||hr(r))&&S(i)&&be(i.bin)?"vertical":"horizontal"}case pr:if(s&&!(S(i)&&be(i.bin))&&o&&!(S(r)&&be(r.bin)))return;case jr:if(o)return S(r)&&be(r.bin)?"horizontal":"vertical";if(s)return S(i)&&be(i.bin)?"vertical":"horizontal";if(e===pr){if(i&&!r)return "vertical";if(r&&!i)return "horizontal"}case Ur:case Xo:{const a=yc(i),c=yc(r);if(n)return n;if(a&&!c)return e!=="tick"?"horizontal":"vertical";if(!a&&c)return e!=="tick"?"vertical":"horizontal";if(a&&c)return "vertical";{const l=Ae(i)&&i.type===Zn,u=Ae(r)&&r.type===Zn;if(l&&!u)return "vertical";if(!l&&u)return "horizontal"}return}}return "vertical"}const YS={vgMark:"arc",encodeEntry:e=>({...Ue(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...Ht(e,"radius"),...Ht(e,"theta")})},KS={vgMark:"area",encodeEntry:e=>({...Ue(e,{align:"ignore",baseline:"ignore",color:"include",orient:"include",size:"ignore",theta:"ignore"}),...Er("x",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="horizontal"}),...Er("y",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="vertical"}),...wa(e)})},QS={vgMark:"rect",encodeEntry:e=>({...Ue(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Ht(e,"x"),...Ht(e,"y")})},JS={vgMark:"shape",encodeEntry:e=>({...Ue(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"})}),postEncodingTransform:e=>{const{encoding:t}=e,n=t.shape;return [{type:"geoshape",projection:e.projectionName(),...n&&S(n)&&n.type===gi?{field:$(n,{expr:"datum"})}:{}}]}},ZS={vgMark:"image",encodeEntry:e=>({...Ue(e,{align:"ignore",baseline:"ignore",color:"ignore",orient:"ignore",size:"ignore",theta:"ignore"}),...Ht(e,"x"),...Ht(e,"y"),...Ea(e,"url")})},eE={vgMark:"line",encodeEntry:e=>({...Ue(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...pe("size",e,{vgChannel:"strokeWidth"}),...wa(e)})},tE={vgMark:"trail",encodeEntry:e=>({...Ue(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...pe("size",e),...wa(e)})};function Ma(e,t){const{config:n}=e;return {...Ue(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...pe("size",e),...pe("angle",e),...nE(e,n,t)}}function nE(e,t,n){return n?{shape:{value:n}}:pe("shape",e)}const iE={vgMark:"symbol",encodeEntry:e=>Ma(e)},rE={vgMark:"symbol",encodeEntry:e=>Ma(e,"circle")},sE={vgMark:"symbol",encodeEntry:e=>Ma(e,"square")},oE={vgMark:"rect",encodeEntry:e=>({...Ue(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Ht(e,"x"),...Ht(e,"y")})},aE={vgMark:"rule",encodeEntry:e=>{const{markDef:t}=e,n=t.orient;return !e.encoding.x&&!e.encoding.y&&!e.encoding.latitude&&!e.encoding.longitude?{}:{...Ue(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Er("x",e,{defaultPos:n==="horizontal"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="vertical"}),...Er("y",e,{defaultPos:n==="vertical"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="horizontal"}),...pe("size",e,{vgChannel:"strokeWidth"})}}},cE={vgMark:"text",encodeEntry:e=>{const{config:t,encoding:n}=e;return {...Ue(e,{align:"include",baseline:"include",color:"include",size:"ignore",orient:"ignore",theta:"include"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...Ea(e),...pe("size",e,{vgChannel:"fontSize"}),...pe("angle",e),...jc("align",lE(e.markDef,n,t)),...jc("baseline",uE(e.markDef,n,t)),...Ee("radius",e,{defaultPos:null}),...Ee("theta",e,{defaultPos:null})}}};function lE(e,t,n){if(H("align",e,n)===void 0)return "center"}function uE(e,t,n){if(H("baseline",e,n)===void 0)return "middle"}const fE={vgMark:"rect",encodeEntry:e=>{const{config:t,markDef:n}=e,i=n.orient,r=i==="horizontal"?"width":"height",s=i==="horizontal"?"height":"width";return {...Ue(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid",vgChannel:"xc"}),...Ee("y",e,{defaultPos:"mid",vgChannel:"yc"}),...pe("size",e,{defaultValue:dE(e),vgChannel:r}),[s]:ne(H("thickness",n,t))}}};function dE(e){const{config:t,markDef:n}=e,{orient:i}=n,r=i==="horizontal"?"width":"height",s=e.getScaleComponent(i==="horizontal"?"x":"y"),o=H("size",n,t,{vgChannel:r})??t.tick.bandSize;if(o!==void 0)return o;{const a=s?s.get("range"):void 0;return a&&tn(a)&&Mr$1(a.step)?a.step*3/4:vr(t.view,r)*3/4}}const ir={arc:YS,area:KS,bar:QS,circle:rE,geoshape:JS,image:ZS,line:eE,point:iE,rect:oE,rule:aE,square:sE,text:cE,tick:fE,trail:tE};function pE(e){if(W([Ur,jr,vm],e.mark)){const t=mf(e.mark,e.encoding);if(t.length>0)return gE(e,t)}else if(e.mark===Br){const t=Gs.some(n=>H(n,e.markDef,e.config));if(e.stack&&!e.fieldDef("size")&&t)return hE(e)}return ja(e)}const ol="faceted_path_";function gE(e,t){return [{name:e.getName("pathgroup"),type:"group",from:{facet:{name:ol+e.requestDataName(J.Main),data:e.requestDataName(J.Main),groupby:t}},encode:{update:{width:{field:{group:"width"}},height:{field:{group:"height"}}}},marks:ja(e,{fromPrefix:ol})}]}const al="stack_group_";function hE(e){const[t]=ja(e,{fromPrefix:al}),n=e.scaleName(e.stack.fieldChannel),i=(l={})=>e.vgField(e.stack.fieldChannel,l),r=(l,u)=>{const f=[i({prefix:"min",suffix:"start",expr:u}),i({prefix:"max",suffix:"start",expr:u}),i({prefix:"min",suffix:"end",expr:u}),i({prefix:"max",suffix:"end",expr:u})];return `${l}(${f.map(d=>`scale('${n}',${d})`).join(",")})`};let s,o;e.stack.fieldChannel==="x"?(s={...Yn(t.encode.update,["y","yc","y2","height",...Gs]),x:{signal:r("min","datum")},x2:{signal:r("max","datum")},clip:{value:!0}},o={x:{field:{group:"x"},mult:-1},height:{field:{group:"height"}}},t.encode.update={...Fe(t.encode.update,["y","yc","y2"]),height:{field:{group:"height"}}}):(s={...Yn(t.encode.update,["x","xc","x2","width"]),y:{signal:r("min","datum")},y2:{signal:r("max","datum")},clip:{value:!0}},o={y:{field:{group:"y"},mult:-1},width:{field:{group:"width"}}},t.encode.update={...Fe(t.encode.update,["x","xc","x2"]),width:{field:{group:"width"}}});for(const l of Gs){const u=Ot(l,e.markDef,e.config);t.encode.update[l]?(s[l]=t.encode.update[l],delete t.encode.update[l]):u&&(s[l]=ne(u)),u&&(t.encode.update[l]={value:0});}const a=[];if(e.stack.groupbyChannels?.length>0)for(const l of e.stack.groupbyChannels){const u=e.fieldDef(l),f=$(u);f&&a.push(f),(u?.bin||u?.timeUnit)&&a.push($(u,{binSuffix:"end"}));}return s=["stroke","strokeWidth","strokeJoin","strokeCap","strokeDash","strokeDashOffset","strokeMiterLimit","strokeOpacity"].reduce((l,u)=>{if(t.encode.update[u])return {...l,[u]:t.encode.update[u]};{const f=Ot(u,e.markDef,e.config);return f!==void 0?{...l,[u]:ne(f)}:l}},s),s.stroke&&(s.strokeForeground={value:!0},s.strokeOffset={value:0}),[{type:"group",from:{facet:{data:e.requestDataName(J.Main),name:al+e.requestDataName(J.Main),groupby:a,aggregate:{fields:[i({suffix:"start"}),i({suffix:"start"}),i({suffix:"end"}),i({suffix:"end"})],ops:["min","max","min","max"]}}},encode:{update:s},marks:[{type:"group",encode:{update:o},marks:[t]}]}]}function mE(e){const{encoding:t,stack:n,mark:i,markDef:r,config:s}=e,o=t.order;if(!(!U$1(o)&&Qe(o)&&Us(o.value)||!o&&Us(H("order",r,s)))){if((U$1(o)||S(o))&&!n)return du(o,{expr:"datum"});if(nn(i)){const a=r.orient==="horizontal"?"y":"x",c=t[a];if(S(c)){const l=c.sort;if(U$1(l))return {field:$(c,{prefix:a,suffix:"sort_index",expr:"datum"})};if(ut(l))return {field:$({aggregate:aa(e.encoding)?l.op:void 0,field:l.field},{expr:"datum"})};if(Qu(l)){const u=e.fieldDef(l.encoding);return {field:$(u,{expr:"datum"}),order:l.order}}else return l===null?void 0:{field:$(c,{binSuffix:e.stack?.impute?"mid":void 0,expr:"datum"})}}return}}}function ja(e,t={fromPrefix:""}){const{mark:n,markDef:i,encoding:r,config:s}=e,o=ue(i.clip,yE(e),bE(e)),a=uu(i),c=r.key,l=mE(e),u=xE(e),f=H("aria",i,s),d=ir[n].postEncodingTransform?ir[n].postEncodingTransform(e):null;return [{name:e.getName("marks"),type:ir[n].vgMark,...o?{clip:!0}:{},...a?{style:a}:{},...c?{key:c.field}:{},...l?{sort:l}:{},...u||{},...f===!1?{aria:f}:{},from:{data:t.fromPrefix+e.requestDataName(J.Main)},encode:{update:ir[n].encodeEntry(e)},...d?{transform:d}:{}}]}function yE(e){const t=e.getScaleComponent("x"),n=e.getScaleComponent("y");return t?.get("selectionExtent")||n?.get("selectionExtent")?!0:void 0}function bE(e){const t=e.component.projection;return t&&!t.isFit?!0:void 0}function xE(e){if(!e.component.selection)return null;const t=x(e.component.selection).length;let n=t,i=e.parent;for(;i&&n===0;)n=x(i.component.selection).length,i=i.parent;return n?{interactive:t>0||e.mark==="geoshape"||!!e.encoding.tooltip}:null}class np extends Qd{constructor(t,n,i,r={},s){super(t,"unit",n,i,s,void 0,Sc(t)?t.view:void 0),this.specifiedScales={},this.specifiedAxes={},this.specifiedLegends={},this.specifiedProjection={},this.selection=[],this.children=[];const o=pt(t.mark)?{...t.mark}:{type:t.mark},a=o.type;o.filled===void 0&&(o.filled=VS(o,s,{graticule:t.data&&va(t.data)}));const c=this.encoding=oy(t.encoding||{},a,o.filled,s);this.markDef=GS(o,c,s),this.size=WS({encoding:c,size:Sc(t)?{...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}}:r}),this.stack=Lf(this.markDef,c),this.specifiedScales=this.initScales(a,c),this.specifiedAxes=this.initAxes(c),this.specifiedLegends=this.initLegends(c),this.specifiedProjection=t.projection,this.selection=(t.params??[]).filter(l=>ha(l));}get hasProjection(){const{encoding:t}=this,n=this.mark===Wu,i=t&&rg.some(r=>z(t[r]));return n||i}scaleDomain(t){const n=this.specifiedScales[t];return n?n.domain:void 0}axis(t){return this.specifiedAxes[t]}legend(t){return this.specifiedLegends[t]}initScales(t,n){return Lr.reduce((i,r)=>{const s=fe(n[r]);return s&&(i[r]=this.initScale(s.scale??{})),i},{})}initScale(t){const{domain:n,range:i}=t,r=Ne(t);return U$1(n)&&(r.domain=n.map(Pe)),U$1(i)&&(r.range=i.map(Pe)),r}initAxes(t){return vt.reduce((n,i)=>{const r=t[i];if(z(r)||i===re&&z(t.x2)||i===he&&z(t.y2)){const s=z(r)?r.axis:void 0;n[i]=s&&this.initAxis({...s});}return n},{})}initAxis(t){const n=x(t),i={};for(const r of n){const s=t[r];i[r]=Xi(s)?lu(s):Pe(s);}return i}initLegends(t){return pg.reduce((n,i)=>{const r=fe(t[i]);if(r&&hg(i)){const s=r.legend;n[i]=s&&Ne(s);}return n},{})}parseData(){this.component.data=hs(this);}parseLayoutSize(){CS(this);}parseSelections(){this.component.selection=yx(this,this.selection);}parseMarkGroup(){this.component.mark=pE(this);}parseAxesAndHeaders(){this.component.axes=PS(this);}assembleSelectionTopLevelSignals(t){return Mb(this,t)}assembleSignals(){return [...yd(this),...zb(this,[])]}assembleSelectionData(t){return jb(this,t)}assembleLayout(){return null}assembleLayoutSignals(){return Fa(this)}assembleMarks(){let t=this.component.mark??[];return (!this.parent||!xi(this.parent))&&(t=Yf(this,t)),t.map(this.correctDataNames)}assembleGroupStyle(){const{style:t}=this.view||{};return t!==void 0?t:this.encoding.x||this.encoding.y?"cell":"view"}getMapping(){return this.encoding}get mark(){return this.markDef.type}channelHasField(t){return hn(this.encoding,t)}fieldDef(t){const n=this.encoding[t];return gt(n)}typedFieldDef(t){const n=this.fieldDef(t);return Ae(n)?n:null}}class Ba extends La{constructor(t,n,i,r,s){super(t,"layer",n,i,s,t.resolve,t.view);const o={...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}};this.children=t.layer.map((a,c)=>{if(Zr(a))return new Ba(a,this,this.getName(`layer_${c}`),o,s);if(zt(a))return new np(a,this,this.getName(`layer_${c}`),o,s);throw new Error(Io(a))});}parseData(){this.component.data=hs(this);for(const t of this.children)t.parseData();}parseLayoutSize(){wS(this);}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of x(t.component.selection))this.component.selection[n]=t.component.selection[n];}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup();}parseAxesAndHeaders(){zS(this);}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleSignals()),yd(this))}assembleLayoutSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleLayoutSignals()),Fa(this))}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleGroupStyle(){const t=new Set;for(const i of this.children)for(const r of H$1(i.assembleGroupStyle()))t.add(r);const n=Array.from(t);return n.length>1?n:n.length===1?n[0]:void 0}assembleTitle(){let t=super.assembleTitle();if(t)return t;for(const n of this.children)if(t=n.assembleTitle(),t)return t}assembleLayout(){return null}assembleMarks(){return Bb(this,this.children.flatMap(t=>t.assembleMarks()))}assembleLegends(){return this.children.reduce((t,n)=>t.concat(n.assembleLegends()),Rd(this))}}function Ua(e,t,n,i,r){if(qr(e))return new Oi(e,t,n,r);if(Zr(e))return new Ba(e,t,n,i,r);if(zt(e))return new np(e,t,n,i,r);if(ky(e))return new OS(e,t,n,r);throw new Error(Io(e))}function vE(e,t={}){t.logger&&Ph(t.logger),t.fieldTitle&&cf(t.fieldTitle);try{const n=Pf(v3(t.config,e.config)),i=Wf(e,n),r=Ua(i,null,"",void 0,n);return r.parse(),Mv(r.component.data,r),{spec:EE(r,SE(e,i.autosize,n,r),e.datasets,e.usermeta),normalized:i}}finally{t.logger&&Lh(),t.fieldTitle&&Qm();}}function SE(e,t,n,i){const r=i.component.layoutSize.get("width"),s=i.component.layoutSize.get("height");if(t===void 0?(t={type:"pad"},i.hasAxisOrientSignalRef()&&(t.resize=!0)):xt$1(t)&&(t={type:t}),r&&s&&Ib(t.type)){if(r==="step"&&s==="step")v(sc()),t.type="pad";else if(r==="step"||s==="step"){const o=r==="step"?"width":"height";v(sc(Pr(o)));const a=o==="width"?"height":"width";t.type=Rb(a);}}return {...x(t).length===1&&t.type?t.type==="pad"?{}:{autosize:t.type}:{autosize:t},...Ic(n,!1),...Ic(e,!0)}}function EE(e,t,n={},i){const r=e.config?Gy(e.config):void 0,s=[].concat(e.assembleSelectionData([]),vS(e.component.data,n)),o=e.assembleProjections(),a=e.assembleTitle(),c=e.assembleGroupStyle(),l=e.assembleGroupEncodeEntry(!0);let u=e.assembleLayoutSignals();u=u.filter(g=>(g.name==="width"||g.name==="height")&&g.value!==void 0?(t[g.name]=+g.value,!1):!0);const{params:f,...d}=t;return {$schema:"https://vega.github.io/schema/vega/v5.json",...e.description?{description:e.description}:{},...d,...a?{title:a}:{},...c?{style:c}:{},...l?{encode:{update:l}}:{},data:s,...o.length>0?{projections:o}:{},...e.assembleGroup([...u,...e.assembleSelectionTopLevelSignals([]),...Of(f)]),...r?{config:r}:{},...i?{usermeta:i}:{}}}const $E=Vp.version,wE=Object.freeze(Object.defineProperty({__proto__:null,accessPathDepth:Kn,accessPathWithDatum:$o,compile:vE,contains:W,deepEqual:ct,deleteNestedProperty:ur,duplicate:L,entries:Wt,every:vo,fieldIntersection:Eo,flatAccessWithDatum:Ul,getFirstDefined:ue,hasIntersection:So,hash:U,internalField:ql,isBoolean:Ii,isEmpty:Y,isEqual:eg,isInternalField:Hl,isNullOrFalse:Us,isNumeric:_r,keys:x,logicalExpr:_i,mergeDeep:jl,never:Ml,normalize:Wf,normalizeAngle:Ri,omit:Fe,pick:Yn,prefixGenerator:Ws,removePathFromField:wo,replaceAll:xn,replacePathInField:Me,resetIdCounter:ng,setEqual:Bl,some:bn,stringify:Z,titleCase:Mi,unique:lt,uniqueId:Gl,vals:xe,varName:ie,version:$E},Symbol.toStringTag,{value:"Module"}));var AE="vega-themes",CE="2.14.0",NE="Themes for stylized Vega and Vega-Lite visualizations.",FE=["vega","vega-lite","themes","style"],_E="BSD-3-Clause",kE={name:"UW Interactive Data Lab",url:"https://idl.cs.washington.edu"},OE=[{name:"Emily Gu",url:"https://github.com/emilygu"},{name:"Arvind Satyanarayan",url:"http://arvindsatya.com"},{name:"Jeffrey Heer",url:"https://idl.cs.washington.edu"},{name:"Dominik Moritz",url:"https://www.domoritz.de"}],TE="build/vega-themes.js",IE="build/vega-themes.module.js",RE="build/vega-themes.min.js",PE="build/vega-themes.min.js",LE="build/vega-themes.module.d.ts",zE={type:"git",url:"https://github.com/vega/vega-themes.git"},DE=["src","build"],ME={prebuild:"yarn clean",build:"rollup -c",clean:"rimraf build && rimraf examples/build","copy:data":"rsync -r node_modules/vega-datasets/data/* examples/data","copy:build":"rsync -r build/* examples/build","deploy:gh":"yarn build && mkdir -p examples/build && rsync -r build/* examples/build && gh-pages -d examples",preversion:"yarn lint",serve:"browser-sync start -s -f build examples --serveStatic examples",start:"yarn build && concurrently --kill-others -n Server,Rollup 'yarn serve' 'rollup -c -w'",format:"eslint . --fix",lint:"eslint .",release:"release-it"},jE={"@babel/core":"^7.22.9","@babel/plugin-proposal-async-generator-functions":"^7.20.7","@babel/plugin-proposal-json-strings":"^7.18.6","@babel/plugin-proposal-object-rest-spread":"^7.20.7","@babel/plugin-proposal-optional-catch-binding":"^7.18.6","@babel/plugin-transform-runtime":"^7.22.9","@babel/preset-env":"^7.22.9","@babel/preset-typescript":"^7.22.5","@release-it/conventional-changelog":"^7.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.1.0","@rollup/plugin-terser":"^0.4.3","@typescript-eslint/eslint-plugin":"^6.0.0","@typescript-eslint/parser":"^6.0.0","browser-sync":"^2.29.3",concurrently:"^8.2.0",eslint:"^8.45.0","eslint-config-prettier":"^8.8.0","eslint-plugin-prettier":"^5.0.0","gh-pages":"^5.0.0",prettier:"^3.0.0","release-it":"^16.1.0",rollup:"^3.26.2","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-ts":"^3.2.0",typescript:"^5.1.6",vega:"^5.25.0","vega-lite":"^5.9.3"},BE={vega:"*","vega-lite":"*"},UE={},WE={name:AE,version:CE,description:NE,keywords:FE,license:_E,author:kE,contributors:OE,main:TE,module:IE,unpkg:RE,jsdelivr:PE,types:LE,repository:zE,files:DE,scripts:ME,devDependencies:jE,peerDependencies:BE,dependencies:UE};const Mn="#fff",cl="#888",GE={background:"#333",view:{stroke:cl},title:{color:Mn,subtitleColor:Mn},style:{"guide-label":{fill:Mn},"guide-title":{fill:Mn}},axis:{domainColor:Mn,gridColor:cl,tickColor:Mn}},sn="#4572a7",qE={background:"#fff",arc:{fill:sn},area:{fill:sn},line:{stroke:sn,strokeWidth:2},path:{stroke:sn},rect:{fill:sn},shape:{stroke:sn},symbol:{fill:sn,strokeWidth:1.5,size:50},axis:{bandPosition:.5,grid:!0,gridColor:"#000000",gridOpacity:1,gridWidth:.5,labelPadding:10,tickSize:5,tickWidth:.5},axisBand:{grid:!1,tickExtra:!0},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:50,symbolType:"square"},range:{category:["#4572a7","#aa4643","#8aa453","#71598e","#4598ae","#d98445","#94aace","#d09393","#b9cc98","#a99cbc"]}},on="#30a2da",Is="#cbcbcb",HE="#999",VE="#333",ll="#f0f0f0",ul="#333",XE={arc:{fill:on},area:{fill:on},axis:{domainColor:Is,grid:!0,gridColor:Is,gridWidth:1,labelColor:HE,labelFontSize:10,titleColor:VE,tickColor:Is,tickSize:10,titleFontSize:14,titlePadding:10,labelPadding:4},axisBand:{grid:!1},background:ll,group:{fill:ll},legend:{labelColor:ul,labelFontSize:11,padding:1,symbolSize:30,symbolType:"square",titleColor:ul,titleFontSize:14,titlePadding:10},line:{stroke:on,strokeWidth:2},path:{stroke:on,strokeWidth:.5},rect:{fill:on},range:{category:["#30a2da","#fc4f30","#e5ae38","#6d904f","#8b8b8b","#b96db8","#ff9e27","#56cc60","#52d2ca","#52689e","#545454","#9fe4f8"],diverging:["#cc0020","#e77866","#f6e7e1","#d6e8ed","#91bfd9","#1d78b5"],heatmap:["#d6e8ed","#cee0e5","#91bfd9","#549cc6","#1d78b5"]},point:{filled:!0,shape:"circle"},shape:{stroke:on},bar:{binSpacing:2,fill:on,stroke:null},title:{anchor:"start",fontSize:24,fontWeight:600,offset:20}},an="#000",YE={group:{fill:"#e5e5e5"},arc:{fill:an},area:{fill:an},line:{stroke:an},path:{stroke:an},rect:{fill:an},shape:{stroke:an},symbol:{fill:an,size:40},axis:{domain:!1,grid:!0,gridColor:"#FFFFFF",gridOpacity:1,labelColor:"#7F7F7F",labelPadding:4,tickColor:"#7F7F7F",tickSize:5.67,titleFontSize:16,titleFontWeight:"normal"},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:40},range:{category:["#000000","#7F7F7F","#1A1A1A","#999999","#333333","#B0B0B0","#4D4D4D","#C9C9C9","#666666","#DCDCDC"]}},KE=22,QE="normal",fl="Benton Gothic, sans-serif",dl=11.5,JE="normal",cn="#82c6df",Rs="Benton Gothic Bold, sans-serif",pl="normal",gl=13,Ei={"category-6":["#ec8431","#829eb1","#c89d29","#3580b1","#adc839","#ab7fb4"],"fire-7":["#fbf2c7","#f9e39c","#f8d36e","#f4bb6a","#e68a4f","#d15a40","#ab4232"],"fireandice-6":["#e68a4f","#f4bb6a","#f9e39c","#dadfe2","#a6b7c6","#849eae"],"ice-7":["#edefee","#dadfe2","#c4ccd2","#a6b7c6","#849eae","#607785","#47525d"]},ZE={background:"#ffffff",title:{anchor:"start",color:"#000000",font:Rs,fontSize:KE,fontWeight:QE},arc:{fill:cn},area:{fill:cn},line:{stroke:cn,strokeWidth:2},path:{stroke:cn},rect:{fill:cn},shape:{stroke:cn},symbol:{fill:cn,size:30},axis:{labelFont:fl,labelFontSize:dl,labelFontWeight:JE,titleFont:Rs,titleFontSize:gl,titleFontWeight:pl},axisX:{labelAngle:0,labelPadding:4,tickSize:3},axisY:{labelBaseline:"middle",maxExtent:45,minExtent:45,tickSize:2,titleAlign:"left",titleAngle:0,titleX:-45,titleY:-11},legend:{labelFont:fl,labelFontSize:dl,symbolType:"square",titleFont:Rs,titleFontSize:gl,titleFontWeight:pl},range:{category:Ei["category-6"],diverging:Ei["fireandice-6"],heatmap:Ei["fire-7"],ordinal:Ei["fire-7"],ramp:Ei["fire-7"]}},ln="#ab5787",rr="#979797",e$={background:"#f9f9f9",arc:{fill:ln},area:{fill:ln},line:{stroke:ln},path:{stroke:ln},rect:{fill:ln},shape:{stroke:ln},symbol:{fill:ln,size:30},axis:{domainColor:rr,domainWidth:.5,gridWidth:.2,labelColor:rr,tickColor:rr,tickWidth:.2,titleColor:rr},axisBand:{grid:!1},axisX:{grid:!0,tickSize:10},axisY:{domain:!1,grid:!0,tickSize:0},legend:{labelFontSize:11,padding:1,symbolSize:30,symbolType:"square"},range:{category:["#ab5787","#51b2e5","#703c5c","#168dd9","#d190b6","#00609f","#d365ba","#154866","#666666","#c4c4c4"]}},un="#3e5c69",t$={background:"#fff",arc:{fill:un},area:{fill:un},line:{stroke:un},path:{stroke:un},rect:{fill:un},shape:{stroke:un},symbol:{fill:un},axis:{domainWidth:.5,grid:!0,labelPadding:2,tickSize:5,tickWidth:.5,titleFontWeight:"normal"},axisBand:{grid:!1},axisX:{gridWidth:.2},axisY:{gridDash:[3],gridWidth:.4},legend:{labelFontSize:11,padding:1,symbolType:"square"},range:{category:["#3e5c69","#6793a6","#182429","#0570b0","#3690c0","#74a9cf","#a6bddb","#e2ddf2"]}},ze="#1696d2",hl="#000000",n$="#FFFFFF",sr="Lato",Ps="Lato",i$="Lato",r$="#DEDDDD",s$=18,$i={"main-colors":["#1696d2","#d2d2d2","#000000","#fdbf11","#ec008b","#55b748","#5c5859","#db2b27"],"shades-blue":["#CFE8F3","#A2D4EC","#73BFE2","#46ABDB","#1696D2","#12719E","#0A4C6A","#062635"],"shades-gray":["#F5F5F5","#ECECEC","#E3E3E3","#DCDBDB","#D2D2D2","#9D9D9D","#696969","#353535"],"shades-yellow":["#FFF2CF","#FCE39E","#FDD870","#FCCB41","#FDBF11","#E88E2D","#CA5800","#843215"],"shades-magenta":["#F5CBDF","#EB99C2","#E46AA7","#E54096","#EC008B","#AF1F6B","#761548","#351123"],"shades-green":["#DCEDD9","#BCDEB4","#98CF90","#78C26D","#55B748","#408941","#2C5C2D","#1A2E19"],"shades-black":["#D5D5D4","#ADABAC","#848081","#5C5859","#332D2F","#262223","#1A1717","#0E0C0D"],"shades-red":["#F8D5D4","#F1AAA9","#E9807D","#E25552","#DB2B27","#A4201D","#6E1614","#370B0A"],"one-group":["#1696d2","#000000"],"two-groups-cat-1":["#1696d2","#000000"],"two-groups-cat-2":["#1696d2","#fdbf11"],"two-groups-cat-3":["#1696d2","#db2b27"],"two-groups-seq":["#a2d4ec","#1696d2"],"three-groups-cat":["#1696d2","#fdbf11","#000000"],"three-groups-seq":["#a2d4ec","#1696d2","#0a4c6a"],"four-groups-cat-1":["#000000","#d2d2d2","#fdbf11","#1696d2"],"four-groups-cat-2":["#1696d2","#ec0008b","#fdbf11","#5c5859"],"four-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a"],"five-groups-cat-1":["#1696d2","#fdbf11","#d2d2d2","#ec008b","#000000"],"five-groups-cat-2":["#1696d2","#0a4c6a","#d2d2d2","#fdbf11","#332d2f"],"five-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a","#000000"],"six-groups-cat-1":["#1696d2","#ec008b","#fdbf11","#000000","#d2d2d2","#55b748"],"six-groups-cat-2":["#1696d2","#d2d2d2","#ec008b","#fdbf11","#332d2f","#0a4c6a"],"six-groups-seq":["#cfe8f3","#a2d4ec","#73bfe2","#46abdb","#1696d2","#12719e"],"diverging-colors":["#ca5800","#fdbf11","#fdd870","#fff2cf","#cfe8f3","#73bfe2","#1696d2","#0a4c6a"]},o$={background:n$,title:{anchor:"start",fontSize:s$,font:sr},axisX:{domain:!0,domainColor:hl,domainWidth:1,grid:!1,labelFontSize:12,labelFont:Ps,labelAngle:0,tickColor:hl,tickSize:5,titleFontSize:12,titlePadding:10,titleFont:sr},axisY:{domain:!1,domainWidth:1,grid:!0,gridColor:r$,gridWidth:1,labelFontSize:12,labelFont:Ps,labelPadding:8,ticks:!1,titleFontSize:12,titlePadding:10,titleFont:sr,titleAngle:0,titleY:-10,titleX:18},legend:{labelFontSize:12,labelFont:Ps,symbolSize:100,titleFontSize:12,titlePadding:10,titleFont:sr,orient:"right",offset:10},view:{stroke:"transparent"},range:{category:$i["six-groups-cat-1"],diverging:$i["diverging-colors"],heatmap:$i["diverging-colors"],ordinal:$i["six-groups-seq"],ramp:$i["shades-blue"]},area:{fill:ze},rect:{fill:ze},line:{color:ze,stroke:ze,strokeWidth:5},trail:{color:ze,stroke:ze,strokeWidth:0,size:1},path:{stroke:ze,strokeWidth:.5},point:{filled:!0},text:{font:i$,color:ze,fontSize:11,align:"center",fontWeight:400,size:11},style:{bar:{fill:ze,stroke:null}},arc:{fill:ze},shape:{stroke:ze},symbol:{fill:ze,size:30}},fn="#3366CC",ml="#ccc",or="Arial, sans-serif",a$={arc:{fill:fn},area:{fill:fn},path:{stroke:fn},rect:{fill:fn},shape:{stroke:fn},symbol:{stroke:fn},circle:{fill:fn},background:"#fff",padding:{top:10,right:10,bottom:10,left:10},style:{"guide-label":{font:or,fontSize:12},"guide-title":{font:or,fontSize:12},"group-title":{font:or,fontSize:12}},title:{font:or,fontSize:14,fontWeight:"bold",dy:-3,anchor:"start"},axis:{gridColor:ml,tickColor:ml,domain:!1,grid:!0},range:{category:["#4285F4","#DB4437","#F4B400","#0F9D58","#AB47BC","#00ACC1","#FF7043","#9E9D24","#5C6BC0","#F06292","#00796B","#C2185B"],heatmap:["#c6dafc","#5e97f6","#2a56c6"]}},Wa=e=>e*(1/3+1),yl=Wa(9),bl=Wa(10),xl=Wa(12),wi="Segoe UI",vl="wf_standard-font, helvetica, arial, sans-serif",Sl="#252423",Ai="#605E5C",El="transparent",c$="#C8C6C4",Ge="#118DFF",l$="#12239E",u$="#E66C37",f$="#6B007B",d$="#E044A7",p$="#744EC2",g$="#D9B300",h$="#D64550",ip=Ge,rp="#DEEFFF",$l=[rp,ip],m$=[rp,"#c7e4ff","#b0d9ff","#9aceff","#83c3ff","#6cb9ff","#55aeff","#3fa3ff","#2898ff",ip],y$={view:{stroke:El},background:El,font:wi,header:{titleFont:vl,titleFontSize:xl,titleColor:Sl,labelFont:wi,labelFontSize:bl,labelColor:Ai},axis:{ticks:!1,grid:!1,domain:!1,labelColor:Ai,labelFontSize:yl,titleFont:vl,titleColor:Sl,titleFontSize:xl,titleFontWeight:"normal"},axisQuantitative:{tickCount:3,grid:!0,gridColor:c$,gridDash:[1,5],labelFlush:!1},axisBand:{tickExtra:!0},axisX:{labelPadding:5},axisY:{labelPadding:10},bar:{fill:Ge},line:{stroke:Ge,strokeWidth:3,strokeCap:"round",strokeJoin:"round"},text:{font:wi,fontSize:yl,fill:Ai},arc:{fill:Ge},area:{fill:Ge,line:!0,opacity:.6},path:{stroke:Ge},rect:{fill:Ge},point:{fill:Ge,filled:!0,size:75},shape:{stroke:Ge},symbol:{fill:Ge,strokeWidth:1.5,size:50},legend:{titleFont:wi,titleFontWeight:"bold",titleColor:Ai,labelFont:wi,labelFontSize:bl,labelColor:Ai,symbolType:"circle",symbolSize:75},range:{category:[Ge,l$,u$,f$,d$,p$,g$,h$],diverging:$l,heatmap:$l,ordinal:m$}},Ls='IBM Plex Sans,system-ui,-apple-system,BlinkMacSystemFont,".sfnstext-regular",sans-serif',wl=400,b$=["#8a3ffc","#33b1ff","#007d79","#ff7eb6","#fa4d56","#fff1f1","#6fdc8c","#4589ff","#d12771","#d2a106","#08bdba","#bae6ff","#ba4e00","#d4bbff"],x$=["#6929c4","#1192e8","#005d5d","#9f1853","#fa4d56","#570408","#198038","#002d9c","#ee538b","#b28600","#009d9a","#012749","#8a3800","#a56eff"];function ms({type:e,background:t}){const n=e==="dark"?"#161616":"#ffffff",i=e==="dark"?"#f4f4f4":"#161616",r=e==="dark"?b$:x$,s=e==="dark"?"#d4bbff":"#6929c4";return {background:t,arc:{fill:s},area:{fill:s},path:{stroke:s},rect:{fill:s},shape:{stroke:s},symbol:{stroke:s},circle:{fill:s},view:{fill:n,stroke:n},group:{fill:n},title:{color:i,anchor:"start",dy:-15,fontSize:16,font:Ls,fontWeight:600},axis:{labelColor:i,labelFontSize:12,grid:!0,gridColor:"#525252",titleColor:i,labelAngle:0},style:{"guide-label":{font:Ls,fill:i,fontWeight:wl},"guide-title":{font:Ls,fill:i,fontWeight:wl}},range:{category:r,diverging:["#750e13","#a2191f","#da1e28","#fa4d56","#ff8389","#ffb3b8","#ffd7d9","#fff1f1","#e5f6ff","#bae6ff","#82cfff","#33b1ff","#1192e8","#0072c3","#00539a","#003a6d"],heatmap:["#f6f2ff","#e8daff","#d4bbff","#be95ff","#a56eff","#8a3ffc","#6929c4","#491d8b","#31135e","#1c0f30"]}}}const v$=ms({type:"light",background:"#ffffff"}),S$=ms({type:"light",background:"#f4f4f4"}),E$=ms({type:"dark",background:"#262626"}),$$=ms({type:"dark",background:"#161616"}),w$=WE.version;Object.freeze(Object.defineProperty({__proto__:null,carbong10:S$,carbong100:$$,carbong90:E$,carbonwhite:v$,dark:GE,excel:qE,fivethirtyeight:XE,ggplot2:YE,googlecharts:a$,latimes:ZE,powerbi:y$,quartz:e$,urbaninstitute:o$,version:w$,vox:t$},Symbol.toStringTag,{value:"Module"}));var zs,Al;function C$(){return Al||(Al=1,zs=function(e){e.prototype[Symbol.iterator]=function*(){for(let t=this.head;t;t=t.next)yield t.value;};}),zs}var N$=q;q.Node=Fn;q.create=q;function q(e){var t=this;if(t instanceof q||(t=new q),t.tail=null,t.head=null,t.length=0,e&&typeof e.forEach=="function")e.forEach(function(r){t.push(r);});else if(arguments.length>0)for(var n=0,i=arguments.length;n<i;n++)t.push(arguments[n]);return t}q.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,n=e.prev;return t&&(t.prev=n),n&&(n.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=n),e.list.length--,e.next=null,e.prev=null,e.list=null,t};q.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++;}};q.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++;}};q.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)_$(this,arguments[e]);return this.length};q.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)k$(this,arguments[e]);return this.length};q.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}};q.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}};q.prototype.forEach=function(e,t){t=t||this;for(var n=this.head,i=0;n!==null;i++)e.call(t,n.value,i,this),n=n.next;};q.prototype.forEachReverse=function(e,t){t=t||this;for(var n=this.tail,i=this.length-1;n!==null;i--)e.call(t,n.value,i,this),n=n.prev;};q.prototype.get=function(e){for(var t=0,n=this.head;n!==null&&t<e;t++)n=n.next;if(t===e&&n!==null)return n.value};q.prototype.getReverse=function(e){for(var t=0,n=this.tail;n!==null&&t<e;t++)n=n.prev;if(t===e&&n!==null)return n.value};q.prototype.map=function(e,t){t=t||this;for(var n=new q,i=this.head;i!==null;)n.push(e.call(t,i.value,this)),i=i.next;return n};q.prototype.mapReverse=function(e,t){t=t||this;for(var n=new q,i=this.tail;i!==null;)n.push(e.call(t,i.value,this)),i=i.prev;return n};q.prototype.reduce=function(e,t){var n,i=this.head;if(arguments.length>1)n=t;else if(this.head)i=this.head.next,n=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=0;i!==null;r++)n=e(n,i.value,r),i=i.next;return n};q.prototype.reduceReverse=function(e,t){var n,i=this.tail;if(arguments.length>1)n=t;else if(this.tail)i=this.tail.prev,n=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=this.length-1;i!==null;r--)n=e(n,i.value,r),i=i.prev;return n};q.prototype.toArray=function(){for(var e=new Array(this.length),t=0,n=this.head;n!==null;t++)e[t]=n.value,n=n.next;return e};q.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,n=this.tail;n!==null;t++)e[t]=n.value,n=n.prev;return e};q.prototype.slice=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new q;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(;r!==null&&i<t;i++,r=r.next)n.push(r.value);return n};q.prototype.sliceReverse=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new q;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=this.length,r=this.tail;r!==null&&i>t;i--)r=r.prev;for(;r!==null&&i>e;i--,r=r.prev)n.push(r.value);return n};q.prototype.splice=function(e,t,...n){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(var s=[],i=0;r&&i<t;i++)s.push(r.value),r=this.removeNode(r);r===null&&(r=this.tail),r!==this.head&&r!==this.tail&&(r=r.prev);for(var i=0;i<n.length;i++)r=F$(this,r,n[i]);return s};q.prototype.reverse=function(){for(var e=this.head,t=this.tail,n=e;n!==null;n=n.prev){var i=n.prev;n.prev=n.next,n.next=i;}return this.head=t,this.tail=e,this};function F$(e,t,n){var i=t===e.head?new Fn(n,null,t,e):new Fn(n,t,t.next,e);return i.next===null&&(e.tail=i),i.prev===null&&(e.head=i),e.length++,i}function _$(e,t){e.tail=new Fn(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++;}function k$(e,t){e.head=new Fn(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++;}function Fn(e,t,n,i){if(!(this instanceof Fn))return new Fn(e,t,n,i);this.list=i,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,n?(n.prev=this,this.next=n):this.next=null;}try{C$()(q);}catch{}const O$=N$,dn=Symbol("max"),wt=Symbol("length"),jn=Symbol("lengthCalculator"),Ti=Symbol("allowStale"),pn=Symbol("maxAge"),$t=Symbol("dispose"),Cl=Symbol("noDisposeOnSet"),de=Symbol("lruList"),qe=Symbol("cache"),sp=Symbol("updateAgeOnGet"),Ds=()=>1;class T${constructor(t){if(typeof t=="number"&&(t={max:t}),t||(t={}),t.max&&(typeof t.max!="number"||t.max<0))throw new TypeError("max must be a non-negative number");this[dn]=t.max||1/0;const n=t.length||Ds;if(this[jn]=typeof n!="function"?Ds:n,this[Ti]=t.stale||!1,t.maxAge&&typeof t.maxAge!="number")throw new TypeError("maxAge must be a number");this[pn]=t.maxAge||0,this[$t]=t.dispose,this[Cl]=t.noDisposeOnSet||!1,this[sp]=t.updateAgeOnGet||!1,this.reset();}set max(t){if(typeof t!="number"||t<0)throw new TypeError("max must be a non-negative number");this[dn]=t||1/0,Ci(this);}get max(){return this[dn]}set allowStale(t){this[Ti]=!!t;}get allowStale(){return this[Ti]}set maxAge(t){if(typeof t!="number")throw new TypeError("maxAge must be a non-negative number");this[pn]=t,Ci(this);}get maxAge(){return this[pn]}set lengthCalculator(t){typeof t!="function"&&(t=Ds),t!==this[jn]&&(this[jn]=t,this[wt]=0,this[de].forEach(n=>{n.length=this[jn](n.value,n.key),this[wt]+=n.length;})),Ci(this);}get lengthCalculator(){return this[jn]}get length(){return this[wt]}get itemCount(){return this[de].length}rforEach(t,n){n=n||this;for(let i=this[de].tail;i!==null;){const r=i.prev;Nl(this,t,i,n),i=r;}}forEach(t,n){n=n||this;for(let i=this[de].head;i!==null;){const r=i.next;Nl(this,t,i,n),i=r;}}keys(){return this[de].toArray().map(t=>t.key)}values(){return this[de].toArray().map(t=>t.value)}reset(){this[$t]&&this[de]&&this[de].length&&this[de].forEach(t=>this[$t](t.key,t.value)),this[qe]=new Map,this[de]=new O$,this[wt]=0;}dump(){return this[de].map(t=>Cr(this,t)?!1:{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}).toArray().filter(t=>t)}dumpLru(){return this[de]}set(t,n,i){if(i=i||this[pn],i&&typeof i!="number")throw new TypeError("maxAge must be a number");const r=i?Date.now():0,s=this[jn](n,t);if(this[qe].has(t)){if(s>this[dn])return Xn(this,this[qe].get(t)),!1;const c=this[qe].get(t).value;return this[$t]&&(this[Cl]||this[$t](t,c.value)),c.now=r,c.maxAge=i,c.value=n,this[wt]+=s-c.length,c.length=s,this.get(t),Ci(this),!0}const o=new I$(t,n,s,r,i);return o.length>this[dn]?(this[$t]&&this[$t](t,n),!1):(this[wt]+=o.length,this[de].unshift(o),this[qe].set(t,this[de].head),Ci(this),!0)}has(t){if(!this[qe].has(t))return !1;const n=this[qe].get(t).value;return !Cr(this,n)}get(t){return Ms(this,t,!0)}peek(t){return Ms(this,t,!1)}pop(){const t=this[de].tail;return t?(Xn(this,t),t.value):null}del(t){Xn(this,this[qe].get(t));}load(t){this.reset();const n=Date.now();for(let i=t.length-1;i>=0;i--){const r=t[i],s=r.e||0;if(s===0)this.set(r.k,r.v);else {const o=s-n;o>0&&this.set(r.k,r.v,o);}}}prune(){this[qe].forEach((t,n)=>Ms(this,n,!1));}}const Ms=(e,t,n)=>{const i=e[qe].get(t);if(i){const r=i.value;if(Cr(e,r)){if(Xn(e,i),!e[Ti])return}else n&&(e[sp]&&(i.value.now=Date.now()),e[de].unshiftNode(i));return r.value}},Cr=(e,t)=>{if(!t||!t.maxAge&&!e[pn])return !1;const n=Date.now()-t.now;return t.maxAge?n>t.maxAge:e[pn]&&n>e[pn]},Ci=e=>{if(e[wt]>e[dn])for(let t=e[de].tail;e[wt]>e[dn]&&t!==null;){const n=t.prev;Xn(e,t),t=n;}},Xn=(e,t)=>{if(t){const n=t.value;e[$t]&&e[$t](n.key,n.value),e[wt]-=n.length,e[qe].delete(n.key),e[de].removeNode(t);}};class I${constructor(t,n,i,r,s){this.key=t,this.value=n,this.length=i,this.now=r,this.maxAge=s||0;}}const Nl=(e,t,n,i)=>{let r=n.value;Cr(e,r)&&(Xn(e,n),e[Ti]||(r=void 0)),r&&t.call(i,r.value,r.key,e);};var R$=T$;const P$=Object.freeze({loose:!0}),L$=Object.freeze({}),z$=e=>e?typeof e!="object"?P$:e:L$;var Ga=z$,po={exports:{}};const D$="2.0.0",op=256,M$=Number.MAX_SAFE_INTEGER||9007199254740991,j$=16,B$=op-6,U$=["major","premajor","minor","preminor","patch","prepatch","prerelease"];var qa={MAX_LENGTH:op,MAX_SAFE_COMPONENT_LENGTH:j$,MAX_SAFE_BUILD_LENGTH:B$,MAX_SAFE_INTEGER:M$,RELEASE_TYPES:U$,SEMVER_SPEC_VERSION:D$,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2};const W$=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var ys=W$;(function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:r}=qa,s=ys;t=e.exports={};const o=t.re=[],a=t.safeRe=[],c=t.src=[],l=t.t={};let u=0;const f="[a-zA-Z0-9-]",d=[["\\s",1],["\\d",r],[f,i]],g=h=>{for(const[m,y]of d)h=h.split(`${m}*`).join(`${m}{0,${y}}`).split(`${m}+`).join(`${m}{1,${y}}`);return h},p=(h,m,y)=>{const b=g(m),O=u++;s(h,O,m),l[h]=O,c[O]=m,o[O]=new RegExp(m,y?"g":void 0),a[O]=new RegExp(b,y?"g":void 0);};p("NUMERICIDENTIFIER","0|[1-9]\\d*"),p("NUMERICIDENTIFIERLOOSE","\\d+"),p("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${f}*`),p("MAINVERSION",`(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})`),p("MAINVERSIONLOOSE",`(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})`),p("PRERELEASEIDENTIFIER",`(?:${c[l.NUMERICIDENTIFIER]}|${c[l.NONNUMERICIDENTIFIER]})`),p("PRERELEASEIDENTIFIERLOOSE",`(?:${c[l.NUMERICIDENTIFIERLOOSE]}|${c[l.NONNUMERICIDENTIFIER]})`),p("PRERELEASE",`(?:-(${c[l.PRERELEASEIDENTIFIER]}(?:\\.${c[l.PRERELEASEIDENTIFIER]})*))`),p("PRERELEASELOOSE",`(?:-?(${c[l.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[l.PRERELEASEIDENTIFIERLOOSE]})*))`),p("BUILDIDENTIFIER",`${f}+`),p("BUILD",`(?:\\+(${c[l.BUILDIDENTIFIER]}(?:\\.${c[l.BUILDIDENTIFIER]})*))`),p("FULLPLAIN",`v?${c[l.MAINVERSION]}${c[l.PRERELEASE]}?${c[l.BUILD]}?`),p("FULL",`^${c[l.FULLPLAIN]}$`),p("LOOSEPLAIN",`[v=\\s]*${c[l.MAINVERSIONLOOSE]}${c[l.PRERELEASELOOSE]}?${c[l.BUILD]}?`),p("LOOSE",`^${c[l.LOOSEPLAIN]}$`),p("GTLT","((?:<|>)?=?)"),p("XRANGEIDENTIFIERLOOSE",`${c[l.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),p("XRANGEIDENTIFIER",`${c[l.NUMERICIDENTIFIER]}|x|X|\\*`),p("XRANGEPLAIN",`[v=\\s]*(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:${c[l.PRERELEASE]})?${c[l.BUILD]}?)?)?`),p("XRANGEPLAINLOOSE",`[v=\\s]*(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:${c[l.PRERELEASELOOSE]})?${c[l.BUILD]}?)?)?`),p("XRANGE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAIN]}$`),p("XRANGELOOSE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAINLOOSE]}$`),p("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),p("COERCE",`${c[l.COERCEPLAIN]}(?:$|[^\\d])`),p("COERCEFULL",c[l.COERCEPLAIN]+`(?:${c[l.PRERELEASE]})?(?:${c[l.BUILD]})?(?:$|[^\\d])`),p("COERCERTL",c[l.COERCE],!0),p("COERCERTLFULL",c[l.COERCEFULL],!0),p("LONETILDE","(?:~>?)"),p("TILDETRIM",`(\\s*)${c[l.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",p("TILDE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAIN]}$`),p("TILDELOOSE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAINLOOSE]}$`),p("LONECARET","(?:\\^)"),p("CARETTRIM",`(\\s*)${c[l.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",p("CARET",`^${c[l.LONECARET]}${c[l.XRANGEPLAIN]}$`),p("CARETLOOSE",`^${c[l.LONECARET]}${c[l.XRANGEPLAINLOOSE]}$`),p("COMPARATORLOOSE",`^${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]})$|^$`),p("COMPARATOR",`^${c[l.GTLT]}\\s*(${c[l.FULLPLAIN]})$|^$`),p("COMPARATORTRIM",`(\\s*)${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]}|${c[l.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",p("HYPHENRANGE",`^\\s*(${c[l.XRANGEPLAIN]})\\s+-\\s+(${c[l.XRANGEPLAIN]})\\s*$`),p("HYPHENRANGELOOSE",`^\\s*(${c[l.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[l.XRANGEPLAINLOOSE]})\\s*$`),p("STAR","(<|>)?=?\\s*\\*"),p("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),p("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$");})(po,po.exports);var Ha=po.exports;const Fl=/^[0-9]+$/,ap=(e,t)=>{const n=Fl.test(e),i=Fl.test(t);return n&&i&&(e=+e,t=+t),e===t?0:n&&!i?-1:i&&!n?1:e<t?-1:1},G$=(e,t)=>ap(t,e);var q$={compareIdentifiers:ap,rcompareIdentifiers:G$};const ar=ys,{MAX_LENGTH:_l,MAX_SAFE_INTEGER:cr}=qa,{safeRe:kl,t:Ol}=Ha,H$=Ga,{compareIdentifiers:Bn}=q$;let V$=class ot{constructor(t,n){if(n=H$(n),t instanceof ot){if(t.loose===!!n.loose&&t.includePrerelease===!!n.includePrerelease)return t;t=t.version;}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>_l)throw new TypeError(`version is longer than ${_l} characters`);ar("SemVer",t,n),this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;const i=t.trim().match(n.loose?kl[Ol.LOOSE]:kl[Ol.FULL]);if(!i)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+i[1],this.minor=+i[2],this.patch=+i[3],this.major>cr||this.major<0)throw new TypeError("Invalid major version");if(this.minor>cr||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>cr||this.patch<0)throw new TypeError("Invalid patch version");i[4]?this.prerelease=i[4].split(".").map(r=>{if(/^[0-9]+$/.test(r)){const s=+r;if(s>=0&&s<cr)return s}return r}):this.prerelease=[],this.build=i[5]?i[5].split("."):[],this.format();}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(ar("SemVer.compare",this.version,this.options,t),!(t instanceof ot)){if(typeof t=="string"&&t===this.version)return 0;t=new ot(t,this.options);}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof ot||(t=new ot(t,this.options)),Bn(this.major,t.major)||Bn(this.minor,t.minor)||Bn(this.patch,t.patch)}comparePre(t){if(t instanceof ot||(t=new ot(t,this.options)),this.prerelease.length&&!t.prerelease.length)return -1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let n=0;do{const i=this.prerelease[n],r=t.prerelease[n];if(ar("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return -1;if(i===r)continue;return Bn(i,r)}while(++n)}compareBuild(t){t instanceof ot||(t=new ot(t,this.options));let n=0;do{const i=this.build[n],r=t.build[n];if(ar("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return -1;if(i===r)continue;return Bn(i,r)}while(++n)}inc(t,n,i){switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n,i);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n,i);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n,i),this.inc("pre",n,i);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",n,i),this.inc("pre",n,i);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const r=Number(i)?1:0;if(!n&&i===!1)throw new Error("invalid increment argument: identifier is empty");if(this.prerelease.length===0)this.prerelease=[r];else {let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(n===this.prerelease.join(".")&&i===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(r);}}if(n){let s=[n,r];i===!1&&(s=[n]),Bn(this.prerelease[0],n)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s;}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};var Va=V$;const Tl=Va,X$=(e,t,n)=>new Tl(e,n).compare(new Tl(t,n));var vi=X$;const Y$=vi,K$=(e,t,n)=>Y$(e,t,n)===0;var Q$=K$;const J$=vi,Z$=(e,t,n)=>J$(e,t,n)!==0;var e1=Z$;const t1=vi,n1=(e,t,n)=>t1(e,t,n)>0;var i1=n1;const r1=vi,s1=(e,t,n)=>r1(e,t,n)>=0;var o1=s1;const a1=vi,c1=(e,t,n)=>a1(e,t,n)<0;var l1=c1;const u1=vi,f1=(e,t,n)=>u1(e,t,n)<=0;var d1=f1;const p1=Q$,g1=e1,h1=i1,m1=o1,y1=l1,b1=d1,x1=(e,t,n,i)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e===n;case"!==":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e!==n;case"":case"=":case"==":return p1(e,n,i);case"!=":return g1(e,n,i);case">":return h1(e,n,i);case">=":return m1(e,n,i);case"<":return y1(e,n,i);case"<=":return b1(e,n,i);default:throw new TypeError(`Invalid operator: ${t}`)}};var v1=x1,js,Il;function S1(){if(Il)return js;Il=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(u,f){if(f=n(f),u instanceof t){if(u.loose===!!f.loose)return u;u=u.value;}u=u.trim().split(/\s+/).join(" "),o("comparator",u,f),this.options=f,this.loose=!!f.loose,this.parse(u),this.semver===e?this.value="":this.value=this.operator+this.semver.version,o("comp",this);}parse(u){const f=this.options.loose?i[r.COMPARATORLOOSE]:i[r.COMPARATOR],d=u.match(f);if(!d)throw new TypeError(`Invalid comparator: ${u}`);this.operator=d[1]!==void 0?d[1]:"",this.operator==="="&&(this.operator=""),d[2]?this.semver=new a(d[2],this.options.loose):this.semver=e;}toString(){return this.value}test(u){if(o("Comparator.test",u,this.options.loose),this.semver===e||u===e)return !0;if(typeof u=="string")try{u=new a(u,this.options);}catch{return !1}return s(u,this.operator,this.semver,this.options)}intersects(u,f){if(!(u instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new c(u.value,f).test(this.value):u.operator===""?u.value===""?!0:new c(this.value,f).test(u.semver):(f=n(f),f.includePrerelease&&(this.value==="<0.0.0-0"||u.value==="<0.0.0-0")||!f.includePrerelease&&(this.value.startsWith("<0.0.0")||u.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&u.operator.startsWith(">")||this.operator.startsWith("<")&&u.operator.startsWith("<")||this.semver.version===u.semver.version&&this.operator.includes("=")&&u.operator.includes("=")||s(this.semver,"<",u.semver,f)&&this.operator.startsWith(">")&&u.operator.startsWith("<")||s(this.semver,">",u.semver,f)&&this.operator.startsWith("<")&&u.operator.startsWith(">")))}}js=t;const n=Ga,{safeRe:i,t:r}=Ha,s=v1,o=ys,a=Va,c=cp();return js}var Bs,Rl;function cp(){if(Rl)return Bs;Rl=1;class e{constructor(E,I){if(I=i(I),E instanceof e)return E.loose===!!I.loose&&E.includePrerelease===!!I.includePrerelease?E:new e(E.raw,I);if(E instanceof r)return this.raw=E.value,this.set=[[E]],this.format(),this;if(this.options=I,this.loose=!!I.loose,this.includePrerelease=!!I.includePrerelease,this.raw=E.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(_=>this.parseRange(_.trim())).filter(_=>_.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const _=this.set[0];if(this.set=this.set.filter(T=>!p(T[0])),this.set.length===0)this.set=[_];else if(this.set.length>1){for(const T of this.set)if(T.length===1&&h(T[0])){this.set=[T];break}}}this.format();}format(){return this.range=this.set.map(E=>E.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange(E){const _=((this.options.includePrerelease&&d)|(this.options.loose&&g))+":"+E,T=n.get(_);if(T)return T;const N=this.options.loose,P=N?a[c.HYPHENRANGELOOSE]:a[c.HYPHENRANGE];E=E.replace(P,ce(this.options.includePrerelease)),s("hyphen replace",E),E=E.replace(a[c.COMPARATORTRIM],l),s("comparator trim",E),E=E.replace(a[c.TILDETRIM],u),s("tilde trim",E),E=E.replace(a[c.CARETTRIM],f),s("caret trim",E);let G=E.split(" ").map(te=>y(te,this.options)).join(" ").split(/\s+/).map(te=>se(te,this.options));N&&(G=G.filter(te=>(s("loose invalid filter",te,this.options),!!te.match(a[c.COMPARATORLOOSE])))),s("range list",G);const j=new Map,Q=G.map(te=>new r(te,this.options));for(const te of Q){if(p(te))return [te];j.set(te.value,te);}j.size>1&&j.has("")&&j.delete("");const Se=[...j.values()];return n.set(_,Se),Se}intersects(E,I){if(!(E instanceof e))throw new TypeError("a Range is required");return this.set.some(_=>m(_,I)&&E.set.some(T=>m(T,I)&&_.every(N=>T.every(P=>N.intersects(P,I)))))}test(E){if(!E)return !1;if(typeof E=="string")try{E=new o(E,this.options);}catch{return !1}for(let I=0;I<this.set.length;I++)if(rt(this.set[I],E,this.options))return !0;return !1}}Bs=e;const t=R$,n=new t({max:1e3}),i=Ga,r=S1(),s=ys,o=Va,{safeRe:a,t:c,comparatorTrimReplace:l,tildeTrimReplace:u,caretTrimReplace:f}=Ha,{FLAG_INCLUDE_PRERELEASE:d,FLAG_LOOSE:g}=qa,p=A=>A.value==="<0.0.0-0",h=A=>A.value==="",m=(A,E)=>{let I=!0;const _=A.slice();let T=_.pop();for(;I&&_.length;)I=_.every(N=>T.intersects(N,E)),T=_.pop();return I},y=(A,E)=>(s("comp",A,E),A=w(A,E),s("caret",A),A=O(A,E),s("tildes",A),A=D(A,E),s("xrange",A),A=ve(A,E),s("stars",A),A),b=A=>!A||A.toLowerCase()==="x"||A==="*",O=(A,E)=>A.trim().split(/\s+/).map(I=>C(I,E)).join(" "),C=(A,E)=>{const I=E.loose?a[c.TILDELOOSE]:a[c.TILDE];return A.replace(I,(_,T,N,P,G)=>{s("tilde",A,_,T,N,P,G);let j;return b(T)?j="":b(N)?j=`>=${T}.0.0 <${+T+1}.0.0-0`:b(P)?j=`>=${T}.${N}.0 <${T}.${+N+1}.0-0`:G?(s("replaceTilde pr",G),j=`>=${T}.${N}.${P}-${G} <${T}.${+N+1}.0-0`):j=`>=${T}.${N}.${P} <${T}.${+N+1}.0-0`,s("tilde return",j),j})},w=(A,E)=>A.trim().split(/\s+/).map(I=>M(I,E)).join(" "),M=(A,E)=>{s("caret",A,E);const I=E.loose?a[c.CARETLOOSE]:a[c.CARET],_=E.includePrerelease?"-0":"";return A.replace(I,(T,N,P,G,j)=>{s("caret",A,T,N,P,G,j);let Q;return b(N)?Q="":b(P)?Q=`>=${N}.0.0${_} <${+N+1}.0.0-0`:b(G)?N==="0"?Q=`>=${N}.${P}.0${_} <${N}.${+P+1}.0-0`:Q=`>=${N}.${P}.0${_} <${+N+1}.0.0-0`:j?(s("replaceCaret pr",j),N==="0"?P==="0"?Q=`>=${N}.${P}.${G}-${j} <${N}.${P}.${+G+1}-0`:Q=`>=${N}.${P}.${G}-${j} <${N}.${+P+1}.0-0`:Q=`>=${N}.${P}.${G}-${j} <${+N+1}.0.0-0`):(s("no pr"),N==="0"?P==="0"?Q=`>=${N}.${P}.${G}${_} <${N}.${P}.${+G+1}-0`:Q=`>=${N}.${P}.${G}${_} <${N}.${+P+1}.0-0`:Q=`>=${N}.${P}.${G} <${+N+1}.0.0-0`),s("caret return",Q),Q})},D=(A,E)=>(s("replaceXRanges",A,E),A.split(/\s+/).map(I=>K(I,E)).join(" ")),K=(A,E)=>{A=A.trim();const I=E.loose?a[c.XRANGELOOSE]:a[c.XRANGE];return A.replace(I,(_,T,N,P,G,j)=>{s("xRange",A,_,T,N,P,G,j);const Q=b(N),Se=Q||b(P),te=Se||b(G),rn=te;return T==="="&&rn&&(T=""),j=E.includePrerelease?"-0":"",Q?T===">"||T==="<"?_="<0.0.0-0":_="*":T&&rn?(Se&&(P=0),G=0,T===">"?(T=">=",Se?(N=+N+1,P=0,G=0):(P=+P+1,G=0)):T==="<="&&(T="<",Se?N=+N+1:P=+P+1),T==="<"&&(j="-0"),_=`${T+N}.${P}.${G}${j}`):Se?_=`>=${N}.0.0${j} <${+N+1}.0.0-0`:te&&(_=`>=${N}.${P}.0${j} <${N}.${+P+1}.0-0`),s("xRange return",_),_})},ve=(A,E)=>(s("replaceStars",A,E),A.trim().replace(a[c.STAR],"")),se=(A,E)=>(s("replaceGTE0",A,E),A.trim().replace(a[E.includePrerelease?c.GTE0PRE:c.GTE0],"")),ce=A=>(E,I,_,T,N,P,G,j,Q,Se,te,rn,bs)=>(b(_)?I="":b(T)?I=`>=${_}.0.0${A?"-0":""}`:b(N)?I=`>=${_}.${T}.0${A?"-0":""}`:P?I=`>=${I}`:I=`>=${I}${A?"-0":""}`,b(Q)?j="":b(Se)?j=`<${+Q+1}.0.0-0`:b(te)?j=`<${Q}.${+Se+1}.0-0`:rn?j=`<=${Q}.${Se}.${te}-${rn}`:A?j=`<${Q}.${Se}.${+te+1}-0`:j=`<=${j}`,`${I} ${j}`.trim()),rt=(A,E,I)=>{for(let _=0;_<A.length;_++)if(!A[_].test(E))return !1;if(E.prerelease.length&&!I.includePrerelease){for(let _=0;_<A.length;_++)if(s(A[_].semver),A[_].semver!==r.ANY&&A[_].semver.prerelease.length>0){const T=A[_].semver;if(T.major===E.major&&T.minor===E.minor&&T.patch===E.patch)return !0}return !1}return !0};return Bs}cp();const Xa=LH;let Nr=wE;const Pl=typeof window<"u"?window:void 0;Nr===void 0&&Pl?.vl?.compile&&(Nr=Pl.vl);Xa.version,Nr&&Nr.version;const _1={code:".altair.svelte-1qhqpn7 canvas{padding:6px}.altair.svelte-1qhqpn7 .vega-embed{padding:0px !important}.altair.svelte-1qhqpn7 .vega-actions{right:0px !important}.layout.svelte-1qhqpn7{display:flex;flex-direction:column;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full);color:var(--body-text-color)}.altair.svelte-1qhqpn7{display:flex;flex-direction:column;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full)}.caption.svelte-1qhqpn7{font-size:var(--text-sm);margin-bottom:6px}#vg-tooltip-element{font-family:var(--font) !important;font-size:var(--text-xs) !important;box-shadow:none !important;background-color:var(--block-background-fill) !important;border:1px solid var(--border-color-primary) !important;color:var(--body-text-color) !important}#vg-tooltip-element .key{color:var(--body-text-color-subdued) !important}",map:'{"version":3,"file":"AltairPlot.svelte","sources":["AltairPlot.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { set_config } from \\"./altair_utils\\";\\nimport { onMount, onDestroy } from \\"svelte\\";\\nimport vegaEmbed from \\"vega-embed\\";\\nexport let value;\\nexport let colors = [];\\nexport let caption;\\nexport let show_actions_button;\\nexport let gradio;\\nlet element;\\nlet parent_element;\\nlet view;\\nexport let _selectable;\\nlet computed_style = window.getComputedStyle(document.body);\\nlet old_spec;\\nlet spec_width;\\n$: plot = value?.plot;\\n$: spec = JSON.parse(plot);\\n$: if (spec && spec.params && !_selectable) {\\n    spec.params = spec.params.filter((param) => param.name !== \\"brush\\");\\n}\\n$: if (old_spec !== spec) {\\n    old_spec = spec;\\n    spec_width = spec.width;\\n}\\n$: if (value.chart) {\\n    spec = set_config(spec, computed_style, value.chart, colors);\\n}\\n$: fit_width_to_parent = spec.encoding?.column?.field || spec.encoding?.row?.field || value.chart === void 0 ? false : true;\\nconst get_width = () => {\\n    return Math.min(parent_element.offsetWidth, spec_width || parent_element.offsetWidth);\\n};\\nlet resize_callback = () => {\\n};\\nconst renderPlot = () => {\\n    if (fit_width_to_parent) {\\n        spec.width = get_width();\\n    }\\n    vegaEmbed(element, spec, { actions: show_actions_button }).then(function (result) {\\n        view = result.view;\\n        resize_callback = () => {\\n            view.signal(\\"width\\", get_width()).run();\\n        };\\n        if (!_selectable)\\n            return;\\n        const callback = (event, item) => {\\n            const brushValue = view.signal(\\"brush\\");\\n            if (brushValue) {\\n                if (Object.keys(brushValue).length === 0) {\\n                    gradio.dispatch(\\"select\\", {\\n                        value: null,\\n                        index: null,\\n                        selected: false\\n                    });\\n                }\\n                else {\\n                    const key = Object.keys(brushValue)[0];\\n                    let range = brushValue[key].map((x) => x / 1e3);\\n                    gradio.dispatch(\\"select\\", {\\n                        value: brushValue,\\n                        index: range,\\n                        selected: true\\n                    });\\n                }\\n            }\\n        };\\n        view.addEventListener(\\"mouseup\\", callback);\\n        view.addEventListener(\\"touchup\\", callback);\\n    });\\n};\\nlet resizeObserver = new ResizeObserver(() => {\\n    if (fit_width_to_parent && spec.width !== parent_element.offsetWidth) {\\n        resize_callback();\\n    }\\n});\\nonMount(() => {\\n    renderPlot();\\n    resizeObserver.observe(parent_element);\\n});\\nonDestroy(() => {\\n    resizeObserver.disconnect();\\n});\\n<\/script>\\n\\n<div data-testid={\\"altair\\"} class=\\"altair layout\\" bind:this={parent_element}>\\n\\t<div bind:this={element}></div>\\n\\t{#if caption}\\n\\t\\t<div class=\\"caption layout\\">\\n\\t\\t\\t{caption}\\n\\t\\t</div>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\t.altair :global(canvas) {\\n\\t\\tpadding: 6px;\\n\\t}\\n\\t.altair :global(.vega-embed) {\\n\\t\\tpadding: 0px !important;\\n\\t}\\n\\t.altair :global(.vega-actions) {\\n\\t\\tright: 0px !important;\\n\\t}\\n\\t.layout {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\twidth: var(--size-full);\\n\\t\\theight: var(--size-full);\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\t.altair {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\twidth: var(--size-full);\\n\\t\\theight: var(--size-full);\\n\\t}\\n\\t.caption {\\n\\t\\tfont-size: var(--text-sm);\\n\\t\\tmargin-bottom: 6px;\\n\\t}\\n\\t:global(#vg-tooltip-element) {\\n\\t\\tfont-family: var(--font) !important;\\n\\t\\tfont-size: var(--text-xs) !important;\\n\\t\\tbox-shadow: none !important;\\n\\t\\tbackground-color: var(--block-background-fill) !important;\\n\\t\\tborder: 1px solid var(--border-color-primary) !important;\\n\\t\\tcolor: var(--body-text-color) !important;\\n\\t}\\n\\t:global(#vg-tooltip-element .key) {\\n\\t\\tcolor: var(--body-text-color-subdued) !important;\\n\\t}</style>\\n"],"names":[],"mappings":"AA6FC,sBAAO,CAAS,MAAQ,CACvB,OAAO,CAAE,GACV,CACA,sBAAO,CAAS,WAAa,CAC5B,OAAO,CAAE,GAAG,CAAC,UACd,CACA,sBAAO,CAAS,aAAe,CAC9B,KAAK,CAAE,GAAG,CAAC,UACZ,CACA,sBAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CACA,sBAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CACA,uBAAS,CACR,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,aAAa,CAAE,GAChB,CACQ,mBAAqB,CAC5B,WAAW,CAAE,IAAI,MAAM,CAAC,CAAC,UAAU,CACnC,SAAS,CAAE,IAAI,SAAS,CAAC,CAAC,UAAU,CACpC,UAAU,CAAE,IAAI,CAAC,UAAU,CAC3B,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAAC,UAAU,CACzD,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAAC,UAAU,CACxD,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAAC,UAC/B,CACQ,wBAA0B,CACjC,KAAK,CAAE,IAAI,yBAAyB,CAAC,CAAC,UACvC"}'},yw=create_ssr_component((e,t,n,i)=>{let r,s,o,{value:a}=t,{colors:c=[]}=t,{caption:l}=t,{show_actions_button:u}=t,{gradio:f}=t,d,g,{_selectable:h}=t,m=window.getComputedStyle(document.body),y;let C=()=>{};let M=new ResizeObserver(()=>{o&&s.width!==g.offsetWidth&&C();});return onDestroy(()=>{M.disconnect();}),t.value===void 0&&n.value&&a!==void 0&&n.value(a),t.colors===void 0&&n.colors&&c!==void 0&&n.colors(c),t.caption===void 0&&n.caption&&l!==void 0&&n.caption(l),t.show_actions_button===void 0&&n.show_actions_button&&u!==void 0&&n.show_actions_button(u),t.gradio===void 0&&n.gradio&&f!==void 0&&n.gradio(f),t._selectable===void 0&&n._selectable&&h!==void 0&&n._selectable(h),e.css.add(_1),r=a?.plot,s=JSON.parse(r),s&&s.params&&!h&&(s.params=s.params.filter(D=>D.name!=="brush")),a.chart&&(s=wp(s,m,a.chart,c)),y!==s&&(y=s,s.width),o=!(s.encoding?.column?.field||s.encoding?.row?.field||a.chart===void 0),`<div${add_attribute("data-testid","altair",0)} class="altair layout svelte-1qhqpn7"${add_attribute("this",g,0)}><div${add_attribute("this",d,0)}></div> ${l?`<div class="caption layout svelte-1qhqpn7">${escape$1(l)}</div>`:""} </div>`});

export { yw as default };
//# sourceMappingURL=AltairPlot-B57avCCP.js.map
