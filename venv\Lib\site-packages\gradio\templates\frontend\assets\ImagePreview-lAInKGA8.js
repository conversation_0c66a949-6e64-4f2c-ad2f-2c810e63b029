import"./index-B7J2Z2jS.js";import{u as O}from"./utils-BsGrhMNe.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{B as Q}from"./BlockLabel-3KxTaaiM.js";import{I as R}from"./IconButton-C_HS7fTi.js";import{E as U}from"./Empty-ZqppqzTN.js";import{S as V}from"./ShareButton-BuIiIMKb.js";import{D as X}from"./Download-DVtk-Jv3.js";import{I as H}from"./Image-Bsh8Umrh.js";import{I as Y}from"./IconButtonWrapper--EIOWuEM.js";import{F as Z}from"./FullscreenButton-jgOGhOHz.js";import{g as x}from"./utils-Gtzs_Zla.js";import{I as ee}from"./Image-CnqB5dbD.js";import{D as te}from"./DownloadLink-QIttOhoR.js";/* empty css                                                   */import"./svelte/svelte.js";import"./prism-python-MMh3z1bK.js";import"./Community-Dw1micSV.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";const{SvelteComponent:ne,append:E,attr:F,binding_callbacks:le,bubble:I,check_outros:B,create_component:d,destroy_component:w,detach:k,element:S,empty:M,flush:g,group_outros:z,init:oe,insert:v,listen:re,mount_component:p,safe_not_equal:se,space:D,toggle_class:L,transition_in:m,transition_out:b}=window.__gradio__svelte__internal,{createEventDispatcher:ie,onMount:He}=window.__gradio__svelte__internal;function ae(r){let e,n,t,l,o,u,f,i,_;return n=new Y({props:{display_top_corner:r[8],$$slots:{default:[_e]},$$scope:{ctx:r}}}),u=new ee({props:{src:r[0].url,alt:"",loading:"lazy"}}),u.$on("load",r[16]),{c(){e=S("div"),d(n.$$.fragment),t=D(),l=S("button"),o=S("div"),d(u.$$.fragment),F(o,"class","image-frame svelte-zxsjoa"),L(o,"selectable",r[4]),F(l,"class","svelte-zxsjoa"),F(e,"class","image-container svelte-zxsjoa")},m(s,c){v(s,e,c),p(n,e,null),E(e,t),E(e,l),E(l,o),p(u,o,null),r[17](e),f=!0,i||(_=re(l,"click",r[11]),i=!0)},p(s,c){const h={};c&256&&(h.display_top_corner=s[8]),c&525033&&(h.$$scope={dirty:c,ctx:s}),n.$set(h);const $={};c&1&&($.src=s[0].url),u.$set($),(!f||c&16)&&L(o,"selectable",s[4])},i(s){f||(m(n.$$.fragment,s),m(u.$$.fragment,s),f=!0)},o(s){b(n.$$.fragment,s),b(u.$$.fragment,s),f=!1},d(s){s&&k(e),w(n),w(u),r[17](null),i=!1,_()}}}function ue(r){let e,n;return e=new U({props:{unpadded_box:!0,size:"large",$$slots:{default:[ce]},$$scope:{ctx:r}}}),{c(){d(e.$$.fragment)},m(t,l){p(e,t,l),n=!0},p(t,l){const o={};l&524288&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){b(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function q(r){let e,n;return e=new Z({props:{fullscreen:r[9]}}),e.$on("fullscreen",r[12]),{c(){d(e.$$.fragment)},m(t,l){p(e,t,l),n=!0},p(t,l){const o={};l&512&&(o.fullscreen=t[9]),e.$set(o)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){b(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function y(r){let e,n;return e=new te({props:{href:r[0].url,download:r[0].orig_name||"image",$$slots:{default:[fe]},$$scope:{ctx:r}}}),{c(){d(e.$$.fragment)},m(t,l){p(e,t,l),n=!0},p(t,l){const o={};l&1&&(o.href=t[0].url),l&1&&(o.download=t[0].orig_name||"image"),l&524352&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){b(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function fe(r){let e,n;return e=new R({props:{Icon:X,label:r[6]("common.download")}}),{c(){d(e.$$.fragment)},m(t,l){p(e,t,l),n=!0},p(t,l){const o={};l&64&&(o.label=t[6]("common.download")),e.$set(o)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){b(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function C(r){let e,n;return e=new V({props:{i18n:r[6],formatter:r[13],value:r[0]}}),e.$on("share",r[14]),e.$on("error",r[15]),{c(){d(e.$$.fragment)},m(t,l){p(e,t,l),n=!0},p(t,l){const o={};l&64&&(o.i18n=t[6]),l&1&&(o.value=t[0]),e.$set(o)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){b(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function _e(r){let e,n,t,l,o=r[7]&&q(r),u=r[3]&&y(r),f=r[5]&&C(r);return{c(){o&&o.c(),e=D(),u&&u.c(),n=D(),f&&f.c(),t=M()},m(i,_){o&&o.m(i,_),v(i,e,_),u&&u.m(i,_),v(i,n,_),f&&f.m(i,_),v(i,t,_),l=!0},p(i,_){i[7]?o?(o.p(i,_),_&128&&m(o,1)):(o=q(i),o.c(),m(o,1),o.m(e.parentNode,e)):o&&(z(),b(o,1,1,()=>{o=null}),B()),i[3]?u?(u.p(i,_),_&8&&m(u,1)):(u=y(i),u.c(),m(u,1),u.m(n.parentNode,n)):u&&(z(),b(u,1,1,()=>{u=null}),B()),i[5]?f?(f.p(i,_),_&32&&m(f,1)):(f=C(i),f.c(),m(f,1),f.m(t.parentNode,t)):f&&(z(),b(f,1,1,()=>{f=null}),B())},i(i){l||(m(o),m(u),m(f),l=!0)},o(i){b(o),b(u),b(f),l=!1},d(i){i&&(k(e),k(n),k(t)),o&&o.d(i),u&&u.d(i),f&&f.d(i)}}}function ce(r){let e,n;return e=new H({}),{c(){d(e.$$.fragment)},m(t,l){p(e,t,l),n=!0},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){b(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function me(r){let e,n,t,l,o,u;e=new Q({props:{show_label:r[2],Icon:H,label:r[2]?r[1]||r[6]("image.image"):""}});const f=[ue,ae],i=[];function _(s,c){return s[0]===null||!s[0].url?0:1}return t=_(r),l=i[t]=f[t](r),{c(){d(e.$$.fragment),n=D(),l.c(),o=M()},m(s,c){p(e,s,c),v(s,n,c),i[t].m(s,c),v(s,o,c),u=!0},p(s,[c]){const h={};c&4&&(h.show_label=s[2]),c&70&&(h.label=s[2]?s[1]||s[6]("image.image"):""),e.$set(h);let $=t;t=_(s),t===$?i[t].p(s,c):(z(),b(i[$],1,1,()=>{i[$]=null}),B(),l=i[t],l?l.p(s,c):(l=i[t]=f[t](s),l.c()),m(l,1),l.m(o.parentNode,o))},i(s){u||(m(e.$$.fragment,s),m(l),u=!0)},o(s){b(e.$$.fragment,s),b(l),u=!1},d(s){s&&(k(n),k(o)),w(e,s),i[t].d(s)}}}function be(r,e,n){let{value:t}=e,{label:l=void 0}=e,{show_label:o}=e,{show_download_button:u=!0}=e,{selectable:f=!1}=e,{show_share_button:i=!1}=e,{i18n:_}=e,{show_fullscreen_button:s=!0}=e,{display_icon_button_wrapper_top_corner:c=!1}=e,{fullscreen:h=!1}=e;const $=ie(),P=a=>{let j=x(a);j&&$("select",{index:j,value:null})};let N;function T(a){I.call(this,r,a)}const W=async a=>a?`<img src="${await O(a)}" />`:"";function A(a){I.call(this,r,a)}function G(a){I.call(this,r,a)}function J(a){I.call(this,r,a)}function K(a){le[a?"unshift":"push"](()=>{N=a,n(10,N)})}return r.$$set=a=>{"value"in a&&n(0,t=a.value),"label"in a&&n(1,l=a.label),"show_label"in a&&n(2,o=a.show_label),"show_download_button"in a&&n(3,u=a.show_download_button),"selectable"in a&&n(4,f=a.selectable),"show_share_button"in a&&n(5,i=a.show_share_button),"i18n"in a&&n(6,_=a.i18n),"show_fullscreen_button"in a&&n(7,s=a.show_fullscreen_button),"display_icon_button_wrapper_top_corner"in a&&n(8,c=a.display_icon_button_wrapper_top_corner),"fullscreen"in a&&n(9,h=a.fullscreen)},[t,l,o,u,f,i,_,s,c,h,N,P,T,W,A,G,J,K]}class Me extends ne{constructor(e){super(),oe(this,e,be,me,se,{value:0,label:1,show_label:2,show_download_button:3,selectable:4,show_share_button:5,i18n:6,show_fullscreen_button:7,display_icon_button_wrapper_top_corner:8,fullscreen:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),g()}get show_download_button(){return this.$$.ctx[3]}set show_download_button(e){this.$$set({show_download_button:e}),g()}get selectable(){return this.$$.ctx[4]}set selectable(e){this.$$set({selectable:e}),g()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),g()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),g()}get show_fullscreen_button(){return this.$$.ctx[7]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),g()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[8]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),g()}get fullscreen(){return this.$$.ctx[9]}set fullscreen(e){this.$$set({fullscreen:e}),g()}}export{Me as default};
//# sourceMappingURL=ImagePreview-lAInKGA8.js.map
