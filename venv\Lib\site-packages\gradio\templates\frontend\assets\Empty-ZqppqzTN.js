/* empty css                                                        */const{SvelteComponent:m,append:p,attr:r,binding_callbacks:c,create_slot:b,detach:z,element:f,flush:g,get_all_dirty_from_scope:v,get_slot_changes:E,init:C,insert:w,safe_not_equal:B,toggle_class:_,transition_in:R,transition_out:k,update_slot_base:q}=window.__gradio__svelte__internal;function S(n){let e,s,i;const u=n[5].default,l=b(u,n,n[4],null);return{c(){e=f("div"),s=f("div"),l&&l.c(),r(s,"class","icon svelte-1oiin9d"),r(e,"class","empty svelte-1oiin9d"),r(e,"aria-label","Empty value"),_(e,"small",n[0]==="small"),_(e,"large",n[0]==="large"),_(e,"unpadded_box",n[1]),_(e,"small_parent",n[3])},m(t,a){w(t,e,a),p(e,s),l&&l.m(s,null),n[6](e),i=!0},p(t,[a]){l&&l.p&&(!i||a&16)&&q(l,u,t,t[4],i?E(u,t[4],a,null):v(t[4]),null),(!i||a&1)&&_(e,"small",t[0]==="small"),(!i||a&1)&&_(e,"large",t[0]==="large"),(!i||a&2)&&_(e,"unpadded_box",t[1]),(!i||a&8)&&_(e,"small_parent",t[3])},i(t){i||(R(l,t),i=!0)},o(t){k(l,t),i=!1},d(t){t&&z(e),l&&l.d(t),n[6](null)}}}function j(n){if(!n)return!1;const{height:e}=n.getBoundingClientRect(),{height:s}=n.parentElement?.getBoundingClientRect()||{height:e};return e>s+2}function y(n,e,s){let i,{$$slots:u={},$$scope:l}=e,{size:t="small"}=e,{unpadded_box:a=!1}=e,d;function h(o){c[o?"unshift":"push"](()=>{d=o,s(2,d)})}return n.$$set=o=>{"size"in o&&s(0,t=o.size),"unpadded_box"in o&&s(1,a=o.unpadded_box),"$$scope"in o&&s(4,l=o.$$scope)},n.$$.update=()=>{n.$$.dirty&4&&s(3,i=j(d))},[t,a,d,i,l,u,h]}class D extends m{constructor(e){super(),C(this,e,y,S,B,{size:0,unpadded_box:1})}get size(){return this.$$.ctx[0]}set size(e){this.$$set({size:e}),g()}get unpadded_box(){return this.$$.ctx[1]}set unpadded_box(e){this.$$set({unpadded_box:e}),g()}}export{D as E};
//# sourceMappingURL=Empty-ZqppqzTN.js.map
