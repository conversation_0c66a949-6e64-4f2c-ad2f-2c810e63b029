{"version": 3, "file": "flowGraphConditionalDataBlock.BpscsBV2.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphConditionalDataBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../flowGraphBlock.js\";\nimport { RichTypeBoolean, RichTypeAny } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\n/**\n * Block that returns a value based on a condition.\n */\nexport class FlowGraphConditionalDataBlock extends FlowGraphBlock {\n    /**\n     * Creates a new instance of the block\n     * @param config optional configuration for this block\n     */\n    constructor(config) {\n        super(config);\n        this.condition = this.registerDataInput(\"condition\", RichTypeBoolean);\n        this.onTrue = this.registerDataInput(\"onTrue\", RichTypeAny);\n        this.onFalse = this.registerDataInput(\"onFalse\", RichTypeAny);\n        this.output = this.registerDataOutput(\"output\", RichTypeAny);\n    }\n    /**\n     * @internal\n     */\n    _updateOutputs(context) {\n        // get the value of the condition\n        const condition = this.condition.getValue(context);\n        // set the value based on the condition truth-ness.\n        this.output.setValue(condition ? this.onTrue.getValue(context) : this.onFalse.getValue(context), context);\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FlowGraphConditionalBlock\" /* FlowGraphBlockNames.Conditional */;\n    }\n}\nRegisterClass(\"FlowGraphConditionalBlock\" /* FlowGraphBlockNames.Conditional */, FlowGraphConditionalDataBlock);\n//# sourceMappingURL=flowGraphConditionalDataBlock.js.map"], "names": ["FlowGraphConditionalDataBlock", "FlowGraphBlock", "config", "RichTypeBoolean", "RichTypeAny", "context", "condition", "RegisterClass"], "mappings": "uJAMO,MAAMA,UAAsCC,CAAe,CAK9D,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,UAAY,KAAK,kBAAkB,YAAaC,CAAe,EACpE,KAAK,OAAS,KAAK,kBAAkB,SAAUC,CAAW,EAC1D,KAAK,QAAU,KAAK,kBAAkB,UAAWA,CAAW,EAC5D,KAAK,OAAS,KAAK,mBAAmB,SAAUA,CAAW,CAC9D,CAID,eAAeC,EAAS,CAEpB,MAAMC,EAAY,KAAK,UAAU,SAASD,CAAO,EAEjD,KAAK,OAAO,SAASC,EAAY,KAAK,OAAO,SAASD,CAAO,EAAI,KAAK,QAAQ,SAASA,CAAO,EAAGA,CAAO,CAC3G,CAKD,cAAe,CACX,MAAO,2BACV,CACL,CACAE,EAAc,4BAAmEP,CAA6B", "x_google_ignoreList": [0]}