{"version": 3, "file": "flowGraphMeshPickEventBlock-BHmTlaoo.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphMeshPickEventBlock.js"], "sourcesContent": ["import { FlowGraphEventBlock } from \"../../flowGraphEventBlock.js\";\nimport { PointerEventTypes } from \"../../../Events/pointerEvents.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { _isADescendantOf } from \"../../utils.js\";\nimport { RichTypeAny, RichTypeNumber, RichTypeVector3 } from \"../../flowGraphRichTypes.js\";\n/**\n * A block that activates when a mesh is picked.\n */\nexport class FlowGraphMeshPickEventBlock extends FlowGraphEventBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        /**\n         * the type of the event this block reacts to\n         */\n        this.type = \"MeshPick\" /* FlowGraphEventType.MeshPick */;\n        this.asset = this.registerDataInput(\"asset\", RichTypeAny, config?.targetMesh);\n        this.pickedPoint = this.registerDataOutput(\"pickedPoint\", RichTypeVector3);\n        this.pickOrigin = this.registerDataOutput(\"pickOrigin\", RichTypeVector3);\n        this.pointerId = this.registerDataOutput(\"pointerId\", RichTypeNumber);\n        this.pickedMesh = this.registerDataOutput(\"pickedMesh\", RichTypeAny);\n        this.pointerType = this.registerDataInput(\"pointerType\", RichTypeAny, PointerEventTypes.POINTERPICK);\n    }\n    _getReferencedMesh(context) {\n        return this.asset.getValue(context);\n    }\n    _executeEvent(context, pickedInfo) {\n        // get the pointer type\n        const pointerType = this.pointerType.getValue(context);\n        if (pointerType !== pickedInfo.type) {\n            // returning true here to continue the propagation of the pointer event to the rest of the blocks\n            return true;\n        }\n        // check if the mesh is the picked mesh or a descendant\n        const mesh = this._getReferencedMesh(context);\n        if (mesh && pickedInfo.pickInfo?.pickedMesh && (pickedInfo.pickInfo?.pickedMesh === mesh || _isADescendantOf(pickedInfo.pickInfo?.pickedMesh, mesh))) {\n            this.pointerId.setValue(pickedInfo.event.pointerId, context);\n            this.pickOrigin.setValue(pickedInfo.pickInfo.ray?.origin, context);\n            this.pickedPoint.setValue(pickedInfo.pickInfo.pickedPoint, context);\n            this.pickedMesh.setValue(pickedInfo.pickInfo.pickedMesh, context);\n            this._execute(context);\n            // stop the propagation if the configuration says so\n            return !this.config?.stopPropagation;\n        }\n        else {\n            // reset the outputs\n            this.pointerId.resetToDefaultValue(context);\n            this.pickOrigin.resetToDefaultValue(context);\n            this.pickedPoint.resetToDefaultValue(context);\n            this.pickedMesh.resetToDefaultValue(context);\n        }\n        return true;\n    }\n    /**\n     * @internal\n     */\n    _preparePendingTasks(_context) {\n        // no-op\n    }\n    /**\n     * @internal\n     */\n    _cancelPendingTasks(_context) {\n        // no-op\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphMeshPickEventBlock\" /* FlowGraphBlockNames.MeshPickEvent */;\n    }\n}\nRegisterClass(\"FlowGraphMeshPickEventBlock\" /* FlowGraphBlockNames.MeshPickEvent */, FlowGraphMeshPickEventBlock);\n//# sourceMappingURL=flowGraphMeshPickEventBlock.js.map"], "names": ["FlowGraphMeshPickEventBlock", "FlowGraphEventBlock", "config", "RichTypeAny", "RichTypeVector3", "RichTypeNumber", "PointerEventTypes", "context", "pickedInfo", "mesh", "_isADescendantOf", "_context", "RegisterClass"], "mappings": "6QAQO,MAAMA,UAAoCC,CAAoB,CACjE,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EAId,KAAK,KAAO,WACZ,KAAK,MAAQ,KAAK,kBAAkB,QAASC,EAAaD,GAAQ,UAAU,EAC5E,KAAK,YAAc,KAAK,mBAAmB,cAAeE,CAAe,EACzE,KAAK,WAAa,KAAK,mBAAmB,aAAcA,CAAe,EACvE,KAAK,UAAY,KAAK,mBAAmB,YAAaC,CAAc,EACpE,KAAK,WAAa,KAAK,mBAAmB,aAAcF,CAAW,EACnE,KAAK,YAAc,KAAK,kBAAkB,cAAeA,EAAaG,EAAkB,WAAW,CACtG,CACD,mBAAmBC,EAAS,CACxB,OAAO,KAAK,MAAM,SAASA,CAAO,CACrC,CACD,cAAcA,EAASC,EAAY,CAG/B,GADoB,KAAK,YAAY,SAASD,CAAO,IACjCC,EAAW,KAE3B,MAAO,GAGX,MAAMC,EAAO,KAAK,mBAAmBF,CAAO,EAC5C,OAAIE,GAAQD,EAAW,UAAU,aAAeA,EAAW,UAAU,aAAeC,GAAQC,EAAiBF,EAAW,UAAU,WAAYC,CAAI,IAC9I,KAAK,UAAU,SAASD,EAAW,MAAM,UAAWD,CAAO,EAC3D,KAAK,WAAW,SAASC,EAAW,SAAS,KAAK,OAAQD,CAAO,EACjE,KAAK,YAAY,SAASC,EAAW,SAAS,YAAaD,CAAO,EAClE,KAAK,WAAW,SAASC,EAAW,SAAS,WAAYD,CAAO,EAChE,KAAK,SAASA,CAAO,EAEd,CAAC,KAAK,QAAQ,kBAIrB,KAAK,UAAU,oBAAoBA,CAAO,EAC1C,KAAK,WAAW,oBAAoBA,CAAO,EAC3C,KAAK,YAAY,oBAAoBA,CAAO,EAC5C,KAAK,WAAW,oBAAoBA,CAAO,EAExC,GACV,CAID,qBAAqBI,EAAU,CAE9B,CAID,oBAAoBA,EAAU,CAE7B,CAID,cAAe,CACX,MAAO,6BACV,CACL,CACAC,EAAc,8BAAuEZ,CAA2B", "x_google_ignoreList": [0]}