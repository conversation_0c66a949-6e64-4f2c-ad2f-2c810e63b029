{"version": 3, "file": "r.DUYO_cvP.js", "sources": ["../../../../../../../../node_modules/.pnpm/@codemirror+legacy-modes@6.4.3/node_modules/@codemirror/legacy-modes/mode/r.js"], "sourcesContent": ["function wordObj(words) {\n  var res = {};\n  for (var i = 0; i < words.length; ++i) res[words[i]] = true;\n  return res;\n}\nvar commonAtoms = [\"NULL\", \"NA\", \"Inf\", \"NaN\", \"NA_integer_\", \"NA_real_\", \"NA_complex_\", \"NA_character_\", \"TRUE\", \"FALSE\"];\nvar commonBuiltins = [\"list\", \"quote\", \"bquote\", \"eval\", \"return\", \"call\", \"parse\", \"deparse\"];\nvar commonKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\", \"in\", \"next\", \"break\"];\nvar commonBlockKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\"];\n\nvar atoms = wordObj(commonAtoms);\nvar builtins = wordObj(commonBuiltins);\nvar keywords = wordObj(commonKeywords);\nvar blockkeywords = wordObj(commonBlockKeywords);\nvar opChars = /[+\\-*\\/^<>=!&|~$:]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  curPunc = null;\n  var ch = stream.next();\n  if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \"0\" && stream.eat(\"x\")) {\n    stream.eatWhile(/[\\da-f]/i);\n    return \"number\";\n  } else if (ch == \".\" && stream.eat(/\\d/)) {\n    stream.match(/\\d*(?:e[+\\-]?\\d+)?/);\n    return \"number\";\n  } else if (/\\d/.test(ch)) {\n    stream.match(/\\d*(?:\\.\\d+)?(?:e[+\\-]\\d+)?L?/);\n    return \"number\";\n  } else if (ch == \"'\" || ch == '\"') {\n    state.tokenize = tokenString(ch);\n    return \"string\";\n  } else if (ch == \"`\") {\n    stream.match(/[^`]+`/);\n    return \"string.special\";\n  } else if (ch == \".\" && stream.match(/.(?:[.]|\\d+)/)) {\n    return \"keyword\";\n  } else if (/[a-zA-Z\\.]/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    var word = stream.current();\n    if (atoms.propertyIsEnumerable(word)) return \"atom\";\n    if (keywords.propertyIsEnumerable(word)) {\n      // Block keywords start new blocks, except 'else if', which only starts\n      // one new block for the 'if', no block for the 'else'.\n      if (blockkeywords.propertyIsEnumerable(word) &&\n          !stream.match(/\\s*if(\\s+|$)/, false))\n        curPunc = \"block\";\n      return \"keyword\";\n    }\n    if (builtins.propertyIsEnumerable(word)) return \"builtin\";\n    return \"variable\";\n  } else if (ch == \"%\") {\n    if (stream.skipTo(\"%\")) stream.next();\n    return \"variableName.special\";\n  } else if (\n    (ch == \"<\" && stream.eat(\"-\")) ||\n      (ch == \"<\" && stream.match(\"<-\")) ||\n      (ch == \"-\" && stream.match(/>>?/))\n  ) {\n    return \"operator\";\n  } else if (ch == \"=\" && state.ctx.argList) {\n    return \"operator\";\n  } else if (opChars.test(ch)) {\n    if (ch == \"$\") return \"operator\";\n    stream.eatWhile(opChars);\n    return \"operator\";\n  } else if (/[\\(\\){}\\[\\];]/.test(ch)) {\n    curPunc = ch;\n    if (ch == \";\") return \"punctuation\";\n    return null;\n  } else {\n    return null;\n  }\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    if (stream.eat(\"\\\\\")) {\n      var ch = stream.next();\n      if (ch == \"x\") stream.match(/^[a-f0-9]{2}/i);\n      else if ((ch == \"u\" || ch == \"U\") && stream.eat(\"{\") && stream.skipTo(\"}\")) stream.next();\n      else if (ch == \"u\") stream.match(/^[a-f0-9]{4}/i);\n      else if (ch == \"U\") stream.match(/^[a-f0-9]{8}/i);\n      else if (/[0-7]/.test(ch)) stream.match(/^[0-7]{1,2}/);\n      return \"string.special\";\n    } else {\n      var next;\n      while ((next = stream.next()) != null) {\n        if (next == quote) { state.tokenize = tokenBase; break; }\n        if (next == \"\\\\\") { stream.backUp(1); break; }\n      }\n      return \"string\";\n    }\n  };\n}\n\nvar ALIGN_YES = 1, ALIGN_NO = 2, BRACELESS = 4\n\nfunction push(state, type, stream) {\n  state.ctx = {type: type,\n               indent: state.indent,\n               flags: 0,\n               column: stream.column(),\n               prev: state.ctx};\n}\nfunction setFlag(state, flag) {\n  var ctx = state.ctx\n  state.ctx = {type: ctx.type,\n               indent: ctx.indent,\n               flags: ctx.flags | flag,\n               column: ctx.column,\n               prev: ctx.prev}\n}\nfunction pop(state) {\n  state.indent = state.ctx.indent;\n  state.ctx = state.ctx.prev;\n}\n\nexport const r = {\n  name: \"r\",\n  startState: function(indentUnit) {\n    return {tokenize: tokenBase,\n            ctx: {type: \"top\",\n                  indent: -indentUnit,\n                  flags: ALIGN_NO},\n            indent: 0,\n            afterIdent: false};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if ((state.ctx.flags & 3) == 0) state.ctx.flags |= ALIGN_NO\n      if (state.ctx.flags & BRACELESS) pop(state)\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    if (style != \"comment\" && (state.ctx.flags & ALIGN_NO) == 0) setFlag(state, ALIGN_YES)\n\n    if ((curPunc == \";\" || curPunc == \"{\" || curPunc == \"}\") && state.ctx.type == \"block\") pop(state);\n    if (curPunc == \"{\") push(state, \"}\", stream);\n    else if (curPunc == \"(\") {\n      push(state, \")\", stream);\n      if (state.afterIdent) state.ctx.argList = true;\n    }\n    else if (curPunc == \"[\") push(state, \"]\", stream);\n    else if (curPunc == \"block\") push(state, \"block\", stream);\n    else if (curPunc == state.ctx.type) pop(state);\n    else if (state.ctx.type == \"block\" && style != \"comment\") setFlag(state, BRACELESS)\n    state.afterIdent = style == \"variable\" || style == \"keyword\";\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase) return 0;\n    var firstChar = textAfter && textAfter.charAt(0), ctx = state.ctx,\n        closing = firstChar == ctx.type;\n    if (ctx.flags & BRACELESS) ctx = ctx.prev\n    if (ctx.type == \"block\") return ctx.indent + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.flags & ALIGN_YES) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    wordChars: \".\",\n    commentTokens: {line: \"#\"},\n    autocomplete: commonAtoms.concat(commonBuiltins, commonKeywords)\n  }\n};\n"], "names": ["wordObj", "words", "res", "i", "commonAtoms", "commonBuiltins", "commonKeywords", "commonBlockKeywords", "atoms", "builtins", "keywords", "blockkeywords", "opChars", "curPunc", "tokenBase", "stream", "state", "ch", "tokenString", "word", "quote", "next", "ALIGN_YES", "ALIGN_NO", "BRACELESS", "push", "type", "setFlag", "flag", "ctx", "pop", "r", "indentUnit", "style", "textAfter", "cx", "firstChar", "closing"], "mappings": "AAAA,SAASA,EAAQC,EAAO,CAEtB,QADIC,EAAM,CAAA,EACDC,EAAI,EAAGA,EAAIF,EAAM,OAAQ,EAAEE,EAAGD,EAAID,EAAME,CAAC,CAAC,EAAI,GACvD,OAAOD,CACT,CACA,IAAIE,EAAc,CAAC,OAAQ,KAAM,MAAO,MAAO,cAAe,WAAY,cAAe,gBAAiB,OAAQ,OAAO,EACrHC,EAAiB,CAAC,OAAQ,QAAS,SAAU,OAAQ,SAAU,OAAQ,QAAS,SAAS,EACzFC,EAAiB,CAAC,KAAM,OAAQ,SAAU,QAAS,WAAY,MAAO,KAAM,OAAQ,OAAO,EAC3FC,EAAsB,CAAC,KAAM,OAAQ,SAAU,QAAS,WAAY,KAAK,EAEzEC,EAAQR,EAAQI,CAAW,EAC3BK,EAAWT,EAAQK,CAAc,EACjCK,EAAWV,EAAQM,CAAc,EACjCK,EAAgBX,EAAQO,CAAmB,EAC3CK,EAAU,qBACVC,EAEJ,SAASC,EAAUC,EAAQC,EAAO,CAChCH,EAAU,KACV,IAAII,EAAKF,EAAO,OAChB,GAAIE,GAAM,IACR,OAAAF,EAAO,UAAS,EACT,UACF,GAAIE,GAAM,KAAOF,EAAO,IAAI,GAAG,EACpC,OAAAA,EAAO,SAAS,UAAU,EACnB,SACF,GAAIE,GAAM,KAAOF,EAAO,IAAI,IAAI,EACrC,OAAAA,EAAO,MAAM,oBAAoB,EAC1B,SACF,GAAI,KAAK,KAAKE,CAAE,EACrB,OAAAF,EAAO,MAAM,+BAA+B,EACrC,SACF,GAAIE,GAAM,KAAOA,GAAM,IAC5B,OAAAD,EAAM,SAAWE,EAAYD,CAAE,EACxB,SACF,GAAIA,GAAM,IACf,OAAAF,EAAO,MAAM,QAAQ,EACd,iBACF,GAAIE,GAAM,KAAOF,EAAO,MAAM,cAAc,EACjD,MAAO,UACF,GAAI,aAAa,KAAKE,CAAE,EAAG,CAChCF,EAAO,SAAS,QAAQ,EACxB,IAAII,EAAOJ,EAAO,UAClB,OAAIP,EAAM,qBAAqBW,CAAI,EAAU,OACzCT,EAAS,qBAAqBS,CAAI,GAGhCR,EAAc,qBAAqBQ,CAAI,GACvC,CAACJ,EAAO,MAAM,eAAgB,EAAK,IACrCF,EAAU,SACL,WAELJ,EAAS,qBAAqBU,CAAI,EAAU,UACzC,UACX,KAAS,QAAIF,GAAM,KACXF,EAAO,OAAO,GAAG,GAAGA,EAAO,KAAI,EAC5B,wBAENE,GAAM,KAAOF,EAAO,IAAI,GAAG,GACzBE,GAAM,KAAOF,EAAO,MAAM,IAAI,GAC9BE,GAAM,KAAOF,EAAO,MAAM,KAAK,GAGzBE,GAAM,KAAOD,EAAM,IAAI,QADzB,WAGEJ,EAAQ,KAAKK,CAAE,GACpBA,GAAM,KACVF,EAAO,SAASH,CAAO,EAChB,YACE,gBAAgB,KAAKK,CAAE,GAChCJ,EAAUI,EACNA,GAAM,IAAY,cACf,MAEA,IAEX,CAEA,SAASC,EAAYE,EAAO,CAC1B,OAAO,SAASL,EAAQC,EAAO,CAC7B,GAAID,EAAO,IAAI,IAAI,EAAG,CACpB,IAAIE,EAAKF,EAAO,OAChB,OAAIE,GAAM,IAAKF,EAAO,MAAM,eAAe,GACjCE,GAAM,KAAOA,GAAM,MAAQF,EAAO,IAAI,GAAG,GAAKA,EAAO,OAAO,GAAG,EAAGA,EAAO,KAAI,EAC9EE,GAAM,IAAKF,EAAO,MAAM,eAAe,EACvCE,GAAM,IAAKF,EAAO,MAAM,eAAe,EACvC,QAAQ,KAAKE,CAAE,GAAGF,EAAO,MAAM,aAAa,EAC9C,gBACb,KAAW,CAEL,QADIM,GACIA,EAAON,EAAO,KAAI,IAAO,MAAM,CACrC,GAAIM,GAAQD,EAAO,CAAEJ,EAAM,SAAWF,EAAW,KAAQ,CACzD,GAAIO,GAAQ,KAAM,CAAEN,EAAO,OAAO,CAAC,EAAG,KAAQ,CAC/C,CACD,MAAO,QACR,CACL,CACA,CAEA,IAAIO,EAAY,EAAGC,EAAW,EAAGC,EAAY,EAE7C,SAASC,EAAKT,EAAOU,EAAMX,EAAQ,CACjCC,EAAM,IAAM,CAAC,KAAMU,EACN,OAAQV,EAAM,OACd,MAAO,EACP,OAAQD,EAAO,OAAQ,EACvB,KAAMC,EAAM,GAAG,CAC9B,CACA,SAASW,EAAQX,EAAOY,EAAM,CAC5B,IAAIC,EAAMb,EAAM,IAChBA,EAAM,IAAM,CAAC,KAAMa,EAAI,KACV,OAAQA,EAAI,OACZ,MAAOA,EAAI,MAAQD,EACnB,OAAQC,EAAI,OACZ,KAAMA,EAAI,IAAI,CAC7B,CACA,SAASC,EAAId,EAAO,CAClBA,EAAM,OAASA,EAAM,IAAI,OACzBA,EAAM,IAAMA,EAAM,IAAI,IACxB,CAEY,MAACe,EAAI,CACf,KAAM,IACN,WAAY,SAASC,EAAY,CAC/B,MAAO,CAAC,SAAUlB,EACV,IAAK,CAAC,KAAM,MACN,OAAQ,CAACkB,EACT,MAAOT,CAAQ,EACrB,OAAQ,EACR,WAAY,EAAK,CAC1B,EAED,MAAO,SAASR,EAAQC,EAAO,CAM7B,GALID,EAAO,QACJC,EAAM,IAAI,MAAQ,IAASA,EAAM,IAAI,OAASO,GAC/CP,EAAM,IAAI,MAAQQ,GAAWM,EAAId,CAAK,EAC1CA,EAAM,OAASD,EAAO,eAEpBA,EAAO,WAAY,OAAO,KAC9B,IAAIkB,EAAQjB,EAAM,SAASD,EAAQC,CAAK,EACxC,OAAIiB,GAAS,WAAc,EAAAjB,EAAM,IAAI,MAAQO,IAAgBI,EAAQX,EAAOM,CAAS,GAEhFT,GAAW,KAAOA,GAAW,KAAOA,GAAW,MAAQG,EAAM,IAAI,MAAQ,SAASc,EAAId,CAAK,EAC5FH,GAAW,IAAKY,EAAKT,EAAO,IAAKD,CAAM,EAClCF,GAAW,KAClBY,EAAKT,EAAO,IAAKD,CAAM,EACnBC,EAAM,aAAYA,EAAM,IAAI,QAAU,KAEnCH,GAAW,IAAKY,EAAKT,EAAO,IAAKD,CAAM,EACvCF,GAAW,QAASY,EAAKT,EAAO,QAASD,CAAM,EAC/CF,GAAWG,EAAM,IAAI,KAAMc,EAAId,CAAK,EACpCA,EAAM,IAAI,MAAQ,SAAWiB,GAAS,WAAWN,EAAQX,EAAOQ,CAAS,EAClFR,EAAM,WAAaiB,GAAS,YAAcA,GAAS,UAC5CA,CACR,EAED,OAAQ,SAASjB,EAAOkB,EAAWC,EAAI,CACrC,GAAInB,EAAM,UAAYF,EAAW,MAAO,GACxC,IAAIsB,EAAYF,GAAaA,EAAU,OAAO,CAAC,EAAGL,EAAMb,EAAM,IAC1DqB,EAAUD,GAAaP,EAAI,KAE/B,OADIA,EAAI,MAAQL,IAAWK,EAAMA,EAAI,MACjCA,EAAI,MAAQ,QAAgBA,EAAI,QAAUO,GAAa,IAAM,EAAID,EAAG,MAC/DN,EAAI,MAAQP,EAAkBO,EAAI,QAAUQ,EAAU,EAAI,GACvDR,EAAI,QAAUQ,EAAU,EAAIF,EAAG,KAC5C,EAED,aAAc,CACZ,UAAW,IACX,cAAe,CAAC,KAAM,GAAG,EACzB,aAAc/B,EAAY,OAAOC,EAAgBC,CAAc,CAChE,CACH", "x_google_ignoreList": [0]}