// qremoteobjectnode.sip generated by MetaSIP
//
// This file is part of the QtRemoteObjects Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QRemoteObjectAbstractPersistedStore : public QObject
{
%TypeHeaderCode
#include <qremoteobjectnode.h>
%End

public:
    QRemoteObjectAbstractPersistedStore(QObject *parent /TransferThis/ = 0);
    virtual ~QRemoteObjectAbstractPersistedStore();
    virtual void saveProperties(const QString &repName, const QByteArray &repSig, const QVariantList &values) = 0;
    virtual QVariantList restoreProperties(const QString &repName, const QByteArray &repSig) = 0;
};

%End
%If (Qt_6_2_0 -)

class QRemoteObjectNode : public QObject
{
%TypeHeaderCode
#include <qremoteobjectnode.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QAbstractItemModelReplica, &sipType_QAbstractItemModelReplica, -1, 1},
        {sipName_QRemoteObjectAbstractPersistedStore, &sipType_QRemoteObjectAbstractPersistedStore, -1, 2},
        {sipName_QRemoteObjectReplica, &sipType_QRemoteObjectReplica, 4, 3},
        {sipName_QRemoteObjectNode, &sipType_QRemoteObjectNode, 6, -1},
        {sipName_QRemoteObjectDynamicReplica, &sipType_QRemoteObjectDynamicReplica, -1, 5},
        {sipName_QRemoteObjectRegistry, &sipType_QRemoteObjectRegistry, -1, -1},
        {sipName_QRemoteObjectHostBase, &sipType_QRemoteObjectHostBase, 7, -1},
        {sipName_QRemoteObjectHost, &sipType_QRemoteObjectHost, -1, 8},
        {sipName_QRemoteObjectRegistryHost, &sipType_QRemoteObjectRegistryHost, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum ErrorCode
    {
        NoError,
        RegistryNotAcquired,
        RegistryAlreadyHosted,
        NodeIsNoServer,
        ServerAlreadyCreated,
        UnintendedRegistryHosting,
        OperationNotValidOnClientNode,
        SourceNotRegistered,
        MissingObjectName,
        HostUrlInvalid,
        ProtocolMismatch,
        ListenFailed,
%If (Qt_6_7_0 -)
        SocketAccessError,
%End
    };

    QRemoteObjectNode(QObject *parent /TransferThis/ = 0);
    QRemoteObjectNode(const QUrl &registryAddress, QObject *parent /TransferThis/ = 0);
    virtual ~QRemoteObjectNode();
    bool connectToNode(const QUrl &address);
    void addClientSideConnection(QIODevice *ioDevice);
    virtual void setName(const QString &name);
    QStringList instances(QStringView typeName) const;
    QRemoteObjectDynamicReplica *acquireDynamic(const QString &name) /Factory/;
    QAbstractItemModelReplica *acquireModel(const QString &name, QtRemoteObjects::InitialAction action = QtRemoteObjects::FetchRootSize, const QList<int> &rolesHint = {});
    QUrl registryUrl() const;
    virtual bool setRegistryUrl(const QUrl &registryAddress);
    bool waitForRegistry(int timeout = 30000) /ReleaseGIL/;
    const QRemoteObjectRegistry *registry() const;
    QRemoteObjectAbstractPersistedStore *persistedStore() const;
    void setPersistedStore(QRemoteObjectAbstractPersistedStore *persistedStore);
    QRemoteObjectNode::ErrorCode lastError() const;
    int heartbeatInterval() const;
    void setHeartbeatInterval(int interval);

signals:
    void remoteObjectAdded(const QRemoteObjectSourceLocation &);
    void remoteObjectRemoved(const QRemoteObjectSourceLocation &);
    void error(QRemoteObjectNode::ErrorCode errorCode);
    void heartbeatIntervalChanged(int heartbeatInterval);

protected:
    virtual void timerEvent(QTimerEvent *);
};

%End
%If (Qt_6_2_0 -)

class QRemoteObjectHostBase : public QRemoteObjectNode /NoDefaultCtors/
{
%TypeHeaderCode
#include <qremoteobjectnode.h>
%End

public:
    enum AllowedSchemas
    {
        BuiltInSchemasOnly,
        AllowExternalRegistration,
    };

    virtual ~QRemoteObjectHostBase();
    virtual void setName(const QString &name);
    bool enableRemoting(QObject *object, const QString &name = QString());
    bool enableRemoting(QAbstractItemModel *model, const QString &name, const QList<int> roles, QItemSelectionModel *selectionModel = 0);
    bool disableRemoting(QObject *remoteObject);
    void addHostSideConnection(QIODevice *ioDevice);
    bool proxy(const QUrl &registryUrl, const QUrl &hostUrl /TypeHintValue="QUrl()"/ = {});
    bool reverseProxy();
};

%End
%If (Qt_6_2_0 -)

class QRemoteObjectHost : public QRemoteObjectHostBase
{
%TypeHeaderCode
#include <qremoteobjectnode.h>
%End

public:
    QRemoteObjectHost(QObject *parent /TransferThis/ = 0);
    QRemoteObjectHost(const QUrl &address, const QUrl &registryAddress = QUrl(), QRemoteObjectHostBase::AllowedSchemas allowedSchemas = QRemoteObjectHostBase::BuiltInSchemasOnly, QObject *parent /TransferThis/ = 0);
    QRemoteObjectHost(const QUrl &address, QObject *parent /TransferThis/);
    virtual ~QRemoteObjectHost();
    virtual QUrl hostUrl() const;
    virtual bool setHostUrl(const QUrl &hostAddress, QRemoteObjectHostBase::AllowedSchemas allowedSchemas = QRemoteObjectHostBase::BuiltInSchemasOnly);

signals:
    void hostUrlChanged();

public:
%If (Qt_6_7_0 -)
    static void setLocalServerOptions(QLocalServer::SocketOptions options);
%End
};

%End
%If (Qt_6_2_0 -)

class QRemoteObjectRegistryHost : public QRemoteObjectHostBase
{
%TypeHeaderCode
#include <qremoteobjectnode.h>
%End

public:
    QRemoteObjectRegistryHost(const QUrl &registryAddress = QUrl(), QObject *parent /TransferThis/ = 0);
    virtual ~QRemoteObjectRegistryHost();
    virtual bool setRegistryUrl(const QUrl &registryUrl);
};

%End
