#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::ScalarType result_type(const at::Tensor & tensor, const at::Tensor & other);
TORCH_API at::ScalarType result_type(const at::Tensor & tensor, const at::Scalar & other);
TORCH_API at::ScalarType result_type(const at::Scalar & scalar, const at::Tensor & tensor);
TORCH_API at::ScalarType result_type(const at::Scalar & scalar1, const at::Scalar & scalar2);
} // namespace native
} // namespace at
