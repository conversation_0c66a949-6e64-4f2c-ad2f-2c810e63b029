{"version": 3, "file": "index.CzquxtZ2.js", "sources": ["../../../../../../../../node_modules/.pnpm/@lezer+markdown@1.4.2/node_modules/@lezer/markdown/dist/index.js", "../../../../../../../../node_modules/.pnpm/@codemirror+lang-markdown@6.3.2/node_modules/@codemirror/lang-markdown/dist/index.js"], "sourcesContent": ["import { NodeType, NodeProp, NodeSet, Tree, Parser, parseMixed } from '@lezer/common';\nimport { styleTags, tags, Tag } from '@lezer/highlight';\n\nclass CompositeBlock {\n    static create(type, value, from, parentHash, end) {\n        let hash = (parentHash + (parentHash << 8) + type + (value << 4)) | 0;\n        return new CompositeBlock(type, value, from, hash, end, [], []);\n    }\n    constructor(type, \n    // Used for indentation in list items, markup character in lists\n    value, from, hash, end, children, positions) {\n        this.type = type;\n        this.value = value;\n        this.from = from;\n        this.hash = hash;\n        this.end = end;\n        this.children = children;\n        this.positions = positions;\n        this.hashProp = [[NodeProp.contextHash, hash]];\n    }\n    addChild(child, pos) {\n        if (child.prop(NodeProp.contextHash) != this.hash)\n            child = new Tree(child.type, child.children, child.positions, child.length, this.hashProp);\n        this.children.push(child);\n        this.positions.push(pos);\n    }\n    toTree(nodeSet, end = this.end) {\n        let last = this.children.length - 1;\n        if (last >= 0)\n            end = Math.max(end, this.positions[last] + this.children[last].length + this.from);\n        return new Tree(nodeSet.types[this.type], this.children, this.positions, end - this.from).balance({\n            makeTree: (children, positions, length) => new Tree(NodeType.none, children, positions, length, this.hashProp)\n        });\n    }\n}\nvar Type;\n(function (Type) {\n    Type[Type[\"Document\"] = 1] = \"Document\";\n    Type[Type[\"CodeBlock\"] = 2] = \"CodeBlock\";\n    Type[Type[\"FencedCode\"] = 3] = \"FencedCode\";\n    Type[Type[\"Blockquote\"] = 4] = \"Blockquote\";\n    Type[Type[\"HorizontalRule\"] = 5] = \"HorizontalRule\";\n    Type[Type[\"BulletList\"] = 6] = \"BulletList\";\n    Type[Type[\"OrderedList\"] = 7] = \"OrderedList\";\n    Type[Type[\"ListItem\"] = 8] = \"ListItem\";\n    Type[Type[\"ATXHeading1\"] = 9] = \"ATXHeading1\";\n    Type[Type[\"ATXHeading2\"] = 10] = \"ATXHeading2\";\n    Type[Type[\"ATXHeading3\"] = 11] = \"ATXHeading3\";\n    Type[Type[\"ATXHeading4\"] = 12] = \"ATXHeading4\";\n    Type[Type[\"ATXHeading5\"] = 13] = \"ATXHeading5\";\n    Type[Type[\"ATXHeading6\"] = 14] = \"ATXHeading6\";\n    Type[Type[\"SetextHeading1\"] = 15] = \"SetextHeading1\";\n    Type[Type[\"SetextHeading2\"] = 16] = \"SetextHeading2\";\n    Type[Type[\"HTMLBlock\"] = 17] = \"HTMLBlock\";\n    Type[Type[\"LinkReference\"] = 18] = \"LinkReference\";\n    Type[Type[\"Paragraph\"] = 19] = \"Paragraph\";\n    Type[Type[\"CommentBlock\"] = 20] = \"CommentBlock\";\n    Type[Type[\"ProcessingInstructionBlock\"] = 21] = \"ProcessingInstructionBlock\";\n    // Inline\n    Type[Type[\"Escape\"] = 22] = \"Escape\";\n    Type[Type[\"Entity\"] = 23] = \"Entity\";\n    Type[Type[\"HardBreak\"] = 24] = \"HardBreak\";\n    Type[Type[\"Emphasis\"] = 25] = \"Emphasis\";\n    Type[Type[\"StrongEmphasis\"] = 26] = \"StrongEmphasis\";\n    Type[Type[\"Link\"] = 27] = \"Link\";\n    Type[Type[\"Image\"] = 28] = \"Image\";\n    Type[Type[\"InlineCode\"] = 29] = \"InlineCode\";\n    Type[Type[\"HTMLTag\"] = 30] = \"HTMLTag\";\n    Type[Type[\"Comment\"] = 31] = \"Comment\";\n    Type[Type[\"ProcessingInstruction\"] = 32] = \"ProcessingInstruction\";\n    Type[Type[\"Autolink\"] = 33] = \"Autolink\";\n    // Smaller tokens\n    Type[Type[\"HeaderMark\"] = 34] = \"HeaderMark\";\n    Type[Type[\"QuoteMark\"] = 35] = \"QuoteMark\";\n    Type[Type[\"ListMark\"] = 36] = \"ListMark\";\n    Type[Type[\"LinkMark\"] = 37] = \"LinkMark\";\n    Type[Type[\"EmphasisMark\"] = 38] = \"EmphasisMark\";\n    Type[Type[\"CodeMark\"] = 39] = \"CodeMark\";\n    Type[Type[\"CodeText\"] = 40] = \"CodeText\";\n    Type[Type[\"CodeInfo\"] = 41] = \"CodeInfo\";\n    Type[Type[\"LinkTitle\"] = 42] = \"LinkTitle\";\n    Type[Type[\"LinkLabel\"] = 43] = \"LinkLabel\";\n    Type[Type[\"URL\"] = 44] = \"URL\";\n})(Type || (Type = {}));\n/**\nData structure used to accumulate a block's content during [leaf\nblock parsing](#BlockParser.leaf).\n*/\nclass LeafBlock {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The start position of the block.\n    */\n    start, \n    /**\n    The block's text content.\n    */\n    content) {\n        this.start = start;\n        this.content = content;\n        /**\n        @internal\n        */\n        this.marks = [];\n        /**\n        The block parsers active for this block.\n        */\n        this.parsers = [];\n    }\n}\n/**\nData structure used during block-level per-line parsing.\n*/\nclass Line {\n    constructor() {\n        /**\n        The line's full text.\n        */\n        this.text = \"\";\n        /**\n        The base indent provided by the composite contexts (that have\n        been handled so far).\n        */\n        this.baseIndent = 0;\n        /**\n        The string position corresponding to the base indent.\n        */\n        this.basePos = 0;\n        /**\n        The number of contexts handled @internal\n        */\n        this.depth = 0;\n        /**\n        Any markers (i.e. block quote markers) parsed for the contexts. @internal\n        */\n        this.markers = [];\n        /**\n        The position of the next non-whitespace character beyond any\n        list, blockquote, or other composite block markers.\n        */\n        this.pos = 0;\n        /**\n        The column of the next non-whitespace character.\n        */\n        this.indent = 0;\n        /**\n        The character code of the character after `pos`.\n        */\n        this.next = -1;\n    }\n    /**\n    @internal\n    */\n    forward() {\n        if (this.basePos > this.pos)\n            this.forwardInner();\n    }\n    /**\n    @internal\n    */\n    forwardInner() {\n        let newPos = this.skipSpace(this.basePos);\n        this.indent = this.countIndent(newPos, this.pos, this.indent);\n        this.pos = newPos;\n        this.next = newPos == this.text.length ? -1 : this.text.charCodeAt(newPos);\n    }\n    /**\n    Skip whitespace after the given position, return the position of\n    the next non-space character or the end of the line if there's\n    only space after `from`.\n    */\n    skipSpace(from) { return skipSpace(this.text, from); }\n    /**\n    @internal\n    */\n    reset(text) {\n        this.text = text;\n        this.baseIndent = this.basePos = this.pos = this.indent = 0;\n        this.forwardInner();\n        this.depth = 1;\n        while (this.markers.length)\n            this.markers.pop();\n    }\n    /**\n    Move the line's base position forward to the given position.\n    This should only be called by composite [block\n    parsers](#BlockParser.parse) or [markup skipping\n    functions](#NodeSpec.composite).\n    */\n    moveBase(to) {\n        this.basePos = to;\n        this.baseIndent = this.countIndent(to, this.pos, this.indent);\n    }\n    /**\n    Move the line's base position forward to the given _column_.\n    */\n    moveBaseColumn(indent) {\n        this.baseIndent = indent;\n        this.basePos = this.findColumn(indent);\n    }\n    /**\n    Store a composite-block-level marker. Should be called from\n    [markup skipping functions](#NodeSpec.composite) when they\n    consume any non-whitespace characters.\n    */\n    addMarker(elt) {\n        this.markers.push(elt);\n    }\n    /**\n    Find the column position at `to`, optionally starting at a given\n    position and column.\n    */\n    countIndent(to, from = 0, indent = 0) {\n        for (let i = from; i < to; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return indent;\n    }\n    /**\n    Find the position corresponding to the given column.\n    */\n    findColumn(goal) {\n        let i = 0;\n        for (let indent = 0; i < this.text.length && indent < goal; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return i;\n    }\n    /**\n    @internal\n    */\n    scrub() {\n        if (!this.baseIndent)\n            return this.text;\n        let result = \"\";\n        for (let i = 0; i < this.basePos; i++)\n            result += \" \";\n        return result + this.text.slice(this.basePos);\n    }\n}\nfunction skipForList(bl, cx, line) {\n    if (line.pos == line.text.length ||\n        (bl != cx.block && line.indent >= cx.stack[line.depth + 1].value + line.baseIndent))\n        return true;\n    if (line.indent >= line.baseIndent + 4)\n        return false;\n    let size = (bl.type == Type.OrderedList ? isOrderedList : isBulletList)(line, cx, false);\n    return size > 0 &&\n        (bl.type != Type.BulletList || isHorizontalRule(line, cx, false) < 0) &&\n        line.text.charCodeAt(line.pos + size - 1) == bl.value;\n}\nconst DefaultSkipMarkup = {\n    [Type.Blockquote](bl, cx, line) {\n        if (line.next != 62 /* '>' */)\n            return false;\n        line.markers.push(elt(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1));\n        line.moveBase(line.pos + (space(line.text.charCodeAt(line.pos + 1)) ? 2 : 1));\n        bl.end = cx.lineStart + line.text.length;\n        return true;\n    },\n    [Type.ListItem](bl, _cx, line) {\n        if (line.indent < line.baseIndent + bl.value && line.next > -1)\n            return false;\n        line.moveBaseColumn(line.baseIndent + bl.value);\n        return true;\n    },\n    [Type.OrderedList]: skipForList,\n    [Type.BulletList]: skipForList,\n    [Type.Document]() { return true; }\n};\nfunction space(ch) { return ch == 32 || ch == 9 || ch == 10 || ch == 13; }\nfunction skipSpace(line, i = 0) {\n    while (i < line.length && space(line.charCodeAt(i)))\n        i++;\n    return i;\n}\nfunction skipSpaceBack(line, i, to) {\n    while (i > to && space(line.charCodeAt(i - 1)))\n        i--;\n    return i;\n}\nfunction isFencedCode(line) {\n    if (line.next != 96 && line.next != 126 /* '`~' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    if (pos < line.pos + 3)\n        return -1;\n    if (line.next == 96)\n        for (let i = pos; i < line.text.length; i++)\n            if (line.text.charCodeAt(i) == 96)\n                return -1;\n    return pos;\n}\nfunction isBlockquote(line) {\n    return line.next != 62 /* '>' */ ? -1 : line.text.charCodeAt(line.pos + 1) == 32 ? 2 : 1;\n}\nfunction isHorizontalRule(line, cx, breaking) {\n    if (line.next != 42 && line.next != 45 && line.next != 95 /* '_-*' */)\n        return -1;\n    let count = 1;\n    for (let pos = line.pos + 1; pos < line.text.length; pos++) {\n        let ch = line.text.charCodeAt(pos);\n        if (ch == line.next)\n            count++;\n        else if (!space(ch))\n            return -1;\n    }\n    // Setext headers take precedence\n    if (breaking && line.next == 45 && isSetextUnderline(line) > -1 && line.depth == cx.stack.length &&\n        cx.parser.leafBlockParsers.indexOf(DefaultLeafBlocks.SetextHeading) > -1)\n        return -1;\n    return count < 3 ? -1 : 1;\n}\nfunction inList(cx, type) {\n    for (let i = cx.stack.length - 1; i >= 0; i--)\n        if (cx.stack[i].type == type)\n            return true;\n    return false;\n}\nfunction isBulletList(line, cx, breaking) {\n    return (line.next == 45 || line.next == 43 || line.next == 42 /* '-+*' */) &&\n        (line.pos == line.text.length - 1 || space(line.text.charCodeAt(line.pos + 1))) &&\n        (!breaking || inList(cx, Type.BulletList) || line.skipSpace(line.pos + 2) < line.text.length) ? 1 : -1;\n}\nfunction isOrderedList(line, cx, breaking) {\n    let pos = line.pos, next = line.next;\n    for (;;) {\n        if (next >= 48 && next <= 57 /* '0-9' */)\n            pos++;\n        else\n            break;\n        if (pos == line.text.length)\n            return -1;\n        next = line.text.charCodeAt(pos);\n    }\n    if (pos == line.pos || pos > line.pos + 9 ||\n        (next != 46 && next != 41 /* '.)' */) ||\n        (pos < line.text.length - 1 && !space(line.text.charCodeAt(pos + 1))) ||\n        breaking && !inList(cx, Type.OrderedList) &&\n            (line.skipSpace(pos + 1) == line.text.length || pos > line.pos + 1 || line.next != 49 /* '1' */))\n        return -1;\n    return pos + 1 - line.pos;\n}\nfunction isAtxHeading(line) {\n    if (line.next != 35 /* '#' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == 35)\n        pos++;\n    if (pos < line.text.length && line.text.charCodeAt(pos) != 32)\n        return -1;\n    let size = pos - line.pos;\n    return size > 6 ? -1 : size;\n}\nfunction isSetextUnderline(line) {\n    if (line.next != 45 && line.next != 61 /* '-=' */ || line.indent >= line.baseIndent + 4)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    let end = pos;\n    while (pos < line.text.length && space(line.text.charCodeAt(pos)))\n        pos++;\n    return pos == line.text.length ? end : -1;\n}\nconst EmptyLine = /^[ \\t]*$/, CommentEnd = /-->/, ProcessingEnd = /\\?>/;\nconst HTMLBlockStyle = [\n    [/^<(?:script|pre|style)(?:\\s|>|$)/i, /<\\/(?:script|pre|style)>/i],\n    [/^\\s*<!--/, CommentEnd],\n    [/^\\s*<\\?/, ProcessingEnd],\n    [/^\\s*<![A-Z]/, />/],\n    [/^\\s*<!\\[CDATA\\[/, /\\]\\]>/],\n    [/^\\s*<\\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\\s|\\/?>|$)/i, EmptyLine],\n    [/^\\s*(?:<\\/[a-z][\\w-]*\\s*>|<[a-z][\\w-]*(\\s+[a-z:_][\\w-.]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*>)\\s*$/i, EmptyLine]\n];\nfunction isHTMLBlock(line, _cx, breaking) {\n    if (line.next != 60 /* '<' */)\n        return -1;\n    let rest = line.text.slice(line.pos);\n    for (let i = 0, e = HTMLBlockStyle.length - (breaking ? 1 : 0); i < e; i++)\n        if (HTMLBlockStyle[i][0].test(rest))\n            return i;\n    return -1;\n}\nfunction getListIndent(line, pos) {\n    let indentAfter = line.countIndent(pos, line.pos, line.indent);\n    let indented = line.countIndent(line.skipSpace(pos), pos, indentAfter);\n    return indented >= indentAfter + 5 ? indentAfter + 1 : indented;\n}\nfunction addCodeText(marks, from, to) {\n    let last = marks.length - 1;\n    if (last >= 0 && marks[last].to == from && marks[last].type == Type.CodeText)\n        marks[last].to = to;\n    else\n        marks.push(elt(Type.CodeText, from, to));\n}\n// Rules for parsing blocks. A return value of false means the rule\n// doesn't apply here, true means it does. When true is returned and\n// `p.line` has been updated, the rule is assumed to have consumed a\n// leaf block. Otherwise, it is assumed to have opened a context.\nconst DefaultBlockParsers = {\n    LinkReference: undefined,\n    IndentedCode(cx, line) {\n        let base = line.baseIndent + 4;\n        if (line.indent < base)\n            return false;\n        let start = line.findColumn(base);\n        let from = cx.lineStart + start, to = cx.lineStart + line.text.length;\n        let marks = [], pendingMarks = [];\n        addCodeText(marks, from, to);\n        while (cx.nextLine() && line.depth >= cx.stack.length) {\n            if (line.pos == line.text.length) { // Empty\n                addCodeText(pendingMarks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    pendingMarks.push(m);\n            }\n            else if (line.indent < base) {\n                break;\n            }\n            else {\n                if (pendingMarks.length) {\n                    for (let m of pendingMarks) {\n                        if (m.type == Type.CodeText)\n                            addCodeText(marks, m.from, m.to);\n                        else\n                            marks.push(m);\n                    }\n                    pendingMarks = [];\n                }\n                addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                to = cx.lineStart + line.text.length;\n                let codeStart = cx.lineStart + line.findColumn(line.baseIndent + 4);\n                if (codeStart < to)\n                    addCodeText(marks, codeStart, to);\n            }\n        }\n        if (pendingMarks.length) {\n            pendingMarks = pendingMarks.filter(m => m.type != Type.CodeText);\n            if (pendingMarks.length)\n                line.markers = pendingMarks.concat(line.markers);\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(Type.CodeBlock, to - from), from);\n        return true;\n    },\n    FencedCode(cx, line) {\n        let fenceEnd = isFencedCode(line);\n        if (fenceEnd < 0)\n            return false;\n        let from = cx.lineStart + line.pos, ch = line.next, len = fenceEnd - line.pos;\n        let infoFrom = line.skipSpace(fenceEnd), infoTo = skipSpaceBack(line.text, line.text.length, infoFrom);\n        let marks = [elt(Type.CodeMark, from, from + len)];\n        if (infoFrom < infoTo)\n            marks.push(elt(Type.CodeInfo, cx.lineStart + infoFrom, cx.lineStart + infoTo));\n        for (let first = true; cx.nextLine() && line.depth >= cx.stack.length; first = false) {\n            let i = line.pos;\n            if (line.indent - line.baseIndent < 4)\n                while (i < line.text.length && line.text.charCodeAt(i) == ch)\n                    i++;\n            if (i - line.pos >= len && line.skipSpace(i) == line.text.length) {\n                for (let m of line.markers)\n                    marks.push(m);\n                marks.push(elt(Type.CodeMark, cx.lineStart + line.pos, cx.lineStart + i));\n                cx.nextLine();\n                break;\n            }\n            else {\n                if (!first)\n                    addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                let textStart = cx.lineStart + line.basePos, textEnd = cx.lineStart + line.text.length;\n                if (textStart < textEnd)\n                    addCodeText(marks, textStart, textEnd);\n            }\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from)\n            .finish(Type.FencedCode, cx.prevLineEnd() - from), from);\n        return true;\n    },\n    Blockquote(cx, line) {\n        let size = isBlockquote(line);\n        if (size < 0)\n            return false;\n        cx.startContext(Type.Blockquote, line.pos);\n        cx.addNode(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1);\n        line.moveBase(line.pos + size);\n        return null;\n    },\n    HorizontalRule(cx, line) {\n        if (isHorizontalRule(line, cx, false) < 0)\n            return false;\n        let from = cx.lineStart + line.pos;\n        cx.nextLine();\n        cx.addNode(Type.HorizontalRule, from);\n        return true;\n    },\n    BulletList(cx, line) {\n        let size = isBulletList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.BulletList)\n            cx.startContext(Type.BulletList, line.basePos, line.next);\n        let newBase = getListIndent(line, line.pos + 1);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    OrderedList(cx, line) {\n        let size = isOrderedList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.OrderedList)\n            cx.startContext(Type.OrderedList, line.basePos, line.text.charCodeAt(line.pos + size - 1));\n        let newBase = getListIndent(line, line.pos + size);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    ATXHeading(cx, line) {\n        let size = isAtxHeading(line);\n        if (size < 0)\n            return false;\n        let off = line.pos, from = cx.lineStart + off;\n        let endOfSpace = skipSpaceBack(line.text, line.text.length, off), after = endOfSpace;\n        while (after > off && line.text.charCodeAt(after - 1) == line.next)\n            after--;\n        if (after == endOfSpace || after == off || !space(line.text.charCodeAt(after - 1)))\n            after = line.text.length;\n        let buf = cx.buffer\n            .write(Type.HeaderMark, 0, size)\n            .writeElements(cx.parser.parseInline(line.text.slice(off + size + 1, after), from + size + 1), -from);\n        if (after < line.text.length)\n            buf.write(Type.HeaderMark, after - off, endOfSpace - off);\n        let node = buf.finish(Type.ATXHeading1 - 1 + size, line.text.length - off);\n        cx.nextLine();\n        cx.addNode(node, from);\n        return true;\n    },\n    HTMLBlock(cx, line) {\n        let type = isHTMLBlock(line, cx, false);\n        if (type < 0)\n            return false;\n        let from = cx.lineStart + line.pos, end = HTMLBlockStyle[type][1];\n        let marks = [], trailing = end != EmptyLine;\n        while (!end.test(line.text) && cx.nextLine()) {\n            if (line.depth < cx.stack.length) {\n                trailing = false;\n                break;\n            }\n            for (let m of line.markers)\n                marks.push(m);\n        }\n        if (trailing)\n            cx.nextLine();\n        let nodeType = end == CommentEnd ? Type.CommentBlock : end == ProcessingEnd ? Type.ProcessingInstructionBlock : Type.HTMLBlock;\n        let to = cx.prevLineEnd();\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(nodeType, to - from), from);\n        return true;\n    },\n    SetextHeading: undefined // Specifies relative precedence for block-continue function\n};\n// This implements a state machine that incrementally parses link references. At each\n// next line, it looks ahead to see if the line continues the reference or not. If it\n// doesn't and a valid link is available ending before that line, it finishes that.\n// Similarly, on `finish` (when the leaf is terminated by external circumstances), it\n// creates a link reference if there's a valid reference up to the current point.\nclass LinkReferenceParser {\n    constructor(leaf) {\n        this.stage = 0 /* RefStage.Start */;\n        this.elts = [];\n        this.pos = 0;\n        this.start = leaf.start;\n        this.advance(leaf.content);\n    }\n    nextLine(cx, line, leaf) {\n        if (this.stage == -1 /* RefStage.Failed */)\n            return false;\n        let content = leaf.content + \"\\n\" + line.scrub();\n        let finish = this.advance(content);\n        if (finish > -1 && finish < content.length)\n            return this.complete(cx, leaf, finish);\n        return false;\n    }\n    finish(cx, leaf) {\n        if ((this.stage == 2 /* RefStage.Link */ || this.stage == 3 /* RefStage.Title */) && skipSpace(leaf.content, this.pos) == leaf.content.length)\n            return this.complete(cx, leaf, leaf.content.length);\n        return false;\n    }\n    complete(cx, leaf, len) {\n        cx.addLeafElement(leaf, elt(Type.LinkReference, this.start, this.start + len, this.elts));\n        return true;\n    }\n    nextStage(elt) {\n        if (elt) {\n            this.pos = elt.to - this.start;\n            this.elts.push(elt);\n            this.stage++;\n            return true;\n        }\n        if (elt === false)\n            this.stage = -1 /* RefStage.Failed */;\n        return false;\n    }\n    advance(content) {\n        for (;;) {\n            if (this.stage == -1 /* RefStage.Failed */) {\n                return -1;\n            }\n            else if (this.stage == 0 /* RefStage.Start */) {\n                if (!this.nextStage(parseLinkLabel(content, this.pos, this.start, true)))\n                    return -1;\n                if (content.charCodeAt(this.pos) != 58 /* ':' */)\n                    return this.stage = -1 /* RefStage.Failed */;\n                this.elts.push(elt(Type.LinkMark, this.pos + this.start, this.pos + this.start + 1));\n                this.pos++;\n            }\n            else if (this.stage == 1 /* RefStage.Label */) {\n                if (!this.nextStage(parseURL(content, skipSpace(content, this.pos), this.start)))\n                    return -1;\n            }\n            else if (this.stage == 2 /* RefStage.Link */) {\n                let skip = skipSpace(content, this.pos), end = 0;\n                if (skip > this.pos) {\n                    let title = parseLinkTitle(content, skip, this.start);\n                    if (title) {\n                        let titleEnd = lineEnd(content, title.to - this.start);\n                        if (titleEnd > 0) {\n                            this.nextStage(title);\n                            end = titleEnd;\n                        }\n                    }\n                }\n                if (!end)\n                    end = lineEnd(content, this.pos);\n                return end > 0 && end < content.length ? end : -1;\n            }\n            else { // RefStage.Title\n                return lineEnd(content, this.pos);\n            }\n        }\n    }\n}\nfunction lineEnd(text, pos) {\n    for (; pos < text.length; pos++) {\n        let next = text.charCodeAt(pos);\n        if (next == 10)\n            break;\n        if (!space(next))\n            return -1;\n    }\n    return pos;\n}\nclass SetextHeadingParser {\n    nextLine(cx, line, leaf) {\n        let underline = line.depth < cx.stack.length ? -1 : isSetextUnderline(line);\n        let next = line.next;\n        if (underline < 0)\n            return false;\n        let underlineMark = elt(Type.HeaderMark, cx.lineStart + line.pos, cx.lineStart + underline);\n        cx.nextLine();\n        cx.addLeafElement(leaf, elt(next == 61 ? Type.SetextHeading1 : Type.SetextHeading2, leaf.start, cx.prevLineEnd(), [\n            ...cx.parser.parseInline(leaf.content, leaf.start),\n            underlineMark\n        ]));\n        return true;\n    }\n    finish() {\n        return false;\n    }\n}\nconst DefaultLeafBlocks = {\n    LinkReference(_, leaf) { return leaf.content.charCodeAt(0) == 91 /* '[' */ ? new LinkReferenceParser(leaf) : null; },\n    SetextHeading() { return new SetextHeadingParser; }\n};\nconst DefaultEndLeaf = [\n    (_, line) => isAtxHeading(line) >= 0,\n    (_, line) => isFencedCode(line) >= 0,\n    (_, line) => isBlockquote(line) >= 0,\n    (p, line) => isBulletList(line, p, true) >= 0,\n    (p, line) => isOrderedList(line, p, true) >= 0,\n    (p, line) => isHorizontalRule(line, p, true) >= 0,\n    (p, line) => isHTMLBlock(line, p, true) >= 0\n];\nconst scanLineResult = { text: \"\", end: 0 };\n/**\nBlock-level parsing functions get access to this context object.\n*/\nclass BlockContext {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser configuration used.\n    */\n    parser, \n    /**\n    @internal\n    */\n    input, fragments, \n    /**\n    @internal\n    */\n    ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.line = new Line();\n        this.atEnd = false;\n        /**\n        For reused nodes on gaps, we can't directly put the original\n        node into the tree, since that may be bigger than its parent.\n        When this happens, we create a dummy tree that is replaced by\n        the proper node in `injectGaps` @internal\n        */\n        this.reusePlaceholders = new Map;\n        this.stoppedAt = null;\n        /**\n        The range index that absoluteLineStart points into @internal\n        */\n        this.rangeI = 0;\n        this.to = ranges[ranges.length - 1].to;\n        this.lineStart = this.absoluteLineStart = this.absoluteLineEnd = ranges[0].from;\n        this.block = CompositeBlock.create(Type.Document, 0, this.lineStart, 0, 0);\n        this.stack = [this.block];\n        this.fragments = fragments.length ? new FragmentCursor(fragments, input) : null;\n        this.readLine();\n    }\n    get parsedPos() {\n        return this.absoluteLineStart;\n    }\n    advance() {\n        if (this.stoppedAt != null && this.absoluteLineStart > this.stoppedAt)\n            return this.finish();\n        let { line } = this;\n        for (;;) {\n            for (let markI = 0;;) {\n                let next = line.depth < this.stack.length ? this.stack[this.stack.length - 1] : null;\n                while (markI < line.markers.length && (!next || line.markers[markI].from < next.end)) {\n                    let mark = line.markers[markI++];\n                    this.addNode(mark.type, mark.from, mark.to);\n                }\n                if (!next)\n                    break;\n                this.finishContext();\n            }\n            if (line.pos < line.text.length)\n                break;\n            // Empty line\n            if (!this.nextLine())\n                return this.finish();\n        }\n        if (this.fragments && this.reuseFragment(line.basePos))\n            return null;\n        start: for (;;) {\n            for (let type of this.parser.blockParsers)\n                if (type) {\n                    let result = type(this, line);\n                    if (result != false) {\n                        if (result == true)\n                            return null;\n                        line.forward();\n                        continue start;\n                    }\n                }\n            break;\n        }\n        let leaf = new LeafBlock(this.lineStart + line.pos, line.text.slice(line.pos));\n        for (let parse of this.parser.leafBlockParsers)\n            if (parse) {\n                let parser = parse(this, leaf);\n                if (parser)\n                    leaf.parsers.push(parser);\n            }\n        lines: while (this.nextLine()) {\n            if (line.pos == line.text.length)\n                break;\n            if (line.indent < line.baseIndent + 4) {\n                for (let stop of this.parser.endLeafBlock)\n                    if (stop(this, line, leaf))\n                        break lines;\n            }\n            for (let parser of leaf.parsers)\n                if (parser.nextLine(this, line, leaf))\n                    return null;\n            leaf.content += \"\\n\" + line.scrub();\n            for (let m of line.markers)\n                leaf.marks.push(m);\n        }\n        this.finishLeaf(leaf);\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    reuseFragment(start) {\n        if (!this.fragments.moveTo(this.absoluteLineStart + start, this.absoluteLineStart) ||\n            !this.fragments.matches(this.block.hash))\n            return false;\n        let taken = this.fragments.takeNodes(this);\n        if (!taken)\n            return false;\n        this.absoluteLineStart += taken;\n        this.lineStart = toRelative(this.absoluteLineStart, this.ranges);\n        this.moveRangeI();\n        if (this.absoluteLineStart < this.to) {\n            this.lineStart++;\n            this.absoluteLineStart++;\n            this.readLine();\n        }\n        else {\n            this.atEnd = true;\n            this.readLine();\n        }\n        return true;\n    }\n    /**\n    The number of parent blocks surrounding the current block.\n    */\n    get depth() {\n        return this.stack.length;\n    }\n    /**\n    Get the type of the parent block at the given depth. When no\n    depth is passed, return the type of the innermost parent.\n    */\n    parentType(depth = this.depth - 1) {\n        return this.parser.nodeSet.types[this.stack[depth].type];\n    }\n    /**\n    Move to the next input line. This should only be called by\n    (non-composite) [block parsers](#BlockParser.parse) that consume\n    the line directly, or leaf block parser\n    [`nextLine`](#LeafBlockParser.nextLine) methods when they\n    consume the current line (and return true).\n    */\n    nextLine() {\n        this.lineStart += this.line.text.length;\n        if (this.absoluteLineEnd >= this.to) {\n            this.absoluteLineStart = this.absoluteLineEnd;\n            this.atEnd = true;\n            this.readLine();\n            return false;\n        }\n        else {\n            this.lineStart++;\n            this.absoluteLineStart = this.absoluteLineEnd + 1;\n            this.moveRangeI();\n            this.readLine();\n            return true;\n        }\n    }\n    /**\n    Retrieve the text of the line after the current one, without\n    actually moving the context's current line forward.\n    */\n    peekLine() {\n        return this.scanLine(this.absoluteLineEnd + 1).text;\n    }\n    moveRangeI() {\n        while (this.rangeI < this.ranges.length - 1 && this.absoluteLineStart >= this.ranges[this.rangeI].to) {\n            this.rangeI++;\n            this.absoluteLineStart = Math.max(this.absoluteLineStart, this.ranges[this.rangeI].from);\n        }\n    }\n    /**\n    @internal\n    Collect the text for the next line.\n    */\n    scanLine(start) {\n        let r = scanLineResult;\n        r.end = start;\n        if (start >= this.to) {\n            r.text = \"\";\n        }\n        else {\n            r.text = this.lineChunkAt(start);\n            r.end += r.text.length;\n            if (this.ranges.length > 1) {\n                let textOffset = this.absoluteLineStart, rangeI = this.rangeI;\n                while (this.ranges[rangeI].to < r.end) {\n                    rangeI++;\n                    let nextFrom = this.ranges[rangeI].from;\n                    let after = this.lineChunkAt(nextFrom);\n                    r.end = nextFrom + after.length;\n                    r.text = r.text.slice(0, this.ranges[rangeI - 1].to - textOffset) + after;\n                    textOffset = r.end - r.text.length;\n                }\n            }\n        }\n        return r;\n    }\n    /**\n    @internal\n    Populate this.line with the content of the next line. Skip\n    leading characters covered by composite blocks.\n    */\n    readLine() {\n        let { line } = this, { text, end } = this.scanLine(this.absoluteLineStart);\n        this.absoluteLineEnd = end;\n        line.reset(text);\n        for (; line.depth < this.stack.length; line.depth++) {\n            let cx = this.stack[line.depth], handler = this.parser.skipContextMarkup[cx.type];\n            if (!handler)\n                throw new Error(\"Unhandled block context \" + Type[cx.type]);\n            if (!handler(cx, this, line))\n                break;\n            line.forward();\n        }\n    }\n    lineChunkAt(pos) {\n        let next = this.input.chunk(pos), text;\n        if (!this.input.lineChunks) {\n            let eol = next.indexOf(\"\\n\");\n            text = eol < 0 ? next : next.slice(0, eol);\n        }\n        else {\n            text = next == \"\\n\" ? \"\" : next;\n        }\n        return pos + text.length > this.to ? text.slice(0, this.to - pos) : text;\n    }\n    /**\n    The end position of the previous line.\n    */\n    prevLineEnd() { return this.atEnd ? this.lineStart : this.lineStart - 1; }\n    /**\n    @internal\n    */\n    startContext(type, start, value = 0) {\n        this.block = CompositeBlock.create(type, value, this.lineStart + start, this.block.hash, this.lineStart + this.line.text.length);\n        this.stack.push(this.block);\n    }\n    /**\n    Start a composite block. Should only be called from [block\n    parser functions](#BlockParser.parse) that return null.\n    */\n    startComposite(type, start, value = 0) {\n        this.startContext(this.parser.getNodeType(type), start, value);\n    }\n    /**\n    @internal\n    */\n    addNode(block, from, to) {\n        if (typeof block == \"number\")\n            block = new Tree(this.parser.nodeSet.types[block], none, none, (to !== null && to !== void 0 ? to : this.prevLineEnd()) - from);\n        this.block.addChild(block, from - this.block.from);\n    }\n    /**\n    Add a block element. Can be called by [block\n    parsers](#BlockParser.parse).\n    */\n    addElement(elt) {\n        this.block.addChild(elt.toTree(this.parser.nodeSet), elt.from - this.block.from);\n    }\n    /**\n    Add a block element from a [leaf parser](#LeafBlockParser). This\n    makes sure any extra composite block markup (such as blockquote\n    markers) inside the block are also added to the syntax tree.\n    */\n    addLeafElement(leaf, elt) {\n        this.addNode(this.buffer\n            .writeElements(injectMarks(elt.children, leaf.marks), -elt.from)\n            .finish(elt.type, elt.to - elt.from), elt.from);\n    }\n    /**\n    @internal\n    */\n    finishContext() {\n        let cx = this.stack.pop();\n        let top = this.stack[this.stack.length - 1];\n        top.addChild(cx.toTree(this.parser.nodeSet), cx.from - top.from);\n        this.block = top;\n    }\n    finish() {\n        while (this.stack.length > 1)\n            this.finishContext();\n        return this.addGaps(this.block.toTree(this.parser.nodeSet, this.lineStart));\n    }\n    addGaps(tree) {\n        return this.ranges.length > 1 ?\n            injectGaps(this.ranges, 0, tree.topNode, this.ranges[0].from, this.reusePlaceholders) : tree;\n    }\n    /**\n    @internal\n    */\n    finishLeaf(leaf) {\n        for (let parser of leaf.parsers)\n            if (parser.finish(this, leaf))\n                return;\n        let inline = injectMarks(this.parser.parseInline(leaf.content, leaf.start), leaf.marks);\n        this.addNode(this.buffer\n            .writeElements(inline, -leaf.start)\n            .finish(Type.Paragraph, leaf.content.length), leaf.start);\n    }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n    /**\n    @internal\n    */\n    get buffer() { return new Buffer(this.parser.nodeSet); }\n}\nfunction injectGaps(ranges, rangeI, tree, offset, dummies) {\n    let rangeEnd = ranges[rangeI].to;\n    let children = [], positions = [], start = tree.from + offset;\n    function movePastNext(upto, inclusive) {\n        while (inclusive ? upto >= rangeEnd : upto > rangeEnd) {\n            let size = ranges[rangeI + 1].from - rangeEnd;\n            offset += size;\n            upto += size;\n            rangeI++;\n            rangeEnd = ranges[rangeI].to;\n        }\n    }\n    for (let ch = tree.firstChild; ch; ch = ch.nextSibling) {\n        movePastNext(ch.from + offset, true);\n        let from = ch.from + offset, node, reuse = dummies.get(ch.tree);\n        if (reuse) {\n            node = reuse;\n        }\n        else if (ch.to + offset > rangeEnd) {\n            node = injectGaps(ranges, rangeI, ch, offset, dummies);\n            movePastNext(ch.to + offset, false);\n        }\n        else {\n            node = ch.toTree();\n        }\n        children.push(node);\n        positions.push(from - start);\n    }\n    movePastNext(tree.to + offset, false);\n    return new Tree(tree.type, children, positions, tree.to + offset - start, tree.tree ? tree.tree.propValues : undefined);\n}\n/**\nA Markdown parser configuration.\n*/\nclass MarkdownParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser's syntax [node\n    types](https://lezer.codemirror.net/docs/ref/#common.NodeSet).\n    */\n    nodeSet, \n    /**\n    @internal\n    */\n    blockParsers, \n    /**\n    @internal\n    */\n    leafBlockParsers, \n    /**\n    @internal\n    */\n    blockNames, \n    /**\n    @internal\n    */\n    endLeafBlock, \n    /**\n    @internal\n    */\n    skipContextMarkup, \n    /**\n    @internal\n    */\n    inlineParsers, \n    /**\n    @internal\n    */\n    inlineNames, \n    /**\n    @internal\n    */\n    wrappers) {\n        super();\n        this.nodeSet = nodeSet;\n        this.blockParsers = blockParsers;\n        this.leafBlockParsers = leafBlockParsers;\n        this.blockNames = blockNames;\n        this.endLeafBlock = endLeafBlock;\n        this.skipContextMarkup = skipContextMarkup;\n        this.inlineParsers = inlineParsers;\n        this.inlineNames = inlineNames;\n        this.wrappers = wrappers;\n        /**\n        @internal\n        */\n        this.nodeTypes = Object.create(null);\n        for (let t of nodeSet.types)\n            this.nodeTypes[t.name] = t.id;\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new BlockContext(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Reconfigure the parser.\n    */\n    configure(spec) {\n        let config = resolveConfig(spec);\n        if (!config)\n            return this;\n        let { nodeSet, skipContextMarkup } = this;\n        let blockParsers = this.blockParsers.slice(), leafBlockParsers = this.leafBlockParsers.slice(), blockNames = this.blockNames.slice(), inlineParsers = this.inlineParsers.slice(), inlineNames = this.inlineNames.slice(), endLeafBlock = this.endLeafBlock.slice(), wrappers = this.wrappers;\n        if (nonEmpty(config.defineNodes)) {\n            skipContextMarkup = Object.assign({}, skipContextMarkup);\n            let nodeTypes = nodeSet.types.slice(), styles;\n            for (let s of config.defineNodes) {\n                let { name, block, composite, style } = typeof s == \"string\" ? { name: s } : s;\n                if (nodeTypes.some(t => t.name == name))\n                    continue;\n                if (composite)\n                    skipContextMarkup[nodeTypes.length] =\n                        (bl, cx, line) => composite(cx, line, bl.value);\n                let id = nodeTypes.length;\n                let group = composite ? [\"Block\", \"BlockContext\"] : !block ? undefined\n                    : id >= Type.ATXHeading1 && id <= Type.SetextHeading2 ? [\"Block\", \"LeafBlock\", \"Heading\"] : [\"Block\", \"LeafBlock\"];\n                nodeTypes.push(NodeType.define({\n                    id,\n                    name,\n                    props: group && [[NodeProp.group, group]]\n                }));\n                if (style) {\n                    if (!styles)\n                        styles = {};\n                    if (Array.isArray(style) || style instanceof Tag)\n                        styles[name] = style;\n                    else\n                        Object.assign(styles, style);\n                }\n            }\n            nodeSet = new NodeSet(nodeTypes);\n            if (styles)\n                nodeSet = nodeSet.extend(styleTags(styles));\n        }\n        if (nonEmpty(config.props))\n            nodeSet = nodeSet.extend(...config.props);\n        if (nonEmpty(config.remove)) {\n            for (let rm of config.remove) {\n                let block = this.blockNames.indexOf(rm), inline = this.inlineNames.indexOf(rm);\n                if (block > -1)\n                    blockParsers[block] = leafBlockParsers[block] = undefined;\n                if (inline > -1)\n                    inlineParsers[inline] = undefined;\n            }\n        }\n        if (nonEmpty(config.parseBlock)) {\n            for (let spec of config.parseBlock) {\n                let found = blockNames.indexOf(spec.name);\n                if (found > -1) {\n                    blockParsers[found] = spec.parse;\n                    leafBlockParsers[found] = spec.leaf;\n                }\n                else {\n                    let pos = spec.before ? findName(blockNames, spec.before)\n                        : spec.after ? findName(blockNames, spec.after) + 1 : blockNames.length - 1;\n                    blockParsers.splice(pos, 0, spec.parse);\n                    leafBlockParsers.splice(pos, 0, spec.leaf);\n                    blockNames.splice(pos, 0, spec.name);\n                }\n                if (spec.endLeaf)\n                    endLeafBlock.push(spec.endLeaf);\n            }\n        }\n        if (nonEmpty(config.parseInline)) {\n            for (let spec of config.parseInline) {\n                let found = inlineNames.indexOf(spec.name);\n                if (found > -1) {\n                    inlineParsers[found] = spec.parse;\n                }\n                else {\n                    let pos = spec.before ? findName(inlineNames, spec.before)\n                        : spec.after ? findName(inlineNames, spec.after) + 1 : inlineNames.length - 1;\n                    inlineParsers.splice(pos, 0, spec.parse);\n                    inlineNames.splice(pos, 0, spec.name);\n                }\n            }\n        }\n        if (config.wrap)\n            wrappers = wrappers.concat(config.wrap);\n        return new MarkdownParser(nodeSet, blockParsers, leafBlockParsers, blockNames, endLeafBlock, skipContextMarkup, inlineParsers, inlineNames, wrappers);\n    }\n    /**\n    @internal\n    */\n    getNodeType(name) {\n        let found = this.nodeTypes[name];\n        if (found == null)\n            throw new RangeError(`Unknown node type '${name}'`);\n        return found;\n    }\n    /**\n    Parse the given piece of inline text at the given offset,\n    returning an array of [`Element`](#Element) objects representing\n    the inline content.\n    */\n    parseInline(text, offset) {\n        let cx = new InlineContext(this, text, offset);\n        outer: for (let pos = offset; pos < cx.end;) {\n            let next = cx.char(pos);\n            for (let token of this.inlineParsers)\n                if (token) {\n                    let result = token(cx, next, pos);\n                    if (result >= 0) {\n                        pos = result;\n                        continue outer;\n                    }\n                }\n            pos++;\n        }\n        return cx.resolveMarkers(0);\n    }\n}\nfunction nonEmpty(a) {\n    return a != null && a.length > 0;\n}\nfunction resolveConfig(spec) {\n    if (!Array.isArray(spec))\n        return spec;\n    if (spec.length == 0)\n        return null;\n    let conf = resolveConfig(spec[0]);\n    if (spec.length == 1)\n        return conf;\n    let rest = resolveConfig(spec.slice(1));\n    if (!rest || !conf)\n        return conf || rest;\n    let conc = (a, b) => (a || none).concat(b || none);\n    let wrapA = conf.wrap, wrapB = rest.wrap;\n    return {\n        props: conc(conf.props, rest.props),\n        defineNodes: conc(conf.defineNodes, rest.defineNodes),\n        parseBlock: conc(conf.parseBlock, rest.parseBlock),\n        parseInline: conc(conf.parseInline, rest.parseInline),\n        remove: conc(conf.remove, rest.remove),\n        wrap: !wrapA ? wrapB : !wrapB ? wrapA :\n            (inner, input, fragments, ranges) => wrapA(wrapB(inner, input, fragments, ranges), input, fragments, ranges)\n    };\n}\nfunction findName(names, name) {\n    let found = names.indexOf(name);\n    if (found < 0)\n        throw new RangeError(`Position specified relative to unknown parser ${name}`);\n    return found;\n}\nlet nodeTypes = [NodeType.none];\nfor (let i = 1, name; name = Type[i]; i++) {\n    nodeTypes[i] = NodeType.define({\n        id: i,\n        name,\n        props: i >= Type.Escape ? [] : [[NodeProp.group, i in DefaultSkipMarkup ? [\"Block\", \"BlockContext\"] : [\"Block\", \"LeafBlock\"]]],\n        top: name == \"Document\"\n    });\n}\nconst none = [];\nclass Buffer {\n    constructor(nodeSet) {\n        this.nodeSet = nodeSet;\n        this.content = [];\n        this.nodes = [];\n    }\n    write(type, from, to, children = 0) {\n        this.content.push(type, from, to, 4 + children * 4);\n        return this;\n    }\n    writeElements(elts, offset = 0) {\n        for (let e of elts)\n            e.writeTo(this, offset);\n        return this;\n    }\n    finish(type, length) {\n        return Tree.build({\n            buffer: this.content,\n            nodeSet: this.nodeSet,\n            reused: this.nodes,\n            topID: type,\n            length\n        });\n    }\n}\n/**\nElements are used to compose syntax nodes during parsing.\n*/\nclass Element {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The node's\n    [id](https://lezer.codemirror.net/docs/ref/#common.NodeType.id).\n    */\n    type, \n    /**\n    The start of the node, as an offset from the start of the document.\n    */\n    from, \n    /**\n    The end of the node.\n    */\n    to, \n    /**\n    The node's child nodes @internal\n    */\n    children = none) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.children = children;\n    }\n    /**\n    @internal\n    */\n    writeTo(buf, offset) {\n        let startOff = buf.content.length;\n        buf.writeElements(this.children, offset);\n        buf.content.push(this.type, this.from + offset, this.to + offset, buf.content.length + 4 - startOff);\n    }\n    /**\n    @internal\n    */\n    toTree(nodeSet) {\n        return new Buffer(nodeSet).writeElements(this.children, -this.from).finish(this.type, this.to - this.from);\n    }\n}\nclass TreeElement {\n    constructor(tree, from) {\n        this.tree = tree;\n        this.from = from;\n    }\n    get to() { return this.from + this.tree.length; }\n    get type() { return this.tree.type.id; }\n    get children() { return none; }\n    writeTo(buf, offset) {\n        buf.nodes.push(this.tree);\n        buf.content.push(buf.nodes.length - 1, this.from + offset, this.to + offset, -1);\n    }\n    toTree() { return this.tree; }\n}\nfunction elt(type, from, to, children) {\n    return new Element(type, from, to, children);\n}\nconst EmphasisUnderscore = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst EmphasisAsterisk = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst LinkStart = {}, ImageStart = {};\nclass InlineDelimiter {\n    constructor(type, from, to, side) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.side = side;\n    }\n}\nconst Escapable = \"!\\\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~\";\nlet Punctuation = /[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~\\xA1\\u2010-\\u2027]/;\ntry {\n    Punctuation = new RegExp(\"[\\\\p{S}|\\\\p{P}]\", \"u\");\n}\ncatch (_) { }\nconst DefaultInline = {\n    Escape(cx, next, start) {\n        if (next != 92 /* '\\\\' */ || start == cx.end - 1)\n            return -1;\n        let escaped = cx.char(start + 1);\n        for (let i = 0; i < Escapable.length; i++)\n            if (Escapable.charCodeAt(i) == escaped)\n                return cx.append(elt(Type.Escape, start, start + 2));\n        return -1;\n    },\n    Entity(cx, next, start) {\n        if (next != 38 /* '&' */)\n            return -1;\n        let m = /^(?:#\\d+|#x[a-f\\d]+|\\w+);/i.exec(cx.slice(start + 1, start + 31));\n        return m ? cx.append(elt(Type.Entity, start, start + 1 + m[0].length)) : -1;\n    },\n    InlineCode(cx, next, start) {\n        if (next != 96 /* '`' */ || start && cx.char(start - 1) == 96)\n            return -1;\n        let pos = start + 1;\n        while (pos < cx.end && cx.char(pos) == 96)\n            pos++;\n        let size = pos - start, curSize = 0;\n        for (; pos < cx.end; pos++) {\n            if (cx.char(pos) == 96) {\n                curSize++;\n                if (curSize == size && cx.char(pos + 1) != 96)\n                    return cx.append(elt(Type.InlineCode, start, pos + 1, [\n                        elt(Type.CodeMark, start, start + size),\n                        elt(Type.CodeMark, pos + 1 - size, pos + 1)\n                    ]));\n            }\n            else {\n                curSize = 0;\n            }\n        }\n        return -1;\n    },\n    HTMLTag(cx, next, start) {\n        if (next != 60 /* '<' */ || start == cx.end - 1)\n            return -1;\n        let after = cx.slice(start + 1, cx.end);\n        let url = /^(?:[a-z][-\\w+.]+:[^\\s>]+|[a-z\\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?(?:\\.[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?)*)>/i.exec(after);\n        if (url) {\n            return cx.append(elt(Type.Autolink, start, start + 1 + url[0].length, [\n                elt(Type.LinkMark, start, start + 1),\n                // url[0] includes the closing bracket, so exclude it from this slice\n                elt(Type.URL, start + 1, start + url[0].length),\n                elt(Type.LinkMark, start + url[0].length, start + 1 + url[0].length)\n            ]));\n        }\n        let comment = /^!--[^>](?:-[^-]|[^-])*?-->/i.exec(after);\n        if (comment)\n            return cx.append(elt(Type.Comment, start, start + 1 + comment[0].length));\n        let procInst = /^\\?[^]*?\\?>/.exec(after);\n        if (procInst)\n            return cx.append(elt(Type.ProcessingInstruction, start, start + 1 + procInst[0].length));\n        let m = /^(?:![A-Z][^]*?>|!\\[CDATA\\[[^]*?\\]\\]>|\\/\\s*[a-zA-Z][\\w-]*\\s*>|\\s*[a-zA-Z][\\w-]*(\\s+[a-zA-Z:_][\\w-.:]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*(\\/\\s*)?>)/.exec(after);\n        if (!m)\n            return -1;\n        return cx.append(elt(Type.HTMLTag, start, start + 1 + m[0].length));\n    },\n    Emphasis(cx, next, start) {\n        if (next != 95 && next != 42)\n            return -1;\n        let pos = start + 1;\n        while (cx.char(pos) == next)\n            pos++;\n        let before = cx.slice(start - 1, start), after = cx.slice(pos, pos + 1);\n        let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n        let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n        let leftFlanking = !sAfter && (!pAfter || sBefore || pBefore);\n        let rightFlanking = !sBefore && (!pBefore || sAfter || pAfter);\n        let canOpen = leftFlanking && (next == 42 || !rightFlanking || pBefore);\n        let canClose = rightFlanking && (next == 42 || !leftFlanking || pAfter);\n        return cx.append(new InlineDelimiter(next == 95 ? EmphasisUnderscore : EmphasisAsterisk, start, pos, (canOpen ? 1 /* Mark.Open */ : 0 /* Mark.None */) | (canClose ? 2 /* Mark.Close */ : 0 /* Mark.None */)));\n    },\n    HardBreak(cx, next, start) {\n        if (next == 92 /* '\\\\' */ && cx.char(start + 1) == 10 /* '\\n' */)\n            return cx.append(elt(Type.HardBreak, start, start + 2));\n        if (next == 32) {\n            let pos = start + 1;\n            while (cx.char(pos) == 32)\n                pos++;\n            if (cx.char(pos) == 10 && pos >= start + 2)\n                return cx.append(elt(Type.HardBreak, start, pos + 1));\n        }\n        return -1;\n    },\n    Link(cx, next, start) {\n        return next == 91 /* '[' */ ? cx.append(new InlineDelimiter(LinkStart, start, start + 1, 1 /* Mark.Open */)) : -1;\n    },\n    Image(cx, next, start) {\n        return next == 33 /* '!' */ && cx.char(start + 1) == 91 /* '[' */\n            ? cx.append(new InlineDelimiter(ImageStart, start, start + 2, 1 /* Mark.Open */)) : -1;\n    },\n    LinkEnd(cx, next, start) {\n        if (next != 93 /* ']' */)\n            return -1;\n        // Scanning back to the next link/image start marker\n        for (let i = cx.parts.length - 1; i >= 0; i--) {\n            let part = cx.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart)) {\n                // If this one has been set invalid (because it would produce\n                // a nested link) or there's no valid link here ignore both.\n                if (!part.side || cx.skipSpace(part.to) == start && !/[(\\[]/.test(cx.slice(start + 1, start + 2))) {\n                    cx.parts[i] = null;\n                    return -1;\n                }\n                // Finish the content and replace the entire range in\n                // this.parts with the link/image node.\n                let content = cx.takeContent(i);\n                let link = cx.parts[i] = finishLink(cx, content, part.type == LinkStart ? Type.Link : Type.Image, part.from, start + 1);\n                // Set any open-link markers before this link to invalid.\n                if (part.type == LinkStart)\n                    for (let j = 0; j < i; j++) {\n                        let p = cx.parts[j];\n                        if (p instanceof InlineDelimiter && p.type == LinkStart)\n                            p.side = 0 /* Mark.None */;\n                    }\n                return link.to;\n            }\n        }\n        return -1;\n    }\n};\nfunction finishLink(cx, content, type, start, startPos) {\n    let { text } = cx, next = cx.char(startPos), endPos = startPos;\n    content.unshift(elt(Type.LinkMark, start, start + (type == Type.Image ? 2 : 1)));\n    content.push(elt(Type.LinkMark, startPos - 1, startPos));\n    if (next == 40 /* '(' */) {\n        let pos = cx.skipSpace(startPos + 1);\n        let dest = parseURL(text, pos - cx.offset, cx.offset), title;\n        if (dest) {\n            pos = cx.skipSpace(dest.to);\n            // The destination and title must be separated by whitespace\n            if (pos != dest.to) {\n                title = parseLinkTitle(text, pos - cx.offset, cx.offset);\n                if (title)\n                    pos = cx.skipSpace(title.to);\n            }\n        }\n        if (cx.char(pos) == 41 /* ')' */) {\n            content.push(elt(Type.LinkMark, startPos, startPos + 1));\n            endPos = pos + 1;\n            if (dest)\n                content.push(dest);\n            if (title)\n                content.push(title);\n            content.push(elt(Type.LinkMark, pos, endPos));\n        }\n    }\n    else if (next == 91 /* '[' */) {\n        let label = parseLinkLabel(text, startPos - cx.offset, cx.offset, false);\n        if (label) {\n            content.push(label);\n            endPos = label.to;\n        }\n    }\n    return elt(type, start, endPos, content);\n}\n// These return `null` when falling off the end of the input, `false`\n// when parsing fails otherwise (for use in the incremental link\n// reference parser).\nfunction parseURL(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next == 60 /* '<' */) {\n        for (let pos = start + 1; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (ch == 62 /* '>' */)\n                return elt(Type.URL, start + offset, pos + 1 + offset);\n            if (ch == 60 || ch == 10 /* '<\\n' */)\n                return false;\n        }\n        return null;\n    }\n    else {\n        let depth = 0, pos = start;\n        for (let escaped = false; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (space(ch)) {\n                break;\n            }\n            else if (escaped) {\n                escaped = false;\n            }\n            else if (ch == 40 /* '(' */) {\n                depth++;\n            }\n            else if (ch == 41 /* ')' */) {\n                if (!depth)\n                    break;\n                depth--;\n            }\n            else if (ch == 92 /* '\\\\' */) {\n                escaped = true;\n            }\n        }\n        return pos > start ? elt(Type.URL, start + offset, pos + offset) : pos == text.length ? null : false;\n    }\n}\nfunction parseLinkTitle(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next != 39 && next != 34 && next != 40 /* '\"\\'(' */)\n        return false;\n    let end = next == 40 ? 41 : next;\n    for (let pos = start + 1, escaped = false; pos < text.length; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == end)\n            return elt(Type.LinkTitle, start + offset, pos + 1 + offset);\n        else if (ch == 92 /* '\\\\' */)\n            escaped = true;\n    }\n    return null;\n}\nfunction parseLinkLabel(text, start, offset, requireNonWS) {\n    for (let escaped = false, pos = start + 1, end = Math.min(text.length, pos + 999); pos < end; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == 93 /* ']' */)\n            return requireNonWS ? false : elt(Type.LinkLabel, start + offset, pos + 1 + offset);\n        else {\n            if (requireNonWS && !space(ch))\n                requireNonWS = false;\n            if (ch == 91 /* '[' */)\n                return false;\n            else if (ch == 92 /* '\\\\' */)\n                escaped = true;\n        }\n    }\n    return null;\n}\n/**\nInline parsing functions get access to this context, and use it to\nread the content and emit syntax nodes.\n*/\nclass InlineContext {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser that is being used.\n    */\n    parser, \n    /**\n    The text of this inline section.\n    */\n    text, \n    /**\n    The starting offset of the section in the document.\n    */\n    offset) {\n        this.parser = parser;\n        this.text = text;\n        this.offset = offset;\n        /**\n        @internal\n        */\n        this.parts = [];\n    }\n    /**\n    Get the character code at the given (document-relative)\n    position.\n    */\n    char(pos) { return pos >= this.end ? -1 : this.text.charCodeAt(pos - this.offset); }\n    /**\n    The position of the end of this inline section.\n    */\n    get end() { return this.offset + this.text.length; }\n    /**\n    Get a substring of this inline section. Again uses\n    document-relative positions.\n    */\n    slice(from, to) { return this.text.slice(from - this.offset, to - this.offset); }\n    /**\n    @internal\n    */\n    append(elt) {\n        this.parts.push(elt);\n        return elt.to;\n    }\n    /**\n    Add a [delimiter](#DelimiterType) at this given position. `open`\n    and `close` indicate whether this delimiter is opening, closing,\n    or both. Returns the end of the delimiter, for convenient\n    returning from [parse functions](#InlineParser.parse).\n    */\n    addDelimiter(type, from, to, open, close) {\n        return this.append(new InlineDelimiter(type, from, to, (open ? 1 /* Mark.Open */ : 0 /* Mark.None */) | (close ? 2 /* Mark.Close */ : 0 /* Mark.None */)));\n    }\n    /**\n    Returns true when there is an unmatched link or image opening\n    token before the current position.\n    */\n    get hasOpenLink() {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart))\n                return true;\n        }\n        return false;\n    }\n    /**\n    Add an inline element. Returns the end of the element.\n    */\n    addElement(elt) {\n        return this.append(elt);\n    }\n    /**\n    Resolve markers between this.parts.length and from, wrapping matched markers in the\n    appropriate node and updating the content of this.parts. @internal\n    */\n    resolveMarkers(from) {\n        // Scan forward, looking for closing tokens\n        for (let i = from; i < this.parts.length; i++) {\n            let close = this.parts[i];\n            if (!(close instanceof InlineDelimiter && close.type.resolve && (close.side & 2 /* Mark.Close */)))\n                continue;\n            let emp = close.type == EmphasisUnderscore || close.type == EmphasisAsterisk;\n            let closeSize = close.to - close.from;\n            let open, j = i - 1;\n            // Continue scanning for a matching opening token\n            for (; j >= from; j--) {\n                let part = this.parts[j];\n                if (part instanceof InlineDelimiter && (part.side & 1 /* Mark.Open */) && part.type == close.type &&\n                    // Ignore emphasis delimiters where the character count doesn't match\n                    !(emp && ((close.side & 1 /* Mark.Open */) || (part.side & 2 /* Mark.Close */)) &&\n                        (part.to - part.from + closeSize) % 3 == 0 && ((part.to - part.from) % 3 || closeSize % 3))) {\n                    open = part;\n                    break;\n                }\n            }\n            if (!open)\n                continue;\n            let type = close.type.resolve, content = [];\n            let start = open.from, end = close.to;\n            // Emphasis marker effect depends on the character count. Size consumed is minimum of the two\n            // markers.\n            if (emp) {\n                let size = Math.min(2, open.to - open.from, closeSize);\n                start = open.to - size;\n                end = close.from + size;\n                type = size == 1 ? \"Emphasis\" : \"StrongEmphasis\";\n            }\n            // Move the covered region into content, optionally adding marker nodes\n            if (open.type.mark)\n                content.push(this.elt(open.type.mark, start, open.to));\n            for (let k = j + 1; k < i; k++) {\n                if (this.parts[k] instanceof Element)\n                    content.push(this.parts[k]);\n                this.parts[k] = null;\n            }\n            if (close.type.mark)\n                content.push(this.elt(close.type.mark, close.from, end));\n            let element = this.elt(type, start, end, content);\n            // If there are leftover emphasis marker characters, shrink the close/open markers. Otherwise, clear them.\n            this.parts[j] = emp && open.from != start ? new InlineDelimiter(open.type, open.from, start, open.side) : null;\n            let keep = this.parts[i] = emp && close.to != end ? new InlineDelimiter(close.type, end, close.to, close.side) : null;\n            // Insert the new element in this.parts\n            if (keep)\n                this.parts.splice(i, 0, element);\n            else\n                this.parts[i] = element;\n        }\n        // Collect the elements remaining in this.parts into an array.\n        let result = [];\n        for (let i = from; i < this.parts.length; i++) {\n            let part = this.parts[i];\n            if (part instanceof Element)\n                result.push(part);\n        }\n        return result;\n    }\n    /**\n    Find an opening delimiter of the given type. Returns `null` if\n    no delimiter is found, or an index that can be passed to\n    [`takeContent`](#InlineContext.takeContent) otherwise.\n    */\n    findOpeningDelimiter(type) {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && part.type == type)\n                return i;\n        }\n        return null;\n    }\n    /**\n    Remove all inline elements and delimiters starting from the\n    given index (which you should get from\n    [`findOpeningDelimiter`](#InlineContext.findOpeningDelimiter),\n    resolve delimiters inside of them, and return them as an array\n    of elements.\n    */\n    takeContent(startIndex) {\n        let content = this.resolveMarkers(startIndex);\n        this.parts.length = startIndex;\n        return content;\n    }\n    /**\n    Skip space after the given (document) position, returning either\n    the position of the next non-space character or the end of the\n    section.\n    */\n    skipSpace(from) { return skipSpace(this.text, from - this.offset) + this.offset; }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n}\nfunction injectMarks(elements, marks) {\n    if (!marks.length)\n        return elements;\n    if (!elements.length)\n        return marks;\n    let elts = elements.slice(), eI = 0;\n    for (let mark of marks) {\n        while (eI < elts.length && elts[eI].to < mark.to)\n            eI++;\n        if (eI < elts.length && elts[eI].from < mark.from) {\n            let e = elts[eI];\n            if (e instanceof Element)\n                elts[eI] = new Element(e.type, e.from, e.to, injectMarks(e.children, [mark]));\n        }\n        else {\n            elts.splice(eI++, 0, mark);\n        }\n    }\n    return elts;\n}\n// These are blocks that can span blank lines, and should thus only be\n// reused if their next sibling is also being reused.\nconst NotLast = [Type.CodeBlock, Type.ListItem, Type.OrderedList, Type.BulletList];\nclass FragmentCursor {\n    constructor(fragments, input) {\n        this.fragments = fragments;\n        this.input = input;\n        // Index into fragment array\n        this.i = 0;\n        // Active fragment\n        this.fragment = null;\n        this.fragmentEnd = -1;\n        // Cursor into the current fragment, if any. When `moveTo` returns\n        // true, this points at the first block after `pos`.\n        this.cursor = null;\n        if (fragments.length)\n            this.fragment = fragments[this.i++];\n    }\n    nextFragment() {\n        this.fragment = this.i < this.fragments.length ? this.fragments[this.i++] : null;\n        this.cursor = null;\n        this.fragmentEnd = -1;\n    }\n    moveTo(pos, lineStart) {\n        while (this.fragment && this.fragment.to <= pos)\n            this.nextFragment();\n        if (!this.fragment || this.fragment.from > (pos ? pos - 1 : 0))\n            return false;\n        if (this.fragmentEnd < 0) {\n            let end = this.fragment.to;\n            while (end > 0 && this.input.read(end - 1, end) != \"\\n\")\n                end--;\n            this.fragmentEnd = end ? end - 1 : 0;\n        }\n        let c = this.cursor;\n        if (!c) {\n            c = this.cursor = this.fragment.tree.cursor();\n            c.firstChild();\n        }\n        let rPos = pos + this.fragment.offset;\n        while (c.to <= rPos)\n            if (!c.parent())\n                return false;\n        for (;;) {\n            if (c.from >= rPos)\n                return this.fragment.from <= lineStart;\n            if (!c.childAfter(rPos))\n                return false;\n        }\n    }\n    matches(hash) {\n        let tree = this.cursor.tree;\n        return tree && tree.prop(NodeProp.contextHash) == hash;\n    }\n    takeNodes(cx) {\n        let cur = this.cursor, off = this.fragment.offset, fragEnd = this.fragmentEnd - (this.fragment.openEnd ? 1 : 0);\n        let start = cx.absoluteLineStart, end = start, blockI = cx.block.children.length;\n        let prevEnd = end, prevI = blockI;\n        for (;;) {\n            if (cur.to - off > fragEnd) {\n                if (cur.type.isAnonymous && cur.firstChild())\n                    continue;\n                break;\n            }\n            let pos = toRelative(cur.from - off, cx.ranges);\n            if (cur.to - off <= cx.ranges[cx.rangeI].to) { // Fits in current range\n                cx.addNode(cur.tree, pos);\n            }\n            else {\n                let dummy = new Tree(cx.parser.nodeSet.types[Type.Paragraph], [], [], 0, cx.block.hashProp);\n                cx.reusePlaceholders.set(dummy, cur.tree);\n                cx.addNode(dummy, pos);\n            }\n            // Taken content must always end in a block, because incremental\n            // parsing happens on block boundaries. Never stop directly\n            // after an indented code block, since those can continue after\n            // any number of blank lines.\n            if (cur.type.is(\"Block\")) {\n                if (NotLast.indexOf(cur.type.id) < 0) {\n                    end = cur.to - off;\n                    blockI = cx.block.children.length;\n                }\n                else {\n                    end = prevEnd;\n                    blockI = prevI;\n                    prevEnd = cur.to - off;\n                    prevI = cx.block.children.length;\n                }\n            }\n            if (!cur.nextSibling())\n                break;\n        }\n        while (cx.block.children.length > blockI) {\n            cx.block.children.pop();\n            cx.block.positions.pop();\n        }\n        return end - start;\n    }\n}\n// Convert an input-stream-relative position to a\n// Markdown-doc-relative position by subtracting the size of all input\n// gaps before `abs`.\nfunction toRelative(abs, ranges) {\n    let pos = abs;\n    for (let i = 1; i < ranges.length; i++) {\n        let gapFrom = ranges[i - 1].to, gapTo = ranges[i].from;\n        if (gapFrom < abs)\n            pos -= gapTo - gapFrom;\n    }\n    return pos;\n}\nconst markdownHighlighting = styleTags({\n    \"Blockquote/...\": tags.quote,\n    HorizontalRule: tags.contentSeparator,\n    \"ATXHeading1/... SetextHeading1/...\": tags.heading1,\n    \"ATXHeading2/... SetextHeading2/...\": tags.heading2,\n    \"ATXHeading3/...\": tags.heading3,\n    \"ATXHeading4/...\": tags.heading4,\n    \"ATXHeading5/...\": tags.heading5,\n    \"ATXHeading6/...\": tags.heading6,\n    \"Comment CommentBlock\": tags.comment,\n    Escape: tags.escape,\n    Entity: tags.character,\n    \"Emphasis/...\": tags.emphasis,\n    \"StrongEmphasis/...\": tags.strong,\n    \"Link/... Image/...\": tags.link,\n    \"OrderedList/... BulletList/...\": tags.list,\n    \"BlockQuote/...\": tags.quote,\n    \"InlineCode CodeText\": tags.monospace,\n    \"URL Autolink\": tags.url,\n    \"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark\": tags.processingInstruction,\n    \"CodeInfo LinkLabel\": tags.labelName,\n    LinkTitle: tags.string,\n    Paragraph: tags.content\n});\n/**\nThe default CommonMark parser.\n*/\nconst parser = new MarkdownParser(new NodeSet(nodeTypes).extend(markdownHighlighting), Object.keys(DefaultBlockParsers).map(n => DefaultBlockParsers[n]), Object.keys(DefaultBlockParsers).map(n => DefaultLeafBlocks[n]), Object.keys(DefaultBlockParsers), DefaultEndLeaf, DefaultSkipMarkup, Object.keys(DefaultInline).map(n => DefaultInline[n]), Object.keys(DefaultInline), []);\n\nfunction leftOverSpace(node, from, to) {\n    let ranges = [];\n    for (let n = node.firstChild, pos = from;; n = n.nextSibling) {\n        let nextPos = n ? n.from : to;\n        if (nextPos > pos)\n            ranges.push({ from: pos, to: nextPos });\n        if (!n)\n            break;\n        pos = n.to;\n    }\n    return ranges;\n}\n/**\nCreate a Markdown extension to enable nested parsing on code\nblocks and/or embedded HTML.\n*/\nfunction parseCode(config) {\n    let { codeParser, htmlParser } = config;\n    let wrap = parseMixed((node, input) => {\n        let id = node.type.id;\n        if (codeParser && (id == Type.CodeBlock || id == Type.FencedCode)) {\n            let info = \"\";\n            if (id == Type.FencedCode) {\n                let infoNode = node.node.getChild(Type.CodeInfo);\n                if (infoNode)\n                    info = input.read(infoNode.from, infoNode.to);\n            }\n            let parser = codeParser(info);\n            if (parser)\n                return { parser, overlay: node => node.type.id == Type.CodeText };\n        }\n        else if (htmlParser && (id == Type.HTMLBlock || id == Type.HTMLTag)) {\n            return { parser: htmlParser, overlay: leftOverSpace(node.node, node.from, node.to) };\n        }\n        return null;\n    });\n    return { wrap };\n}\n\nconst StrikethroughDelim = { resolve: \"Strikethrough\", mark: \"StrikethroughMark\" };\n/**\nAn extension that implements\n[GFM-style](https://github.github.com/gfm/#strikethrough-extension-)\nStrikethrough syntax using `~~` delimiters.\n*/\nconst Strikethrough = {\n    defineNodes: [{\n            name: \"Strikethrough\",\n            style: { \"Strikethrough/...\": tags.strikethrough }\n        }, {\n            name: \"StrikethroughMark\",\n            style: tags.processingInstruction\n        }],\n    parseInline: [{\n            name: \"Strikethrough\",\n            parse(cx, next, pos) {\n                if (next != 126 /* '~' */ || cx.char(pos + 1) != 126 || cx.char(pos + 2) == 126)\n                    return -1;\n                let before = cx.slice(pos - 1, pos), after = cx.slice(pos + 2, pos + 3);\n                let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n                let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n                return cx.addDelimiter(StrikethroughDelim, pos, pos + 2, !sAfter && (!pAfter || sBefore || pBefore), !sBefore && (!pBefore || sAfter || pAfter));\n            },\n            after: \"Emphasis\"\n        }]\n};\n// Parse a line as a table row and return the row count. When `elts`\n// is given, push syntax elements for the content onto it.\nfunction parseRow(cx, line, startI = 0, elts, offset = 0) {\n    let count = 0, first = true, cellStart = -1, cellEnd = -1, esc = false;\n    let parseCell = () => {\n        elts.push(cx.elt(\"TableCell\", offset + cellStart, offset + cellEnd, cx.parser.parseInline(line.slice(cellStart, cellEnd), offset + cellStart)));\n    };\n    for (let i = startI; i < line.length; i++) {\n        let next = line.charCodeAt(i);\n        if (next == 124 /* '|' */ && !esc) {\n            if (!first || cellStart > -1)\n                count++;\n            first = false;\n            if (elts) {\n                if (cellStart > -1)\n                    parseCell();\n                elts.push(cx.elt(\"TableDelimiter\", i + offset, i + offset + 1));\n            }\n            cellStart = cellEnd = -1;\n        }\n        else if (esc || next != 32 && next != 9) {\n            if (cellStart < 0)\n                cellStart = i;\n            cellEnd = i + 1;\n        }\n        esc = !esc && next == 92;\n    }\n    if (cellStart > -1) {\n        count++;\n        if (elts)\n            parseCell();\n    }\n    return count;\n}\nfunction hasPipe(str, start) {\n    for (let i = start; i < str.length; i++) {\n        let next = str.charCodeAt(i);\n        if (next == 124 /* '|' */)\n            return true;\n        if (next == 92 /* '\\\\' */)\n            i++;\n    }\n    return false;\n}\nconst delimiterLine = /^\\|?(\\s*:?-+:?\\s*\\|)+(\\s*:?-+:?\\s*)?$/;\nclass TableParser {\n    constructor() {\n        // Null means we haven't seen the second line yet, false means this\n        // isn't a table, and an array means this is a table and we've\n        // parsed the given rows so far.\n        this.rows = null;\n    }\n    nextLine(cx, line, leaf) {\n        if (this.rows == null) { // Second line\n            this.rows = false;\n            let lineText;\n            if ((line.next == 45 || line.next == 58 || line.next == 124 /* '-:|' */) &&\n                delimiterLine.test(lineText = line.text.slice(line.pos))) {\n                let firstRow = [], firstCount = parseRow(cx, leaf.content, 0, firstRow, leaf.start);\n                if (firstCount == parseRow(cx, lineText, line.pos))\n                    this.rows = [cx.elt(\"TableHeader\", leaf.start, leaf.start + leaf.content.length, firstRow),\n                        cx.elt(\"TableDelimiter\", cx.lineStart + line.pos, cx.lineStart + line.text.length)];\n            }\n        }\n        else if (this.rows) { // Line after the second\n            let content = [];\n            parseRow(cx, line.text, line.pos, content, cx.lineStart);\n            this.rows.push(cx.elt(\"TableRow\", cx.lineStart + line.pos, cx.lineStart + line.text.length, content));\n        }\n        return false;\n    }\n    finish(cx, leaf) {\n        if (!this.rows)\n            return false;\n        cx.addLeafElement(leaf, cx.elt(\"Table\", leaf.start, leaf.start + leaf.content.length, this.rows));\n        return true;\n    }\n}\n/**\nThis extension provides\n[GFM-style](https://github.github.com/gfm/#tables-extension-)\ntables, using syntax like this:\n\n```\n| head 1 | head 2 |\n| ---    | ---    |\n| cell 1 | cell 2 |\n```\n*/\nconst Table = {\n    defineNodes: [\n        { name: \"Table\", block: true },\n        { name: \"TableHeader\", style: { \"TableHeader/...\": tags.heading } },\n        \"TableRow\",\n        { name: \"TableCell\", style: tags.content },\n        { name: \"TableDelimiter\", style: tags.processingInstruction },\n    ],\n    parseBlock: [{\n            name: \"Table\",\n            leaf(_, leaf) { return hasPipe(leaf.content, 0) ? new TableParser : null; },\n            endLeaf(cx, line, leaf) {\n                if (leaf.parsers.some(p => p instanceof TableParser) || !hasPipe(line.text, line.basePos))\n                    return false;\n                let next = cx.peekLine();\n                return delimiterLine.test(next) && parseRow(cx, line.text, line.basePos) == parseRow(cx, next, line.basePos);\n            },\n            before: \"SetextHeading\"\n        }]\n};\nclass TaskParser {\n    nextLine() { return false; }\n    finish(cx, leaf) {\n        cx.addLeafElement(leaf, cx.elt(\"Task\", leaf.start, leaf.start + leaf.content.length, [\n            cx.elt(\"TaskMarker\", leaf.start, leaf.start + 3),\n            ...cx.parser.parseInline(leaf.content.slice(3), leaf.start + 3)\n        ]));\n        return true;\n    }\n}\n/**\nExtension providing\n[GFM-style](https://github.github.com/gfm/#task-list-items-extension-)\ntask list items, where list items can be prefixed with `[ ]` or\n`[x]` to add a checkbox.\n*/\nconst TaskList = {\n    defineNodes: [\n        { name: \"Task\", block: true, style: tags.list },\n        { name: \"TaskMarker\", style: tags.atom }\n    ],\n    parseBlock: [{\n            name: \"TaskList\",\n            leaf(cx, leaf) {\n                return /^\\[[ xX]\\][ \\t]/.test(leaf.content) && cx.parentType().name == \"ListItem\" ? new TaskParser : null;\n            },\n            after: \"SetextHeading\"\n        }]\n};\nconst autolinkRE = /(www\\.)|(https?:\\/\\/)|([\\w.+-]{1,100}@)|(mailto:|xmpp:)/gy;\nconst urlRE = /[\\w-]+(\\.[\\w-]+)+(\\/[^\\s<]*)?/gy;\nconst lastTwoDomainWords = /[\\w-]+\\.[\\w-]+($|\\/)/;\nconst emailRE = /[\\w.+-]+@[\\w-]+(\\.[\\w.-]+)+/gy;\nconst xmppResourceRE = /\\/[a-zA-Z\\d@.]+/gy;\nfunction count(str, from, to, ch) {\n    let result = 0;\n    for (let i = from; i < to; i++)\n        if (str[i] == ch)\n            result++;\n    return result;\n}\nfunction autolinkURLEnd(text, from) {\n    urlRE.lastIndex = from;\n    let m = urlRE.exec(text);\n    if (!m || lastTwoDomainWords.exec(m[0])[0].indexOf(\"_\") > -1)\n        return -1;\n    let end = from + m[0].length;\n    for (;;) {\n        let last = text[end - 1], m;\n        if (/[?!.,:*_~]/.test(last) ||\n            last == \")\" && count(text, from, end, \")\") > count(text, from, end, \"(\"))\n            end--;\n        else if (last == \";\" && (m = /&(?:#\\d+|#x[a-f\\d]+|\\w+);$/.exec(text.slice(from, end))))\n            end = from + m.index;\n        else\n            break;\n    }\n    return end;\n}\nfunction autolinkEmailEnd(text, from) {\n    emailRE.lastIndex = from;\n    let m = emailRE.exec(text);\n    if (!m)\n        return -1;\n    let last = m[0][m[0].length - 1];\n    return last == \"_\" || last == \"-\" ? -1 : from + m[0].length - (last == \".\" ? 1 : 0);\n}\n/**\nExtension that implements autolinking for\n`www.`/`http://`/`https://`/`mailto:`/`xmpp:` URLs and email\naddresses.\n*/\nconst Autolink = {\n    parseInline: [{\n            name: \"Autolink\",\n            parse(cx, next, absPos) {\n                let pos = absPos - cx.offset;\n                if (pos && /\\w/.test(cx.text[pos - 1]))\n                    return -1;\n                autolinkRE.lastIndex = pos;\n                let m = autolinkRE.exec(cx.text), end = -1;\n                if (!m)\n                    return -1;\n                if (m[1] || m[2]) { // www., http://\n                    end = autolinkURLEnd(cx.text, pos + m[0].length);\n                    if (end > -1 && cx.hasOpenLink) {\n                        let noBracket = /([^\\[\\]]|\\[[^\\]]*\\])*/.exec(cx.text.slice(pos, end));\n                        end = pos + noBracket[0].length;\n                    }\n                }\n                else if (m[3]) { // email address\n                    end = autolinkEmailEnd(cx.text, pos);\n                }\n                else { // mailto:/xmpp:\n                    end = autolinkEmailEnd(cx.text, pos + m[0].length);\n                    if (end > -1 && m[0] == \"xmpp:\") {\n                        xmppResourceRE.lastIndex = end;\n                        m = xmppResourceRE.exec(cx.text);\n                        if (m)\n                            end = m.index + m[0].length;\n                    }\n                }\n                if (end < 0)\n                    return -1;\n                cx.addElement(cx.elt(\"URL\", absPos, end + cx.offset));\n                return end + cx.offset;\n            }\n        }]\n};\n/**\nExtension bundle containing [`Table`](#Table),\n[`TaskList`](#TaskList), [`Strikethrough`](#Strikethrough), and\n[`Autolink`](#Autolink).\n*/\nconst GFM = [Table, TaskList, Strikethrough, Autolink];\nfunction parseSubSuper(ch, node, mark) {\n    return (cx, next, pos) => {\n        if (next != ch || cx.char(pos + 1) == ch)\n            return -1;\n        let elts = [cx.elt(mark, pos, pos + 1)];\n        for (let i = pos + 1; i < cx.end; i++) {\n            let next = cx.char(i);\n            if (next == ch)\n                return cx.addElement(cx.elt(node, pos, i + 1, elts.concat(cx.elt(mark, i, i + 1))));\n            if (next == 92 /* '\\\\' */)\n                elts.push(cx.elt(\"Escape\", i, i++ + 2));\n            if (space(next))\n                break;\n        }\n        return -1;\n    };\n}\n/**\nExtension providing\n[Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\nsuperscript using `^` markers.\n*/\nconst Superscript = {\n    defineNodes: [\n        { name: \"Superscript\", style: tags.special(tags.content) },\n        { name: \"SuperscriptMark\", style: tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Superscript\",\n            parse: parseSubSuper(94 /* '^' */, \"Superscript\", \"SuperscriptMark\")\n        }]\n};\n/**\nExtension providing\n[Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\nsubscript using `~` markers.\n*/\nconst Subscript = {\n    defineNodes: [\n        { name: \"Subscript\", style: tags.special(tags.content) },\n        { name: \"SubscriptMark\", style: tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Subscript\",\n            parse: parseSubSuper(126 /* '~' */, \"Subscript\", \"SubscriptMark\")\n        }]\n};\n/**\nExtension that parses two colons with only letters, underscores,\nand numbers between them as `Emoji` nodes.\n*/\nconst Emoji = {\n    defineNodes: [{ name: \"Emoji\", style: tags.character }],\n    parseInline: [{\n            name: \"Emoji\",\n            parse(cx, next, pos) {\n                let match;\n                if (next != 58 /* ':' */ || !(match = /^[a-zA-Z_0-9]+:/.exec(cx.slice(pos + 1, cx.end))))\n                    return -1;\n                return cx.addElement(cx.elt(\"Emoji\", pos, pos + 1 + match[0].length));\n            }\n        }]\n};\n\nexport { Autolink, BlockContext, Element, Emoji, GFM, InlineContext, LeafBlock, Line, MarkdownParser, Strikethrough, Subscript, Superscript, Table, TaskList, parseCode, parser };\n", "import { EditorSelection, countColumn, Prec, EditorState } from '@codemirror/state';\nimport { keymap } from '@codemirror/view';\nimport { defineLanguageFacet, foldNodeProp, indentNodeProp, languageDataProp, foldService, syntaxTree, Language, LanguageDescription, ParseContext, indentUnit, LanguageSupport } from '@codemirror/language';\nimport { CompletionContext } from '@codemirror/autocomplete';\nimport { parser, GFM, Subscript, Superscript, Emoji, MarkdownParser, parseCode } from '@lezer/markdown';\nimport { html, htmlCompletionSource } from '@codemirror/lang-html';\nimport { NodeProp } from '@lezer/common';\n\nconst data = /*@__PURE__*/defineLanguageFacet({ commentTokens: { block: { open: \"<!--\", close: \"-->\" } } });\nconst headingProp = /*@__PURE__*/new NodeProp();\nconst commonmark = /*@__PURE__*/parser.configure({\n    props: [\n        /*@__PURE__*/foldNodeProp.add(type => {\n            return !type.is(\"Block\") || type.is(\"Document\") || isHeading(type) != null || isList(type) ? undefined\n                : (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to });\n        }),\n        /*@__PURE__*/headingProp.add(isHeading),\n        /*@__PURE__*/indentNodeProp.add({\n            Document: () => null\n        }),\n        /*@__PURE__*/languageDataProp.add({\n            Document: data\n        })\n    ]\n});\nfunction isHeading(type) {\n    let match = /^(?:ATX|Setext)Heading(\\d)$/.exec(type.name);\n    return match ? +match[1] : undefined;\n}\nfunction isList(type) {\n    return type.name == \"OrderedList\" || type.name == \"BulletList\";\n}\nfunction findSectionEnd(headerNode, level) {\n    let last = headerNode;\n    for (;;) {\n        let next = last.nextSibling, heading;\n        if (!next || (heading = isHeading(next.type)) != null && heading <= level)\n            break;\n        last = next;\n    }\n    return last.to;\n}\nconst headerIndent = /*@__PURE__*/foldService.of((state, start, end) => {\n    for (let node = syntaxTree(state).resolveInner(end, -1); node; node = node.parent) {\n        if (node.from < start)\n            break;\n        let heading = node.type.prop(headingProp);\n        if (heading == null)\n            continue;\n        let upto = findSectionEnd(node, heading);\n        if (upto > end)\n            return { from: end, to: upto };\n    }\n    return null;\n});\nfunction mkLang(parser) {\n    return new Language(data, parser, [headerIndent], \"markdown\");\n}\n/**\nLanguage support for strict CommonMark.\n*/\nconst commonmarkLanguage = /*@__PURE__*/mkLang(commonmark);\nconst extended = /*@__PURE__*/commonmark.configure([GFM, Subscript, Superscript, Emoji, {\n        props: [\n            /*@__PURE__*/foldNodeProp.add({\n                Table: (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to })\n            })\n        ]\n    }]);\n/**\nLanguage support for [GFM](https://github.github.com/gfm/) plus\nsubscript, superscript, and emoji syntax.\n*/\nconst markdownLanguage = /*@__PURE__*/mkLang(extended);\nfunction getCodeParser(languages, defaultLanguage) {\n    return (info) => {\n        if (info && languages) {\n            let found = null;\n            // Strip anything after whitespace\n            info = /\\S*/.exec(info)[0];\n            if (typeof languages == \"function\")\n                found = languages(info);\n            else\n                found = LanguageDescription.matchLanguageName(languages, info, true);\n            if (found instanceof LanguageDescription)\n                return found.support ? found.support.language.parser : ParseContext.getSkippingParser(found.load());\n            else if (found)\n                return found.parser;\n        }\n        return defaultLanguage ? defaultLanguage.parser : null;\n    };\n}\n\nclass Context {\n    constructor(node, from, to, spaceBefore, spaceAfter, type, item) {\n        this.node = node;\n        this.from = from;\n        this.to = to;\n        this.spaceBefore = spaceBefore;\n        this.spaceAfter = spaceAfter;\n        this.type = type;\n        this.item = item;\n    }\n    blank(maxWidth, trailing = true) {\n        let result = this.spaceBefore + (this.node.name == \"Blockquote\" ? \">\" : \"\");\n        if (maxWidth != null) {\n            while (result.length < maxWidth)\n                result += \" \";\n            return result;\n        }\n        else {\n            for (let i = this.to - this.from - result.length - this.spaceAfter.length; i > 0; i--)\n                result += \" \";\n            return result + (trailing ? this.spaceAfter : \"\");\n        }\n    }\n    marker(doc, add) {\n        let number = this.node.name == \"OrderedList\" ? String((+itemNumber(this.item, doc)[2] + add)) : \"\";\n        return this.spaceBefore + number + this.type + this.spaceAfter;\n    }\n}\nfunction getContext(node, doc) {\n    let nodes = [], context = [];\n    for (let cur = node; cur; cur = cur.parent) {\n        if (cur.name == \"FencedCode\")\n            return context;\n        if (cur.name == \"ListItem\" || cur.name == \"Blockquote\")\n            nodes.push(cur);\n    }\n    for (let i = nodes.length - 1; i >= 0; i--) {\n        let node = nodes[i], match;\n        let line = doc.lineAt(node.from), startPos = node.from - line.from;\n        if (node.name == \"Blockquote\" && (match = /^ *>( ?)/.exec(line.text.slice(startPos)))) {\n            context.push(new Context(node, startPos, startPos + match[0].length, \"\", match[1], \">\", null));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"OrderedList\" &&\n            (match = /^( *)\\d+([.)])( *)/.exec(line.text.slice(startPos)))) {\n            let after = match[3], len = match[0].length;\n            if (after.length >= 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, match[2], node));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"BulletList\" &&\n            (match = /^( *)([-+*])( {1,4}\\[[ xX]\\])?( +)/.exec(line.text.slice(startPos)))) {\n            let after = match[4], len = match[0].length;\n            if (after.length > 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            let type = match[2];\n            if (match[3])\n                type += match[3].replace(/[xX]/, ' ');\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, type, node));\n        }\n    }\n    return context;\n}\nfunction itemNumber(item, doc) {\n    return /^(\\s*)(\\d+)(?=[.)])/.exec(doc.sliceString(item.from, item.from + 10));\n}\nfunction renumberList(after, doc, changes, offset = 0) {\n    for (let prev = -1, node = after;;) {\n        if (node.name == \"ListItem\") {\n            let m = itemNumber(node, doc);\n            let number = +m[2];\n            if (prev >= 0) {\n                if (number != prev + 1)\n                    return;\n                changes.push({ from: node.from + m[1].length, to: node.from + m[0].length, insert: String(prev + 2 + offset) });\n            }\n            prev = number;\n        }\n        let next = node.nextSibling;\n        if (!next)\n            break;\n        node = next;\n    }\n}\nfunction normalizeIndent(content, state) {\n    let blank = /^[ \\t]*/.exec(content)[0].length;\n    if (!blank || state.facet(indentUnit) != \"\\t\")\n        return content;\n    let col = countColumn(content, 4, blank);\n    let space = \"\";\n    for (let i = col; i > 0;) {\n        if (i >= 4) {\n            space += \"\\t\";\n            i -= 4;\n        }\n        else {\n            space += \" \";\n            i--;\n        }\n    }\n    return space + content.slice(blank);\n}\n/**\nThis command, when invoked in Markdown context with cursor\nselection(s), will create a new line with the markup for\nblockquotes and lists that were active on the old line. If the\ncursor was directly after the end of the markup for the old line,\ntrailing whitespace and list markers are removed from that line.\n\nThe command does nothing in non-Markdown context, so it should\nnot be used as the only binding for Enter (even in a Markdown\ndocument, HTML and code regions might use a different language).\n*/\nconst insertNewlineContinueMarkup = ({ state, dispatch }) => {\n    let tree = syntaxTree(state), { doc } = state;\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty || !markdownLanguage.isActiveAt(state, range.from, 0))\n            return dont = { range };\n        let pos = range.from, line = doc.lineAt(pos);\n        let context = getContext(tree.resolveInner(pos, -1), doc);\n        while (context.length && context[context.length - 1].from > pos - line.from)\n            context.pop();\n        if (!context.length)\n            return dont = { range };\n        let inner = context[context.length - 1];\n        if (inner.to - inner.spaceAfter.length > pos - line.from)\n            return dont = { range };\n        let emptyLine = pos >= (inner.to - inner.spaceAfter.length) && !/\\S/.test(line.text.slice(inner.to));\n        // Empty line in list\n        if (inner.item && emptyLine) {\n            let first = inner.node.firstChild, second = inner.node.getChild(\"ListItem\", \"ListItem\");\n            // Not second item or blank line before: delete a level of markup\n            if (first.to >= pos || second && second.to < pos ||\n                line.from > 0 && !/[^\\s>]/.test(doc.lineAt(line.from - 1).text)) {\n                let next = context.length > 1 ? context[context.length - 2] : null;\n                let delTo, insert = \"\";\n                if (next && next.item) { // Re-add marker for the list at the next level\n                    delTo = line.from + next.from;\n                    insert = next.marker(doc, 1);\n                }\n                else {\n                    delTo = line.from + (next ? next.to : 0);\n                }\n                let changes = [{ from: delTo, to: pos, insert }];\n                if (inner.node.name == \"OrderedList\")\n                    renumberList(inner.item, doc, changes, -2);\n                if (next && next.node.name == \"OrderedList\")\n                    renumberList(next.item, doc, changes);\n                return { range: EditorSelection.cursor(delTo + insert.length), changes };\n            }\n            else { // Move second item down, making tight two-item list non-tight\n                let insert = blankLine(context, state, line);\n                return { range: EditorSelection.cursor(pos + insert.length + 1),\n                    changes: { from: line.from, insert: insert + state.lineBreak } };\n            }\n        }\n        if (inner.node.name == \"Blockquote\" && emptyLine && line.from) {\n            let prevLine = doc.lineAt(line.from - 1), quoted = />\\s*$/.exec(prevLine.text);\n            // Two aligned empty quoted lines in a row\n            if (quoted && quoted.index == inner.from) {\n                let changes = state.changes([{ from: prevLine.from + quoted.index, to: prevLine.to },\n                    { from: line.from + inner.from, to: line.to }]);\n                return { range: range.map(changes), changes };\n            }\n        }\n        let changes = [];\n        if (inner.node.name == \"OrderedList\")\n            renumberList(inner.item, doc, changes);\n        let continued = inner.item && inner.item.from < line.from;\n        let insert = \"\";\n        // If not dedented\n        if (!continued || /^[\\s\\d.)\\-+*>]*/.exec(line.text)[0].length >= inner.to) {\n            for (let i = 0, e = context.length - 1; i <= e; i++) {\n                insert += i == e && !continued ? context[i].marker(doc, 1)\n                    : context[i].blank(i < e ? countColumn(line.text, 4, context[i + 1].from) - insert.length : null);\n            }\n        }\n        let from = pos;\n        while (from > line.from && /\\s/.test(line.text.charAt(from - line.from - 1)))\n            from--;\n        insert = normalizeIndent(insert, state);\n        if (nonTightList(inner.node, state.doc))\n            insert = blankLine(context, state, line) + state.lineBreak + insert;\n        changes.push({ from, to: pos, insert: state.lineBreak + insert });\n        return { range: EditorSelection.cursor(from + insert.length + 1), changes };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\nfunction isMark(node) {\n    return node.name == \"QuoteMark\" || node.name == \"ListMark\";\n}\nfunction nonTightList(node, doc) {\n    if (node.name != \"OrderedList\" && node.name != \"BulletList\")\n        return false;\n    let first = node.firstChild, second = node.getChild(\"ListItem\", \"ListItem\");\n    if (!second)\n        return false;\n    let line1 = doc.lineAt(first.to), line2 = doc.lineAt(second.from);\n    let empty = /^[\\s>]*$/.test(line1.text);\n    return line1.number + (empty ? 0 : 1) < line2.number;\n}\nfunction blankLine(context, state, line) {\n    let insert = \"\";\n    for (let i = 0, e = context.length - 2; i <= e; i++) {\n        insert += context[i].blank(i < e\n            ? countColumn(line.text, 4, Math.min(line.text.length, context[i + 1].from)) - insert.length\n            : null, i < e);\n    }\n    return normalizeIndent(insert, state);\n}\nfunction contextNodeForDelete(tree, pos) {\n    let node = tree.resolveInner(pos, -1), scan = pos;\n    if (isMark(node)) {\n        scan = node.from;\n        node = node.parent;\n    }\n    for (let prev; prev = node.childBefore(scan);) {\n        if (isMark(prev)) {\n            scan = prev.from;\n        }\n        else if (prev.name == \"OrderedList\" || prev.name == \"BulletList\") {\n            node = prev.lastChild;\n            scan = node.to;\n        }\n        else {\n            break;\n        }\n    }\n    return node;\n}\n/**\nThis command will, when invoked in a Markdown context with the\ncursor directly after list or blockquote markup, delete one level\nof markup. When the markup is for a list, it will be replaced by\nspaces on the first invocation (a further invocation will delete\nthe spaces), to make it easy to continue a list.\n\nWhen not after Markdown block markup, this command will return\nfalse, so it is intended to be bound alongside other deletion\ncommands, with a higher precedence than the more generic commands.\n*/\nconst deleteMarkupBackward = ({ state, dispatch }) => {\n    let tree = syntaxTree(state);\n    let dont = null, changes = state.changeByRange(range => {\n        let pos = range.from, { doc } = state;\n        if (range.empty && markdownLanguage.isActiveAt(state, range.from)) {\n            let line = doc.lineAt(pos);\n            let context = getContext(contextNodeForDelete(tree, pos), doc);\n            if (context.length) {\n                let inner = context[context.length - 1];\n                let spaceEnd = inner.to - inner.spaceAfter.length + (inner.spaceAfter ? 1 : 0);\n                // Delete extra trailing space after markup\n                if (pos - line.from > spaceEnd && !/\\S/.test(line.text.slice(spaceEnd, pos - line.from)))\n                    return { range: EditorSelection.cursor(line.from + spaceEnd),\n                        changes: { from: line.from + spaceEnd, to: pos } };\n                if (pos - line.from == spaceEnd &&\n                    // Only apply this if we're on the line that has the\n                    // construct's syntax, or there's only indentation in the\n                    // target range\n                    (!inner.item || line.from <= inner.item.from || !/\\S/.test(line.text.slice(0, inner.to)))) {\n                    let start = line.from + inner.from;\n                    // Replace a list item marker with blank space\n                    if (inner.item && inner.node.from < inner.item.from && /\\S/.test(line.text.slice(inner.from, inner.to))) {\n                        let insert = inner.blank(countColumn(line.text, 4, inner.to) - countColumn(line.text, 4, inner.from));\n                        if (start == line.from)\n                            insert = normalizeIndent(insert, state);\n                        return { range: EditorSelection.cursor(start + insert.length),\n                            changes: { from: start, to: line.from + inner.to, insert } };\n                    }\n                    // Delete one level of indentation\n                    if (start < pos)\n                        return { range: EditorSelection.cursor(start), changes: { from: start, to: pos } };\n                }\n            }\n        }\n        return dont = { range };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete\" }));\n    return true;\n};\n\n/**\nA small keymap with Markdown-specific bindings. Binds Enter to\n[`insertNewlineContinueMarkup`](https://codemirror.net/6/docs/ref/#lang-markdown.insertNewlineContinueMarkup)\nand Backspace to\n[`deleteMarkupBackward`](https://codemirror.net/6/docs/ref/#lang-markdown.deleteMarkupBackward).\n*/\nconst markdownKeymap = [\n    { key: \"Enter\", run: insertNewlineContinueMarkup },\n    { key: \"Backspace\", run: deleteMarkupBackward }\n];\nconst htmlNoMatch = /*@__PURE__*/html({ matchClosingTags: false });\n/**\nMarkdown language support.\n*/\nfunction markdown(config = {}) {\n    let { codeLanguages, defaultCodeLanguage, addKeymap = true, base: { parser } = commonmarkLanguage, completeHTMLTags = true, htmlTagLanguage = htmlNoMatch } = config;\n    if (!(parser instanceof MarkdownParser))\n        throw new RangeError(\"Base parser provided to `markdown` should be a Markdown parser\");\n    let extensions = config.extensions ? [config.extensions] : [];\n    let support = [htmlTagLanguage.support], defaultCode;\n    if (defaultCodeLanguage instanceof LanguageSupport) {\n        support.push(defaultCodeLanguage.support);\n        defaultCode = defaultCodeLanguage.language;\n    }\n    else if (defaultCodeLanguage) {\n        defaultCode = defaultCodeLanguage;\n    }\n    let codeParser = codeLanguages || defaultCode ? getCodeParser(codeLanguages, defaultCode) : undefined;\n    extensions.push(parseCode({ codeParser, htmlParser: htmlTagLanguage.language.parser }));\n    if (addKeymap)\n        support.push(Prec.high(keymap.of(markdownKeymap)));\n    let lang = mkLang(parser.configure(extensions));\n    if (completeHTMLTags)\n        support.push(lang.data.of({ autocomplete: htmlTagCompletion }));\n    return new LanguageSupport(lang, support);\n}\nfunction htmlTagCompletion(context) {\n    let { state, pos } = context, m = /<[:\\-\\.\\w\\u00b7-\\uffff]*$/.exec(state.sliceDoc(pos - 25, pos));\n    if (!m)\n        return null;\n    let tree = syntaxTree(state).resolveInner(pos, -1);\n    while (tree && !tree.type.isTop) {\n        if (tree.name == \"CodeBlock\" || tree.name == \"FencedCode\" || tree.name == \"ProcessingInstructionBlock\" ||\n            tree.name == \"CommentBlock\" || tree.name == \"Link\" || tree.name == \"Image\")\n            return null;\n        tree = tree.parent;\n    }\n    return {\n        from: pos - m[0].length, to: pos,\n        options: htmlTagCompletions(),\n        validFor: /^<[:\\-\\.\\w\\u00b7-\\uffff]*$/\n    };\n}\nlet _tagCompletions = null;\nfunction htmlTagCompletions() {\n    if (_tagCompletions)\n        return _tagCompletions;\n    let result = htmlCompletionSource(new CompletionContext(EditorState.create({ extensions: htmlNoMatch }), 0, true));\n    return _tagCompletions = result ? result.options : [];\n}\n\nexport { commonmarkLanguage, deleteMarkupBackward, insertNewlineContinueMarkup, markdown, markdownKeymap, markdownLanguage };\n"], "names": ["CompositeBlock", "type", "value", "from", "parentHash", "end", "hash", "children", "positions", "NodeProp", "child", "pos", "Tree", "nodeSet", "last", "length", "NodeType", "Type", "LeafBlock", "start", "content", "Line", "newPos", "skipSpace", "text", "to", "indent", "elt", "i", "goal", "result", "skipForList", "bl", "cx", "line", "size", "isOrderedList", "isBulletList", "isHorizontalRule", "DefaultSkipMarkup", "space", "_cx", "ch", "skipSpaceBack", "isFencedCode", "isBlockquote", "breaking", "count", "isSetextUnderline", "DefaultLeafBlocks", "inList", "next", "isAtxHeading", "EmptyLine", "CommentEnd", "ProcessingEnd", "HTMLBlockStyle", "isHTMLBlock", "rest", "e", "getListIndent", "indentAfter", "indented", "addCodeText", "marks", "DefaultBlockParsers", "base", "pendingMarks", "m", "codeStart", "fenceEnd", "len", "infoFrom", "infoTo", "first", "textStart", "textEnd", "newBase", "off", "endOfSpace", "after", "buf", "node", "trailing", "nodeType", "LinkReferenceParser", "leaf", "finish", "parseLinkLabel", "parseURL", "skip", "title", "parseLinkTitle", "titleEnd", "lineEnd", "SetextHeadingParser", "underline", "underlineMark", "_", "DefaultEndLeaf", "p", "scanLineResult", "BlockContext", "parser", "input", "fragments", "ranges", "FragmentCursor", "markI", "mark", "parse", "lines", "stop", "taken", "toRelative", "depth", "textOffset", "rangeI", "nextFrom", "handler", "eol", "block", "none", "injectMarks", "top", "tree", "injectGaps", "inline", "TreeElement", "<PERSON><PERSON><PERSON>", "offset", "dummies", "rangeEnd", "movePastNext", "upto", "inclusive", "reuse", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "blockParsers", "leafBlockParsers", "blockNames", "endLeafBlock", "skipContextMarkup", "inlineParsers", "inlineNames", "wrappers", "t", "w", "spec", "config", "resolveConfig", "nonEmpty", "nodeTypes", "styles", "s", "name", "composite", "style", "id", "group", "Tag", "NodeSet", "styleTags", "rm", "found", "<PERSON><PERSON><PERSON>", "InlineContext", "outer", "token", "a", "conf", "conc", "b", "wrapA", "wrapB", "inner", "names", "elts", "Element", "startOff", "EmphasisUnderscore", "EmphasisAsterisk", "LinkStart", "ImageStart", "InlineDelimiter", "side", "Escapable", "Punctuation", "DefaultInline", "escaped", "curSize", "url", "comment", "procInst", "before", "pBefore", "pAfter", "sBefore", "sAfter", "leftFlanking", "rightFlanking", "canOpen", "canClose", "part", "link", "finishLink", "j", "startPos", "endPos", "dest", "label", "requireNonWS", "open", "close", "emp", "closeSize", "element", "startIndex", "elements", "eI", "NotLast", "lineStart", "c", "rPos", "cur", "fragEnd", "blockI", "prevEnd", "prevI", "dummy", "abs", "gapFrom", "gapTo", "markdownHighlighting", "tags", "n", "leftOverSpace", "nextPos", "parseCode", "codeParser", "htmlParser", "parseMixed", "info", "infoNode", "<PERSON>th<PERSON><PERSON><PERSON><PERSON>", "Strikethrough", "parseRow", "startI", "cellStart", "cellEnd", "esc", "parseCell", "hasPipe", "str", "delimiterLine", "<PERSON><PERSON><PERSON>er", "lineText", "firstRow", "Table", "TaskParser", "TaskList", "autolinkRE", "urlRE", "lastTwoDomainWords", "emailRE", "xmppResourceRE", "autolinkURLEnd", "autolinkEmailEnd", "Autolink", "absPos", "noBracket", "GFM", "parseSubSuper", "Superscript", "Subscript", "<PERSON><PERSON><PERSON>", "match", "data", "defineLanguageFacet", "headingProp", "commonmark", "foldNodeProp", "isHeading", "isList", "state", "indentNodeProp", "languageDataProp", "findSectionEnd", "headerNode", "level", "heading", "headerIndent", "foldService", "syntaxTree", "mkLang", "Language", "commonmarkLanguage", "extended", "markdownLanguage", "getCodeP<PERSON>er", "languages", "defaultLanguage", "LanguageDescription", "ParseContext", "Context", "spaceBefore", "spaceAfter", "item", "max<PERSON><PERSON><PERSON>", "doc", "add", "number", "itemNumber", "getContext", "nodes", "context", "renumberList", "changes", "prev", "normalizeIndent", "blank", "indentUnit", "col", "countColumn", "insertNewlineContinueMarkup", "dispatch", "dont", "range", "emptyLine", "second", "delTo", "insert", "EditorSelection", "blankLine", "prevLine", "quoted", "continued", "nonTightList", "isMark", "line1", "line2", "empty", "contextNodeForDelete", "scan", "deleteMarkupBackward", "spaceEnd", "markdownKeymap", "htmlNoMatch", "html", "markdown", "codeLanguages", "defaultCodeLanguage", "addKeymap", "completeHTMLTags", "htmlTagLanguage", "extensions", "support", "defaultCode", "LanguageSupport", "Prec", "keymap", "lang", "htmlTagCompletion", "htmlTagCompletions", "_tagCompletions", "htmlCompletionSource", "CompletionContext", "EditorState"], "mappings": "iTAGA,MAAMA,CAAe,CACjB,OAAO,OAAOC,EAAMC,EAAOC,EAAMC,EAAYC,EAAK,CAC9C,IAAIC,EAAQF,GAAcA,GAAc,GAAKH,GAAQC,GAAS,GAAM,EACpE,OAAO,IAAIF,EAAeC,EAAMC,EAAOC,EAAMG,EAAMD,EAAK,GAAI,CAAA,CAAE,CACjE,CACD,YAAYJ,EAEZC,EAAOC,EAAMG,EAAMD,EAAKE,EAAUC,EAAW,CACzC,KAAK,KAAOP,EACZ,KAAK,MAAQC,EACb,KAAK,KAAOC,EACZ,KAAK,KAAOG,EACZ,KAAK,IAAMD,EACX,KAAK,SAAWE,EAChB,KAAK,UAAYC,EACjB,KAAK,SAAW,CAAC,CAACC,EAAS,YAAaH,CAAI,CAAC,CAChD,CACD,SAASI,EAAOC,EAAK,CACbD,EAAM,KAAKD,EAAS,WAAW,GAAK,KAAK,OACzCC,EAAQ,IAAIE,EAAKF,EAAM,KAAMA,EAAM,SAAUA,EAAM,UAAWA,EAAM,OAAQ,KAAK,QAAQ,GAC7F,KAAK,SAAS,KAAKA,CAAK,EACxB,KAAK,UAAU,KAAKC,CAAG,CAC1B,CACD,OAAOE,EAASR,EAAM,KAAK,IAAK,CAC5B,IAAIS,EAAO,KAAK,SAAS,OAAS,EAClC,OAAIA,GAAQ,IACRT,EAAM,KAAK,IAAIA,EAAK,KAAK,UAAUS,CAAI,EAAI,KAAK,SAASA,CAAI,EAAE,OAAS,KAAK,IAAI,GAC9E,IAAIF,EAAKC,EAAQ,MAAM,KAAK,IAAI,EAAG,KAAK,SAAU,KAAK,UAAWR,EAAM,KAAK,IAAI,EAAE,QAAQ,CAC9F,SAAU,CAACE,EAAUC,EAAWO,IAAW,IAAIH,EAAKI,EAAS,KAAMT,EAAUC,EAAWO,EAAQ,KAAK,QAAQ,CACzH,CAAS,CACJ,CACL,CACA,IAAIE,GACH,SAAUA,EAAM,CACbA,EAAKA,EAAK,SAAc,CAAC,EAAI,WAC7BA,EAAKA,EAAK,UAAe,CAAC,EAAI,YAC9BA,EAAKA,EAAK,WAAgB,CAAC,EAAI,aAC/BA,EAAKA,EAAK,WAAgB,CAAC,EAAI,aAC/BA,EAAKA,EAAK,eAAoB,CAAC,EAAI,iBACnCA,EAAKA,EAAK,WAAgB,CAAC,EAAI,aAC/BA,EAAKA,EAAK,YAAiB,CAAC,EAAI,cAChCA,EAAKA,EAAK,SAAc,CAAC,EAAI,WAC7BA,EAAKA,EAAK,YAAiB,CAAC,EAAI,cAChCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,YAAiB,EAAE,EAAI,cACjCA,EAAKA,EAAK,eAAoB,EAAE,EAAI,iBACpCA,EAAKA,EAAK,eAAoB,EAAE,EAAI,iBACpCA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,cAAmB,EAAE,EAAI,gBACnCA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,aAAkB,EAAE,EAAI,eAClCA,EAAKA,EAAK,2BAAgC,EAAE,EAAI,6BAEhDA,EAAKA,EAAK,OAAY,EAAE,EAAI,SAC5BA,EAAKA,EAAK,OAAY,EAAE,EAAI,SAC5BA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,eAAoB,EAAE,EAAI,iBACpCA,EAAKA,EAAK,KAAU,EAAE,EAAI,OAC1BA,EAAKA,EAAK,MAAW,EAAE,EAAI,QAC3BA,EAAKA,EAAK,WAAgB,EAAE,EAAI,aAChCA,EAAKA,EAAK,QAAa,EAAE,EAAI,UAC7BA,EAAKA,EAAK,QAAa,EAAE,EAAI,UAC7BA,EAAKA,EAAK,sBAA2B,EAAE,EAAI,wBAC3CA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAE9BA,EAAKA,EAAK,WAAgB,EAAE,EAAI,aAChCA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,aAAkB,EAAE,EAAI,eAClCA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,SAAc,EAAE,EAAI,WAC9BA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,UAAe,EAAE,EAAI,YAC/BA,EAAKA,EAAK,IAAS,EAAE,EAAI,KAC7B,GAAGA,IAASA,EAAO,CAAE,EAAC,EAKtB,MAAMC,EAAU,CAIZ,YAIAC,EAIAC,EAAS,CACL,KAAK,MAAQD,EACb,KAAK,QAAUC,EAIf,KAAK,MAAQ,GAIb,KAAK,QAAU,EAClB,CACL,CAIA,MAAMC,EAAK,CACP,aAAc,CAIV,KAAK,KAAO,GAKZ,KAAK,WAAa,EAIlB,KAAK,QAAU,EAIf,KAAK,MAAQ,EAIb,KAAK,QAAU,GAKf,KAAK,IAAM,EAIX,KAAK,OAAS,EAId,KAAK,KAAO,EACf,CAID,SAAU,CACF,KAAK,QAAU,KAAK,KACpB,KAAK,aAAY,CACxB,CAID,cAAe,CACX,IAAIC,EAAS,KAAK,UAAU,KAAK,OAAO,EACxC,KAAK,OAAS,KAAK,YAAYA,EAAQ,KAAK,IAAK,KAAK,MAAM,EAC5D,KAAK,IAAMA,EACX,KAAK,KAAOA,GAAU,KAAK,KAAK,OAAS,GAAK,KAAK,KAAK,WAAWA,CAAM,CAC5E,CAMD,UAAUnB,EAAM,CAAE,OAAOoB,EAAU,KAAK,KAAMpB,CAAI,CAAI,CAItD,MAAMqB,EAAM,CAKR,IAJA,KAAK,KAAOA,EACZ,KAAK,WAAa,KAAK,QAAU,KAAK,IAAM,KAAK,OAAS,EAC1D,KAAK,aAAY,EACjB,KAAK,MAAQ,EACN,KAAK,QAAQ,QAChB,KAAK,QAAQ,KACpB,CAOD,SAASC,EAAI,CACT,KAAK,QAAUA,EACf,KAAK,WAAa,KAAK,YAAYA,EAAI,KAAK,IAAK,KAAK,MAAM,CAC/D,CAID,eAAeC,EAAQ,CACnB,KAAK,WAAaA,EAClB,KAAK,QAAU,KAAK,WAAWA,CAAM,CACxC,CAMD,UAAUC,EAAK,CACX,KAAK,QAAQ,KAAKA,CAAG,CACxB,CAKD,YAAYF,EAAItB,EAAO,EAAGuB,EAAS,EAAG,CAClC,QAASE,EAAIzB,EAAMyB,EAAIH,EAAIG,IACvBF,GAAU,KAAK,KAAK,WAAWE,CAAC,GAAK,EAAI,EAAIF,EAAS,EAAI,EAC9D,OAAOA,CACV,CAID,WAAWG,EAAM,CACb,IAAID,EAAI,EACR,QAASF,EAAS,EAAGE,EAAI,KAAK,KAAK,QAAUF,EAASG,EAAMD,IACxDF,GAAU,KAAK,KAAK,WAAWE,CAAC,GAAK,EAAI,EAAIF,EAAS,EAAI,EAC9D,OAAOE,CACV,CAID,OAAQ,CACJ,GAAI,CAAC,KAAK,WACN,OAAO,KAAK,KAChB,IAAIE,EAAS,GACb,QAASF,EAAI,EAAGA,EAAI,KAAK,QAASA,IAC9BE,GAAU,IACd,OAAOA,EAAS,KAAK,KAAK,MAAM,KAAK,OAAO,CAC/C,CACL,CACA,SAASC,GAAYC,EAAIC,EAAIC,EAAM,CAC/B,GAAIA,EAAK,KAAOA,EAAK,KAAK,QACrBF,GAAMC,EAAG,OAASC,EAAK,QAAUD,EAAG,MAAMC,EAAK,MAAQ,CAAC,EAAE,MAAQA,EAAK,WACxE,MAAO,GACX,GAAIA,EAAK,QAAUA,EAAK,WAAa,EACjC,MAAO,GACX,IAAIC,GAAQH,EAAG,MAAQf,EAAK,YAAcmB,GAAgBC,IAAcH,EAAMD,EAAI,EAAK,EACvF,OAAOE,EAAO,IACTH,EAAG,MAAQf,EAAK,YAAcqB,GAAiBJ,EAAMD,EAAI,EAAK,EAAI,IACnEC,EAAK,KAAK,WAAWA,EAAK,IAAMC,EAAO,CAAC,GAAKH,EAAG,KACxD,CACA,MAAMO,GAAoB,CACtB,CAACtB,EAAK,UAAU,EAAEe,EAAIC,EAAIC,EAAM,CAC5B,OAAIA,EAAK,MAAQ,GACN,IACXA,EAAK,QAAQ,KAAKP,EAAIV,EAAK,UAAWgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,IAAM,CAAC,CAAC,EAC3FA,EAAK,SAASA,EAAK,KAAOM,EAAMN,EAAK,KAAK,WAAWA,EAAK,IAAM,CAAC,CAAC,EAAI,EAAI,EAAE,EAC5EF,EAAG,IAAMC,EAAG,UAAYC,EAAK,KAAK,OAC3B,GACV,EACD,CAACjB,EAAK,QAAQ,EAAEe,EAAIS,EAAKP,EAAM,CAC3B,OAAIA,EAAK,OAASA,EAAK,WAAaF,EAAG,OAASE,EAAK,KAAO,GACjD,IACXA,EAAK,eAAeA,EAAK,WAAaF,EAAG,KAAK,EACvC,GACV,EACD,CAACf,EAAK,WAAW,EAAGc,GACpB,CAACd,EAAK,UAAU,EAAGc,GACnB,CAACd,EAAK,QAAQ,GAAI,CAAE,MAAO,EAAO,CACtC,EACA,SAASuB,EAAME,EAAI,CAAE,OAAOA,GAAM,IAAMA,GAAM,GAAKA,GAAM,IAAMA,GAAM,EAAK,CAC1E,SAASnB,EAAUW,EAAMN,EAAI,EAAG,CAC5B,KAAOA,EAAIM,EAAK,QAAUM,EAAMN,EAAK,WAAWN,CAAC,CAAC,GAC9CA,IACJ,OAAOA,CACX,CACA,SAASe,GAAcT,EAAMN,EAAGH,EAAI,CAChC,KAAOG,EAAIH,GAAMe,EAAMN,EAAK,WAAWN,EAAI,CAAC,CAAC,GACzCA,IACJ,OAAOA,CACX,CACA,SAASgB,GAAaV,EAAM,CACxB,GAAIA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAChC,MAAO,GACX,IAAIvB,EAAMuB,EAAK,IAAM,EACrB,KAAOvB,EAAMuB,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWvB,CAAG,GAAKuB,EAAK,MAC/DvB,IACJ,GAAIA,EAAMuB,EAAK,IAAM,EACjB,MAAO,GACX,GAAIA,EAAK,MAAQ,IACb,QAASN,EAAIjB,EAAKiB,EAAIM,EAAK,KAAK,OAAQN,IACpC,GAAIM,EAAK,KAAK,WAAWN,CAAC,GAAK,GAC3B,MAAO,GACnB,OAAOjB,CACX,CACA,SAASkC,GAAaX,EAAM,CACxB,OAAOA,EAAK,MAAQ,GAAe,GAAKA,EAAK,KAAK,WAAWA,EAAK,IAAM,CAAC,GAAK,GAAK,EAAI,CAC3F,CACA,SAASI,GAAiBJ,EAAMD,EAAIa,EAAU,CAC1C,GAAIZ,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,GACnD,MAAO,GACX,IAAIa,EAAQ,EACZ,QAASpC,EAAMuB,EAAK,IAAM,EAAGvB,EAAMuB,EAAK,KAAK,OAAQvB,IAAO,CACxD,IAAI+B,EAAKR,EAAK,KAAK,WAAWvB,CAAG,EACjC,GAAI+B,GAAMR,EAAK,KACXa,YACK,CAACP,EAAME,CAAE,EACd,MAAO,EACd,CAED,OAAII,GAAYZ,EAAK,MAAQ,IAAMc,GAAkBd,CAAI,EAAI,IAAMA,EAAK,OAASD,EAAG,MAAM,QACtFA,EAAG,OAAO,iBAAiB,QAAQgB,GAAkB,aAAa,EAAI,IAEnEF,EAAQ,EADJ,GACa,CAC5B,CACA,SAASG,GAAOjB,EAAIhC,EAAM,CACtB,QAAS2B,EAAIK,EAAG,MAAM,OAAS,EAAGL,GAAK,EAAGA,IACtC,GAAIK,EAAG,MAAML,CAAC,EAAE,MAAQ3B,EACpB,MAAO,GACf,MAAO,EACX,CACA,SAASoC,GAAaH,EAAMD,EAAIa,EAAU,CACtC,OAAQZ,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,MACtDA,EAAK,KAAOA,EAAK,KAAK,OAAS,GAAKM,EAAMN,EAAK,KAAK,WAAWA,EAAK,IAAM,CAAC,CAAC,KAC5E,CAACY,GAAYI,GAAOjB,EAAIhB,EAAK,UAAU,GAAKiB,EAAK,UAAUA,EAAK,IAAM,CAAC,EAAIA,EAAK,KAAK,QAAU,EAAI,EAC5G,CACA,SAASE,GAAcF,EAAMD,EAAIa,EAAU,CACvC,IAAInC,EAAMuB,EAAK,IAAKiB,EAAOjB,EAAK,KAChC,KACQiB,GAAQ,IAAMA,GAAQ,IADrB,CAEDxC,IAGJ,GAAIA,GAAOuB,EAAK,KAAK,OACjB,MAAO,GACXiB,EAAOjB,EAAK,KAAK,WAAWvB,CAAG,CAClC,CACD,OAAIA,GAAOuB,EAAK,KAAOvB,EAAMuB,EAAK,IAAM,GACnCiB,GAAQ,IAAMA,GAAQ,IACtBxC,EAAMuB,EAAK,KAAK,OAAS,GAAK,CAACM,EAAMN,EAAK,KAAK,WAAWvB,EAAM,CAAC,CAAC,GACnEmC,GAAY,CAACI,GAAOjB,EAAIhB,EAAK,WAAW,IACnCiB,EAAK,UAAUvB,EAAM,CAAC,GAAKuB,EAAK,KAAK,QAAUvB,EAAMuB,EAAK,IAAM,GAAKA,EAAK,MAAQ,IAChF,GACJvB,EAAM,EAAIuB,EAAK,GAC1B,CACA,SAASkB,GAAalB,EAAM,CACxB,GAAIA,EAAK,MAAQ,GACb,MAAO,GACX,IAAIvB,EAAMuB,EAAK,IAAM,EACrB,KAAOvB,EAAMuB,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWvB,CAAG,GAAK,IAC1DA,IACJ,GAAIA,EAAMuB,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWvB,CAAG,GAAK,GACvD,MAAO,GACX,IAAIwB,EAAOxB,EAAMuB,EAAK,IACtB,OAAOC,EAAO,EAAI,GAAKA,CAC3B,CACA,SAASa,GAAkBd,EAAM,CAC7B,GAAIA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAAiBA,EAAK,QAAUA,EAAK,WAAa,EAClF,MAAO,GACX,IAAIvB,EAAMuB,EAAK,IAAM,EACrB,KAAOvB,EAAMuB,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWvB,CAAG,GAAKuB,EAAK,MAC/DvB,IACJ,IAAIN,EAAMM,EACV,KAAOA,EAAMuB,EAAK,KAAK,QAAUM,EAAMN,EAAK,KAAK,WAAWvB,CAAG,CAAC,GAC5DA,IACJ,OAAOA,GAAOuB,EAAK,KAAK,OAAS7B,EAAM,EAC3C,CACA,MAAMgD,EAAY,WAAYC,GAAa,MAAOC,GAAgB,MAC5DC,EAAiB,CACnB,CAAC,oCAAqC,2BAA2B,EACjE,CAAC,WAAYF,EAAU,EACvB,CAAC,UAAWC,EAAa,EACzB,CAAC,cAAe,GAAG,EACnB,CAAC,kBAAmB,OAAO,EAC3B,CAAC,gYAAiYF,CAAS,EAC3Y,CAAC,mHAAoHA,CAAS,CAClI,EACA,SAASI,GAAYvB,EAAMO,EAAKK,EAAU,CACtC,GAAIZ,EAAK,MAAQ,GACb,MAAO,GACX,IAAIwB,EAAOxB,EAAK,KAAK,MAAMA,EAAK,GAAG,EACnC,QAASN,EAAI,EAAG+B,EAAIH,EAAe,QAAUV,EAAW,EAAI,GAAIlB,EAAI+B,EAAG/B,IACnE,GAAI4B,EAAe5B,CAAC,EAAE,CAAC,EAAE,KAAK8B,CAAI,EAC9B,OAAO9B,EACf,MAAO,EACX,CACA,SAASgC,GAAc1B,EAAMvB,EAAK,CAC9B,IAAIkD,EAAc3B,EAAK,YAAYvB,EAAKuB,EAAK,IAAKA,EAAK,MAAM,EACzD4B,EAAW5B,EAAK,YAAYA,EAAK,UAAUvB,CAAG,EAAGA,EAAKkD,CAAW,EACrE,OAAOC,GAAYD,EAAc,EAAIA,EAAc,EAAIC,CAC3D,CACA,SAASC,EAAYC,EAAO7D,EAAMsB,EAAI,CAClC,IAAIX,EAAOkD,EAAM,OAAS,EACtBlD,GAAQ,GAAKkD,EAAMlD,CAAI,EAAE,IAAMX,GAAQ6D,EAAMlD,CAAI,EAAE,MAAQG,EAAK,SAChE+C,EAAMlD,CAAI,EAAE,GAAKW,EAEjBuC,EAAM,KAAKrC,EAAIV,EAAK,SAAUd,EAAMsB,CAAE,CAAC,CAC/C,CAKA,MAAMwC,EAAsB,CACxB,cAAe,OACf,aAAahC,EAAIC,EAAM,CACnB,IAAIgC,EAAOhC,EAAK,WAAa,EAC7B,GAAIA,EAAK,OAASgC,EACd,MAAO,GACX,IAAI/C,EAAQe,EAAK,WAAWgC,CAAI,EAC5B/D,EAAO8B,EAAG,UAAYd,EAAOM,EAAKQ,EAAG,UAAYC,EAAK,KAAK,OAC3D8B,EAAQ,CAAA,EAAIG,EAAe,GAE/B,IADAJ,EAAYC,EAAO7D,EAAMsB,CAAE,EACpBQ,EAAG,YAAcC,EAAK,OAASD,EAAG,MAAM,QAC3C,GAAIC,EAAK,KAAOA,EAAK,KAAK,OAAQ,CAC9B6B,EAAYI,EAAclC,EAAG,UAAY,EAAGA,EAAG,SAAS,EACxD,QAASmC,KAAKlC,EAAK,QACfiC,EAAa,KAAKC,CAAC,CAC1B,KACI,IAAIlC,EAAK,OAASgC,EACnB,MAEC,CACD,GAAIC,EAAa,OAAQ,CACrB,QAASC,KAAKD,EACNC,EAAE,MAAQnD,EAAK,SACf8C,EAAYC,EAAOI,EAAE,KAAMA,EAAE,EAAE,EAE/BJ,EAAM,KAAKI,CAAC,EAEpBD,EAAe,CAAA,CAClB,CACDJ,EAAYC,EAAO/B,EAAG,UAAY,EAAGA,EAAG,SAAS,EACjD,QAASmC,KAAKlC,EAAK,QACf8B,EAAM,KAAKI,CAAC,EAChB3C,EAAKQ,EAAG,UAAYC,EAAK,KAAK,OAC9B,IAAImC,EAAYpC,EAAG,UAAYC,EAAK,WAAWA,EAAK,WAAa,CAAC,EAC9DmC,EAAY5C,GACZsC,EAAYC,EAAOK,EAAW5C,CAAE,CACvC,EAEL,OAAI0C,EAAa,SACbA,EAAeA,EAAa,OAAOC,GAAKA,EAAE,MAAQnD,EAAK,QAAQ,EAC3DkD,EAAa,SACbjC,EAAK,QAAUiC,EAAa,OAAOjC,EAAK,OAAO,IAEvDD,EAAG,QAAQA,EAAG,OAAO,cAAc+B,EAAO,CAAC7D,CAAI,EAAE,OAAOc,EAAK,UAAWQ,EAAKtB,CAAI,EAAGA,CAAI,EACjF,EACV,EACD,WAAW8B,EAAIC,EAAM,CACjB,IAAIoC,EAAW1B,GAAaV,CAAI,EAChC,GAAIoC,EAAW,EACX,MAAO,GACX,IAAInE,EAAO8B,EAAG,UAAYC,EAAK,IAAKQ,EAAKR,EAAK,KAAMqC,EAAMD,EAAWpC,EAAK,IACtEsC,EAAWtC,EAAK,UAAUoC,CAAQ,EAAGG,EAAS9B,GAAcT,EAAK,KAAMA,EAAK,KAAK,OAAQsC,CAAQ,EACjGR,EAAQ,CAACrC,EAAIV,EAAK,SAAUd,EAAMA,EAAOoE,CAAG,CAAC,EAC7CC,EAAWC,GACXT,EAAM,KAAKrC,EAAIV,EAAK,SAAUgB,EAAG,UAAYuC,EAAUvC,EAAG,UAAYwC,CAAM,CAAC,EACjF,QAASC,EAAQ,GAAMzC,EAAG,SAAQ,GAAMC,EAAK,OAASD,EAAG,MAAM,OAAQyC,EAAQ,GAAO,CAClF,IAAI9C,EAAIM,EAAK,IACb,GAAIA,EAAK,OAASA,EAAK,WAAa,EAChC,KAAON,EAAIM,EAAK,KAAK,QAAUA,EAAK,KAAK,WAAWN,CAAC,GAAKc,GACtDd,IACR,GAAIA,EAAIM,EAAK,KAAOqC,GAAOrC,EAAK,UAAUN,CAAC,GAAKM,EAAK,KAAK,OAAQ,CAC9D,QAASkC,KAAKlC,EAAK,QACf8B,EAAM,KAAKI,CAAC,EAChBJ,EAAM,KAAKrC,EAAIV,EAAK,SAAUgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYL,CAAC,CAAC,EACxEK,EAAG,SAAQ,EACX,KACH,KACI,CACIyC,GACDX,EAAYC,EAAO/B,EAAG,UAAY,EAAGA,EAAG,SAAS,EACrD,QAAS,KAAKC,EAAK,QACf8B,EAAM,KAAK,CAAC,EAChB,IAAIW,EAAY1C,EAAG,UAAYC,EAAK,QAAS0C,EAAU3C,EAAG,UAAYC,EAAK,KAAK,OAC5EyC,EAAYC,GACZb,EAAYC,EAAOW,EAAWC,CAAO,CAC5C,CACJ,CACD,OAAA3C,EAAG,QAAQA,EAAG,OAAO,cAAc+B,EAAO,CAAC7D,CAAI,EAC1C,OAAOc,EAAK,WAAYgB,EAAG,YAAW,EAAK9B,CAAI,EAAGA,CAAI,EACpD,EACV,EACD,WAAW8B,EAAIC,EAAM,CACjB,IAAIC,EAAOU,GAAaX,CAAI,EAC5B,OAAIC,EAAO,EACA,IACXF,EAAG,aAAahB,EAAK,WAAYiB,EAAK,GAAG,EACzCD,EAAG,QAAQhB,EAAK,UAAWgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,IAAM,CAAC,EAC/EA,EAAK,SAASA,EAAK,IAAMC,CAAI,EACtB,KACV,EACD,eAAeF,EAAIC,EAAM,CACrB,GAAII,GAAiBJ,EAAMD,EAAI,EAAK,EAAI,EACpC,MAAO,GACX,IAAI9B,EAAO8B,EAAG,UAAYC,EAAK,IAC/B,OAAAD,EAAG,SAAQ,EACXA,EAAG,QAAQhB,EAAK,eAAgBd,CAAI,EAC7B,EACV,EACD,WAAW8B,EAAIC,EAAM,CACjB,IAAIC,EAAOE,GAAaH,EAAMD,EAAI,EAAK,EACvC,GAAIE,EAAO,EACP,MAAO,GACPF,EAAG,MAAM,MAAQhB,EAAK,YACtBgB,EAAG,aAAahB,EAAK,WAAYiB,EAAK,QAASA,EAAK,IAAI,EAC5D,IAAI2C,EAAUjB,GAAc1B,EAAMA,EAAK,IAAM,CAAC,EAC9C,OAAAD,EAAG,aAAahB,EAAK,SAAUiB,EAAK,QAAS2C,EAAU3C,EAAK,UAAU,EACtED,EAAG,QAAQhB,EAAK,SAAUgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,IAAMC,CAAI,EACjFD,EAAK,eAAe2C,CAAO,EACpB,IACV,EACD,YAAY5C,EAAIC,EAAM,CAClB,IAAIC,EAAOC,GAAcF,EAAMD,EAAI,EAAK,EACxC,GAAIE,EAAO,EACP,MAAO,GACPF,EAAG,MAAM,MAAQhB,EAAK,aACtBgB,EAAG,aAAahB,EAAK,YAAaiB,EAAK,QAASA,EAAK,KAAK,WAAWA,EAAK,IAAMC,EAAO,CAAC,CAAC,EAC7F,IAAI0C,EAAUjB,GAAc1B,EAAMA,EAAK,IAAMC,CAAI,EACjD,OAAAF,EAAG,aAAahB,EAAK,SAAUiB,EAAK,QAAS2C,EAAU3C,EAAK,UAAU,EACtED,EAAG,QAAQhB,EAAK,SAAUgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,IAAMC,CAAI,EACjFD,EAAK,eAAe2C,CAAO,EACpB,IACV,EACD,WAAW5C,EAAIC,EAAM,CACjB,IAAIC,EAAOiB,GAAalB,CAAI,EAC5B,GAAIC,EAAO,EACP,MAAO,GACX,IAAI2C,EAAM5C,EAAK,IAAK/B,EAAO8B,EAAG,UAAY6C,EACtCC,EAAapC,GAAcT,EAAK,KAAMA,EAAK,KAAK,OAAQ4C,CAAG,EAAGE,EAAQD,EAC1E,KAAOC,EAAQF,GAAO5C,EAAK,KAAK,WAAW8C,EAAQ,CAAC,GAAK9C,EAAK,MAC1D8C,KACAA,GAASD,GAAcC,GAASF,GAAO,CAACtC,EAAMN,EAAK,KAAK,WAAW8C,EAAQ,CAAC,CAAC,KAC7EA,EAAQ9C,EAAK,KAAK,QACtB,IAAI+C,EAAMhD,EAAG,OACR,MAAMhB,EAAK,WAAY,EAAGkB,CAAI,EAC9B,cAAcF,EAAG,OAAO,YAAYC,EAAK,KAAK,MAAM4C,EAAM3C,EAAO,EAAG6C,CAAK,EAAG7E,EAAOgC,EAAO,CAAC,EAAG,CAAChC,CAAI,EACpG6E,EAAQ9C,EAAK,KAAK,QAClB+C,EAAI,MAAMhE,EAAK,WAAY+D,EAAQF,EAAKC,EAAaD,CAAG,EAC5D,IAAII,EAAOD,EAAI,OAAOhE,EAAK,YAAc,EAAIkB,EAAMD,EAAK,KAAK,OAAS4C,CAAG,EACzE,OAAA7C,EAAG,SAAQ,EACXA,EAAG,QAAQiD,EAAM/E,CAAI,EACd,EACV,EACD,UAAU8B,EAAIC,EAAM,CAChB,IAAIjC,EAAOwD,GAAYvB,EAAMD,EAAI,EAAK,EACtC,GAAIhC,EAAO,EACP,MAAO,GACX,IAAIE,EAAO8B,EAAG,UAAYC,EAAK,IAAK7B,EAAMmD,EAAevD,CAAI,EAAE,CAAC,EAC5D+D,EAAQ,CAAE,EAAEmB,EAAW9E,GAAOgD,EAClC,KAAO,CAAChD,EAAI,KAAK6B,EAAK,IAAI,GAAKD,EAAG,YAAY,CAC1C,GAAIC,EAAK,MAAQD,EAAG,MAAM,OAAQ,CAC9BkD,EAAW,GACX,KACH,CACD,QAASf,KAAKlC,EAAK,QACf8B,EAAM,KAAKI,CAAC,CACnB,CACGe,GACAlD,EAAG,SAAQ,EACf,IAAImD,EAAW/E,GAAOiD,GAAarC,EAAK,aAAeZ,GAAOkD,GAAgBtC,EAAK,2BAA6BA,EAAK,UACjHQ,EAAKQ,EAAG,cACZ,OAAAA,EAAG,QAAQA,EAAG,OAAO,cAAc+B,EAAO,CAAC7D,CAAI,EAAE,OAAOiF,EAAU3D,EAAKtB,CAAI,EAAGA,CAAI,EAC3E,EACV,EACD,cAAe,MACnB,EAMA,MAAMkF,EAAoB,CACtB,YAAYC,EAAM,CACd,KAAK,MAAQ,EACb,KAAK,KAAO,GACZ,KAAK,IAAM,EACX,KAAK,MAAQA,EAAK,MAClB,KAAK,QAAQA,EAAK,OAAO,CAC5B,CACD,SAASrD,EAAIC,EAAMoD,EAAM,CACrB,GAAI,KAAK,OAAS,GACd,MAAO,GACX,IAAIlE,EAAUkE,EAAK,QAAU;AAAA,EAAOpD,EAAK,QACrCqD,EAAS,KAAK,QAAQnE,CAAO,EACjC,OAAImE,EAAS,IAAMA,EAASnE,EAAQ,OACzB,KAAK,SAASa,EAAIqD,EAAMC,CAAM,EAClC,EACV,CACD,OAAOtD,EAAIqD,EAAM,CACb,OAAK,KAAK,OAAS,GAAyB,KAAK,OAAS,IAA2B/D,EAAU+D,EAAK,QAAS,KAAK,GAAG,GAAKA,EAAK,QAAQ,OAC5H,KAAK,SAASrD,EAAIqD,EAAMA,EAAK,QAAQ,MAAM,EAC/C,EACV,CACD,SAASrD,EAAIqD,EAAMf,EAAK,CACpB,OAAAtC,EAAG,eAAeqD,EAAM3D,EAAIV,EAAK,cAAe,KAAK,MAAO,KAAK,MAAQsD,EAAK,KAAK,IAAI,CAAC,EACjF,EACV,CACD,UAAU5C,EAAK,CACX,OAAIA,GACA,KAAK,IAAMA,EAAI,GAAK,KAAK,MACzB,KAAK,KAAK,KAAKA,CAAG,EAClB,KAAK,QACE,KAEPA,IAAQ,KACR,KAAK,MAAQ,IACV,GACV,CACD,QAAQP,EAAS,CACb,OAAS,CACL,GAAI,KAAK,OAAS,GACd,MAAO,GAEN,GAAI,KAAK,OAAS,EAAwB,CAC3C,GAAI,CAAC,KAAK,UAAUoE,GAAepE,EAAS,KAAK,IAAK,KAAK,MAAO,EAAI,CAAC,EACnE,MAAO,GACX,GAAIA,EAAQ,WAAW,KAAK,GAAG,GAAK,GAChC,OAAO,KAAK,MAAQ,GACxB,KAAK,KAAK,KAAKO,EAAIV,EAAK,SAAU,KAAK,IAAM,KAAK,MAAO,KAAK,IAAM,KAAK,MAAQ,CAAC,CAAC,EACnF,KAAK,KACR,SACQ,KAAK,OAAS,GACnB,GAAI,CAAC,KAAK,UAAUwE,GAASrE,EAASG,EAAUH,EAAS,KAAK,GAAG,EAAG,KAAK,KAAK,CAAC,EAC3E,MAAO,WAEN,KAAK,OAAS,EAAuB,CAC1C,IAAIsE,EAAOnE,EAAUH,EAAS,KAAK,GAAG,EAAGf,EAAM,EAC/C,GAAIqF,EAAO,KAAK,IAAK,CACjB,IAAIC,EAAQC,GAAexE,EAASsE,EAAM,KAAK,KAAK,EACpD,GAAIC,EAAO,CACP,IAAIE,EAAWC,EAAQ1E,EAASuE,EAAM,GAAK,KAAK,KAAK,EACjDE,EAAW,IACX,KAAK,UAAUF,CAAK,EACpBtF,EAAMwF,EAEb,CACJ,CACD,OAAKxF,IACDA,EAAMyF,EAAQ1E,EAAS,KAAK,GAAG,GAC5Bf,EAAM,GAAKA,EAAMe,EAAQ,OAASf,EAAM,EAClD,KAEG,QAAOyF,EAAQ1E,EAAS,KAAK,GAAG,CAEvC,CACJ,CACL,CACA,SAAS0E,EAAQtE,EAAMb,EAAK,CACxB,KAAOA,EAAMa,EAAK,OAAQb,IAAO,CAC7B,IAAIwC,EAAO3B,EAAK,WAAWb,CAAG,EAC9B,GAAIwC,GAAQ,GACR,MACJ,GAAI,CAACX,EAAMW,CAAI,EACX,MAAO,EACd,CACD,OAAOxC,CACX,CACA,MAAMoF,EAAoB,CACtB,SAAS9D,EAAIC,EAAMoD,EAAM,CACrB,IAAIU,EAAY9D,EAAK,MAAQD,EAAG,MAAM,OAAS,GAAKe,GAAkBd,CAAI,EACtEiB,EAAOjB,EAAK,KAChB,GAAI8D,EAAY,EACZ,MAAO,GACX,IAAIC,EAAgBtE,EAAIV,EAAK,WAAYgB,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAY+D,CAAS,EAC1F,OAAA/D,EAAG,SAAQ,EACXA,EAAG,eAAeqD,EAAM3D,EAAIwB,GAAQ,GAAKlC,EAAK,eAAiBA,EAAK,eAAgBqE,EAAK,MAAOrD,EAAG,YAAW,EAAI,CAC9G,GAAGA,EAAG,OAAO,YAAYqD,EAAK,QAASA,EAAK,KAAK,EACjDW,CACH,CAAA,CAAC,EACK,EACV,CACD,QAAS,CACL,MAAO,EACV,CACL,CACA,MAAMhD,GAAoB,CACtB,cAAciD,EAAGZ,EAAM,CAAE,OAAOA,EAAK,QAAQ,WAAW,CAAC,GAAK,GAAe,IAAID,GAAoBC,CAAI,EAAI,IAAO,EACpH,eAAgB,CAAE,OAAO,IAAIS,EAAsB,CACvD,EACMI,GAAiB,CACnB,CAACD,EAAGhE,IAASkB,GAAalB,CAAI,GAAK,EACnC,CAACgE,EAAGhE,IAASU,GAAaV,CAAI,GAAK,EACnC,CAACgE,EAAGhE,IAASW,GAAaX,CAAI,GAAK,EACnC,CAACkE,EAAGlE,IAASG,GAAaH,EAAMkE,EAAG,EAAI,GAAK,EAC5C,CAACA,EAAGlE,IAASE,GAAcF,EAAMkE,EAAG,EAAI,GAAK,EAC7C,CAACA,EAAGlE,IAASI,GAAiBJ,EAAMkE,EAAG,EAAI,GAAK,EAChD,CAACA,EAAGlE,IAASuB,GAAYvB,EAAMkE,EAAG,EAAI,GAAK,CAC/C,EACMC,GAAiB,CAAE,KAAM,GAAI,IAAK,CAAC,EAIzC,MAAMC,EAAa,CAIf,YAIAC,EAIAC,EAAOC,EAIPC,EAAQ,CACJ,KAAK,OAASH,EACd,KAAK,MAAQC,EACb,KAAK,OAASE,EACd,KAAK,KAAO,IAAIrF,GAChB,KAAK,MAAQ,GAOb,KAAK,kBAAoB,IAAI,IAC7B,KAAK,UAAY,KAIjB,KAAK,OAAS,EACd,KAAK,GAAKqF,EAAOA,EAAO,OAAS,CAAC,EAAE,GACpC,KAAK,UAAY,KAAK,kBAAoB,KAAK,gBAAkBA,EAAO,CAAC,EAAE,KAC3E,KAAK,MAAQ1G,EAAe,OAAOiB,EAAK,SAAU,EAAG,KAAK,UAAW,EAAG,CAAC,EACzE,KAAK,MAAQ,CAAC,KAAK,KAAK,EACxB,KAAK,UAAYwF,EAAU,OAAS,IAAIE,GAAeF,EAAWD,CAAK,EAAI,KAC3E,KAAK,SAAQ,CAChB,CACD,IAAI,WAAY,CACZ,OAAO,KAAK,iBACf,CACD,SAAU,CACN,GAAI,KAAK,WAAa,MAAQ,KAAK,kBAAoB,KAAK,UACxD,OAAO,KAAK,SAChB,GAAI,CAAE,KAAAtE,CAAM,EAAG,KACf,OAAS,CACL,QAAS0E,EAAQ,IAAK,CAClB,IAAIzD,EAAOjB,EAAK,MAAQ,KAAK,MAAM,OAAS,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAAI,KAChF,KAAO0E,EAAQ1E,EAAK,QAAQ,SAAW,CAACiB,GAAQjB,EAAK,QAAQ0E,CAAK,EAAE,KAAOzD,EAAK,MAAM,CAClF,IAAI0D,EAAO3E,EAAK,QAAQ0E,GAAO,EAC/B,KAAK,QAAQC,EAAK,KAAMA,EAAK,KAAMA,EAAK,EAAE,CAC7C,CACD,GAAI,CAAC1D,EACD,MACJ,KAAK,cAAa,CACrB,CACD,GAAIjB,EAAK,IAAMA,EAAK,KAAK,OACrB,MAEJ,GAAI,CAAC,KAAK,SAAU,EAChB,OAAO,KAAK,QACnB,CACD,GAAI,KAAK,WAAa,KAAK,cAAcA,EAAK,OAAO,EACjD,OAAO,KACXf,EAAO,OAAS,CACZ,QAASlB,KAAQ,KAAK,OAAO,aACzB,GAAIA,EAAM,CACN,IAAI6B,EAAS7B,EAAK,KAAMiC,CAAI,EAC5B,GAAIJ,GAAU,GAAO,CACjB,GAAIA,GAAU,GACV,OAAO,KACXI,EAAK,QAAO,EACZ,SAASf,CACZ,CACJ,CACL,KACH,CACD,IAAImE,EAAO,IAAIpE,GAAU,KAAK,UAAYgB,EAAK,IAAKA,EAAK,KAAK,MAAMA,EAAK,GAAG,CAAC,EAC7E,QAAS4E,KAAS,KAAK,OAAO,iBAC1B,GAAIA,EAAO,CACP,IAAIP,EAASO,EAAM,KAAMxB,CAAI,EACzBiB,GACAjB,EAAK,QAAQ,KAAKiB,CAAM,CAC/B,CACLQ,EAAO,KAAO,KAAK,YACX7E,EAAK,KAAOA,EAAK,KAAK,QADC,CAG3B,GAAIA,EAAK,OAASA,EAAK,WAAa,GAChC,QAAS8E,KAAQ,KAAK,OAAO,aACzB,GAAIA,EAAK,KAAM9E,EAAMoD,CAAI,EACrB,MAAMyB,EAElB,QAASR,KAAUjB,EAAK,QACpB,GAAIiB,EAAO,SAAS,KAAMrE,EAAMoD,CAAI,EAChC,OAAO,KACfA,EAAK,SAAW;AAAA,EAAOpD,EAAK,MAAK,EACjC,QAASkC,KAAKlC,EAAK,QACfoD,EAAK,MAAM,KAAKlB,CAAC,CACxB,CACD,YAAK,WAAWkB,CAAI,EACb,IACV,CACD,OAAO3E,EAAK,CACR,GAAI,KAAK,WAAa,MAAQ,KAAK,UAAYA,EAC3C,MAAM,IAAI,WAAW,8BAA8B,EACvD,KAAK,UAAYA,CACpB,CACD,cAAcQ,EAAO,CACjB,GAAI,CAAC,KAAK,UAAU,OAAO,KAAK,kBAAoBA,EAAO,KAAK,iBAAiB,GAC7E,CAAC,KAAK,UAAU,QAAQ,KAAK,MAAM,IAAI,EACvC,MAAO,GACX,IAAI8F,EAAQ,KAAK,UAAU,UAAU,IAAI,EACzC,OAAKA,GAEL,KAAK,mBAAqBA,EAC1B,KAAK,UAAYC,GAAW,KAAK,kBAAmB,KAAK,MAAM,EAC/D,KAAK,WAAU,EACX,KAAK,kBAAoB,KAAK,IAC9B,KAAK,YACL,KAAK,oBACL,KAAK,SAAQ,IAGb,KAAK,MAAQ,GACb,KAAK,SAAQ,GAEV,IAbI,EAcd,CAID,IAAI,OAAQ,CACR,OAAO,KAAK,MAAM,MACrB,CAKD,WAAWC,EAAQ,KAAK,MAAQ,EAAG,CAC/B,OAAO,KAAK,OAAO,QAAQ,MAAM,KAAK,MAAMA,CAAK,EAAE,IAAI,CAC1D,CAQD,UAAW,CAEP,OADA,KAAK,WAAa,KAAK,KAAK,KAAK,OAC7B,KAAK,iBAAmB,KAAK,IAC7B,KAAK,kBAAoB,KAAK,gBAC9B,KAAK,MAAQ,GACb,KAAK,SAAQ,EACN,KAGP,KAAK,YACL,KAAK,kBAAoB,KAAK,gBAAkB,EAChD,KAAK,WAAU,EACf,KAAK,SAAQ,EACN,GAEd,CAKD,UAAW,CACP,OAAO,KAAK,SAAS,KAAK,gBAAkB,CAAC,EAAE,IAClD,CACD,YAAa,CACT,KAAO,KAAK,OAAS,KAAK,OAAO,OAAS,GAAK,KAAK,mBAAqB,KAAK,OAAO,KAAK,MAAM,EAAE,IAC9F,KAAK,SACL,KAAK,kBAAoB,KAAK,IAAI,KAAK,kBAAmB,KAAK,OAAO,KAAK,MAAM,EAAE,IAAI,CAE9F,CAKD,SAAShG,EAAO,CACZ,IAAI,EAAIkF,GAER,GADA,EAAE,IAAMlF,EACJA,GAAS,KAAK,GACd,EAAE,KAAO,WAGT,EAAE,KAAO,KAAK,YAAYA,CAAK,EAC/B,EAAE,KAAO,EAAE,KAAK,OACZ,KAAK,OAAO,OAAS,EAAG,CACxB,IAAIiG,EAAa,KAAK,kBAAmBC,EAAS,KAAK,OACvD,KAAO,KAAK,OAAOA,CAAM,EAAE,GAAK,EAAE,KAAK,CACnCA,IACA,IAAIC,EAAW,KAAK,OAAOD,CAAM,EAAE,KAC/BrC,EAAQ,KAAK,YAAYsC,CAAQ,EACrC,EAAE,IAAMA,EAAWtC,EAAM,OACzB,EAAE,KAAO,EAAE,KAAK,MAAM,EAAG,KAAK,OAAOqC,EAAS,CAAC,EAAE,GAAKD,CAAU,EAAIpC,EACpEoC,EAAa,EAAE,IAAM,EAAE,KAAK,MAC/B,CACJ,CAEL,OAAO,CACV,CAMD,UAAW,CACP,GAAI,CAAE,KAAAlF,CAAI,EAAK,KAAM,CAAE,KAAAV,EAAM,IAAAnB,CAAK,EAAG,KAAK,SAAS,KAAK,iBAAiB,EAGzE,IAFA,KAAK,gBAAkBA,EACvB6B,EAAK,MAAMV,CAAI,EACRU,EAAK,MAAQ,KAAK,MAAM,OAAQA,EAAK,QAAS,CACjD,IAAID,EAAK,KAAK,MAAMC,EAAK,KAAK,EAAGqF,EAAU,KAAK,OAAO,kBAAkBtF,EAAG,IAAI,EAChF,GAAI,CAACsF,EACD,MAAM,IAAI,MAAM,2BAA6BtG,EAAKgB,EAAG,IAAI,CAAC,EAC9D,GAAI,CAACsF,EAAQtF,EAAI,KAAMC,CAAI,EACvB,MACJA,EAAK,QAAO,CACf,CACJ,CACD,YAAYvB,EAAK,CACb,IAAIwC,EAAO,KAAK,MAAM,MAAMxC,CAAG,EAAGa,EAClC,GAAK,KAAK,MAAM,WAKZA,EAAO2B,GAAQ;AAAA,EAAO,GAAKA,MALH,CACxB,IAAIqE,EAAMrE,EAAK,QAAQ;AAAA,CAAI,EAC3B3B,EAAOgG,EAAM,EAAIrE,EAAOA,EAAK,MAAM,EAAGqE,CAAG,CAC5C,CAID,OAAO7G,EAAMa,EAAK,OAAS,KAAK,GAAKA,EAAK,MAAM,EAAG,KAAK,GAAKb,CAAG,EAAIa,CACvE,CAID,aAAc,CAAE,OAAO,KAAK,MAAQ,KAAK,UAAY,KAAK,UAAY,CAAI,CAI1E,aAAavB,EAAMkB,EAAOjB,EAAQ,EAAG,CACjC,KAAK,MAAQF,EAAe,OAAOC,EAAMC,EAAO,KAAK,UAAYiB,EAAO,KAAK,MAAM,KAAM,KAAK,UAAY,KAAK,KAAK,KAAK,MAAM,EAC/H,KAAK,MAAM,KAAK,KAAK,KAAK,CAC7B,CAKD,eAAelB,EAAMkB,EAAOjB,EAAQ,EAAG,CACnC,KAAK,aAAa,KAAK,OAAO,YAAYD,CAAI,EAAGkB,EAAOjB,CAAK,CAChE,CAID,QAAQuH,EAAOtH,EAAMsB,EAAI,CACjB,OAAOgG,GAAS,WAChBA,EAAQ,IAAI7G,EAAK,KAAK,OAAO,QAAQ,MAAM6G,CAAK,EAAGC,EAAMA,GAAOjG,GAAoC,KAAK,eAAiBtB,CAAI,GAClI,KAAK,MAAM,SAASsH,EAAOtH,EAAO,KAAK,MAAM,IAAI,CACpD,CAKD,WAAWwB,EAAK,CACZ,KAAK,MAAM,SAASA,EAAI,OAAO,KAAK,OAAO,OAAO,EAAGA,EAAI,KAAO,KAAK,MAAM,IAAI,CAClF,CAMD,eAAe2D,EAAM3D,EAAK,CACtB,KAAK,QAAQ,KAAK,OACb,cAAcgG,GAAYhG,EAAI,SAAU2D,EAAK,KAAK,EAAG,CAAC3D,EAAI,IAAI,EAC9D,OAAOA,EAAI,KAAMA,EAAI,GAAKA,EAAI,IAAI,EAAGA,EAAI,IAAI,CACrD,CAID,eAAgB,CACZ,IAAIM,EAAK,KAAK,MAAM,IAAG,EACnB2F,EAAM,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAC1CA,EAAI,SAAS3F,EAAG,OAAO,KAAK,OAAO,OAAO,EAAGA,EAAG,KAAO2F,EAAI,IAAI,EAC/D,KAAK,MAAQA,CAChB,CACD,QAAS,CACL,KAAO,KAAK,MAAM,OAAS,GACvB,KAAK,cAAa,EACtB,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,KAAK,OAAO,QAAS,KAAK,SAAS,CAAC,CAC7E,CACD,QAAQC,EAAM,CACV,OAAO,KAAK,OAAO,OAAS,EACxBC,GAAW,KAAK,OAAQ,EAAGD,EAAK,QAAS,KAAK,OAAO,CAAC,EAAE,KAAM,KAAK,iBAAiB,EAAIA,CAC/F,CAID,WAAWvC,EAAM,CACb,QAASiB,KAAUjB,EAAK,QACpB,GAAIiB,EAAO,OAAO,KAAMjB,CAAI,EACxB,OACR,IAAIyC,EAASJ,GAAY,KAAK,OAAO,YAAYrC,EAAK,QAASA,EAAK,KAAK,EAAGA,EAAK,KAAK,EACtF,KAAK,QAAQ,KAAK,OACb,cAAcyC,EAAQ,CAACzC,EAAK,KAAK,EACjC,OAAOrE,EAAK,UAAWqE,EAAK,QAAQ,MAAM,EAAGA,EAAK,KAAK,CAC/D,CACD,IAAIrF,EAAME,EAAMsB,EAAIlB,EAAU,CAC1B,OAAI,OAAON,GAAQ,SACR0B,EAAI,KAAK,OAAO,YAAY1B,CAAI,EAAGE,EAAMsB,EAAIlB,CAAQ,EACzD,IAAIyH,GAAY/H,EAAME,CAAI,CACpC,CAID,IAAI,QAAS,CAAE,OAAO,IAAI8H,GAAO,KAAK,OAAO,OAAO,CAAI,CAC5D,CACA,SAASH,GAAWpB,EAAQW,EAAQQ,EAAMK,EAAQC,EAAS,CACvD,IAAIC,EAAW1B,EAAOW,CAAM,EAAE,GAC1B9G,EAAW,CAAE,EAAEC,EAAY,CAAA,EAAIW,EAAQ0G,EAAK,KAAOK,EACvD,SAASG,EAAaC,EAAMC,EAAW,CACnC,KAAOA,EAAYD,GAAQF,EAAWE,EAAOF,GAAU,CACnD,IAAIjG,EAAOuE,EAAOW,EAAS,CAAC,EAAE,KAAOe,EACrCF,GAAU/F,EACVmG,GAAQnG,EACRkF,IACAe,EAAW1B,EAAOW,CAAM,EAAE,EAC7B,CACJ,CACD,QAAS3E,EAAKmF,EAAK,WAAYnF,EAAIA,EAAKA,EAAG,YAAa,CACpD2F,EAAa3F,EAAG,KAAOwF,EAAQ,EAAI,EACnC,IAAI/H,EAAOuC,EAAG,KAAOwF,EAAQhD,EAAMsD,EAAQL,EAAQ,IAAIzF,EAAG,IAAI,EAC1D8F,EACAtD,EAAOsD,EAEF9F,EAAG,GAAKwF,EAASE,GACtBlD,EAAO4C,GAAWpB,EAAQW,EAAQ3E,EAAIwF,EAAQC,CAAO,EACrDE,EAAa3F,EAAG,GAAKwF,EAAQ,EAAK,GAGlChD,EAAOxC,EAAG,SAEdnC,EAAS,KAAK2E,CAAI,EAClB1E,EAAU,KAAKL,EAAOgB,CAAK,CAC9B,CACD,OAAAkH,EAAaR,EAAK,GAAKK,EAAQ,EAAK,EAC7B,IAAItH,EAAKiH,EAAK,KAAMtH,EAAUC,EAAWqH,EAAK,GAAKK,EAAS/G,EAAO0G,EAAK,KAAOA,EAAK,KAAK,WAAa,MAAS,CAC1H,CAIA,MAAMY,UAAuBC,EAAO,CAIhC,YAKA7H,EAIA8H,EAIAC,EAIAC,EAIAC,EAIAC,EAIAC,EAIAC,EAIAC,EAAU,CACN,QACA,KAAK,QAAUrI,EACf,KAAK,aAAe8H,EACpB,KAAK,iBAAmBC,EACxB,KAAK,WAAaC,EAClB,KAAK,aAAeC,EACpB,KAAK,kBAAoBC,EACzB,KAAK,cAAgBC,EACrB,KAAK,YAAcC,EACnB,KAAK,SAAWC,EAIhB,KAAK,UAAY,OAAO,OAAO,IAAI,EACnC,QAASC,KAAKtI,EAAQ,MAClB,KAAK,UAAUsI,EAAE,IAAI,EAAIA,EAAE,EAClC,CACD,YAAY3C,EAAOC,EAAWC,EAAQ,CAClC,IAAII,EAAQ,IAAIR,GAAa,KAAME,EAAOC,EAAWC,CAAM,EAC3D,QAAS0C,KAAK,KAAK,SACftC,EAAQsC,EAAEtC,EAAON,EAAOC,EAAWC,CAAM,EAC7C,OAAOI,CACV,CAID,UAAUuC,EAAM,CACZ,IAAIC,EAASC,EAAcF,CAAI,EAC/B,GAAI,CAACC,EACD,OAAO,KACX,GAAI,CAAE,QAAAzI,EAAS,kBAAAkI,CAAmB,EAAG,KACjCJ,EAAe,KAAK,aAAa,MAAO,EAAEC,EAAmB,KAAK,iBAAiB,MAAK,EAAIC,EAAa,KAAK,WAAW,MAAO,EAAEG,EAAgB,KAAK,cAAc,MAAK,EAAIC,EAAc,KAAK,YAAY,MAAO,EAAEH,EAAe,KAAK,aAAa,MAAK,EAAII,EAAW,KAAK,SACpR,GAAIM,EAASF,EAAO,WAAW,EAAG,CAC9BP,EAAoB,OAAO,OAAO,CAAE,EAAEA,CAAiB,EACvD,IAAIU,EAAY5I,EAAQ,MAAM,MAAK,EAAI6I,EACvC,QAASC,KAAKL,EAAO,YAAa,CAC9B,GAAI,CAAE,KAAAM,EAAM,MAAAnC,EAAO,UAAAoC,EAAW,MAAAC,CAAO,EAAG,OAAOH,GAAK,SAAW,CAAE,KAAMA,CAAC,EAAKA,EAC7E,GAAIF,EAAU,KAAKN,GAAKA,EAAE,MAAQS,CAAI,EAClC,SACAC,IACAd,EAAkBU,EAAU,MAAM,EAC9B,CAACzH,EAAIC,GAAIC,KAAS2H,EAAU5H,GAAIC,GAAMF,EAAG,KAAK,GACtD,IAAI+H,EAAKN,EAAU,OACfO,EAAQH,EAAY,CAAC,QAAS,cAAc,EAAKpC,EAC/CsC,GAAM9I,EAAK,aAAe8I,GAAM9I,EAAK,eAAiB,CAAC,QAAS,YAAa,SAAS,EAAI,CAAC,QAAS,WAAW,EADxD,OAE7DwI,EAAU,KAAKzI,EAAS,OAAO,CAC3B,GAAA+I,EACA,KAAAH,EACA,MAAOI,GAAS,CAAC,CAACvJ,EAAS,MAAOuJ,CAAK,CAAC,CAC3C,CAAA,CAAC,EACEF,IACKJ,IACDA,EAAS,CAAA,GACT,MAAM,QAAQI,CAAK,GAAKA,aAAiBG,GACzCP,EAAOE,CAAI,EAAIE,EAEf,OAAO,OAAOJ,EAAQI,CAAK,EAEtC,CACDjJ,EAAU,IAAIqJ,GAAQT,CAAS,EAC3BC,IACA7I,EAAUA,EAAQ,OAAOsJ,GAAUT,CAAM,CAAC,EACjD,CAGD,GAFIF,EAASF,EAAO,KAAK,IACrBzI,EAAUA,EAAQ,OAAO,GAAGyI,EAAO,KAAK,GACxCE,EAASF,EAAO,MAAM,EACtB,QAASc,KAAMd,EAAO,OAAQ,CAC1B,IAAI7B,EAAQ,KAAK,WAAW,QAAQ2C,CAAE,EAAGrC,EAAS,KAAK,YAAY,QAAQqC,CAAE,EACzE3C,EAAQ,KACRkB,EAAalB,CAAK,EAAImB,EAAiBnB,CAAK,EAAI,QAChDM,EAAS,KACTiB,EAAcjB,CAAM,EAAI,OAC/B,CAEL,GAAIyB,EAASF,EAAO,UAAU,EAC1B,QAASD,KAAQC,EAAO,WAAY,CAChC,IAAIe,EAAQxB,EAAW,QAAQQ,EAAK,IAAI,EACxC,GAAIgB,EAAQ,GACR1B,EAAa0B,CAAK,EAAIhB,EAAK,MAC3BT,EAAiByB,CAAK,EAAIhB,EAAK,SAE9B,CACD,IAAI1I,EAAM0I,EAAK,OAASiB,EAASzB,EAAYQ,EAAK,MAAM,EAClDA,EAAK,MAAQiB,EAASzB,EAAYQ,EAAK,KAAK,EAAI,EAAIR,EAAW,OAAS,EAC9EF,EAAa,OAAOhI,EAAK,EAAG0I,EAAK,KAAK,EACtCT,EAAiB,OAAOjI,EAAK,EAAG0I,EAAK,IAAI,EACzCR,EAAW,OAAOlI,EAAK,EAAG0I,EAAK,IAAI,CACtC,CACGA,EAAK,SACLP,EAAa,KAAKO,EAAK,OAAO,CACrC,CAEL,GAAIG,EAASF,EAAO,WAAW,EAC3B,QAASD,KAAQC,EAAO,YAAa,CACjC,IAAIe,EAAQpB,EAAY,QAAQI,EAAK,IAAI,EACzC,GAAIgB,EAAQ,GACRrB,EAAcqB,CAAK,EAAIhB,EAAK,UAE3B,CACD,IAAI1I,EAAM0I,EAAK,OAASiB,EAASrB,EAAaI,EAAK,MAAM,EACnDA,EAAK,MAAQiB,EAASrB,EAAaI,EAAK,KAAK,EAAI,EAAIJ,EAAY,OAAS,EAChFD,EAAc,OAAOrI,EAAK,EAAG0I,EAAK,KAAK,EACvCJ,EAAY,OAAOtI,EAAK,EAAG0I,EAAK,IAAI,CACvC,CACJ,CAEL,OAAIC,EAAO,OACPJ,EAAWA,EAAS,OAAOI,EAAO,IAAI,GACnC,IAAIb,EAAe5H,EAAS8H,EAAcC,EAAkBC,EAAYC,EAAcC,EAAmBC,EAAeC,EAAaC,CAAQ,CACvJ,CAID,YAAYU,EAAM,CACd,IAAIS,EAAQ,KAAK,UAAUT,CAAI,EAC/B,GAAIS,GAAS,KACT,MAAM,IAAI,WAAW,sBAAsBT,CAAI,GAAG,EACtD,OAAOS,CACV,CAMD,YAAY7I,EAAM0G,EAAQ,CACtB,IAAIjG,EAAK,IAAIsI,GAAc,KAAM/I,EAAM0G,CAAM,EAC7CsC,EAAO,QAAS7J,EAAMuH,EAAQvH,EAAMsB,EAAG,KAAM,CACzC,IAAIkB,EAAOlB,EAAG,KAAKtB,CAAG,EACtB,QAAS8J,KAAS,KAAK,cACnB,GAAIA,EAAO,CACP,IAAI3I,EAAS2I,EAAMxI,EAAIkB,EAAMxC,CAAG,EAChC,GAAImB,GAAU,EAAG,CACbnB,EAAMmB,EACN,SAAS0I,CACZ,CACJ,CACL7J,GACH,CACD,OAAOsB,EAAG,eAAe,CAAC,CAC7B,CACL,CACA,SAASuH,EAASkB,EAAG,CACjB,OAAOA,GAAK,MAAQA,EAAE,OAAS,CACnC,CACA,SAASnB,EAAcF,EAAM,CACzB,GAAI,CAAC,MAAM,QAAQA,CAAI,EACnB,OAAOA,EACX,GAAIA,EAAK,QAAU,EACf,OAAO,KACX,IAAIsB,EAAOpB,EAAcF,EAAK,CAAC,CAAC,EAChC,GAAIA,EAAK,QAAU,EACf,OAAOsB,EACX,IAAIjH,EAAO6F,EAAcF,EAAK,MAAM,CAAC,CAAC,EACtC,GAAI,CAAC3F,GAAQ,CAACiH,EACV,OAAOA,GAAQjH,EACnB,IAAIkH,EAAO,CAACF,EAAGG,KAAOH,GAAKhD,GAAM,OAAOmD,GAAKnD,CAAI,EAC7CoD,EAAQH,EAAK,KAAMI,EAAQrH,EAAK,KACpC,MAAO,CACH,MAAOkH,EAAKD,EAAK,MAAOjH,EAAK,KAAK,EAClC,YAAakH,EAAKD,EAAK,YAAajH,EAAK,WAAW,EACpD,WAAYkH,EAAKD,EAAK,WAAYjH,EAAK,UAAU,EACjD,YAAakH,EAAKD,EAAK,YAAajH,EAAK,WAAW,EACpD,OAAQkH,EAAKD,EAAK,OAAQjH,EAAK,MAAM,EACrC,KAAOoH,EAAiBC,EACpB,CAACC,EAAOxE,EAAOC,EAAWC,IAAWoE,EAAMC,EAAMC,EAAOxE,EAAOC,EAAWC,CAAM,EAAGF,EAAOC,EAAWC,CAAM,EAD/EoE,EAAjBC,CAEvB,CACA,CACA,SAAST,EAASW,EAAOrB,EAAM,CAC3B,IAAIS,EAAQY,EAAM,QAAQrB,CAAI,EAC9B,GAAIS,EAAQ,EACR,MAAM,IAAI,WAAW,iDAAiDT,CAAI,EAAE,EAChF,OAAOS,CACX,CACA,IAAIZ,GAAY,CAACzI,EAAS,IAAI,EAC9B,QAASY,EAAI,EAAGgI,EAAMA,EAAO3I,EAAKW,CAAC,EAAGA,IAClC6H,GAAU7H,CAAC,EAAIZ,EAAS,OAAO,CAC3B,GAAIY,EACJ,KAAAgI,EACA,MAAOhI,GAAKX,EAAK,OAAS,CAAA,EAAK,CAAC,CAACR,EAAS,MAAOmB,KAAKW,GAAoB,CAAC,QAAS,cAAc,EAAI,CAAC,QAAS,WAAW,CAAC,CAAC,EAC7H,IAAKqH,GAAQ,UACrB,CAAK,EAEL,MAAMlC,EAAO,CAAA,EACb,MAAMO,EAAO,CACT,YAAYpH,EAAS,CACjB,KAAK,QAAUA,EACf,KAAK,QAAU,GACf,KAAK,MAAQ,EAChB,CACD,MAAMZ,EAAME,EAAMsB,EAAIlB,EAAW,EAAG,CAChC,YAAK,QAAQ,KAAKN,EAAME,EAAMsB,EAAI,EAAIlB,EAAW,CAAC,EAC3C,IACV,CACD,cAAc2K,EAAMhD,EAAS,EAAG,CAC5B,QAASvE,KAAKuH,EACVvH,EAAE,QAAQ,KAAMuE,CAAM,EAC1B,OAAO,IACV,CACD,OAAOjI,EAAMc,EAAQ,CACjB,OAAOH,EAAK,MAAM,CACd,OAAQ,KAAK,QACb,QAAS,KAAK,QACd,OAAQ,KAAK,MACb,MAAOX,EACP,OAAAc,CACZ,CAAS,CACJ,CACL,CAIA,MAAMoK,CAAQ,CAIV,YAKAlL,EAIAE,EAIAsB,EAIAlB,EAAWmH,EAAM,CACb,KAAK,KAAOzH,EACZ,KAAK,KAAOE,EACZ,KAAK,GAAKsB,EACV,KAAK,SAAWlB,CACnB,CAID,QAAQ0E,EAAKiD,EAAQ,CACjB,IAAIkD,EAAWnG,EAAI,QAAQ,OAC3BA,EAAI,cAAc,KAAK,SAAUiD,CAAM,EACvCjD,EAAI,QAAQ,KAAK,KAAK,KAAM,KAAK,KAAOiD,EAAQ,KAAK,GAAKA,EAAQjD,EAAI,QAAQ,OAAS,EAAImG,CAAQ,CACtG,CAID,OAAOvK,EAAS,CACZ,OAAO,IAAIoH,GAAOpH,CAAO,EAAE,cAAc,KAAK,SAAU,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,KAAM,KAAK,GAAK,KAAK,IAAI,CAC5G,CACL,CACA,MAAMmH,EAAY,CACd,YAAYH,EAAM1H,EAAM,CACpB,KAAK,KAAO0H,EACZ,KAAK,KAAO1H,CACf,CACD,IAAI,IAAK,CAAE,OAAO,KAAK,KAAO,KAAK,KAAK,MAAS,CACjD,IAAI,MAAO,CAAE,OAAO,KAAK,KAAK,KAAK,EAAK,CACxC,IAAI,UAAW,CAAE,OAAOuH,CAAO,CAC/B,QAAQzC,EAAKiD,EAAQ,CACjBjD,EAAI,MAAM,KAAK,KAAK,IAAI,EACxBA,EAAI,QAAQ,KAAKA,EAAI,MAAM,OAAS,EAAG,KAAK,KAAOiD,EAAQ,KAAK,GAAKA,EAAQ,EAAE,CAClF,CACD,QAAS,CAAE,OAAO,KAAK,IAAO,CAClC,CACA,SAASvG,EAAI1B,EAAME,EAAMsB,EAAIlB,EAAU,CACnC,OAAO,IAAI4K,EAAQlL,EAAME,EAAMsB,EAAIlB,CAAQ,CAC/C,CACA,MAAM8K,GAAqB,CAAE,QAAS,WAAY,KAAM,cAAc,EAChEC,GAAmB,CAAE,QAAS,WAAY,KAAM,cAAc,EAC9DC,EAAY,CAAA,EAAIC,EAAa,GACnC,MAAMC,CAAgB,CAClB,YAAYxL,EAAME,EAAMsB,EAAIiK,EAAM,CAC9B,KAAK,KAAOzL,EACZ,KAAK,KAAOE,EACZ,KAAK,GAAKsB,EACV,KAAK,KAAOiK,CACf,CACL,CACA,MAAMC,GAAY,qCAClB,IAAIC,EAAc,2DAClB,GAAI,CACAA,EAAc,IAAI,OAAO,kBAAmB,GAAG,CACnD,MACU,CAAG,CACb,MAAMC,EAAgB,CAClB,OAAO5J,EAAIkB,EAAMhC,EAAO,CACpB,GAAIgC,GAAQ,IAAiBhC,GAASc,EAAG,IAAM,EAC3C,MAAO,GACX,IAAI6J,EAAU7J,EAAG,KAAKd,EAAQ,CAAC,EAC/B,QAASS,EAAI,EAAGA,EAAI+J,GAAU,OAAQ/J,IAClC,GAAI+J,GAAU,WAAW/J,CAAC,GAAKkK,EAC3B,OAAO7J,EAAG,OAAON,EAAIV,EAAK,OAAQE,EAAOA,EAAQ,CAAC,CAAC,EAC3D,MAAO,EACV,EACD,OAAOc,EAAIkB,EAAMhC,EAAO,CACpB,GAAIgC,GAAQ,GACR,MAAO,GACX,IAAIiB,EAAI,6BAA6B,KAAKnC,EAAG,MAAMd,EAAQ,EAAGA,EAAQ,EAAE,CAAC,EACzE,OAAOiD,EAAInC,EAAG,OAAON,EAAIV,EAAK,OAAQE,EAAOA,EAAQ,EAAIiD,EAAE,CAAC,EAAE,MAAM,CAAC,EAAI,EAC5E,EACD,WAAWnC,EAAIkB,EAAMhC,EAAO,CACxB,GAAIgC,GAAQ,IAAgBhC,GAASc,EAAG,KAAKd,EAAQ,CAAC,GAAK,GACvD,MAAO,GACX,IAAIR,EAAMQ,EAAQ,EAClB,KAAOR,EAAMsB,EAAG,KAAOA,EAAG,KAAKtB,CAAG,GAAK,IACnCA,IACJ,IAAIwB,EAAOxB,EAAMQ,EAAO4K,EAAU,EAClC,KAAOpL,EAAMsB,EAAG,IAAKtB,IACjB,GAAIsB,EAAG,KAAKtB,CAAG,GAAK,IAEhB,GADAoL,IACIA,GAAW5J,GAAQF,EAAG,KAAKtB,EAAM,CAAC,GAAK,GACvC,OAAOsB,EAAG,OAAON,EAAIV,EAAK,WAAYE,EAAOR,EAAM,EAAG,CAClDgB,EAAIV,EAAK,SAAUE,EAAOA,EAAQgB,CAAI,EACtCR,EAAIV,EAAK,SAAUN,EAAM,EAAIwB,EAAMxB,EAAM,CAAC,CAC7C,CAAA,CAAC,OAGNoL,EAAU,EAGlB,MAAO,EACV,EACD,QAAQ9J,EAAIkB,EAAMhC,EAAO,CACrB,GAAIgC,GAAQ,IAAgBhC,GAASc,EAAG,IAAM,EAC1C,MAAO,GACX,IAAI+C,EAAQ/C,EAAG,MAAMd,EAAQ,EAAGc,EAAG,GAAG,EAClC+J,EAAM,sIAAsI,KAAKhH,CAAK,EAC1J,GAAIgH,EACA,OAAO/J,EAAG,OAAON,EAAIV,EAAK,SAAUE,EAAOA,EAAQ,EAAI6K,EAAI,CAAC,EAAE,OAAQ,CAClErK,EAAIV,EAAK,SAAUE,EAAOA,EAAQ,CAAC,EAEnCQ,EAAIV,EAAK,IAAKE,EAAQ,EAAGA,EAAQ6K,EAAI,CAAC,EAAE,MAAM,EAC9CrK,EAAIV,EAAK,SAAUE,EAAQ6K,EAAI,CAAC,EAAE,OAAQ7K,EAAQ,EAAI6K,EAAI,CAAC,EAAE,MAAM,CACtE,CAAA,CAAC,EAEN,IAAIC,EAAU,+BAA+B,KAAKjH,CAAK,EACvD,GAAIiH,EACA,OAAOhK,EAAG,OAAON,EAAIV,EAAK,QAASE,EAAOA,EAAQ,EAAI8K,EAAQ,CAAC,EAAE,MAAM,CAAC,EAC5E,IAAIC,EAAW,cAAc,KAAKlH,CAAK,EACvC,GAAIkH,EACA,OAAOjK,EAAG,OAAON,EAAIV,EAAK,sBAAuBE,EAAOA,EAAQ,EAAI+K,EAAS,CAAC,EAAE,MAAM,CAAC,EAC3F,IAAI9H,EAAI,mKAAmK,KAAKY,CAAK,EACrL,OAAKZ,EAEEnC,EAAG,OAAON,EAAIV,EAAK,QAASE,EAAOA,EAAQ,EAAIiD,EAAE,CAAC,EAAE,MAAM,CAAC,EADvD,EAEd,EACD,SAASnC,EAAIkB,EAAMhC,EAAO,CACtB,GAAIgC,GAAQ,IAAMA,GAAQ,GACtB,MAAO,GACX,IAAIxC,EAAMQ,EAAQ,EAClB,KAAOc,EAAG,KAAKtB,CAAG,GAAKwC,GACnBxC,IACJ,IAAIwL,EAASlK,EAAG,MAAMd,EAAQ,EAAGA,CAAK,EAAG6D,EAAQ/C,EAAG,MAAMtB,EAAKA,EAAM,CAAC,EAClEyL,EAAUR,EAAY,KAAKO,CAAM,EAAGE,EAAST,EAAY,KAAK5G,CAAK,EACnEsH,EAAU,QAAQ,KAAKH,CAAM,EAAGI,EAAS,QAAQ,KAAKvH,CAAK,EAC3DwH,EAAe,CAACD,IAAW,CAACF,GAAUC,GAAWF,GACjDK,EAAgB,CAACH,IAAY,CAACF,GAAWG,GAAUF,GACnDK,EAAUF,IAAiBrJ,GAAQ,IAAM,CAACsJ,GAAiBL,GAC3DO,EAAWF,IAAkBtJ,GAAQ,IAAM,CAACqJ,GAAgBH,GAChE,OAAOpK,EAAG,OAAO,IAAIwJ,EAAgBtI,GAAQ,GAAKkI,GAAqBC,GAAkBnK,EAAOR,GAAM+L,EAAU,EAAoB,IAAsBC,EAAW,EAAqB,EAAkB,CAAC,CAChN,EACD,UAAU1K,EAAIkB,EAAMhC,EAAO,CACvB,GAAIgC,GAAQ,IAAiBlB,EAAG,KAAKd,EAAQ,CAAC,GAAK,GAC/C,OAAOc,EAAG,OAAON,EAAIV,EAAK,UAAWE,EAAOA,EAAQ,CAAC,CAAC,EAC1D,GAAIgC,GAAQ,GAAI,CACZ,IAAIxC,EAAMQ,EAAQ,EAClB,KAAOc,EAAG,KAAKtB,CAAG,GAAK,IACnBA,IACJ,GAAIsB,EAAG,KAAKtB,CAAG,GAAK,IAAMA,GAAOQ,EAAQ,EACrC,OAAOc,EAAG,OAAON,EAAIV,EAAK,UAAWE,EAAOR,EAAM,CAAC,CAAC,CAC3D,CACD,MAAO,EACV,EACD,KAAKsB,EAAIkB,EAAMhC,EAAO,CAClB,OAAOgC,GAAQ,GAAelB,EAAG,OAAO,IAAIwJ,EAAgBF,EAAWpK,EAAOA,EAAQ,EAAG,CAAC,CAAiB,EAAI,EAClH,EACD,MAAMc,EAAIkB,EAAMhC,EAAO,CACnB,OAAOgC,GAAQ,IAAgBlB,EAAG,KAAKd,EAAQ,CAAC,GAAK,GAC/Cc,EAAG,OAAO,IAAIwJ,EAAgBD,EAAYrK,EAAOA,EAAQ,EAAG,EAAkB,EAAI,EAC3F,EACD,QAAQc,EAAIkB,EAAMhC,EAAO,CACrB,GAAIgC,GAAQ,GACR,MAAO,GAEX,QAASvB,EAAIK,EAAG,MAAM,OAAS,EAAGL,GAAK,EAAGA,IAAK,CAC3C,IAAIgL,EAAO3K,EAAG,MAAML,CAAC,EACrB,GAAIgL,aAAgBnB,IAAoBmB,EAAK,MAAQrB,GAAaqB,EAAK,MAAQpB,GAAa,CAGxF,GAAI,CAACoB,EAAK,MAAQ3K,EAAG,UAAU2K,EAAK,EAAE,GAAKzL,GAAS,CAAC,QAAQ,KAAKc,EAAG,MAAMd,EAAQ,EAAGA,EAAQ,CAAC,CAAC,EAC5F,OAAAc,EAAG,MAAML,CAAC,EAAI,KACP,GAIX,IAAIR,EAAUa,EAAG,YAAYL,CAAC,EAC1BiL,EAAO5K,EAAG,MAAML,CAAC,EAAIkL,GAAW7K,EAAIb,EAASwL,EAAK,MAAQrB,EAAYtK,EAAK,KAAOA,EAAK,MAAO2L,EAAK,KAAMzL,EAAQ,CAAC,EAEtH,GAAIyL,EAAK,MAAQrB,EACb,QAASwB,EAAI,EAAGA,EAAInL,EAAGmL,IAAK,CACxB,IAAI3G,EAAInE,EAAG,MAAM8K,CAAC,EACd3G,aAAaqF,GAAmBrF,EAAE,MAAQmF,IAC1CnF,EAAE,KAAO,EAChB,CACL,OAAOyG,EAAK,EACf,CACJ,CACD,MAAO,EACV,CACL,EACA,SAASC,GAAW7K,EAAIb,EAASnB,EAAMkB,EAAO6L,EAAU,CACpD,GAAI,CAAE,KAAAxL,GAASS,EAAIkB,EAAOlB,EAAG,KAAK+K,CAAQ,EAAGC,EAASD,EAGtD,GAFA5L,EAAQ,QAAQO,EAAIV,EAAK,SAAUE,EAAOA,GAASlB,GAAQgB,EAAK,MAAQ,EAAI,EAAE,CAAC,EAC/EG,EAAQ,KAAKO,EAAIV,EAAK,SAAU+L,EAAW,EAAGA,CAAQ,CAAC,EACnD7J,GAAQ,GAAc,CACtB,IAAIxC,EAAMsB,EAAG,UAAU+K,EAAW,CAAC,EAC/BE,EAAOzH,GAASjE,EAAMb,EAAMsB,EAAG,OAAQA,EAAG,MAAM,EAAG0D,EACnDuH,IACAvM,EAAMsB,EAAG,UAAUiL,EAAK,EAAE,EAEtBvM,GAAOuM,EAAK,KACZvH,EAAQC,GAAepE,EAAMb,EAAMsB,EAAG,OAAQA,EAAG,MAAM,EACnD0D,IACAhF,EAAMsB,EAAG,UAAU0D,EAAM,EAAE,KAGnC1D,EAAG,KAAKtB,CAAG,GAAK,KAChBS,EAAQ,KAAKO,EAAIV,EAAK,SAAU+L,EAAUA,EAAW,CAAC,CAAC,EACvDC,EAAStM,EAAM,EACXuM,GACA9L,EAAQ,KAAK8L,CAAI,EACjBvH,GACAvE,EAAQ,KAAKuE,CAAK,EACtBvE,EAAQ,KAAKO,EAAIV,EAAK,SAAUN,EAAKsM,CAAM,CAAC,EAEnD,SACQ9J,GAAQ,GAAc,CAC3B,IAAIgK,EAAQ3H,GAAehE,EAAMwL,EAAW/K,EAAG,OAAQA,EAAG,OAAQ,EAAK,EACnEkL,IACA/L,EAAQ,KAAK+L,CAAK,EAClBF,EAASE,EAAM,GAEtB,CACD,OAAOxL,EAAI1B,EAAMkB,EAAO8L,EAAQ7L,CAAO,CAC3C,CAIA,SAASqE,GAASjE,EAAML,EAAO+G,EAAQ,CAEnC,GADW1G,EAAK,WAAWL,CAAK,GACpB,GAAc,CACtB,QAASR,EAAMQ,EAAQ,EAAGR,EAAMa,EAAK,OAAQb,IAAO,CAChD,IAAI+B,EAAKlB,EAAK,WAAWb,CAAG,EAC5B,GAAI+B,GAAM,GACN,OAAOf,EAAIV,EAAK,IAAKE,EAAQ+G,EAAQvH,EAAM,EAAIuH,CAAM,EACzD,GAAIxF,GAAM,IAAMA,GAAM,GAClB,MAAO,EACd,CACD,OAAO,IACV,KACI,CACD,IAAIyE,EAAQ,EAAGxG,EAAMQ,EACrB,QAAS2K,EAAU,GAAOnL,EAAMa,EAAK,OAAQb,IAAO,CAChD,IAAI+B,EAAKlB,EAAK,WAAWb,CAAG,EAC5B,GAAI6B,EAAME,CAAE,EACR,MAEC,GAAIoJ,EACLA,EAAU,WAELpJ,GAAM,GACXyE,YAEKzE,GAAM,GAAc,CACzB,GAAI,CAACyE,EACD,MACJA,GACH,MACQzE,GAAM,KACXoJ,EAAU,GAEjB,CACD,OAAOnL,EAAMQ,EAAQQ,EAAIV,EAAK,IAAKE,EAAQ+G,EAAQvH,EAAMuH,CAAM,EAAIvH,GAAOa,EAAK,OAAS,KAAO,EAClG,CACL,CACA,SAASoE,GAAepE,EAAML,EAAO+G,EAAQ,CACzC,IAAI/E,EAAO3B,EAAK,WAAWL,CAAK,EAChC,GAAIgC,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,GACpC,MAAO,GACX,IAAI9C,EAAM8C,GAAQ,GAAK,GAAKA,EAC5B,QAASxC,EAAMQ,EAAQ,EAAG2K,EAAU,GAAOnL,EAAMa,EAAK,OAAQb,IAAO,CACjE,IAAI+B,EAAKlB,EAAK,WAAWb,CAAG,EAC5B,GAAImL,EACAA,EAAU,OACT,IAAIpJ,GAAMrC,EACX,OAAOsB,EAAIV,EAAK,UAAWE,EAAQ+G,EAAQvH,EAAM,EAAIuH,CAAM,EACtDxF,GAAM,KACXoJ,EAAU,IACjB,CACD,OAAO,IACX,CACA,SAAStG,GAAehE,EAAML,EAAO+G,EAAQkF,EAAc,CACvD,QAAStB,EAAU,GAAOnL,EAAMQ,EAAQ,EAAGd,EAAM,KAAK,IAAImB,EAAK,OAAQb,EAAM,GAAG,EAAGA,EAAMN,EAAKM,IAAO,CACjG,IAAI+B,EAAKlB,EAAK,WAAWb,CAAG,EAC5B,GAAImL,EACAA,EAAU,OACT,IAAIpJ,GAAM,GACX,OAAO0K,EAAe,GAAQzL,EAAIV,EAAK,UAAWE,EAAQ+G,EAAQvH,EAAM,EAAIuH,CAAM,EAIlF,GAFIkF,GAAgB,CAAC5K,EAAME,CAAE,IACzB0K,EAAe,IACf1K,GAAM,GACN,MAAO,GACFA,GAAM,KACXoJ,EAAU,IAErB,CACD,OAAO,IACX,CAKA,MAAMvB,EAAc,CAIhB,YAIAhE,EAIA/E,EAIA0G,EAAQ,CACJ,KAAK,OAAS3B,EACd,KAAK,KAAO/E,EACZ,KAAK,OAAS0G,EAId,KAAK,MAAQ,EAChB,CAKD,KAAKvH,EAAK,CAAE,OAAOA,GAAO,KAAK,IAAM,GAAK,KAAK,KAAK,WAAWA,EAAM,KAAK,MAAM,CAAI,CAIpF,IAAI,KAAM,CAAE,OAAO,KAAK,OAAS,KAAK,KAAK,MAAS,CAKpD,MAAMR,EAAMsB,EAAI,CAAE,OAAO,KAAK,KAAK,MAAMtB,EAAO,KAAK,OAAQsB,EAAK,KAAK,MAAM,CAAI,CAIjF,OAAOE,EAAK,CACR,YAAK,MAAM,KAAKA,CAAG,EACZA,EAAI,EACd,CAOD,aAAa1B,EAAME,EAAMsB,EAAI4L,EAAMC,EAAO,CACtC,OAAO,KAAK,OAAO,IAAI7B,EAAgBxL,EAAME,EAAMsB,GAAK4L,EAAO,EAAoB,IAAsBC,EAAQ,EAAqB,EAAkB,CAAC,CAC5J,CAKD,IAAI,aAAc,CACd,QAAS1L,EAAI,KAAK,MAAM,OAAS,EAAGA,GAAK,EAAGA,IAAK,CAC7C,IAAIgL,EAAO,KAAK,MAAMhL,CAAC,EACvB,GAAIgL,aAAgBnB,IAAoBmB,EAAK,MAAQrB,GAAaqB,EAAK,MAAQpB,GAC3E,MAAO,EACd,CACD,MAAO,EACV,CAID,WAAW7J,EAAK,CACZ,OAAO,KAAK,OAAOA,CAAG,CACzB,CAKD,eAAexB,EAAM,CAEjB,QAASyB,EAAIzB,EAAMyB,EAAI,KAAK,MAAM,OAAQA,IAAK,CAC3C,IAAI0L,EAAQ,KAAK,MAAM1L,CAAC,EACxB,GAAI,EAAE0L,aAAiB7B,GAAmB6B,EAAM,KAAK,SAAYA,EAAM,KAAO,GAC1E,SACJ,IAAIC,EAAMD,EAAM,MAAQjC,IAAsBiC,EAAM,MAAQhC,GACxDkC,EAAYF,EAAM,GAAKA,EAAM,KAC7BD,EAAMN,EAAInL,EAAI,EAElB,KAAOmL,GAAK5M,EAAM4M,IAAK,CACnB,IAAIH,EAAO,KAAK,MAAMG,CAAC,EACvB,GAAIH,aAAgBnB,GAAoBmB,EAAK,KAAO,GAAsBA,EAAK,MAAQU,EAAM,MAEzF,EAAEC,IAASD,EAAM,KAAO,GAAuBV,EAAK,KAAO,KACtDA,EAAK,GAAKA,EAAK,KAAOY,GAAa,GAAK,KAAOZ,EAAK,GAAKA,EAAK,MAAQ,GAAKY,EAAY,IAAK,CACjGH,EAAOT,EACP,KACH,CACJ,CACD,GAAI,CAACS,EACD,SACJ,IAAIpN,EAAOqN,EAAM,KAAK,QAASlM,EAAU,CAAA,EACrCD,EAAQkM,EAAK,KAAMhN,EAAMiN,EAAM,GAGnC,GAAIC,EAAK,CACL,IAAIpL,EAAO,KAAK,IAAI,EAAGkL,EAAK,GAAKA,EAAK,KAAMG,CAAS,EACrDrM,EAAQkM,EAAK,GAAKlL,EAClB9B,EAAMiN,EAAM,KAAOnL,EACnBlC,EAAOkC,GAAQ,EAAI,WAAa,gBACnC,CAEGkL,EAAK,KAAK,MACVjM,EAAQ,KAAK,KAAK,IAAIiM,EAAK,KAAK,KAAMlM,EAAOkM,EAAK,EAAE,CAAC,EACzD,QAAS,EAAIN,EAAI,EAAG,EAAInL,EAAG,IACnB,KAAK,MAAM,CAAC,YAAauJ,GACzB/J,EAAQ,KAAK,KAAK,MAAM,CAAC,CAAC,EAC9B,KAAK,MAAM,CAAC,EAAI,KAEhBkM,EAAM,KAAK,MACXlM,EAAQ,KAAK,KAAK,IAAIkM,EAAM,KAAK,KAAMA,EAAM,KAAMjN,CAAG,CAAC,EAC3D,IAAIoN,EAAU,KAAK,IAAIxN,EAAMkB,EAAOd,EAAKe,CAAO,EAEhD,KAAK,MAAM2L,CAAC,EAAIQ,GAAOF,EAAK,MAAQlM,EAAQ,IAAIsK,EAAgB4B,EAAK,KAAMA,EAAK,KAAMlM,EAAOkM,EAAK,IAAI,EAAI,MAC/F,KAAK,MAAMzL,CAAC,EAAI2L,GAAOD,EAAM,IAAMjN,EAAM,IAAIoL,EAAgB6B,EAAM,KAAMjN,EAAKiN,EAAM,GAAIA,EAAM,IAAI,EAAI,MAG7G,KAAK,MAAM,OAAO1L,EAAG,EAAG6L,CAAO,EAE/B,KAAK,MAAM7L,CAAC,EAAI6L,CACvB,CAED,IAAI3L,EAAS,CAAA,EACb,QAASF,EAAIzB,EAAMyB,EAAI,KAAK,MAAM,OAAQA,IAAK,CAC3C,IAAIgL,EAAO,KAAK,MAAMhL,CAAC,EACnBgL,aAAgBzB,GAChBrJ,EAAO,KAAK8K,CAAI,CACvB,CACD,OAAO9K,CACV,CAMD,qBAAqB7B,EAAM,CACvB,QAAS2B,EAAI,KAAK,MAAM,OAAS,EAAGA,GAAK,EAAGA,IAAK,CAC7C,IAAIgL,EAAO,KAAK,MAAMhL,CAAC,EACvB,GAAIgL,aAAgBnB,GAAmBmB,EAAK,MAAQ3M,EAChD,OAAO2B,CACd,CACD,OAAO,IACV,CAQD,YAAY8L,EAAY,CACpB,IAAItM,EAAU,KAAK,eAAesM,CAAU,EAC5C,YAAK,MAAM,OAASA,EACbtM,CACV,CAMD,UAAUjB,EAAM,CAAE,OAAOoB,EAAU,KAAK,KAAMpB,EAAO,KAAK,MAAM,EAAI,KAAK,MAAS,CAClF,IAAIF,EAAME,EAAMsB,EAAIlB,EAAU,CAC1B,OAAI,OAAON,GAAQ,SACR0B,EAAI,KAAK,OAAO,YAAY1B,CAAI,EAAGE,EAAMsB,EAAIlB,CAAQ,EACzD,IAAIyH,GAAY/H,EAAME,CAAI,CACpC,CACL,CACA,SAASwH,GAAYgG,EAAU3J,EAAO,CAClC,GAAI,CAACA,EAAM,OACP,OAAO2J,EACX,GAAI,CAACA,EAAS,OACV,OAAO3J,EACX,IAAIkH,EAAOyC,EAAS,MAAK,EAAIC,EAAK,EAClC,QAAS/G,KAAQ7C,EAAO,CACpB,KAAO4J,EAAK1C,EAAK,QAAUA,EAAK0C,CAAE,EAAE,GAAK/G,EAAK,IAC1C+G,IACJ,GAAIA,EAAK1C,EAAK,QAAUA,EAAK0C,CAAE,EAAE,KAAO/G,EAAK,KAAM,CAC/C,IAAIlD,EAAIuH,EAAK0C,CAAE,EACXjK,aAAawH,IACbD,EAAK0C,CAAE,EAAI,IAAIzC,EAAQxH,EAAE,KAAMA,EAAE,KAAMA,EAAE,GAAIgE,GAAYhE,EAAE,SAAU,CAACkD,CAAI,CAAC,CAAC,EACnF,MAEGqE,EAAK,OAAO0C,IAAM,EAAG/G,CAAI,CAEhC,CACD,OAAOqE,CACX,CAGA,MAAM2C,GAAU,CAAC5M,EAAK,UAAWA,EAAK,SAAUA,EAAK,YAAaA,EAAK,UAAU,EACjF,MAAM0F,EAAe,CACjB,YAAYF,EAAWD,EAAO,CAC1B,KAAK,UAAYC,EACjB,KAAK,MAAQD,EAEb,KAAK,EAAI,EAET,KAAK,SAAW,KAChB,KAAK,YAAc,GAGnB,KAAK,OAAS,KACVC,EAAU,SACV,KAAK,SAAWA,EAAU,KAAK,GAAG,EACzC,CACD,cAAe,CACX,KAAK,SAAW,KAAK,EAAI,KAAK,UAAU,OAAS,KAAK,UAAU,KAAK,GAAG,EAAI,KAC5E,KAAK,OAAS,KACd,KAAK,YAAc,EACtB,CACD,OAAO9F,EAAKmN,EAAW,CACnB,KAAO,KAAK,UAAY,KAAK,SAAS,IAAMnN,GACxC,KAAK,aAAY,EACrB,GAAI,CAAC,KAAK,UAAY,KAAK,SAAS,MAAQA,EAAMA,EAAM,EAAI,GACxD,MAAO,GACX,GAAI,KAAK,YAAc,EAAG,CACtB,IAAIN,EAAM,KAAK,SAAS,GACxB,KAAOA,EAAM,GAAK,KAAK,MAAM,KAAKA,EAAM,EAAGA,CAAG,GAAK;AAAA,GAC/CA,IACJ,KAAK,YAAcA,EAAMA,EAAM,EAAI,CACtC,CACD,IAAI0N,EAAI,KAAK,OACRA,IACDA,EAAI,KAAK,OAAS,KAAK,SAAS,KAAK,SACrCA,EAAE,WAAU,GAEhB,IAAIC,EAAOrN,EAAM,KAAK,SAAS,OAC/B,KAAOoN,EAAE,IAAMC,GACX,GAAI,CAACD,EAAE,OAAQ,EACX,MAAO,GACf,OAAS,CACL,GAAIA,EAAE,MAAQC,EACV,OAAO,KAAK,SAAS,MAAQF,EACjC,GAAI,CAACC,EAAE,WAAWC,CAAI,EAClB,MAAO,EACd,CACJ,CACD,QAAQ1N,EAAM,CACV,IAAIuH,EAAO,KAAK,OAAO,KACvB,OAAOA,GAAQA,EAAK,KAAKpH,EAAS,WAAW,GAAKH,CACrD,CACD,UAAU2B,EAAI,CACV,IAAIgM,EAAM,KAAK,OAAQnJ,EAAM,KAAK,SAAS,OAAQoJ,EAAU,KAAK,aAAe,KAAK,SAAS,QAAU,EAAI,GACzG/M,EAAQc,EAAG,kBAAmB5B,EAAMc,EAAOgN,EAASlM,EAAG,MAAM,SAAS,OACtEmM,EAAU/N,EAAKgO,EAAQF,EAC3B,OAAS,CACL,GAAIF,EAAI,GAAKnJ,EAAMoJ,EAAS,CACxB,GAAID,EAAI,KAAK,aAAeA,EAAI,WAAY,EACxC,SACJ,KACH,CACD,IAAItN,EAAMuG,GAAW+G,EAAI,KAAOnJ,EAAK7C,EAAG,MAAM,EAC9C,GAAIgM,EAAI,GAAKnJ,GAAO7C,EAAG,OAAOA,EAAG,MAAM,EAAE,GACrCA,EAAG,QAAQgM,EAAI,KAAMtN,CAAG,MAEvB,CACD,IAAI2N,EAAQ,IAAI1N,EAAKqB,EAAG,OAAO,QAAQ,MAAMhB,EAAK,SAAS,EAAG,CAAE,EAAE,GAAI,EAAGgB,EAAG,MAAM,QAAQ,EAC1FA,EAAG,kBAAkB,IAAIqM,EAAOL,EAAI,IAAI,EACxChM,EAAG,QAAQqM,EAAO3N,CAAG,CACxB,CAiBD,GAZIsN,EAAI,KAAK,GAAG,OAAO,IACfJ,GAAQ,QAAQI,EAAI,KAAK,EAAE,EAAI,GAC/B5N,EAAM4N,EAAI,GAAKnJ,EACfqJ,EAASlM,EAAG,MAAM,SAAS,SAG3B5B,EAAM+N,EACND,EAASE,EACTD,EAAUH,EAAI,GAAKnJ,EACnBuJ,EAAQpM,EAAG,MAAM,SAAS,SAG9B,CAACgM,EAAI,YAAa,EAClB,KACP,CACD,KAAOhM,EAAG,MAAM,SAAS,OAASkM,GAC9BlM,EAAG,MAAM,SAAS,MAClBA,EAAG,MAAM,UAAU,MAEvB,OAAO5B,EAAMc,CAChB,CACL,CAIA,SAAS+F,GAAWqH,EAAK7H,EAAQ,CAC7B,IAAI/F,EAAM4N,EACV,QAAS3M,EAAI,EAAGA,EAAI8E,EAAO,OAAQ9E,IAAK,CACpC,IAAI4M,EAAU9H,EAAO9E,EAAI,CAAC,EAAE,GAAI6M,EAAQ/H,EAAO9E,CAAC,EAAE,KAC9C4M,EAAUD,IACV5N,GAAO8N,EAAQD,EACtB,CACD,OAAO7N,CACX,CACA,MAAM+N,GAAuBvE,GAAU,CACnC,iBAAkBwE,EAAK,MACvB,eAAgBA,EAAK,iBACrB,qCAAsCA,EAAK,SAC3C,qCAAsCA,EAAK,SAC3C,kBAAmBA,EAAK,SACxB,kBAAmBA,EAAK,SACxB,kBAAmBA,EAAK,SACxB,kBAAmBA,EAAK,SACxB,uBAAwBA,EAAK,QAC7B,OAAQA,EAAK,OACb,OAAQA,EAAK,UACb,eAAgBA,EAAK,SACrB,qBAAsBA,EAAK,OAC3B,qBAAsBA,EAAK,KAC3B,iCAAkCA,EAAK,KACvC,iBAAkBA,EAAK,MACvB,sBAAuBA,EAAK,UAC5B,eAAgBA,EAAK,IACrB,yEAA0EA,EAAK,sBAC/E,qBAAsBA,EAAK,UAC3B,UAAWA,EAAK,OAChB,UAAWA,EAAK,OACpB,CAAC,EAIKpI,GAAS,IAAIkC,EAAe,IAAIyB,GAAQT,EAAS,EAAE,OAAOiF,EAAoB,EAAG,OAAO,KAAKzK,CAAmB,EAAE,IAAI2K,GAAK3K,EAAoB2K,CAAC,CAAC,EAAG,OAAO,KAAK3K,CAAmB,EAAE,IAAI2K,GAAK3L,GAAkB2L,CAAC,CAAC,EAAG,OAAO,KAAK3K,CAAmB,EAAGkC,GAAgB5D,GAAmB,OAAO,KAAKsJ,CAAa,EAAE,IAAI+C,GAAK/C,EAAc+C,CAAC,CAAC,EAAG,OAAO,KAAK/C,CAAa,EAAG,CAAA,CAAE,EAErX,SAASgD,GAAc3J,EAAM/E,EAAMsB,EAAI,CACnC,IAAIiF,EAAS,CAAA,EACb,QAASkI,EAAI1J,EAAK,WAAYvE,EAAMR,GAAOyO,EAAIA,EAAE,YAAa,CAC1D,IAAIE,EAAUF,EAAIA,EAAE,KAAOnN,EAG3B,GAFIqN,EAAUnO,GACV+F,EAAO,KAAK,CAAE,KAAM/F,EAAK,GAAImO,CAAO,CAAE,EACtC,CAACF,EACD,MACJjO,EAAMiO,EAAE,EACX,CACD,OAAOlI,CACX,CAKA,SAASqI,GAAUzF,EAAQ,CACvB,GAAI,CAAE,WAAA0F,EAAY,WAAAC,CAAY,EAAG3F,EAmBjC,MAAO,CAAE,KAlBE4F,GAAW,CAAChK,EAAMsB,IAAU,CACnC,IAAIuD,EAAK7E,EAAK,KAAK,GACnB,GAAI8J,IAAejF,GAAM9I,EAAK,WAAa8I,GAAM9I,EAAK,YAAa,CAC/D,IAAIkO,EAAO,GACX,GAAIpF,GAAM9I,EAAK,WAAY,CACvB,IAAImO,EAAWlK,EAAK,KAAK,SAASjE,EAAK,QAAQ,EAC3CmO,IACAD,EAAO3I,EAAM,KAAK4I,EAAS,KAAMA,EAAS,EAAE,EACnD,CACD,IAAI7I,EAASyI,EAAWG,CAAI,EAC5B,GAAI5I,EACA,MAAO,CAAE,OAAAA,EAAQ,QAASrB,GAAQA,EAAK,KAAK,IAAMjE,EAAK,SAC9D,SACQgO,IAAelF,GAAM9I,EAAK,WAAa8I,GAAM9I,EAAK,SACvD,MAAO,CAAE,OAAQgO,EAAY,QAASJ,GAAc3J,EAAK,KAAMA,EAAK,KAAMA,EAAK,EAAE,CAAC,EAEtF,OAAO,IACf,CAAK,CACY,CACjB,CAEA,MAAMmK,GAAqB,CAAE,QAAS,gBAAiB,KAAM,mBAAmB,EAM1EC,GAAgB,CAClB,YAAa,CAAC,CACN,KAAM,gBACN,MAAO,CAAE,oBAAqBX,EAAK,aAAe,CAC9D,EAAW,CACC,KAAM,oBACN,MAAOA,EAAK,qBACxB,CAAS,EACL,YAAa,CAAC,CACN,KAAM,gBACN,MAAM1M,EAAIkB,EAAMxC,EAAK,CACjB,GAAIwC,GAAQ,KAAiBlB,EAAG,KAAKtB,EAAM,CAAC,GAAK,KAAOsB,EAAG,KAAKtB,EAAM,CAAC,GAAK,IACxE,MAAO,GACX,IAAIwL,EAASlK,EAAG,MAAMtB,EAAM,EAAGA,CAAG,EAAGqE,EAAQ/C,EAAG,MAAMtB,EAAM,EAAGA,EAAM,CAAC,EAClE2L,EAAU,QAAQ,KAAKH,CAAM,EAAGI,EAAS,QAAQ,KAAKvH,CAAK,EAC3DoH,EAAUR,EAAY,KAAKO,CAAM,EAAGE,EAAST,EAAY,KAAK5G,CAAK,EACvE,OAAO/C,EAAG,aAAaoN,GAAoB1O,EAAKA,EAAM,EAAG,CAAC4L,IAAW,CAACF,GAAUC,GAAWF,GAAU,CAACE,IAAY,CAACF,GAAWG,GAAUF,EAAO,CAClJ,EACD,MAAO,UACnB,CAAS,CACT,EAGA,SAASkD,EAAStN,EAAIC,EAAMsN,EAAS,EAAGtE,EAAMhD,EAAS,EAAG,CACtD,IAAInF,EAAQ,EAAG2B,EAAQ,GAAM+K,EAAY,GAAIC,EAAU,GAAIC,EAAM,GAC7DC,EAAY,IAAM,CAClB1E,EAAK,KAAKjJ,EAAG,IAAI,YAAaiG,EAASuH,EAAWvH,EAASwH,EAASzN,EAAG,OAAO,YAAYC,EAAK,MAAMuN,EAAWC,CAAO,EAAGxH,EAASuH,CAAS,CAAC,CAAC,CACtJ,EACI,QAAS7N,EAAI4N,EAAQ5N,EAAIM,EAAK,OAAQN,IAAK,CACvC,IAAIuB,EAAOjB,EAAK,WAAWN,CAAC,EACxBuB,GAAQ,KAAiB,CAACwM,IACtB,CAACjL,GAAS+K,EAAY,KACtB1M,IACJ2B,EAAQ,GACJwG,IACIuE,EAAY,IACZG,IACJ1E,EAAK,KAAKjJ,EAAG,IAAI,iBAAkBL,EAAIsG,EAAQtG,EAAIsG,EAAS,CAAC,CAAC,GAElEuH,EAAYC,EAAU,KAEjBC,GAAOxM,GAAQ,IAAMA,GAAQ,KAC9BsM,EAAY,IACZA,EAAY7N,GAChB8N,EAAU9N,EAAI,GAElB+N,EAAM,CAACA,GAAOxM,GAAQ,EACzB,CACD,OAAIsM,EAAY,KACZ1M,IACImI,GACA0E,KAED7M,CACX,CACA,SAAS8M,GAAQC,EAAK3O,EAAO,CACzB,QAASS,EAAIT,EAAOS,EAAIkO,EAAI,OAAQlO,IAAK,CACrC,IAAIuB,EAAO2M,EAAI,WAAWlO,CAAC,EAC3B,GAAIuB,GAAQ,IACR,MAAO,GACPA,GAAQ,IACRvB,GACP,CACD,MAAO,EACX,CACA,MAAMmO,GAAgB,wCACtB,MAAMC,EAAY,CACd,aAAc,CAIV,KAAK,KAAO,IACf,CACD,SAAS/N,EAAIC,EAAMoD,EAAM,CACrB,GAAI,KAAK,MAAQ,KAAM,CACnB,KAAK,KAAO,GACZ,IAAI2K,EACJ,IAAK/N,EAAK,MAAQ,IAAMA,EAAK,MAAQ,IAAMA,EAAK,MAAQ,MACpD6N,GAAc,KAAKE,EAAW/N,EAAK,KAAK,MAAMA,EAAK,GAAG,CAAC,EAAG,CAC1D,IAAIgO,EAAW,CAAA,EAAiBX,EAAStN,EAAIqD,EAAK,QAAS,EAAG4K,EAAU5K,EAAK,KAAK,GAChEiK,EAAStN,EAAIgO,EAAU/N,EAAK,GAAG,IAC7C,KAAK,KAAO,CAACD,EAAG,IAAI,cAAeqD,EAAK,MAAOA,EAAK,MAAQA,EAAK,QAAQ,OAAQ4K,CAAQ,EACrFjO,EAAG,IAAI,iBAAkBA,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,KAAK,MAAM,CAAC,EAC7F,CACJ,SACQ,KAAK,KAAM,CAChB,IAAId,EAAU,CAAA,EACdmO,EAAStN,EAAIC,EAAK,KAAMA,EAAK,IAAKd,EAASa,EAAG,SAAS,EACvD,KAAK,KAAK,KAAKA,EAAG,IAAI,WAAYA,EAAG,UAAYC,EAAK,IAAKD,EAAG,UAAYC,EAAK,KAAK,OAAQd,CAAO,CAAC,CACvG,CACD,MAAO,EACV,CACD,OAAOa,EAAIqD,EAAM,CACb,OAAK,KAAK,MAEVrD,EAAG,eAAeqD,EAAMrD,EAAG,IAAI,QAASqD,EAAK,MAAOA,EAAK,MAAQA,EAAK,QAAQ,OAAQ,KAAK,IAAI,CAAC,EACzF,IAFI,EAGd,CACL,CAYA,MAAM6K,GAAQ,CACV,YAAa,CACT,CAAE,KAAM,QAAS,MAAO,EAAM,EAC9B,CAAE,KAAM,cAAe,MAAO,CAAE,kBAAmBxB,EAAK,QAAW,EACnE,WACA,CAAE,KAAM,YAAa,MAAOA,EAAK,OAAS,EAC1C,CAAE,KAAM,iBAAkB,MAAOA,EAAK,qBAAuB,CAChE,EACD,WAAY,CAAC,CACL,KAAM,QACN,KAAKzI,EAAGZ,EAAM,CAAE,OAAOuK,GAAQvK,EAAK,QAAS,CAAC,EAAI,IAAI0K,GAAc,IAAO,EAC3E,QAAQ/N,EAAIC,EAAMoD,EAAM,CACpB,GAAIA,EAAK,QAAQ,KAAKc,GAAKA,aAAa4J,EAAW,GAAK,CAACH,GAAQ3N,EAAK,KAAMA,EAAK,OAAO,EACpF,MAAO,GACX,IAAIiB,EAAOlB,EAAG,WACd,OAAO8N,GAAc,KAAK5M,CAAI,GAAKoM,EAAStN,EAAIC,EAAK,KAAMA,EAAK,OAAO,GAAKqN,EAAStN,EAAIkB,EAAMjB,EAAK,OAAO,CAC9G,EACD,OAAQ,eACpB,CAAS,CACT,EACA,MAAMkO,EAAW,CACb,UAAW,CAAE,MAAO,EAAQ,CAC5B,OAAOnO,EAAIqD,EAAM,CACb,OAAArD,EAAG,eAAeqD,EAAMrD,EAAG,IAAI,OAAQqD,EAAK,MAAOA,EAAK,MAAQA,EAAK,QAAQ,OAAQ,CACjFrD,EAAG,IAAI,aAAcqD,EAAK,MAAOA,EAAK,MAAQ,CAAC,EAC/C,GAAGrD,EAAG,OAAO,YAAYqD,EAAK,QAAQ,MAAM,CAAC,EAAGA,EAAK,MAAQ,CAAC,CACjE,CAAA,CAAC,EACK,EACV,CACL,CAOA,MAAM+K,GAAW,CACb,YAAa,CACT,CAAE,KAAM,OAAQ,MAAO,GAAM,MAAO1B,EAAK,IAAM,EAC/C,CAAE,KAAM,aAAc,MAAOA,EAAK,IAAM,CAC3C,EACD,WAAY,CAAC,CACL,KAAM,WACN,KAAK1M,EAAIqD,EAAM,CACX,MAAO,kBAAkB,KAAKA,EAAK,OAAO,GAAKrD,EAAG,WAAU,EAAG,MAAQ,WAAa,IAAImO,GAAa,IACxG,EACD,MAAO,eACnB,CAAS,CACT,EACME,GAAa,4DACbC,GAAQ,kCACRC,GAAqB,uBACrBC,GAAU,gCACVC,GAAiB,oBACvB,SAAS3N,GAAM+M,EAAK3P,EAAMsB,EAAIiB,EAAI,CAC9B,IAAIZ,EAAS,EACb,QAAS,EAAI3B,EAAM,EAAIsB,EAAI,IACnBqO,EAAI,CAAC,GAAKpN,GACVZ,IACR,OAAOA,CACX,CACA,SAAS6O,GAAenP,EAAMrB,EAAM,CAChCoQ,GAAM,UAAYpQ,EAClB,IAAIiE,EAAImM,GAAM,KAAK/O,CAAI,EACvB,GAAI,CAAC4C,GAAKoM,GAAmB,KAAKpM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,GAAG,EAAI,GACtD,MAAO,GACX,IAAI/D,EAAMF,EAAOiE,EAAE,CAAC,EAAE,OACtB,OAAS,CACL,IAAItD,EAAOU,EAAKnB,EAAM,CAAC,EAAG+D,EAC1B,GAAI,aAAa,KAAKtD,CAAI,GACtBA,GAAQ,KAAOiC,GAAMvB,EAAMrB,EAAME,EAAK,GAAG,EAAI0C,GAAMvB,EAAMrB,EAAME,EAAK,GAAG,EACvEA,YACKS,GAAQ,MAAQsD,EAAI,6BAA6B,KAAK5C,EAAK,MAAMrB,EAAME,CAAG,CAAC,GAChFA,EAAMF,EAAOiE,EAAE,UAEf,MACP,CACD,OAAO/D,CACX,CACA,SAASuQ,GAAiBpP,EAAMrB,EAAM,CAClCsQ,GAAQ,UAAYtQ,EACpB,IAAIiE,EAAIqM,GAAQ,KAAKjP,CAAI,EACzB,GAAI,CAAC4C,EACD,MAAO,GACX,IAAItD,EAAOsD,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,OAAS,CAAC,EAC/B,OAAOtD,GAAQ,KAAOA,GAAQ,IAAM,GAAKX,EAAOiE,EAAE,CAAC,EAAE,QAAUtD,GAAQ,IAAM,EAAI,EACrF,CAMA,MAAM+P,GAAW,CACb,YAAa,CAAC,CACN,KAAM,WACN,MAAM5O,EAAIkB,EAAM2N,EAAQ,CACpB,IAAInQ,EAAMmQ,EAAS7O,EAAG,OACtB,GAAItB,GAAO,KAAK,KAAKsB,EAAG,KAAKtB,EAAM,CAAC,CAAC,EACjC,MAAO,GACX2P,GAAW,UAAY3P,EACvB,IAAIyD,EAAIkM,GAAW,KAAKrO,EAAG,IAAI,EAAG5B,EAAM,GACxC,GAAI,CAAC+D,EACD,MAAO,GACX,GAAIA,EAAE,CAAC,GAAKA,EAAE,CAAC,GAEX,GADA/D,EAAMsQ,GAAe1O,EAAG,KAAMtB,EAAMyD,EAAE,CAAC,EAAE,MAAM,EAC3C/D,EAAM,IAAM4B,EAAG,YAAa,CAC5B,IAAI8O,EAAY,wBAAwB,KAAK9O,EAAG,KAAK,MAAMtB,EAAKN,CAAG,CAAC,EACpEA,EAAMM,EAAMoQ,EAAU,CAAC,EAAE,MAC5B,OAEI3M,EAAE,CAAC,EACR/D,EAAMuQ,GAAiB3O,EAAG,KAAMtB,CAAG,GAGnCN,EAAMuQ,GAAiB3O,EAAG,KAAMtB,EAAMyD,EAAE,CAAC,EAAE,MAAM,EAC7C/D,EAAM,IAAM+D,EAAE,CAAC,GAAK,UACpBsM,GAAe,UAAYrQ,EAC3B+D,EAAIsM,GAAe,KAAKzO,EAAG,IAAI,EAC3BmC,IACA/D,EAAM+D,EAAE,MAAQA,EAAE,CAAC,EAAE,UAGjC,OAAI/D,EAAM,EACC,IACX4B,EAAG,WAAWA,EAAG,IAAI,MAAO6O,EAAQzQ,EAAM4B,EAAG,MAAM,CAAC,EAC7C5B,EAAM4B,EAAG,OACnB,CACb,CAAS,CACT,EAMM+O,GAAM,CAACb,GAAOE,GAAUf,GAAeuB,EAAQ,EACrD,SAASI,GAAcvO,EAAIwC,EAAM2B,EAAM,CACnC,MAAO,CAAC5E,EAAIkB,EAAMxC,IAAQ,CACtB,GAAIwC,GAAQT,GAAMT,EAAG,KAAKtB,EAAM,CAAC,GAAK+B,EAClC,MAAO,GACX,IAAIwI,EAAO,CAACjJ,EAAG,IAAI4E,EAAMlG,EAAKA,EAAM,CAAC,CAAC,EACtC,QAASiB,EAAIjB,EAAM,EAAGiB,EAAIK,EAAG,IAAKL,IAAK,CACnC,IAAIuB,EAAOlB,EAAG,KAAKL,CAAC,EACpB,GAAIuB,GAAQT,EACR,OAAOT,EAAG,WAAWA,EAAG,IAAIiD,EAAMvE,EAAKiB,EAAI,EAAGsJ,EAAK,OAAOjJ,EAAG,IAAI4E,EAAMjF,EAAGA,EAAI,CAAC,CAAC,CAAC,CAAC,EAGtF,GAFIuB,GAAQ,IACR+H,EAAK,KAAKjJ,EAAG,IAAI,SAAUL,EAAGA,IAAM,CAAC,CAAC,EACtCY,EAAMW,CAAI,EACV,KACP,CACD,MAAO,EACf,CACA,CAMA,MAAM+N,GAAc,CAChB,YAAa,CACT,CAAE,KAAM,cAAe,MAAOvC,EAAK,QAAQA,EAAK,OAAO,CAAG,EAC1D,CAAE,KAAM,kBAAmB,MAAOA,EAAK,qBAAuB,CACjE,EACD,YAAa,CAAC,CACN,KAAM,cACN,MAAOsC,GAAc,GAAc,cAAe,iBAAiB,CAC/E,CAAS,CACT,EAMME,GAAY,CACd,YAAa,CACT,CAAE,KAAM,YAAa,MAAOxC,EAAK,QAAQA,EAAK,OAAO,CAAG,EACxD,CAAE,KAAM,gBAAiB,MAAOA,EAAK,qBAAuB,CAC/D,EACD,YAAa,CAAC,CACN,KAAM,YACN,MAAOsC,GAAc,IAAe,YAAa,eAAe,CAC5E,CAAS,CACT,EAKMG,GAAQ,CACV,YAAa,CAAC,CAAE,KAAM,QAAS,MAAOzC,EAAK,UAAW,EACtD,YAAa,CAAC,CACN,KAAM,QACN,MAAM1M,EAAIkB,EAAMxC,EAAK,CACjB,IAAI0Q,EACJ,OAAIlO,GAAQ,IAAgB,EAAEkO,EAAQ,kBAAkB,KAAKpP,EAAG,MAAMtB,EAAM,EAAGsB,EAAG,GAAG,CAAC,GAC3E,GACJA,EAAG,WAAWA,EAAG,IAAI,QAAStB,EAAKA,EAAM,EAAI0Q,EAAM,CAAC,EAAE,MAAM,CAAC,CACvE,CACb,CAAS,CACT,ECtvEMC,GAAoBC,GAAoB,CAAE,cAAe,CAAE,MAAO,CAAE,KAAM,OAAQ,MAAO,KAAO,CAAA,CAAI,CAAA,EACpGC,GAA2B,IAAI/Q,EAC/BgR,GAA0BlL,GAAO,UAAU,CAC7C,MAAO,CACUmL,GAAa,IAAIzR,GACnB,CAACA,EAAK,GAAG,OAAO,GAAKA,EAAK,GAAG,UAAU,GAAK0R,GAAU1R,CAAI,GAAK,MAAQ2R,GAAO3R,CAAI,EAAI,OACvF,CAAC4H,EAAMgK,KAAW,CAAE,KAAMA,EAAM,IAAI,OAAOhK,EAAK,IAAI,EAAE,GAAI,GAAIA,EAAK,EAAI,EAChF,EACY2J,GAAY,IAAIG,EAAS,EACzBG,GAAe,IAAI,CAC5B,SAAU,IAAM,IAC5B,CAAS,EACYC,GAAiB,IAAI,CAC9B,SAAUT,EACtB,CAAS,CACJ,CACL,CAAC,EACD,SAASK,GAAU1R,EAAM,CACrB,IAAIoR,EAAQ,8BAA8B,KAAKpR,EAAK,IAAI,EACxD,OAAOoR,EAAQ,CAACA,EAAM,CAAC,EAAI,MAC/B,CACA,SAASO,GAAO3R,EAAM,CAClB,OAAOA,EAAK,MAAQ,eAAiBA,EAAK,MAAQ,YACtD,CACA,SAAS+R,GAAeC,EAAYC,EAAO,CACvC,IAAIpR,EAAOmR,EACX,OAAS,CACL,IAAI9O,EAAOrC,EAAK,YAAaqR,EAC7B,GAAI,CAAChP,IAASgP,EAAUR,GAAUxO,EAAK,IAAI,IAAM,MAAQgP,GAAWD,EAChE,MACJpR,EAAOqC,CACV,CACD,OAAOrC,EAAK,EAChB,CACA,MAAMsR,GAA4BC,GAAY,GAAG,CAACR,EAAO1Q,EAAOd,IAAQ,CACpE,QAAS6E,EAAOoN,EAAWT,CAAK,EAAE,aAAaxR,EAAK,EAAE,EAAG6E,GACjD,EAAAA,EAAK,KAAO/D,GAD2C+D,EAAOA,EAAK,OAAQ,CAG/E,IAAIiN,EAAUjN,EAAK,KAAK,KAAKsM,EAAW,EACxC,GAAIW,GAAW,KACX,SACJ,IAAI7J,EAAO0J,GAAe9M,EAAMiN,CAAO,EACvC,GAAI7J,EAAOjI,EACP,MAAO,CAAE,KAAMA,EAAK,GAAIiI,CAAI,CACnC,CACD,OAAO,IACX,CAAC,EACD,SAASiK,GAAOhM,EAAQ,CACpB,OAAO,IAAIiM,GAASlB,GAAM/K,EAAQ,CAAC6L,EAAY,EAAG,UAAU,CAChE,CAIK,MAACK,GAAkCF,GAAOd,EAAU,EACnDiB,GAAwBjB,GAAW,UAAU,CAACT,GAAKG,GAAWD,GAAaE,GAAO,CAChF,MAAO,CACUM,GAAa,IAAI,CAC1B,MAAO,CAAC7J,EAAMgK,KAAW,CAAE,KAAMA,EAAM,IAAI,OAAOhK,EAAK,IAAI,EAAE,GAAI,GAAIA,EAAK,IAC1F,CAAa,CACJ,CACJ,CAAA,CAAC,EAKA8K,GAAgCJ,GAAOG,EAAQ,EACrD,SAASE,GAAcC,EAAWC,EAAiB,CAC/C,OAAQ3D,GAAS,CACb,GAAIA,GAAQ0D,EAAW,CACnB,IAAIxI,EAAQ,KAOZ,GALA8E,EAAO,MAAM,KAAKA,CAAI,EAAE,CAAC,EACrB,OAAO0D,GAAa,WACpBxI,EAAQwI,EAAU1D,CAAI,EAEtB9E,EAAQ0I,GAAoB,kBAAkBF,EAAW1D,EAAM,EAAI,EACnE9E,aAAiB0I,GACjB,OAAO1I,EAAM,QAAUA,EAAM,QAAQ,SAAS,OAAS2I,GAAa,kBAAkB3I,EAAM,KAAM,CAAA,EACjG,GAAIA,EACL,OAAOA,EAAM,MACpB,CACD,OAAOyI,EAAkBA,EAAgB,OAAS,IAC1D,CACA,CAEA,MAAMG,CAAQ,CACV,YAAY/N,EAAM/E,EAAMsB,EAAIyR,EAAaC,EAAYlT,EAAMmT,EAAM,CAC7D,KAAK,KAAOlO,EACZ,KAAK,KAAO/E,EACZ,KAAK,GAAKsB,EACV,KAAK,YAAcyR,EACnB,KAAK,WAAaC,EAClB,KAAK,KAAOlT,EACZ,KAAK,KAAOmT,CACf,CACD,MAAMC,EAAUlO,EAAW,GAAM,CAC7B,IAAIrD,EAAS,KAAK,aAAe,KAAK,KAAK,MAAQ,aAAe,IAAM,IACxE,GAAIuR,GAAY,KAAM,CAClB,KAAOvR,EAAO,OAASuR,GACnBvR,GAAU,IACd,OAAOA,CACV,KACI,CACD,QAASF,EAAI,KAAK,GAAK,KAAK,KAAOE,EAAO,OAAS,KAAK,WAAW,OAAQF,EAAI,EAAGA,IAC9EE,GAAU,IACd,OAAOA,GAAUqD,EAAW,KAAK,WAAa,GACjD,CACJ,CACD,OAAOmO,EAAKC,EAAK,CACb,IAAIC,EAAS,KAAK,KAAK,MAAQ,cAAgB,OAAQ,CAACC,GAAW,KAAK,KAAMH,CAAG,EAAE,CAAC,EAAIC,CAAK,EAAG,GAChG,OAAO,KAAK,YAAcC,EAAS,KAAK,KAAO,KAAK,UACvD,CACL,CACA,SAASE,GAAWxO,EAAMoO,EAAK,CAC3B,IAAIK,EAAQ,CAAA,EAAIC,EAAU,GAC1B,QAAS3F,EAAM/I,EAAM+I,EAAKA,EAAMA,EAAI,OAAQ,CACxC,GAAIA,EAAI,MAAQ,aACZ,OAAO2F,GACP3F,EAAI,MAAQ,YAAcA,EAAI,MAAQ,eACtC0F,EAAM,KAAK1F,CAAG,CACrB,CACD,QAASrM,EAAI+R,EAAM,OAAS,EAAG/R,GAAK,EAAGA,IAAK,CACxC,IAAIsD,EAAOyO,EAAM/R,CAAC,EAAGyP,EACjBnP,EAAOoR,EAAI,OAAOpO,EAAK,IAAI,EAAG8H,EAAW9H,EAAK,KAAOhD,EAAK,KAC9D,GAAIgD,EAAK,MAAQ,eAAiBmM,EAAQ,WAAW,KAAKnP,EAAK,KAAK,MAAM8K,CAAQ,CAAC,GAC/E4G,EAAQ,KAAK,IAAIX,EAAQ/N,EAAM8H,EAAUA,EAAWqE,EAAM,CAAC,EAAE,OAAQ,GAAIA,EAAM,CAAC,EAAG,IAAK,IAAI,CAAC,UAExFnM,EAAK,MAAQ,YAAcA,EAAK,OAAO,MAAQ,gBACnDmM,EAAQ,qBAAqB,KAAKnP,EAAK,KAAK,MAAM8K,CAAQ,CAAC,GAAI,CAChE,IAAIhI,EAAQqM,EAAM,CAAC,EAAG9M,EAAM8M,EAAM,CAAC,EAAE,OACjCrM,EAAM,QAAU,IAChBA,EAAQA,EAAM,MAAM,EAAGA,EAAM,OAAS,CAAC,EACvCT,GAAO,GAEXqP,EAAQ,KAAK,IAAIX,EAAQ/N,EAAK,OAAQ8H,EAAUA,EAAWzI,EAAK8M,EAAM,CAAC,EAAGrM,EAAOqM,EAAM,CAAC,EAAGnM,CAAI,CAAC,CACnG,SACQA,EAAK,MAAQ,YAAcA,EAAK,OAAO,MAAQ,eACnDmM,EAAQ,qCAAqC,KAAKnP,EAAK,KAAK,MAAM8K,CAAQ,CAAC,GAAI,CAChF,IAAIhI,EAAQqM,EAAM,CAAC,EAAG9M,EAAM8M,EAAM,CAAC,EAAE,OACjCrM,EAAM,OAAS,IACfA,EAAQA,EAAM,MAAM,EAAGA,EAAM,OAAS,CAAC,EACvCT,GAAO,GAEX,IAAItE,EAAOoR,EAAM,CAAC,EACdA,EAAM,CAAC,IACPpR,GAAQoR,EAAM,CAAC,EAAE,QAAQ,OAAQ,GAAG,GACxCuC,EAAQ,KAAK,IAAIX,EAAQ/N,EAAK,OAAQ8H,EAAUA,EAAWzI,EAAK8M,EAAM,CAAC,EAAGrM,EAAO/E,EAAMiF,CAAI,CAAC,CAC/F,CACJ,CACD,OAAO0O,CACX,CACA,SAASH,GAAWL,EAAME,EAAK,CAC3B,MAAO,sBAAsB,KAAKA,EAAI,YAAYF,EAAK,KAAMA,EAAK,KAAO,EAAE,CAAC,CAChF,CACA,SAASS,EAAa7O,EAAOsO,EAAKQ,EAAS5L,EAAS,EAAG,CACnD,QAAS6L,EAAO,GAAI7O,EAAOF,IAAS,CAChC,GAAIE,EAAK,MAAQ,WAAY,CACzB,IAAId,EAAIqP,GAAWvO,EAAMoO,CAAG,EACxBE,EAAS,CAACpP,EAAE,CAAC,EACjB,GAAI2P,GAAQ,EAAG,CACX,GAAIP,GAAUO,EAAO,EACjB,OACJD,EAAQ,KAAK,CAAE,KAAM5O,EAAK,KAAOd,EAAE,CAAC,EAAE,OAAQ,GAAIc,EAAK,KAAOd,EAAE,CAAC,EAAE,OAAQ,OAAQ,OAAO2P,EAAO,EAAI7L,CAAM,CAAC,CAAE,CACjH,CACD6L,EAAOP,CACV,CACD,IAAIrQ,EAAO+B,EAAK,YAChB,GAAI,CAAC/B,EACD,MACJ+B,EAAO/B,CACV,CACL,CACA,SAAS6Q,GAAgB5S,EAASyQ,EAAO,CACrC,IAAIoC,EAAQ,UAAU,KAAK7S,CAAO,EAAE,CAAC,EAAE,OACvC,GAAI,CAAC6S,GAASpC,EAAM,MAAMqC,EAAU,GAAK,IACrC,OAAO9S,EACX,IAAI+S,EAAMC,EAAYhT,EAAS,EAAG6S,CAAK,EACnCzR,EAAQ,GACZ,QAAS,EAAI2R,EAAK,EAAI,GACd,GAAK,GACL3R,GAAS,IACT,GAAK,IAGLA,GAAS,IACT,KAGR,OAAOA,EAAQpB,EAAQ,MAAM6S,CAAK,CACtC,CAYK,MAACI,GAA8B,CAAC,CAAE,MAAAxC,EAAO,SAAAyC,KAAe,CACzD,IAAIzM,EAAOyK,EAAWT,CAAK,EAAG,CAAE,IAAAyB,CAAK,EAAGzB,EACpC0C,EAAO,KAAMT,EAAUjC,EAAM,cAAc2C,GAAS,CACpD,GAAI,CAACA,EAAM,OAAS,CAAC7B,GAAiB,WAAWd,EAAO2C,EAAM,KAAM,CAAC,EACjE,OAAOD,EAAO,CAAE,MAAAC,GACpB,IAAI7T,EAAM6T,EAAM,KAAMtS,EAAOoR,EAAI,OAAO3S,CAAG,EACvCiT,EAAUF,GAAW7L,EAAK,aAAalH,EAAK,EAAE,EAAG2S,CAAG,EACxD,KAAOM,EAAQ,QAAUA,EAAQA,EAAQ,OAAS,CAAC,EAAE,KAAOjT,EAAMuB,EAAK,MACnE0R,EAAQ,IAAG,EACf,GAAI,CAACA,EAAQ,OACT,OAAOW,EAAO,CAAE,MAAAC,GACpB,IAAIxJ,EAAQ4I,EAAQA,EAAQ,OAAS,CAAC,EACtC,GAAI5I,EAAM,GAAKA,EAAM,WAAW,OAASrK,EAAMuB,EAAK,KAChD,OAAOqS,EAAO,CAAE,MAAAC,GACpB,IAAIC,EAAY9T,GAAQqK,EAAM,GAAKA,EAAM,WAAW,QAAW,CAAC,KAAK,KAAK9I,EAAK,KAAK,MAAM8I,EAAM,EAAE,CAAC,EAEnG,GAAIA,EAAM,MAAQyJ,EAAW,CACzB,IAAI/P,EAAQsG,EAAM,KAAK,WAAY0J,EAAS1J,EAAM,KAAK,SAAS,WAAY,UAAU,EAEtF,GAAItG,EAAM,IAAM/D,GAAO+T,GAAUA,EAAO,GAAK/T,GACzCuB,EAAK,KAAO,GAAK,CAAC,SAAS,KAAKoR,EAAI,OAAOpR,EAAK,KAAO,CAAC,EAAE,IAAI,EAAG,CACjE,IAAIiB,EAAOyQ,EAAQ,OAAS,EAAIA,EAAQA,EAAQ,OAAS,CAAC,EAAI,KAC1De,EAAOC,EAAS,GAChBzR,GAAQA,EAAK,MACbwR,EAAQzS,EAAK,KAAOiB,EAAK,KACzByR,EAASzR,EAAK,OAAOmQ,EAAK,CAAC,GAG3BqB,EAAQzS,EAAK,MAAQiB,EAAOA,EAAK,GAAK,GAE1C,IAAI2Q,EAAU,CAAC,CAAE,KAAMa,EAAO,GAAIhU,EAAK,OAAAiU,CAAM,CAAE,EAC/C,OAAI5J,EAAM,KAAK,MAAQ,eACnB6I,EAAa7I,EAAM,KAAMsI,EAAKQ,EAAS,EAAE,EACzC3Q,GAAQA,EAAK,KAAK,MAAQ,eAC1B0Q,EAAa1Q,EAAK,KAAMmQ,EAAKQ,CAAO,EACjC,CAAE,MAAOe,EAAgB,OAAOF,EAAQC,EAAO,MAAM,EAAG,QAAAd,EAClE,KACI,CACD,IAAIc,EAASE,GAAUlB,EAAS/B,EAAO3P,CAAI,EAC3C,MAAO,CAAE,MAAO2S,EAAgB,OAAOlU,EAAMiU,EAAO,OAAS,CAAC,EAC1D,QAAS,CAAE,KAAM1S,EAAK,KAAM,OAAQ0S,EAAS/C,EAAM,SAAS,EACnE,CACJ,CACD,GAAI7G,EAAM,KAAK,MAAQ,cAAgByJ,GAAavS,EAAK,KAAM,CAC3D,IAAI6S,EAAWzB,EAAI,OAAOpR,EAAK,KAAO,CAAC,EAAG8S,EAAS,QAAQ,KAAKD,EAAS,IAAI,EAE7E,GAAIC,GAAUA,EAAO,OAAShK,EAAM,KAAM,CACtC,IAAI8I,EAAUjC,EAAM,QAAQ,CAAC,CAAE,KAAMkD,EAAS,KAAOC,EAAO,MAAO,GAAID,EAAS,EAAI,EAChF,CAAE,KAAM7S,EAAK,KAAO8I,EAAM,KAAM,GAAI9I,EAAK,EAAI,CAAA,CAAC,EAClD,MAAO,CAAE,MAAOsS,EAAM,IAAIV,CAAO,EAAG,QAAAA,EACvC,CACJ,CACD,IAAIA,EAAU,CAAA,EACV9I,EAAM,KAAK,MAAQ,eACnB6I,EAAa7I,EAAM,KAAMsI,EAAKQ,CAAO,EACzC,IAAImB,EAAYjK,EAAM,MAAQA,EAAM,KAAK,KAAO9I,EAAK,KACjD0S,EAAS,GAEb,GAAI,CAACK,GAAa,kBAAkB,KAAK/S,EAAK,IAAI,EAAE,CAAC,EAAE,QAAU8I,EAAM,GACnE,QAASpJ,EAAI,EAAG+B,EAAIiQ,EAAQ,OAAS,EAAGhS,GAAK+B,EAAG/B,IAC5CgT,GAAUhT,GAAK+B,GAAK,CAACsR,EAAYrB,EAAQhS,CAAC,EAAE,OAAO0R,EAAK,CAAC,EACnDM,EAAQhS,CAAC,EAAE,MAAMA,EAAI+B,EAAIyQ,EAAYlS,EAAK,KAAM,EAAG0R,EAAQhS,EAAI,CAAC,EAAE,IAAI,EAAIgT,EAAO,OAAS,IAAI,EAG5G,IAAIzU,EAAOQ,EACX,KAAOR,EAAO+B,EAAK,MAAQ,KAAK,KAAKA,EAAK,KAAK,OAAO/B,EAAO+B,EAAK,KAAO,CAAC,CAAC,GACvE/B,IACJ,OAAAyU,EAASZ,GAAgBY,EAAQ/C,CAAK,EAClCqD,GAAalK,EAAM,KAAM6G,EAAM,GAAG,IAClC+C,EAASE,GAAUlB,EAAS/B,EAAO3P,CAAI,EAAI2P,EAAM,UAAY+C,GACjEd,EAAQ,KAAK,CAAE,KAAA3T,EAAM,GAAIQ,EAAK,OAAQkR,EAAM,UAAY+C,CAAM,CAAE,EACzD,CAAE,MAAOC,EAAgB,OAAO1U,EAAOyU,EAAO,OAAS,CAAC,EAAG,QAAAd,EAC1E,CAAK,EACD,OAAIS,EACO,IACXD,EAASzC,EAAM,OAAOiC,EAAS,CAAE,eAAgB,GAAM,UAAW,OAAS,CAAA,CAAC,EACrE,GACX,EACA,SAASqB,GAAOjQ,EAAM,CAClB,OAAOA,EAAK,MAAQ,aAAeA,EAAK,MAAQ,UACpD,CACA,SAASgQ,GAAahQ,EAAMoO,EAAK,CAC7B,GAAIpO,EAAK,MAAQ,eAAiBA,EAAK,MAAQ,aAC3C,MAAO,GACX,IAAIR,EAAQQ,EAAK,WAAYwP,EAASxP,EAAK,SAAS,WAAY,UAAU,EAC1E,GAAI,CAACwP,EACD,MAAO,GACX,IAAIU,EAAQ9B,EAAI,OAAO5O,EAAM,EAAE,EAAG2Q,EAAQ/B,EAAI,OAAOoB,EAAO,IAAI,EAC5DY,EAAQ,WAAW,KAAKF,EAAM,IAAI,EACtC,OAAOA,EAAM,QAAUE,EAAQ,EAAI,GAAKD,EAAM,MAClD,CACA,SAASP,GAAUlB,EAAS/B,EAAO3P,EAAM,CACrC,IAAI0S,EAAS,GACb,QAAShT,EAAI,EAAG+B,EAAIiQ,EAAQ,OAAS,EAAGhS,GAAK+B,EAAG/B,IAC5CgT,GAAUhB,EAAQhS,CAAC,EAAE,MAAMA,EAAI+B,EACzByQ,EAAYlS,EAAK,KAAM,EAAG,KAAK,IAAIA,EAAK,KAAK,OAAQ0R,EAAQhS,EAAI,CAAC,EAAE,IAAI,CAAC,EAAIgT,EAAO,OACpF,KAAMhT,EAAI+B,CAAC,EAErB,OAAOqQ,GAAgBY,EAAQ/C,CAAK,CACxC,CACA,SAAS0D,GAAqB1N,EAAMlH,EAAK,CACrC,IAAIuE,EAAO2C,EAAK,aAAalH,EAAK,EAAE,EAAG6U,EAAO7U,EAC1CwU,GAAOjQ,CAAI,IACXsQ,EAAOtQ,EAAK,KACZA,EAAOA,EAAK,QAEhB,QAAS6O,EAAMA,EAAO7O,EAAK,YAAYsQ,CAAI,GACvC,GAAIL,GAAOpB,CAAI,EACXyB,EAAOzB,EAAK,aAEPA,EAAK,MAAQ,eAAiBA,EAAK,MAAQ,aAChD7O,EAAO6O,EAAK,UACZyB,EAAOtQ,EAAK,OAGZ,OAGR,OAAOA,CACX,CAYK,MAACuQ,GAAuB,CAAC,CAAE,MAAA5D,EAAO,SAAAyC,KAAe,CAClD,IAAIzM,EAAOyK,EAAWT,CAAK,EACvB0C,EAAO,KAAMT,EAAUjC,EAAM,cAAc2C,GAAS,CACpD,IAAI7T,EAAM6T,EAAM,KAAM,CAAE,IAAAlB,CAAG,EAAKzB,EAChC,GAAI2C,EAAM,OAAS7B,GAAiB,WAAWd,EAAO2C,EAAM,IAAI,EAAG,CAC/D,IAAItS,EAAOoR,EAAI,OAAO3S,CAAG,EACrBiT,EAAUF,GAAW6B,GAAqB1N,EAAMlH,CAAG,EAAG2S,CAAG,EAC7D,GAAIM,EAAQ,OAAQ,CAChB,IAAI5I,EAAQ4I,EAAQA,EAAQ,OAAS,CAAC,EAClC8B,EAAW1K,EAAM,GAAKA,EAAM,WAAW,QAAUA,EAAM,WAAa,EAAI,GAE5E,GAAIrK,EAAMuB,EAAK,KAAOwT,GAAY,CAAC,KAAK,KAAKxT,EAAK,KAAK,MAAMwT,EAAU/U,EAAMuB,EAAK,IAAI,CAAC,EACnF,MAAO,CAAE,MAAO2S,EAAgB,OAAO3S,EAAK,KAAOwT,CAAQ,EACvD,QAAS,CAAE,KAAMxT,EAAK,KAAOwT,EAAU,GAAI/U,CAAG,GACtD,GAAIA,EAAMuB,EAAK,MAAQwT,IAIlB,CAAC1K,EAAM,MAAQ9I,EAAK,MAAQ8I,EAAM,KAAK,MAAQ,CAAC,KAAK,KAAK9I,EAAK,KAAK,MAAM,EAAG8I,EAAM,EAAE,CAAC,GAAI,CAC3F,IAAI7J,EAAQe,EAAK,KAAO8I,EAAM,KAE9B,GAAIA,EAAM,MAAQA,EAAM,KAAK,KAAOA,EAAM,KAAK,MAAQ,KAAK,KAAK9I,EAAK,KAAK,MAAM8I,EAAM,KAAMA,EAAM,EAAE,CAAC,EAAG,CACrG,IAAI4J,EAAS5J,EAAM,MAAMoJ,EAAYlS,EAAK,KAAM,EAAG8I,EAAM,EAAE,EAAIoJ,EAAYlS,EAAK,KAAM,EAAG8I,EAAM,IAAI,CAAC,EACpG,OAAI7J,GAASe,EAAK,OACd0S,EAASZ,GAAgBY,EAAQ/C,CAAK,GACnC,CAAE,MAAOgD,EAAgB,OAAO1T,EAAQyT,EAAO,MAAM,EACxD,QAAS,CAAE,KAAMzT,EAAO,GAAIe,EAAK,KAAO8I,EAAM,GAAI,OAAA4J,CAAM,EAC/D,CAED,GAAIzT,EAAQR,EACR,MAAO,CAAE,MAAOkU,EAAgB,OAAO1T,CAAK,EAAG,QAAS,CAAE,KAAMA,EAAO,GAAIR,CAAK,CAAA,CACvF,CACJ,CACJ,CACD,OAAO4T,EAAO,CAAE,MAAAC,EACxB,CAAK,EACD,OAAID,EACO,IACXD,EAASzC,EAAM,OAAOiC,EAAS,CAAE,eAAgB,GAAM,UAAW,QAAU,CAAA,CAAC,EACtE,GACX,EAQM6B,GAAiB,CACnB,CAAE,IAAK,QAAS,IAAKtB,EAA6B,EAClD,CAAE,IAAK,YAAa,IAAKoB,EAAsB,CACnD,EACMG,GAA2BC,GAAK,CAAE,iBAAkB,EAAO,CAAA,EAIjE,SAASC,GAASxM,EAAS,GAAI,CAC3B,GAAI,CAAE,cAAAyM,EAAe,oBAAAC,EAAqB,UAAAC,EAAY,GAAM,KAAM,CAAE,OAAA1P,CAAQ,EAAGkM,GAAoB,iBAAAyD,EAAmB,GAAM,gBAAAC,EAAkBP,EAAW,EAAKtM,EAC9J,GAAI,EAAE/C,aAAkBkC,GACpB,MAAM,IAAI,WAAW,gEAAgE,EACzF,IAAI2N,EAAa9M,EAAO,WAAa,CAACA,EAAO,UAAU,EAAI,GACvD+M,EAAU,CAACF,EAAgB,OAAO,EAAGG,EACrCN,aAA+BO,IAC/BF,EAAQ,KAAKL,EAAoB,OAAO,EACxCM,EAAcN,EAAoB,UAE7BA,IACLM,EAAcN,GAElB,IAAIhH,EAAa+G,GAAiBO,EAAc1D,GAAcmD,EAAeO,CAAW,EAAI,OAC5FF,EAAW,KAAKrH,GAAU,CAAE,WAAAC,EAAY,WAAYmH,EAAgB,SAAS,MAAQ,CAAA,CAAC,EAClFF,GACAI,EAAQ,KAAKG,GAAK,KAAKC,GAAO,GAAGd,EAAc,CAAC,CAAC,EACrD,IAAIe,EAAOnE,GAAOhM,EAAO,UAAU6P,CAAU,CAAC,EAC9C,OAAIF,GACAG,EAAQ,KAAKK,EAAK,KAAK,GAAG,CAAE,aAAcC,EAAmB,CAAA,CAAC,EAC3D,IAAIJ,GAAgBG,EAAML,CAAO,CAC5C,CACA,SAASM,GAAkB/C,EAAS,CAChC,GAAI,CAAE,MAAA/B,EAAO,IAAAlR,CAAK,EAAGiT,EAASxP,EAAI,4BAA4B,KAAKyN,EAAM,SAASlR,EAAM,GAAIA,CAAG,CAAC,EAChG,GAAI,CAACyD,EACD,OAAO,KACX,IAAIyD,EAAOyK,EAAWT,CAAK,EAAE,aAAalR,EAAK,EAAE,EACjD,KAAOkH,GAAQ,CAACA,EAAK,KAAK,OAAO,CAC7B,GAAIA,EAAK,MAAQ,aAAeA,EAAK,MAAQ,cAAgBA,EAAK,MAAQ,8BACtEA,EAAK,MAAQ,gBAAkBA,EAAK,MAAQ,QAAUA,EAAK,MAAQ,QACnE,OAAO,KACXA,EAAOA,EAAK,MACf,CACD,MAAO,CACH,KAAMlH,EAAMyD,EAAE,CAAC,EAAE,OAAQ,GAAIzD,EAC7B,QAASiW,GAAoB,EAC7B,SAAU,4BAClB,CACA,CACA,IAAIC,EAAkB,KACtB,SAASD,IAAqB,CAC1B,GAAIC,EACA,OAAOA,EACX,IAAI/U,EAASgV,GAAqB,IAAIC,GAAkBC,GAAY,OAAO,CAAE,WAAYpB,EAAW,CAAE,EAAG,EAAG,EAAI,CAAC,EACjH,OAAOiB,EAAkB/U,EAASA,EAAO,QAAU,CAAA,CACvD", "x_google_ignoreList": [0, 1]}