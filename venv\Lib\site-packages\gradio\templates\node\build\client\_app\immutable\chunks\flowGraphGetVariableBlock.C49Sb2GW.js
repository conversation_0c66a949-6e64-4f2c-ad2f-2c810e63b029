import{F as r}from"./KHR_interactivity.DEAVS2UW.js";import{R as i}from"./declarationMapper.UBCwU7BT.js";import{R as l}from"./index.BoI39RQH.js";class s extends r{constructor(a){super(a),this.config=a,this.value=this.registerDataOutput("value",i,a.initialValue)}_updateOutputs(a){const e=this.config.variable;a.hasVariable(e)&&this.value.setValue(a.getVariable(e),a)}serialize(a){super.serialize(a),a.config.variable=this.config.variable}getClassName(){return"FlowGraphGetVariableBlock"}}l("FlowGraphGetVariableBlock",s);export{s as FlowGraphGetVariableBlock};
//# sourceMappingURL=flowGraphGetVariableBlock.C49Sb2GW.js.map
