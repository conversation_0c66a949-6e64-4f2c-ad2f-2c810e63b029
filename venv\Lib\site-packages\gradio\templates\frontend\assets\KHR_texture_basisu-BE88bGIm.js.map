{"version": 3, "file": "KHR_texture_basisu-BE88bGIm.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_texture_basisu.js"], "sourcesContent": ["import { GLTFLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_texture_basisu\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_texture_basisu/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_texture_basisu {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /** The name of this extension. */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    _loadTextureAsync(context, texture, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, texture, this.name, (extensionContext, extension) => {\n            const sampler = texture.sampler == undefined ? GLTFLoader.DefaultSampler : ArrayItem.Get(`${context}/sampler`, this._loader.gltf.samplers, texture.sampler);\n            const image = ArrayItem.Get(`${extensionContext}/source`, this._loader.gltf.images, extension.source);\n            return this._loader._createTextureAsync(context, sampler, image, (babylonTexture) => {\n                assign(babylonTexture);\n            }, texture._textureInfo.nonColorData ? { useRGBAIfASTCBC7NotAvailableWhenUASTC: true } : undefined, !texture._textureInfo.nonColorData);\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_texture_basisu(loader));\n//# sourceMappingURL=KHR_texture_basisu.js.map"], "names": ["NAME", "KHR_texture_basisu", "loader", "context", "texture", "assign", "GLTFLoader", "extensionContext", "extension", "sampler", "ArrayItem", "image", "babylonTexture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "uTAEA,MAAMA,EAAO,qBAKN,MAAMC,CAAmB,CAI5B,YAAYC,EAAQ,CAEhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAUA,EAAO,gBAAgBF,CAAI,CAC7C,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,kBAAkBG,EAASC,EAASC,EAAQ,CACxC,OAAOC,EAAW,mBAAmBH,EAASC,EAAS,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAC/F,MAAMC,EAAUL,EAAQ,SAAW,KAAYE,EAAW,eAAiBI,EAAU,IAAI,GAAGP,CAAO,WAAY,KAAK,QAAQ,KAAK,SAAUC,EAAQ,OAAO,EACpJO,EAAQD,EAAU,IAAI,GAAGH,CAAgB,UAAW,KAAK,QAAQ,KAAK,OAAQC,EAAU,MAAM,EACpG,OAAO,KAAK,QAAQ,oBAAoBL,EAASM,EAASE,EAAQC,GAAmB,CACjFP,EAAOO,CAAc,CACxB,EAAER,EAAQ,aAAa,aAAe,CAAE,sCAAuC,IAAS,OAAW,CAACA,EAAQ,aAAa,YAAY,CAClJ,CAAS,CACJ,CACL,CACAS,EAAwBb,CAAI,EAC5Bc,EAAsBd,EAAM,GAAOE,GAAW,IAAID,EAAmBC,CAAM,CAAC", "x_google_ignoreList": [0]}