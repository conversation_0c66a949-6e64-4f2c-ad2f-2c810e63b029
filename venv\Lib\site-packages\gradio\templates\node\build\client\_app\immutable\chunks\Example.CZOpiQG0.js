import{SvelteComponent as d,init as u,safe_not_equal as m,element as h,text as y,claim_element as _,children as c,claim_text as v,detach as f,attr as g,toggle_class as s,insert_hydration as b,append_hydration as A,set_data as j,noop as o}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";/* empty css                                              */function E(a){let e,i=(a[0]?Array.isArray(a[0])?a[0].join(", "):a[0]:"")+"",n;return{c(){e=h("div"),n=y(i),this.h()},l(t){e=_(t,"DIV",{class:!0});var l=c(e);n=v(l,i),l.forEach(f),this.h()},h(){g(e,"class","svelte-rgtszb"),s(e,"table",a[1]==="table"),s(e,"gallery",a[1]==="gallery"),s(e,"selected",a[2])},m(t,l){b(t,e,l),A(e,n)},p(t,[l]){l&1&&i!==(i=(t[0]?Array.isArray(t[0])?t[0].join(", "):t[0]:"")+"")&&j(n,i),l&2&&s(e,"table",t[1]==="table"),l&2&&s(e,"gallery",t[1]==="gallery"),l&4&&s(e,"selected",t[2])},i:o,o,d(t){t&&f(e)}}}function p(a,e,i){let{value:n}=e,{type:t}=e,{selected:l=!1}=e;return a.$$set=r=>{"value"in r&&i(0,n=r.value),"type"in r&&i(1,t=r.type),"selected"in r&&i(2,l=r.selected)},[n,t,l]}class D extends d{constructor(e){super(),u(this,e,p,E,m,{value:0,type:1,selected:2})}}export{D as default};
//# sourceMappingURL=Example.CZOpiQG0.js.map
