import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/sceneeffects_p.h"
        name: "DepthOfFieldEffect"
        accessSemantics: "reference"
        prototype: "SceneEffectBase"
        exports: ["QtQuick3D.Helpers.impl/DepthOfFieldEffect 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Signal { name: "enabledChanged" }
    }
    Component {
        file: "private/sceneeffects_p.h"
        name: "MainSceneEffect"
        accessSemantics: "reference"
        prototype: "SceneEffectBase"
        exports: ["QtQuick3D.Helpers.impl/MainSceneEffect 6.0"]
        exportMetaObjectRevisions: [1536]
    }
    Component {
        file: "qabstractitemmodel.h"
        name: "QAbstractTableModel"
        accessSemantics: "reference"
        prototype: "QAbstractItemModel"
    }
    Component {
        file: "private/qquick3drenderstatsmeshesmodel_p.h"
        name: "QQuick3DRenderStatsMeshesModel"
        accessSemantics: "reference"
        prototype: "QAbstractTableModel"
        exports: [
            "QtQuick3D.Helpers.impl/RenderStatsMeshesModel 6.0",
            "QtQuick3D.Helpers.impl/RenderStatsMeshesModel 6.4"
        ]
        exportMetaObjectRevisions: [1536, 1540]
        Property {
            name: "meshData"
            type: "QString"
            read: "meshData"
            write: "setMeshData"
            notify: "meshDataChanged"
            index: 0
        }
        Signal { name: "meshDataChanged" }
        Method {
            name: "setMeshData"
            Parameter { name: "newMeshData"; type: "QString" }
        }
    }
    Component {
        file: "private/qquick3drenderstatspassesmodel_p.h"
        name: "QQuick3DRenderStatsPassesModel"
        accessSemantics: "reference"
        prototype: "QAbstractTableModel"
        exports: [
            "QtQuick3D.Helpers.impl/RenderStatsPassesModel 6.0",
            "QtQuick3D.Helpers.impl/RenderStatsPassesModel 6.4"
        ]
        exportMetaObjectRevisions: [1536, 1540]
        Property {
            name: "passData"
            type: "QString"
            read: "passData"
            write: "setPassData"
            notify: "passDataChanged"
            index: 0
        }
        Signal { name: "passDataChanged" }
        Method {
            name: "setPassData"
            Parameter { name: "newPassData"; type: "QString" }
        }
    }
    Component {
        file: "private/qquick3drenderstatstexturesmodel_p.h"
        name: "QQuick3DRenderStatsTexturesModel"
        accessSemantics: "reference"
        prototype: "QAbstractTableModel"
        exports: [
            "QtQuick3D.Helpers.impl/RenderStatsTexturesModel 6.0",
            "QtQuick3D.Helpers.impl/RenderStatsTexturesModel 6.4"
        ]
        exportMetaObjectRevisions: [1536, 1540]
        Property {
            name: "textureData"
            type: "QString"
            read: "textureData"
            write: "setTextureData"
            notify: "textureDataChanged"
            index: 0
        }
        Signal { name: "textureDataChanged" }
        Method {
            name: "setTextureData"
            Parameter { name: "newTextureData"; type: "QString" }
        }
    }
    Component {
        file: "private/sceneeffects_p.h"
        name: "SceneEffectBase"
        accessSemantics: "reference"
        prototype: "QQuick3DEffect"
        exports: ["QtQuick3D.Helpers.impl/SceneEffectBase 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "environment"
            type: "QQuick3DSceneEnvironment"
            isPointer: true
            read: "environment"
            write: "setEnvironment"
            notify: "environmentChanged"
            index: 0
        }
        Signal { name: "environmentChanged" }
    }
    Component {
        file: "private/sceneeffects_p.h"
        name: "SceneEffectEnvironment"
        accessSemantics: "reference"
        prototype: "QQuick3DSceneEnvironment"
        exports: [
            "QtQuick3D.Helpers.impl/SceneEffectEnvironment 6.0",
            "QtQuick3D.Helpers.impl/SceneEffectEnvironment 6.4",
            "QtQuick3D.Helpers.impl/SceneEffectEnvironment 6.5",
            "QtQuick3D.Helpers.impl/SceneEffectEnvironment 6.9"
        ]
        exportMetaObjectRevisions: [1536, 1540, 1541, 1545]
    }
}
