{"version": 3, "file": "dds-D1_iteoW.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/dds.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\nimport { Clamp } from \"../Maths/math.scalar.functions.js\";\n\nimport { Logger } from \"../Misc/logger.js\";\nimport { CubeMapToSphericalPolynomialTools } from \"../Misc/HighDynamicRange/cubemapToSphericalPolynomial.js\";\nimport { FromHalfFloat, ToHalfFloat } from \"./textureTools.js\";\nimport \"../Engines/AbstractEngine/abstractEngine.cubeTexture.js\";\n// Based on demo done by <PERSON> - http://media.tojicode.com/webgl-samples/dds.html\n// All values and structures referenced from:\n// http://msdn.microsoft.com/en-us/library/bb943991.aspx/\nconst DDS_MAGIC = 0x20534444;\nconst //DDSD_CAPS = 0x1,\n//DDSD_HEIGHT = 0x2,\n//DDSD_WIDTH = 0x4,\n//DDSD_PITCH = 0x8,\n//DDSD_PIXELFORMAT = 0x1000,\nDDSD_MIPMAPCOUNT = 0x20000;\n//DDSD_LINEARSIZE = 0x80000,\n//DDSD_DEPTH = 0x800000;\n// var DDSCAPS_COMPLEX = 0x8,\n//     DDSCAPS_MIPMAP = 0x400000,\n//     DDSCAPS_TEXTURE = 0x1000;\nconst DDSCAPS2_CUBEMAP = 0x200;\n// DDSCAPS2_CUBEMAP_POSITIVEX = 0x400,\n// DDSCAPS2_CUBEMAP_NEGATIVEX = 0x800,\n// DDSCAPS2_CUBEMAP_POSITIVEY = 0x1000,\n// DDSCAPS2_CUBEMAP_NEGATIVEY = 0x2000,\n// DDSCAPS2_CUBEMAP_POSITIVEZ = 0x4000,\n// DDSCAPS2_CUBEMAP_NEGATIVEZ = 0x8000,\n// DDSCAPS2_VOLUME = 0x200000;\nconst //DDPF_ALPHAPIXELS = 0x1,\n//DDPF_ALPHA = 0x2,\nDDPF_FOURCC = 0x4, DDPF_RGB = 0x40, \n//DDPF_YUV = 0x200,\nDDPF_LUMINANCE = 0x20000;\nfunction FourCCToInt32(value) {\n    return value.charCodeAt(0) + (value.charCodeAt(1) << 8) + (value.charCodeAt(2) << 16) + (value.charCodeAt(3) << 24);\n}\nfunction Int32ToFourCC(value) {\n    return String.fromCharCode(value & 0xff, (value >> 8) & 0xff, (value >> 16) & 0xff, (value >> 24) & 0xff);\n}\nconst FOURCC_DXT1 = FourCCToInt32(\"DXT1\");\nconst FOURCC_DXT3 = FourCCToInt32(\"DXT3\");\nconst FOURCC_DXT5 = FourCCToInt32(\"DXT5\");\nconst FOURCC_DX10 = FourCCToInt32(\"DX10\");\nconst FOURCC_D3DFMT_R16G16B16A16F = 113;\nconst FOURCC_D3DFMT_R32G32B32A32F = 116;\nconst DXGI_FORMAT_R32G32B32A32_FLOAT = 2;\nconst DXGI_FORMAT_R16G16B16A16_FLOAT = 10;\nconst DXGI_FORMAT_B8G8R8X8_UNORM = 88;\nconst headerLengthInt = 31; // The header length in 32 bit ints\n// Offsets into the header array\nconst off_magic = 0;\nconst off_size = 1;\nconst off_flags = 2;\nconst off_height = 3;\nconst off_width = 4;\nconst off_mipmapCount = 7;\nconst off_pfFlags = 20;\nconst off_pfFourCC = 21;\nconst off_RGBbpp = 22;\nconst off_RMask = 23;\nconst off_GMask = 24;\nconst off_BMask = 25;\nconst off_AMask = 26;\n// var off_caps1 = 27;\nconst off_caps2 = 28;\n// var off_caps3 = 29;\n// var off_caps4 = 30;\nconst off_dxgiFormat = 32;\n/**\n * Class used to provide DDS decompression tools\n */\nexport class DDSTools {\n    /**\n     * Gets DDS information from an array buffer\n     * @param data defines the array buffer view to read data from\n     * @returns the DDS information\n     */\n    static GetDDSInfo(data) {\n        const header = new Int32Array(data.buffer, data.byteOffset, headerLengthInt);\n        const extendedHeader = new Int32Array(data.buffer, data.byteOffset, headerLengthInt + 4);\n        let mipmapCount = 1;\n        if (header[off_flags] & DDSD_MIPMAPCOUNT) {\n            mipmapCount = Math.max(1, header[off_mipmapCount]);\n        }\n        const fourCC = header[off_pfFourCC];\n        const dxgiFormat = fourCC === FOURCC_DX10 ? extendedHeader[off_dxgiFormat] : 0;\n        let textureType = 0;\n        switch (fourCC) {\n            case FOURCC_D3DFMT_R16G16B16A16F:\n                textureType = 2;\n                break;\n            case FOURCC_D3DFMT_R32G32B32A32F:\n                textureType = 1;\n                break;\n            case FOURCC_DX10:\n                if (dxgiFormat === DXGI_FORMAT_R16G16B16A16_FLOAT) {\n                    textureType = 2;\n                    break;\n                }\n                if (dxgiFormat === DXGI_FORMAT_R32G32B32A32_FLOAT) {\n                    textureType = 1;\n                    break;\n                }\n        }\n        return {\n            width: header[off_width],\n            height: header[off_height],\n            mipmapCount: mipmapCount,\n            isFourCC: (header[off_pfFlags] & DDPF_FOURCC) === DDPF_FOURCC,\n            isRGB: (header[off_pfFlags] & DDPF_RGB) === DDPF_RGB,\n            isLuminance: (header[off_pfFlags] & DDPF_LUMINANCE) === DDPF_LUMINANCE,\n            isCube: (header[off_caps2] & DDSCAPS2_CUBEMAP) === DDSCAPS2_CUBEMAP,\n            isCompressed: fourCC === FOURCC_DXT1 || fourCC === FOURCC_DXT3 || fourCC === FOURCC_DXT5,\n            dxgiFormat: dxgiFormat,\n            textureType: textureType,\n        };\n    }\n    static _GetHalfFloatAsFloatRGBAArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer, lod) {\n        const destArray = new Float32Array(dataLength);\n        const srcData = new Uint16Array(arrayBuffer, dataOffset);\n        let index = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                const srcPos = (x + y * width) * 4;\n                destArray[index] = FromHalfFloat(srcData[srcPos]);\n                destArray[index + 1] = FromHalfFloat(srcData[srcPos + 1]);\n                destArray[index + 2] = FromHalfFloat(srcData[srcPos + 2]);\n                if (DDSTools.StoreLODInAlphaChannel) {\n                    destArray[index + 3] = lod;\n                }\n                else {\n                    destArray[index + 3] = FromHalfFloat(srcData[srcPos + 3]);\n                }\n                index += 4;\n            }\n        }\n        return destArray;\n    }\n    static _GetHalfFloatRGBAArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer, lod) {\n        if (DDSTools.StoreLODInAlphaChannel) {\n            const destArray = new Uint16Array(dataLength);\n            const srcData = new Uint16Array(arrayBuffer, dataOffset);\n            let index = 0;\n            for (let y = 0; y < height; y++) {\n                for (let x = 0; x < width; x++) {\n                    const srcPos = (x + y * width) * 4;\n                    destArray[index] = srcData[srcPos];\n                    destArray[index + 1] = srcData[srcPos + 1];\n                    destArray[index + 2] = srcData[srcPos + 2];\n                    destArray[index + 3] = ToHalfFloat(lod);\n                    index += 4;\n                }\n            }\n            return destArray;\n        }\n        return new Uint16Array(arrayBuffer, dataOffset, dataLength);\n    }\n    static _GetFloatRGBAArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer, lod) {\n        if (DDSTools.StoreLODInAlphaChannel) {\n            const destArray = new Float32Array(dataLength);\n            const srcData = new Float32Array(arrayBuffer, dataOffset);\n            let index = 0;\n            for (let y = 0; y < height; y++) {\n                for (let x = 0; x < width; x++) {\n                    const srcPos = (x + y * width) * 4;\n                    destArray[index] = srcData[srcPos];\n                    destArray[index + 1] = srcData[srcPos + 1];\n                    destArray[index + 2] = srcData[srcPos + 2];\n                    destArray[index + 3] = lod;\n                    index += 4;\n                }\n            }\n            return destArray;\n        }\n        return new Float32Array(arrayBuffer, dataOffset, dataLength);\n    }\n    static _GetFloatAsHalfFloatRGBAArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer, lod) {\n        const destArray = new Uint16Array(dataLength);\n        const srcData = new Float32Array(arrayBuffer, dataOffset);\n        let index = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                destArray[index] = ToHalfFloat(srcData[index]);\n                destArray[index + 1] = ToHalfFloat(srcData[index + 1]);\n                destArray[index + 2] = ToHalfFloat(srcData[index + 2]);\n                if (DDSTools.StoreLODInAlphaChannel) {\n                    destArray[index + 3] = ToHalfFloat(lod);\n                }\n                else {\n                    destArray[index + 3] = ToHalfFloat(srcData[index + 3]);\n                }\n                index += 4;\n            }\n        }\n        return destArray;\n    }\n    static _GetFloatAsUIntRGBAArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer, lod) {\n        const destArray = new Uint8Array(dataLength);\n        const srcData = new Float32Array(arrayBuffer, dataOffset);\n        let index = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                const srcPos = (x + y * width) * 4;\n                destArray[index] = Clamp(srcData[srcPos]) * 255;\n                destArray[index + 1] = Clamp(srcData[srcPos + 1]) * 255;\n                destArray[index + 2] = Clamp(srcData[srcPos + 2]) * 255;\n                if (DDSTools.StoreLODInAlphaChannel) {\n                    destArray[index + 3] = lod;\n                }\n                else {\n                    destArray[index + 3] = Clamp(srcData[srcPos + 3]) * 255;\n                }\n                index += 4;\n            }\n        }\n        return destArray;\n    }\n    static _GetHalfFloatAsUIntRGBAArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer, lod) {\n        const destArray = new Uint8Array(dataLength);\n        const srcData = new Uint16Array(arrayBuffer, dataOffset);\n        let index = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                const srcPos = (x + y * width) * 4;\n                destArray[index] = Clamp(FromHalfFloat(srcData[srcPos])) * 255;\n                destArray[index + 1] = Clamp(FromHalfFloat(srcData[srcPos + 1])) * 255;\n                destArray[index + 2] = Clamp(FromHalfFloat(srcData[srcPos + 2])) * 255;\n                if (DDSTools.StoreLODInAlphaChannel) {\n                    destArray[index + 3] = lod;\n                }\n                else {\n                    destArray[index + 3] = Clamp(FromHalfFloat(srcData[srcPos + 3])) * 255;\n                }\n                index += 4;\n            }\n        }\n        return destArray;\n    }\n    static _GetRGBAArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer, rOffset, gOffset, bOffset, aOffset) {\n        const byteArray = new Uint8Array(dataLength);\n        const srcData = new Uint8Array(arrayBuffer, dataOffset);\n        let index = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                const srcPos = (x + y * width) * 4;\n                byteArray[index] = srcData[srcPos + rOffset];\n                byteArray[index + 1] = srcData[srcPos + gOffset];\n                byteArray[index + 2] = srcData[srcPos + bOffset];\n                byteArray[index + 3] = srcData[srcPos + aOffset];\n                index += 4;\n            }\n        }\n        return byteArray;\n    }\n    static _ExtractLongWordOrder(value) {\n        if (value === 0 || value === 255 || value === -16777216) {\n            return 0;\n        }\n        return 1 + DDSTools._ExtractLongWordOrder(value >> 8);\n    }\n    static _GetRGBArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer, rOffset, gOffset, bOffset) {\n        const byteArray = new Uint8Array(dataLength);\n        const srcData = new Uint8Array(arrayBuffer, dataOffset);\n        let index = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                const srcPos = (x + y * width) * 3;\n                byteArray[index] = srcData[srcPos + rOffset];\n                byteArray[index + 1] = srcData[srcPos + gOffset];\n                byteArray[index + 2] = srcData[srcPos + bOffset];\n                index += 3;\n            }\n        }\n        return byteArray;\n    }\n    static _GetLuminanceArrayBuffer(width, height, dataOffset, dataLength, arrayBuffer) {\n        const byteArray = new Uint8Array(dataLength);\n        const srcData = new Uint8Array(arrayBuffer, dataOffset);\n        let index = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                const srcPos = x + y * width;\n                byteArray[index] = srcData[srcPos];\n                index++;\n            }\n        }\n        return byteArray;\n    }\n    /**\n     * Uploads DDS Levels to a Babylon Texture\n     * @internal\n     */\n    static UploadDDSLevels(engine, texture, data, info, loadMipmaps, faces, lodIndex = -1, currentFace, destTypeMustBeFilterable = true) {\n        let sphericalPolynomialFaces = null;\n        if (info.sphericalPolynomial) {\n            sphericalPolynomialFaces = [];\n        }\n        const ext = !!engine.getCaps().s3tc;\n        // TODO WEBGPU Once generateMipMaps is split into generateMipMaps + hasMipMaps in InternalTexture this line can be removed\n        texture.generateMipMaps = loadMipmaps;\n        const header = new Int32Array(data.buffer, data.byteOffset, headerLengthInt);\n        let fourCC, width, height, dataLength = 0, dataOffset;\n        let byteArray, mipmapCount, mip;\n        let internalCompressedFormat = 0;\n        let blockBytes = 1;\n        if (header[off_magic] !== DDS_MAGIC) {\n            Logger.Error(\"Invalid magic number in DDS header\");\n            return;\n        }\n        if (!info.isFourCC && !info.isRGB && !info.isLuminance) {\n            Logger.Error(\"Unsupported format, must contain a FourCC, RGB or LUMINANCE code\");\n            return;\n        }\n        if (info.isCompressed && !ext) {\n            Logger.Error(\"Compressed textures are not supported on this platform.\");\n            return;\n        }\n        let bpp = header[off_RGBbpp];\n        dataOffset = header[off_size] + 4;\n        let computeFormats = false;\n        if (info.isFourCC) {\n            fourCC = header[off_pfFourCC];\n            switch (fourCC) {\n                case FOURCC_DXT1:\n                    blockBytes = 8;\n                    internalCompressedFormat = 33777;\n                    break;\n                case FOURCC_DXT3:\n                    blockBytes = 16;\n                    internalCompressedFormat = 33778;\n                    break;\n                case FOURCC_DXT5:\n                    blockBytes = 16;\n                    internalCompressedFormat = 33779;\n                    break;\n                case FOURCC_D3DFMT_R16G16B16A16F:\n                    computeFormats = true;\n                    bpp = 64;\n                    break;\n                case FOURCC_D3DFMT_R32G32B32A32F:\n                    computeFormats = true;\n                    bpp = 128;\n                    break;\n                case FOURCC_DX10: {\n                    // There is an additionnal header so dataOffset need to be changed\n                    dataOffset += 5 * 4; // 5 uints\n                    let supported = false;\n                    switch (info.dxgiFormat) {\n                        case DXGI_FORMAT_R16G16B16A16_FLOAT:\n                            computeFormats = true;\n                            bpp = 64;\n                            supported = true;\n                            break;\n                        case DXGI_FORMAT_R32G32B32A32_FLOAT:\n                            computeFormats = true;\n                            bpp = 128;\n                            supported = true;\n                            break;\n                        case DXGI_FORMAT_B8G8R8X8_UNORM:\n                            info.isRGB = true;\n                            info.isFourCC = false;\n                            bpp = 32;\n                            supported = true;\n                            break;\n                    }\n                    if (supported) {\n                        break;\n                    }\n                }\n                // eslint-disable-next-line no-fallthrough\n                default:\n                    Logger.Error([\"Unsupported FourCC code:\", Int32ToFourCC(fourCC)]);\n                    return;\n            }\n        }\n        const rOffset = DDSTools._ExtractLongWordOrder(header[off_RMask]);\n        const gOffset = DDSTools._ExtractLongWordOrder(header[off_GMask]);\n        const bOffset = DDSTools._ExtractLongWordOrder(header[off_BMask]);\n        const aOffset = DDSTools._ExtractLongWordOrder(header[off_AMask]);\n        if (computeFormats) {\n            internalCompressedFormat = engine._getRGBABufferInternalSizedFormat(info.textureType);\n        }\n        mipmapCount = 1;\n        if (header[off_flags] & DDSD_MIPMAPCOUNT && loadMipmaps !== false) {\n            mipmapCount = Math.max(1, header[off_mipmapCount]);\n        }\n        const startFace = currentFace || 0;\n        const caps = engine.getCaps();\n        for (let face = startFace; face < faces; face++) {\n            width = header[off_width];\n            height = header[off_height];\n            for (mip = 0; mip < mipmapCount; ++mip) {\n                if (lodIndex === -1 || lodIndex === mip) {\n                    // In case of fixed LOD, if the lod has just been uploaded, early exit.\n                    const i = lodIndex === -1 ? mip : 0;\n                    if (!info.isCompressed && info.isFourCC) {\n                        texture.format = 5;\n                        dataLength = width * height * 4;\n                        let floatArray = null;\n                        if (engine._badOS || engine._badDesktopOS || (!caps.textureHalfFloat && !caps.textureFloat)) {\n                            // Required because iOS has many issues with float and half float generation\n                            if (bpp === 128) {\n                                floatArray = DDSTools._GetFloatAsUIntRGBAArrayBuffer(width, height, data.byteOffset + dataOffset, dataLength, data.buffer, i);\n                                if (sphericalPolynomialFaces && i == 0) {\n                                    sphericalPolynomialFaces.push(DDSTools._GetFloatRGBAArrayBuffer(width, height, data.byteOffset + dataOffset, dataLength, data.buffer, i));\n                                }\n                            }\n                            else if (bpp === 64) {\n                                floatArray = DDSTools._GetHalfFloatAsUIntRGBAArrayBuffer(width, height, data.byteOffset + dataOffset, dataLength, data.buffer, i);\n                                if (sphericalPolynomialFaces && i == 0) {\n                                    sphericalPolynomialFaces.push(DDSTools._GetHalfFloatAsFloatRGBAArrayBuffer(width, height, data.byteOffset + dataOffset, dataLength, data.buffer, i));\n                                }\n                            }\n                            texture.type = 0;\n                        }\n                        else {\n                            const floatAvailable = caps.textureFloat && ((destTypeMustBeFilterable && caps.textureFloatLinearFiltering) || !destTypeMustBeFilterable);\n                            const halfFloatAvailable = caps.textureHalfFloat && ((destTypeMustBeFilterable && caps.textureHalfFloatLinearFiltering) || !destTypeMustBeFilterable);\n                            const destType = (bpp === 128 || (bpp === 64 && !halfFloatAvailable)) && floatAvailable\n                                ? 1\n                                : (bpp === 64 || (bpp === 128 && !floatAvailable)) && halfFloatAvailable\n                                    ? 2\n                                    : 0;\n                            let dataGetter;\n                            let dataGetterPolynomial = null;\n                            switch (bpp) {\n                                case 128: {\n                                    switch (destType) {\n                                        case 1:\n                                            dataGetter = DDSTools._GetFloatRGBAArrayBuffer;\n                                            dataGetterPolynomial = null;\n                                            break;\n                                        case 2:\n                                            dataGetter = DDSTools._GetFloatAsHalfFloatRGBAArrayBuffer;\n                                            dataGetterPolynomial = DDSTools._GetFloatRGBAArrayBuffer;\n                                            break;\n                                        case 0:\n                                            dataGetter = DDSTools._GetFloatAsUIntRGBAArrayBuffer;\n                                            dataGetterPolynomial = DDSTools._GetFloatRGBAArrayBuffer;\n                                            break;\n                                    }\n                                    break;\n                                }\n                                default: {\n                                    // 64 bpp\n                                    switch (destType) {\n                                        case 1:\n                                            dataGetter = DDSTools._GetHalfFloatAsFloatRGBAArrayBuffer;\n                                            dataGetterPolynomial = null;\n                                            break;\n                                        case 2:\n                                            dataGetter = DDSTools._GetHalfFloatRGBAArrayBuffer;\n                                            dataGetterPolynomial = DDSTools._GetHalfFloatAsFloatRGBAArrayBuffer;\n                                            break;\n                                        case 0:\n                                            dataGetter = DDSTools._GetHalfFloatAsUIntRGBAArrayBuffer;\n                                            dataGetterPolynomial = DDSTools._GetHalfFloatAsFloatRGBAArrayBuffer;\n                                            break;\n                                    }\n                                    break;\n                                }\n                            }\n                            texture.type = destType;\n                            floatArray = dataGetter(width, height, data.byteOffset + dataOffset, dataLength, data.buffer, i);\n                            if (sphericalPolynomialFaces && i == 0) {\n                                sphericalPolynomialFaces.push(dataGetterPolynomial ? dataGetterPolynomial(width, height, data.byteOffset + dataOffset, dataLength, data.buffer, i) : floatArray);\n                            }\n                        }\n                        if (floatArray) {\n                            engine._uploadDataToTextureDirectly(texture, floatArray, face, i);\n                        }\n                    }\n                    else if (info.isRGB) {\n                        texture.type = 0;\n                        if (bpp === 24) {\n                            texture.format = 4;\n                            dataLength = width * height * 3;\n                            byteArray = DDSTools._GetRGBArrayBuffer(width, height, data.byteOffset + dataOffset, dataLength, data.buffer, rOffset, gOffset, bOffset);\n                            engine._uploadDataToTextureDirectly(texture, byteArray, face, i);\n                        }\n                        else {\n                            // 32\n                            texture.format = 5;\n                            dataLength = width * height * 4;\n                            byteArray = DDSTools._GetRGBAArrayBuffer(width, height, data.byteOffset + dataOffset, dataLength, data.buffer, rOffset, gOffset, bOffset, aOffset);\n                            engine._uploadDataToTextureDirectly(texture, byteArray, face, i);\n                        }\n                    }\n                    else if (info.isLuminance) {\n                        const unpackAlignment = engine._getUnpackAlignement();\n                        const unpaddedRowSize = width;\n                        const paddedRowSize = Math.floor((width + unpackAlignment - 1) / unpackAlignment) * unpackAlignment;\n                        dataLength = paddedRowSize * (height - 1) + unpaddedRowSize;\n                        byteArray = DDSTools._GetLuminanceArrayBuffer(width, height, data.byteOffset + dataOffset, dataLength, data.buffer);\n                        texture.format = 1;\n                        texture.type = 0;\n                        engine._uploadDataToTextureDirectly(texture, byteArray, face, i);\n                    }\n                    else {\n                        dataLength = (((Math.max(4, width) / 4) * Math.max(4, height)) / 4) * blockBytes;\n                        byteArray = new Uint8Array(data.buffer, data.byteOffset + dataOffset, dataLength);\n                        texture.type = 0;\n                        engine._uploadCompressedDataToTextureDirectly(texture, internalCompressedFormat, width, height, byteArray, face, i);\n                    }\n                }\n                dataOffset += bpp ? width * height * (bpp / 8) : dataLength;\n                width *= 0.5;\n                height *= 0.5;\n                width = Math.max(1.0, width);\n                height = Math.max(1.0, height);\n            }\n            if (currentFace !== undefined) {\n                // Loading a single face\n                break;\n            }\n        }\n        if (sphericalPolynomialFaces && sphericalPolynomialFaces.length > 0) {\n            info.sphericalPolynomial = CubeMapToSphericalPolynomialTools.ConvertCubeMapToSphericalPolynomial({\n                size: header[off_width],\n                right: sphericalPolynomialFaces[0],\n                left: sphericalPolynomialFaces[1],\n                up: sphericalPolynomialFaces[2],\n                down: sphericalPolynomialFaces[3],\n                front: sphericalPolynomialFaces[4],\n                back: sphericalPolynomialFaces[5],\n                format: 5,\n                type: 1,\n                gammaSpace: false,\n            });\n        }\n        else {\n            info.sphericalPolynomial = undefined;\n        }\n    }\n}\n/**\n * Gets or sets a boolean indicating that LOD info is stored in alpha channel (false by default)\n */\nDDSTools.StoreLODInAlphaChannel = false;\n//# sourceMappingURL=dds.js.map"], "names": ["DDS_MAGIC", "DDSD_MIPMAPCOUNT", "DDSCAPS2_CUBEMAP", "DDPF_FOURCC", "DDPF_RGB", "DDPF_LUMINANCE", "FourCCToInt32", "value", "Int32ToFourCC", "FOURCC_DXT1", "FOURCC_DXT3", "FOURCC_DXT5", "FOURCC_DX10", "FOURCC_D3DFMT_R16G16B16A16F", "FOURCC_D3DFMT_R32G32B32A32F", "DXGI_FORMAT_R32G32B32A32_FLOAT", "DXGI_FORMAT_R16G16B16A16_FLOAT", "DXGI_FORMAT_B8G8R8X8_UNORM", "headerLengthInt", "off_magic", "off_size", "off_flags", "off_height", "off_width", "off_mipmapCount", "off_pfFlags", "off_pfFourCC", "off_RGBbpp", "off_RMask", "off_GMask", "off_BMask", "off_AMask", "off_caps2", "off_dxgiFormat", "DDSTools", "data", "header", "extendedHeader", "mipmapCount", "fourCC", "dxgiFormat", "textureType", "width", "height", "dataOffset", "dataLength", "arrayBuffer", "lod", "destArray", "srcData", "index", "y", "x", "srcPos", "FromHalfFloat", "ToHalfFloat", "C<PERSON>", "rOffset", "gOffset", "bOffset", "aOffset", "byteArray", "engine", "texture", "info", "loadMipmaps", "faces", "lodIndex", "currentFace", "destTypeMustBeFilterable", "sphericalPolynomialFaces", "ext", "mip", "internalCompressedFormat", "blockBytes", "<PERSON><PERSON>", "bpp", "computeFormats", "supported", "startFace", "caps", "face", "i", "floatArray", "floatAvailable", "halfFloatAvailable", "destType", "dataGetter", "dataGetterPolynomial", "unpackAlignment", "unpaddedRowSize", "CubeMapToSphericalPolynomialTools"], "mappings": "iLAUA,MAAMA,GAAY,UAMlBC,EAAmB,OAMbC,EAAmB,IAUzBC,EAAc,EAAKC,EAAW,GAE9BC,EAAiB,OACjB,SAASC,EAAcC,EAAO,CAC1B,OAAOA,EAAM,WAAW,CAAC,GAAKA,EAAM,WAAW,CAAC,GAAK,IAAMA,EAAM,WAAW,CAAC,GAAK,KAAOA,EAAM,WAAW,CAAC,GAAK,GACpH,CACA,SAASC,GAAcD,EAAO,CAC1B,OAAO,OAAO,aAAaA,EAAQ,IAAOA,GAAS,EAAK,IAAOA,GAAS,GAAM,IAAOA,GAAS,GAAM,GAAI,CAC5G,CACA,MAAME,EAAcH,EAAc,MAAM,EAClCI,EAAcJ,EAAc,MAAM,EAClCK,GAAcL,EAAc,MAAM,EAClCM,EAAcN,EAAc,MAAM,EAClCO,GAA8B,IAC9BC,GAA8B,IAC9BC,GAAiC,EACjCC,GAAiC,GACjCC,GAA6B,GAC7BC,EAAkB,GAElBC,GAAY,EACZC,GAAW,EACXC,GAAY,EACZC,GAAa,EACbC,EAAY,EACZC,GAAkB,EAClBC,EAAc,GACdC,GAAe,GACfC,GAAa,GACbC,GAAY,GACZC,GAAY,GACZC,GAAY,GACZC,GAAY,GAEZC,GAAY,GAGZC,GAAiB,GAIhB,MAAMC,CAAS,CAMlB,OAAO,WAAWC,EAAM,CACpB,MAAMC,EAAS,IAAI,WAAWD,EAAK,OAAQA,EAAK,WAAYjB,CAAe,EACrEmB,EAAiB,IAAI,WAAWF,EAAK,OAAQA,EAAK,WAAYjB,EAAkB,CAAC,EACvF,IAAIoB,EAAc,EACdF,EAAOf,EAAS,EAAIpB,IACpBqC,EAAc,KAAK,IAAI,EAAGF,EAAOZ,EAAe,CAAC,GAErD,MAAMe,EAASH,EAAOV,EAAY,EAC5Bc,EAAaD,IAAW3B,EAAcyB,EAAeJ,EAAc,EAAI,EAC7E,IAAIQ,EAAc,EAClB,OAAQF,EAAM,CACV,KAAK1B,GACD4B,EAAc,EACd,MACJ,KAAK3B,GACD2B,EAAc,EACd,MACJ,KAAK7B,EACD,GAAI4B,IAAexB,GAAgC,CAC/CyB,EAAc,EACd,KACH,CACD,GAAID,IAAezB,GAAgC,CAC/C0B,EAAc,EACd,KACH,CACR,CACD,MAAO,CACH,MAAOL,EAAOb,CAAS,EACvB,OAAQa,EAAOd,EAAU,EACzB,YAAagB,EACb,UAAWF,EAAOX,CAAW,EAAItB,KAAiBA,EAClD,OAAQiC,EAAOX,CAAW,EAAIrB,KAAcA,EAC5C,aAAcgC,EAAOX,CAAW,EAAIpB,KAAoBA,EACxD,QAAS+B,EAAOJ,EAAS,EAAI9B,KAAsBA,EACnD,aAAcqC,IAAW9B,GAAe8B,IAAW7B,GAAe6B,IAAW5B,GAC7E,WAAY6B,EACZ,YAAaC,CACzB,CACK,CACD,OAAO,oCAAoCC,EAAOC,EAAQC,EAAYC,EAAYC,EAAaC,EAAK,CAChG,MAAMC,EAAY,IAAI,aAAaH,CAAU,EACvCI,EAAU,IAAI,YAAYH,EAAaF,CAAU,EACvD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IAAK,CAC5B,MAAMC,GAAUD,EAAID,EAAIT,GAAS,EACjCM,EAAUE,CAAK,EAAII,EAAcL,EAAQI,CAAM,CAAC,EAChDL,EAAUE,EAAQ,CAAC,EAAII,EAAcL,EAAQI,EAAS,CAAC,CAAC,EACxDL,EAAUE,EAAQ,CAAC,EAAII,EAAcL,EAAQI,EAAS,CAAC,CAAC,EACpDnB,EAAS,uBACTc,EAAUE,EAAQ,CAAC,EAAIH,EAGvBC,EAAUE,EAAQ,CAAC,EAAII,EAAcL,EAAQI,EAAS,CAAC,CAAC,EAE5DH,GAAS,CACZ,CAEL,OAAOF,CACV,CACD,OAAO,6BAA6BN,EAAOC,EAAQC,EAAYC,EAAYC,EAAaC,EAAK,CACzF,GAAIb,EAAS,uBAAwB,CACjC,MAAMc,EAAY,IAAI,YAAYH,CAAU,EACtCI,EAAU,IAAI,YAAYH,EAAaF,CAAU,EACvD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IAAK,CAC5B,MAAMC,GAAUD,EAAID,EAAIT,GAAS,EACjCM,EAAUE,CAAK,EAAID,EAAQI,CAAM,EACjCL,EAAUE,EAAQ,CAAC,EAAID,EAAQI,EAAS,CAAC,EACzCL,EAAUE,EAAQ,CAAC,EAAID,EAAQI,EAAS,CAAC,EACzCL,EAAUE,EAAQ,CAAC,EAAIK,EAAYR,CAAG,EACtCG,GAAS,CACZ,CAEL,OAAOF,CACV,CACD,OAAO,IAAI,YAAYF,EAAaF,EAAYC,CAAU,CAC7D,CACD,OAAO,yBAAyBH,EAAOC,EAAQC,EAAYC,EAAYC,EAAaC,EAAK,CACrF,GAAIb,EAAS,uBAAwB,CACjC,MAAMc,EAAY,IAAI,aAAaH,CAAU,EACvCI,EAAU,IAAI,aAAaH,EAAaF,CAAU,EACxD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IAAK,CAC5B,MAAMC,GAAUD,EAAID,EAAIT,GAAS,EACjCM,EAAUE,CAAK,EAAID,EAAQI,CAAM,EACjCL,EAAUE,EAAQ,CAAC,EAAID,EAAQI,EAAS,CAAC,EACzCL,EAAUE,EAAQ,CAAC,EAAID,EAAQI,EAAS,CAAC,EACzCL,EAAUE,EAAQ,CAAC,EAAIH,EACvBG,GAAS,CACZ,CAEL,OAAOF,CACV,CACD,OAAO,IAAI,aAAaF,EAAaF,EAAYC,CAAU,CAC9D,CACD,OAAO,oCAAoCH,EAAOC,EAAQC,EAAYC,EAAYC,EAAaC,EAAK,CAChG,MAAMC,EAAY,IAAI,YAAYH,CAAU,EACtCI,EAAU,IAAI,aAAaH,EAAaF,CAAU,EACxD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IACvBJ,EAAUE,CAAK,EAAIK,EAAYN,EAAQC,CAAK,CAAC,EAC7CF,EAAUE,EAAQ,CAAC,EAAIK,EAAYN,EAAQC,EAAQ,CAAC,CAAC,EACrDF,EAAUE,EAAQ,CAAC,EAAIK,EAAYN,EAAQC,EAAQ,CAAC,CAAC,EACjDhB,EAAS,uBACTc,EAAUE,EAAQ,CAAC,EAAIK,EAAYR,CAAG,EAGtCC,EAAUE,EAAQ,CAAC,EAAIK,EAAYN,EAAQC,EAAQ,CAAC,CAAC,EAEzDA,GAAS,EAGjB,OAAOF,CACV,CACD,OAAO,+BAA+BN,EAAOC,EAAQC,EAAYC,EAAYC,EAAaC,EAAK,CAC3F,MAAMC,EAAY,IAAI,WAAWH,CAAU,EACrCI,EAAU,IAAI,aAAaH,EAAaF,CAAU,EACxD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IAAK,CAC5B,MAAMC,GAAUD,EAAID,EAAIT,GAAS,EACjCM,EAAUE,CAAK,EAAIM,EAAMP,EAAQI,CAAM,CAAC,EAAI,IAC5CL,EAAUE,EAAQ,CAAC,EAAIM,EAAMP,EAAQI,EAAS,CAAC,CAAC,EAAI,IACpDL,EAAUE,EAAQ,CAAC,EAAIM,EAAMP,EAAQI,EAAS,CAAC,CAAC,EAAI,IAChDnB,EAAS,uBACTc,EAAUE,EAAQ,CAAC,EAAIH,EAGvBC,EAAUE,EAAQ,CAAC,EAAIM,EAAMP,EAAQI,EAAS,CAAC,CAAC,EAAI,IAExDH,GAAS,CACZ,CAEL,OAAOF,CACV,CACD,OAAO,mCAAmCN,EAAOC,EAAQC,EAAYC,EAAYC,EAAaC,EAAK,CAC/F,MAAMC,EAAY,IAAI,WAAWH,CAAU,EACrCI,EAAU,IAAI,YAAYH,EAAaF,CAAU,EACvD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IAAK,CAC5B,MAAMC,GAAUD,EAAID,EAAIT,GAAS,EACjCM,EAAUE,CAAK,EAAIM,EAAMF,EAAcL,EAAQI,CAAM,CAAC,CAAC,EAAI,IAC3DL,EAAUE,EAAQ,CAAC,EAAIM,EAAMF,EAAcL,EAAQI,EAAS,CAAC,CAAC,CAAC,EAAI,IACnEL,EAAUE,EAAQ,CAAC,EAAIM,EAAMF,EAAcL,EAAQI,EAAS,CAAC,CAAC,CAAC,EAAI,IAC/DnB,EAAS,uBACTc,EAAUE,EAAQ,CAAC,EAAIH,EAGvBC,EAAUE,EAAQ,CAAC,EAAIM,EAAMF,EAAcL,EAAQI,EAAS,CAAC,CAAC,CAAC,EAAI,IAEvEH,GAAS,CACZ,CAEL,OAAOF,CACV,CACD,OAAO,oBAAoBN,EAAOC,EAAQC,EAAYC,EAAYC,EAAaW,EAASC,EAASC,EAASC,EAAS,CAC/G,MAAMC,EAAY,IAAI,WAAWhB,CAAU,EACrCI,EAAU,IAAI,WAAWH,EAAaF,CAAU,EACtD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IAAK,CAC5B,MAAMC,GAAUD,EAAID,EAAIT,GAAS,EACjCmB,EAAUX,CAAK,EAAID,EAAQI,EAASI,CAAO,EAC3CI,EAAUX,EAAQ,CAAC,EAAID,EAAQI,EAASK,CAAO,EAC/CG,EAAUX,EAAQ,CAAC,EAAID,EAAQI,EAASM,CAAO,EAC/CE,EAAUX,EAAQ,CAAC,EAAID,EAAQI,EAASO,CAAO,EAC/CV,GAAS,CACZ,CAEL,OAAOW,CACV,CACD,OAAO,sBAAsBtD,EAAO,CAChC,OAAIA,IAAU,GAAKA,IAAU,KAAOA,IAAU,UACnC,EAEJ,EAAI2B,EAAS,sBAAsB3B,GAAS,CAAC,CACvD,CACD,OAAO,mBAAmBmC,EAAOC,EAAQC,EAAYC,EAAYC,EAAaW,EAASC,EAASC,EAAS,CACrG,MAAME,EAAY,IAAI,WAAWhB,CAAU,EACrCI,EAAU,IAAI,WAAWH,EAAaF,CAAU,EACtD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IAAK,CAC5B,MAAMC,GAAUD,EAAID,EAAIT,GAAS,EACjCmB,EAAUX,CAAK,EAAID,EAAQI,EAASI,CAAO,EAC3CI,EAAUX,EAAQ,CAAC,EAAID,EAAQI,EAASK,CAAO,EAC/CG,EAAUX,EAAQ,CAAC,EAAID,EAAQI,EAASM,CAAO,EAC/CT,GAAS,CACZ,CAEL,OAAOW,CACV,CACD,OAAO,yBAAyBnB,EAAOC,EAAQC,EAAYC,EAAYC,EAAa,CAChF,MAAMe,EAAY,IAAI,WAAWhB,CAAU,EACrCI,EAAU,IAAI,WAAWH,EAAaF,CAAU,EACtD,IAAIM,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIR,EAAQQ,IACxB,QAASC,EAAI,EAAGA,EAAIV,EAAOU,IAAK,CAC5B,MAAMC,EAASD,EAAID,EAAIT,EACvBmB,EAAUX,CAAK,EAAID,EAAQI,CAAM,EACjCH,GACH,CAEL,OAAOW,CACV,CAKD,OAAO,gBAAgBC,EAAQC,EAAS5B,EAAM6B,EAAMC,EAAaC,EAAOC,EAAW,GAAIC,EAAaC,EAA2B,GAAM,CACjI,IAAIC,EAA2B,KAC3BN,EAAK,sBACLM,EAA2B,CAAA,GAE/B,MAAMC,EAAM,CAAC,CAACT,EAAO,QAAO,EAAG,KAE/BC,EAAQ,gBAAkBE,EAC1B,MAAM7B,EAAS,IAAI,WAAWD,EAAK,OAAQA,EAAK,WAAYjB,CAAe,EAC3E,IAAIqB,EAAQG,EAAOC,EAAQE,EAAa,EAAGD,EACvCiB,EAAWvB,EAAakC,EACxBC,EAA2B,EAC3BC,EAAa,EACjB,GAAItC,EAAOjB,EAAS,IAAMnB,GAAW,CACjC2E,EAAO,MAAM,oCAAoC,EACjD,MACH,CACD,GAAI,CAACX,EAAK,UAAY,CAACA,EAAK,OAAS,CAACA,EAAK,YAAa,CACpDW,EAAO,MAAM,kEAAkE,EAC/E,MACH,CACD,GAAIX,EAAK,cAAgB,CAACO,EAAK,CAC3BI,EAAO,MAAM,yDAAyD,EACtE,MACH,CACD,IAAIC,EAAMxC,EAAOT,EAAU,EAC3BiB,EAAaR,EAAOhB,EAAQ,EAAI,EAChC,IAAIyD,EAAiB,GACrB,GAAIb,EAAK,SAEL,OADAzB,EAASH,EAAOV,EAAY,EACpBa,EAAM,CACV,KAAK9B,EACDiE,EAAa,EACbD,EAA2B,MAC3B,MACJ,KAAK/D,EACDgE,EAAa,GACbD,EAA2B,MAC3B,MACJ,KAAK9D,GACD+D,EAAa,GACbD,EAA2B,MAC3B,MACJ,KAAK5D,GACDgE,EAAiB,GACjBD,EAAM,GACN,MACJ,KAAK9D,GACD+D,EAAiB,GACjBD,EAAM,IACN,MACJ,KAAKhE,EAAa,CAEdgC,GAAc,EAAI,EAClB,IAAIkC,EAAY,GAChB,OAAQd,EAAK,WAAU,CACnB,KAAKhD,GACD6D,EAAiB,GACjBD,EAAM,GACNE,EAAY,GACZ,MACJ,KAAK/D,GACD8D,EAAiB,GACjBD,EAAM,IACNE,EAAY,GACZ,MACJ,KAAK7D,GACD+C,EAAK,MAAQ,GACbA,EAAK,SAAW,GAChBY,EAAM,GACNE,EAAY,GACZ,KACP,CACD,GAAIA,EACA,KAEP,CAED,QACIH,EAAO,MAAM,CAAC,2BAA4BnE,GAAc+B,CAAM,CAAC,CAAC,EAChE,MACP,CAEL,MAAMkB,EAAUvB,EAAS,sBAAsBE,EAAOR,EAAS,CAAC,EAC1D8B,EAAUxB,EAAS,sBAAsBE,EAAOP,EAAS,CAAC,EAC1D8B,EAAUzB,EAAS,sBAAsBE,EAAON,EAAS,CAAC,EAC1D8B,GAAU1B,EAAS,sBAAsBE,EAAOL,EAAS,CAAC,EAC5D8C,IACAJ,EAA2BX,EAAO,kCAAkCE,EAAK,WAAW,GAExF1B,EAAc,EACVF,EAAOf,EAAS,EAAIpB,GAAoBgE,IAAgB,KACxD3B,EAAc,KAAK,IAAI,EAAGF,EAAOZ,EAAe,CAAC,GAErD,MAAMuD,GAAYX,GAAe,EAC3BY,EAAOlB,EAAO,UACpB,QAASmB,EAAOF,GAAWE,EAAOf,EAAOe,IAAQ,CAG7C,IAFAvC,EAAQN,EAAOb,CAAS,EACxBoB,EAASP,EAAOd,EAAU,EACrBkD,EAAM,EAAGA,EAAMlC,EAAa,EAAEkC,EAAK,CACpC,GAAIL,IAAa,IAAMA,IAAaK,EAAK,CAErC,MAAMU,EAAIf,IAAa,GAAKK,EAAM,EAClC,GAAI,CAACR,EAAK,cAAgBA,EAAK,SAAU,CACrCD,EAAQ,OAAS,EACjBlB,EAAaH,EAAQC,EAAS,EAC9B,IAAIwC,EAAa,KACjB,GAAIrB,EAAO,QAAUA,EAAO,eAAkB,CAACkB,EAAK,kBAAoB,CAACA,EAAK,aAEtEJ,IAAQ,KACRO,EAAajD,EAAS,+BAA+BQ,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,OAAQ+C,CAAC,EACxHZ,GAA4BY,GAAK,GACjCZ,EAAyB,KAAKpC,EAAS,yBAAyBQ,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,OAAQ+C,CAAC,CAAC,GAGvIN,IAAQ,KACbO,EAAajD,EAAS,mCAAmCQ,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,OAAQ+C,CAAC,EAC5HZ,GAA4BY,GAAK,GACjCZ,EAAyB,KAAKpC,EAAS,oCAAoCQ,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,OAAQ+C,CAAC,CAAC,GAG3JnB,EAAQ,KAAO,MAEd,CACD,MAAMqB,EAAiBJ,EAAK,eAAkBX,GAA4BW,EAAK,6BAAgC,CAACX,GAC1GgB,EAAqBL,EAAK,mBAAsBX,GAA4BW,EAAK,iCAAoC,CAACX,GACtHiB,GAAYV,IAAQ,KAAQA,IAAQ,IAAM,CAACS,IAAwBD,EACnE,GACCR,IAAQ,IAAOA,IAAQ,KAAO,CAACQ,IAAoBC,EAChD,EACA,EACV,IAAIE,EACAC,EAAuB,KAC3B,OAAQZ,EAAG,CACP,IAAK,KAAK,CACN,OAAQU,EAAQ,CACZ,IAAK,GACDC,EAAarD,EAAS,yBACtBsD,EAAuB,KACvB,MACJ,IAAK,GACDD,EAAarD,EAAS,oCACtBsD,EAAuBtD,EAAS,yBAChC,MACJ,IAAK,GACDqD,EAAarD,EAAS,+BACtBsD,EAAuBtD,EAAS,yBAChC,KACP,CACD,KACH,CACD,QAAS,CAEL,OAAQoD,EAAQ,CACZ,IAAK,GACDC,EAAarD,EAAS,oCACtBsD,EAAuB,KACvB,MACJ,IAAK,GACDD,EAAarD,EAAS,6BACtBsD,EAAuBtD,EAAS,oCAChC,MACJ,IAAK,GACDqD,EAAarD,EAAS,mCACtBsD,EAAuBtD,EAAS,oCAChC,KACP,CACD,KACH,CACJ,CACD6B,EAAQ,KAAOuB,EACfH,EAAaI,EAAW7C,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,OAAQ+C,CAAC,EAC3FZ,GAA4BY,GAAK,GACjCZ,EAAyB,KAAKkB,EAAuBA,EAAqB9C,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,OAAQ+C,CAAC,EAAIC,CAAU,CAEtK,CACGA,GACArB,EAAO,6BAA6BC,EAASoB,EAAYF,EAAMC,CAAC,CAEvE,SACQlB,EAAK,MACVD,EAAQ,KAAO,EACXa,IAAQ,IACRb,EAAQ,OAAS,EACjBlB,EAAaH,EAAQC,EAAS,EAC9BkB,EAAY3B,EAAS,mBAAmBQ,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,OAAQsB,EAASC,EAASC,CAAO,EACvIG,EAAO,6BAA6BC,EAASF,EAAWoB,EAAMC,CAAC,IAI/DnB,EAAQ,OAAS,EACjBlB,EAAaH,EAAQC,EAAS,EAC9BkB,EAAY3B,EAAS,oBAAoBQ,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,OAAQsB,EAASC,EAASC,EAASC,EAAO,EACjJE,EAAO,6BAA6BC,EAASF,EAAWoB,EAAMC,CAAC,WAG9DlB,EAAK,YAAa,CACvB,MAAMyB,EAAkB3B,EAAO,uBACzB4B,EAAkBhD,EAExBG,EADsB,KAAK,OAAOH,EAAQ+C,EAAkB,GAAKA,CAAe,EAAIA,GACtD9C,EAAS,GAAK+C,EAC5C7B,EAAY3B,EAAS,yBAAyBQ,EAAOC,EAAQR,EAAK,WAAaS,EAAYC,EAAYV,EAAK,MAAM,EAClH4B,EAAQ,OAAS,EACjBA,EAAQ,KAAO,EACfD,EAAO,6BAA6BC,EAASF,EAAWoB,EAAMC,CAAC,CAClE,MAEGrC,EAAgB,KAAK,IAAI,EAAGH,CAAK,EAAI,EAAK,KAAK,IAAI,EAAGC,CAAM,EAAK,EAAK+B,EACtEb,EAAY,IAAI,WAAW1B,EAAK,OAAQA,EAAK,WAAaS,EAAYC,CAAU,EAChFkB,EAAQ,KAAO,EACfD,EAAO,uCAAuCC,EAASU,EAA0B/B,EAAOC,EAAQkB,EAAWoB,EAAMC,CAAC,CAEzH,CACDtC,GAAcgC,EAAMlC,EAAQC,GAAUiC,EAAM,GAAK/B,EACjDH,GAAS,GACTC,GAAU,GACVD,EAAQ,KAAK,IAAI,EAAKA,CAAK,EAC3BC,EAAS,KAAK,IAAI,EAAKA,CAAM,CAChC,CACD,GAAIyB,IAAgB,OAEhB,KAEP,CACGE,GAA4BA,EAAyB,OAAS,EAC9DN,EAAK,oBAAsB2B,GAAkC,oCAAoC,CAC7F,KAAMvD,EAAOb,CAAS,EACtB,MAAO+C,EAAyB,CAAC,EACjC,KAAMA,EAAyB,CAAC,EAChC,GAAIA,EAAyB,CAAC,EAC9B,KAAMA,EAAyB,CAAC,EAChC,MAAOA,EAAyB,CAAC,EACjC,KAAMA,EAAyB,CAAC,EAChC,OAAQ,EACR,KAAM,EACN,WAAY,EAC5B,CAAa,EAGDN,EAAK,oBAAsB,MAElC,CACL,CAIA9B,EAAS,uBAAyB", "x_google_ignoreList": [0]}