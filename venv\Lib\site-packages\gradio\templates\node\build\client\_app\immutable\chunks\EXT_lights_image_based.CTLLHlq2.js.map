{"version": 3, "file": "EXT_lights_image_based.CTLLHlq2.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/rawCubeTexture.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/EXT_lights_image_based.js"], "sourcesContent": ["import { Serial<PERSON>Helper } from \"../../Misc/decorators.serialization.js\";\nimport { _UpdateRGBDAsync as UpdateRGBDAsyncEnvTools } from \"../../Misc/environmentTextureTools.js\";\nimport { CubeTexture } from \"./cubeTexture.js\";\n\n/**\n * Raw cube texture where the raw buffers are passed in\n */\nexport class RawCubeTexture extends CubeTexture {\n    /**\n     * Creates a cube texture where the raw buffers are passed in.\n     * @param scene defines the scene the texture is attached to\n     * @param data defines the array of data to use to create each face\n     * @param size defines the size of the textures\n     * @param format defines the format of the data\n     * @param type defines the type of the data (like Engine.TEXTURETYPE_UNSIGNED_BYTE)\n     * @param generateMipMaps  defines if the engine should generate the mip levels\n     * @param invertY defines if data must be stored with Y axis inverted\n     * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\n     * @param compression defines the compression used (null by default)\n     */\n    constructor(scene, data, size, format = 5, type = 0, generateMipMaps = false, invertY = false, samplingMode = 3, compression = null) {\n        super(\"\", scene);\n        this._texture = scene.getEngine().createRawCubeTexture(data, size, format, type, generateMipMaps, invertY, samplingMode, compression);\n    }\n    /**\n     * Updates the raw cube texture.\n     * @param data defines the data to store\n     * @param format defines the data format\n     * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\n     * @param invertY defines if data must be stored with Y axis inverted\n     * @param compression defines the compression used (null by default)\n     */\n    update(data, format, type, invertY, compression = null) {\n        this._texture.getEngine().updateRawCubeTexture(this._texture, data, format, type, invertY, compression);\n    }\n    /**\n     * Updates a raw cube texture with RGBD encoded data.\n     * @param data defines the array of data [mipmap][face] to use to create each face\n     * @param sphericalPolynomial defines the spherical polynomial for irradiance\n     * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\n     * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\n     * @returns a promise that resolves when the operation is complete\n     */\n    updateRGBDAsync(data, sphericalPolynomial = null, lodScale = 0.8, lodOffset = 0) {\n        return UpdateRGBDAsyncEnvTools(this._texture, data, sphericalPolynomial, lodScale, lodOffset).then(() => { });\n    }\n    /**\n     * Clones the raw cube texture.\n     * @returns a new cube texture\n     */\n    clone() {\n        return SerializationHelper.Clone(() => {\n            const scene = this.getScene();\n            const internalTexture = this._texture;\n            const texture = new RawCubeTexture(scene, internalTexture._bufferViewArray, internalTexture.width, internalTexture.format, internalTexture.type, internalTexture.generateMipMaps, internalTexture.invertY, internalTexture.samplingMode, internalTexture._compression);\n            if (internalTexture.source === 13 /* InternalTextureSource.CubeRawRGBD */) {\n                texture.updateRGBDAsync(internalTexture._bufferViewArrayArray, internalTexture._sphericalPolynomial, internalTexture._lodGenerationScale, internalTexture._lodGenerationOffset);\n            }\n            return texture;\n        }, this);\n    }\n}\n//# sourceMappingURL=rawCubeTexture.js.map", "import { SphericalHarmonics, SphericalPolynomial } from \"@babylonjs/core/Maths/sphericalPolynomial.js\";\nimport { Quaternion, Matrix } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { RawCubeTexture } from \"@babylonjs/core/Materials/Textures/rawCubeTexture.js\";\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"EXT_lights_image_based\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Vendor/EXT_lights_image_based/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class EXT_lights_image_based {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n        delete this._lights;\n    }\n    /** @internal */\n    onLoading() {\n        const extensions = this._loader.gltf.extensions;\n        if (extensions && extensions[this.name]) {\n            const extension = extensions[this.name];\n            this._lights = extension.lights;\n        }\n    }\n    /**\n     * @internal\n     */\n    loadSceneAsync(context, scene) {\n        return GLTFLoader.LoadExtensionAsync(context, scene, this.name, (extensionContext, extension) => {\n            this._loader._allMaterialsDirtyRequired = true;\n            const promises = new Array();\n            promises.push(this._loader.loadSceneAsync(context, scene));\n            this._loader.logOpen(`${extensionContext}`);\n            const light = ArrayItem.Get(`${extensionContext}/light`, this._lights, extension.light);\n            promises.push(this._loadLightAsync(`/extensions/${this.name}/lights/${extension.light}`, light).then((texture) => {\n                this._loader.babylonScene.environmentTexture = texture;\n            }));\n            this._loader.logClose();\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadLightAsync(context, light) {\n        if (!light._loaded) {\n            const promises = new Array();\n            this._loader.logOpen(`${context}`);\n            const imageData = new Array(light.specularImages.length);\n            for (let mipmap = 0; mipmap < light.specularImages.length; mipmap++) {\n                const faces = light.specularImages[mipmap];\n                imageData[mipmap] = new Array(faces.length);\n                for (let face = 0; face < faces.length; face++) {\n                    const specularImageContext = `${context}/specularImages/${mipmap}/${face}`;\n                    this._loader.logOpen(`${specularImageContext}`);\n                    const index = faces[face];\n                    const image = ArrayItem.Get(specularImageContext, this._loader.gltf.images, index);\n                    promises.push(this._loader.loadImageAsync(`/images/${index}`, image).then((data) => {\n                        imageData[mipmap][face] = data;\n                    }));\n                    this._loader.logClose();\n                }\n            }\n            this._loader.logClose();\n            light._loaded = Promise.all(promises).then(() => {\n                const babylonTexture = new RawCubeTexture(this._loader.babylonScene, null, light.specularImageSize);\n                babylonTexture.name = light.name || \"environment\";\n                light._babylonTexture = babylonTexture;\n                if (light.intensity != undefined) {\n                    babylonTexture.level = light.intensity;\n                }\n                if (light.rotation) {\n                    let rotation = Quaternion.FromArray(light.rotation);\n                    // Invert the rotation so that positive rotation is counter-clockwise.\n                    if (!this._loader.babylonScene.useRightHandedSystem) {\n                        rotation = Quaternion.Inverse(rotation);\n                    }\n                    Matrix.FromQuaternionToRef(rotation, babylonTexture.getReflectionTextureMatrix());\n                }\n                if (!light.irradianceCoefficients) {\n                    throw new Error(`${context}: Irradiance coefficients are missing`);\n                }\n                const sphericalHarmonics = SphericalHarmonics.FromArray(light.irradianceCoefficients);\n                sphericalHarmonics.scaleInPlace(light.intensity);\n                sphericalHarmonics.convertIrradianceToLambertianRadiance();\n                const sphericalPolynomial = SphericalPolynomial.FromHarmonics(sphericalHarmonics);\n                // Compute the lod generation scale to fit exactly to the number of levels available.\n                const lodGenerationScale = (imageData.length - 1) / Math.log2(light.specularImageSize);\n                return babylonTexture.updateRGBDAsync(imageData, sphericalPolynomial, lodGenerationScale);\n            });\n        }\n        return light._loaded.then(() => {\n            return light._babylonTexture;\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new EXT_lights_image_based(loader));\n//# sourceMappingURL=EXT_lights_image_based.js.map"], "names": ["RawCubeTexture", "CubeTexture", "scene", "data", "size", "format", "type", "generateMipMaps", "invertY", "samplingMode", "compression", "sphericalPolynomial", "lodScale", "lodOffset", "UpdateRGBDAsyncEnvTools", "SerializationHelper", "internalTexture", "texture", "NAME", "EXT_lights_image_based", "loader", "extensions", "extension", "context", "GLTFLoader", "extensionContext", "promises", "light", "ArrayItem", "imageData", "mipmap", "faces", "face", "specularImageContext", "index", "image", "babylonTexture", "rotation", "Quaternion", "Matrix", "sphericalHarmonics", "SphericalHarmonics", "SphericalPolynomial", "lodGenerationScale", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "6QAOO,MAAMA,UAAuBC,CAAY,CAa5C,YAAYC,EAAOC,EAAMC,EAAMC,EAAS,EAAGC,EAAO,EAAGC,EAAkB,GAAOC,EAAU,GAAOC,EAAe,EAAGC,EAAc,KAAM,CACjI,MAAM,GAAIR,CAAK,EACf,KAAK,SAAWA,EAAM,UAAW,EAAC,qBAAqBC,EAAMC,EAAMC,EAAQC,EAAMC,EAAiBC,EAASC,EAAcC,CAAW,CACvI,CASD,OAAOP,EAAME,EAAQC,EAAME,EAASE,EAAc,KAAM,CACpD,KAAK,SAAS,UAAW,EAAC,qBAAqB,KAAK,SAAUP,EAAME,EAAQC,EAAME,EAASE,CAAW,CACzG,CASD,gBAAgBP,EAAMQ,EAAsB,KAAMC,EAAW,GAAKC,EAAY,EAAG,CAC7E,OAAOC,EAAwB,KAAK,SAAUX,EAAMQ,EAAqBC,EAAUC,CAAS,EAAE,KAAK,IAAM,CAAG,CAAA,CAC/G,CAKD,OAAQ,CACJ,OAAOE,EAAoB,MAAM,IAAM,CACnC,MAAMb,EAAQ,KAAK,WACbc,EAAkB,KAAK,SACvBC,EAAU,IAAIjB,EAAeE,EAAOc,EAAgB,iBAAkBA,EAAgB,MAAOA,EAAgB,OAAQA,EAAgB,KAAMA,EAAgB,gBAAiBA,EAAgB,QAASA,EAAgB,aAAcA,EAAgB,YAAY,EACrQ,OAAIA,EAAgB,SAAW,IAC3BC,EAAQ,gBAAgBD,EAAgB,sBAAuBA,EAAgB,qBAAsBA,EAAgB,oBAAqBA,EAAgB,oBAAoB,EAE3KC,CACV,EAAE,IAAI,CACV,CACL,CCxDA,MAAMC,EAAO,yBAKN,MAAMC,CAAuB,CAIhC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,KACf,OAAO,KAAK,OACf,CAED,WAAY,CACR,MAAMG,EAAa,KAAK,QAAQ,KAAK,WACrC,GAAIA,GAAcA,EAAW,KAAK,IAAI,EAAG,CACrC,MAAMC,EAAYD,EAAW,KAAK,IAAI,EACtC,KAAK,QAAUC,EAAU,MAC5B,CACJ,CAID,eAAeC,EAASrB,EAAO,CAC3B,OAAOsB,EAAW,mBAAmBD,EAASrB,EAAO,KAAK,KAAM,CAACuB,EAAkBH,IAAc,CAC7F,KAAK,QAAQ,2BAA6B,GAC1C,MAAMI,EAAW,IAAI,MACrBA,EAAS,KAAK,KAAK,QAAQ,eAAeH,EAASrB,CAAK,CAAC,EACzD,KAAK,QAAQ,QAAQ,GAAGuB,CAAgB,EAAE,EAC1C,MAAME,EAAQC,EAAU,IAAI,GAAGH,CAAgB,SAAU,KAAK,QAASH,EAAU,KAAK,EACtF,OAAAI,EAAS,KAAK,KAAK,gBAAgB,eAAe,KAAK,IAAI,WAAWJ,EAAU,KAAK,GAAIK,CAAK,EAAE,KAAMV,GAAY,CAC9G,KAAK,QAAQ,aAAa,mBAAqBA,CAClD,CAAA,CAAC,EACF,KAAK,QAAQ,WACN,QAAQ,IAAIS,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,gBAAgBH,EAASI,EAAO,CAC5B,GAAI,CAACA,EAAM,QAAS,CAChB,MAAMD,EAAW,IAAI,MACrB,KAAK,QAAQ,QAAQ,GAAGH,CAAO,EAAE,EACjC,MAAMM,EAAY,IAAI,MAAMF,EAAM,eAAe,MAAM,EACvD,QAASG,EAAS,EAAGA,EAASH,EAAM,eAAe,OAAQG,IAAU,CACjE,MAAMC,EAAQJ,EAAM,eAAeG,CAAM,EACzCD,EAAUC,CAAM,EAAI,IAAI,MAAMC,EAAM,MAAM,EAC1C,QAASC,EAAO,EAAGA,EAAOD,EAAM,OAAQC,IAAQ,CAC5C,MAAMC,EAAuB,GAAGV,CAAO,mBAAmBO,CAAM,IAAIE,CAAI,GACxE,KAAK,QAAQ,QAAQ,GAAGC,CAAoB,EAAE,EAC9C,MAAMC,EAAQH,EAAMC,CAAI,EAClBG,EAAQP,EAAU,IAAIK,EAAsB,KAAK,QAAQ,KAAK,OAAQC,CAAK,EACjFR,EAAS,KAAK,KAAK,QAAQ,eAAe,WAAWQ,CAAK,GAAIC,CAAK,EAAE,KAAMhC,GAAS,CAChF0B,EAAUC,CAAM,EAAEE,CAAI,EAAI7B,CAC7B,CAAA,CAAC,EACF,KAAK,QAAQ,UAChB,CACJ,CACD,KAAK,QAAQ,WACbwB,EAAM,QAAU,QAAQ,IAAID,CAAQ,EAAE,KAAK,IAAM,CAC7C,MAAMU,EAAiB,IAAIpC,EAAe,KAAK,QAAQ,aAAc,KAAM2B,EAAM,iBAAiB,EAMlG,GALAS,EAAe,KAAOT,EAAM,MAAQ,cACpCA,EAAM,gBAAkBS,EACpBT,EAAM,WAAa,OACnBS,EAAe,MAAQT,EAAM,WAE7BA,EAAM,SAAU,CAChB,IAAIU,EAAWC,EAAW,UAAUX,EAAM,QAAQ,EAE7C,KAAK,QAAQ,aAAa,uBAC3BU,EAAWC,EAAW,QAAQD,CAAQ,GAE1CE,EAAO,oBAAoBF,EAAUD,EAAe,2BAA4B,CAAA,CACnF,CACD,GAAI,CAACT,EAAM,uBACP,MAAM,IAAI,MAAM,GAAGJ,CAAO,uCAAuC,EAErE,MAAMiB,EAAqBC,EAAmB,UAAUd,EAAM,sBAAsB,EACpFa,EAAmB,aAAab,EAAM,SAAS,EAC/Ca,EAAmB,sCAAqC,EACxD,MAAM7B,EAAsB+B,EAAoB,cAAcF,CAAkB,EAE1EG,GAAsBd,EAAU,OAAS,GAAK,KAAK,KAAKF,EAAM,iBAAiB,EACrF,OAAOS,EAAe,gBAAgBP,EAAWlB,EAAqBgC,CAAkB,CACxG,CAAa,CACJ,CACD,OAAOhB,EAAM,QAAQ,KAAK,IACfA,EAAM,eAChB,CACJ,CACL,CACAiB,EAAwB1B,CAAI,EAC5B2B,EAAsB3B,EAAM,GAAOE,GAAW,IAAID,EAAuBC,CAAM,CAAC", "x_google_ignoreList": [0, 1]}