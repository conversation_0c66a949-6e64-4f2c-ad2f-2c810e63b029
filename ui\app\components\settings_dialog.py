"""
设置对话框
包含模型下载、参数调整、主题设置等功能
"""

import os
from typing import Dict, Any

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, 
                            QWidget, QLabel, QLineEdit, QSpinBox, QDoubleSpinBox,
                            QComboBox, QCheckBox, QPushButton, QProgressBar,
                            QTextEdit, QGroupBox, QFormLayout, QSlider,
                            QMessageBox, QFileDialog, QListWidget, QListWidgetItem)
from PyQt6.QtCore import Qt, pyqtSignal, QThread
from PyQt6.QtGui import QFont

from core.utils.logger import get_logger
from core.utils.config import config_manager
from core.llm.model_manager import model_manager
from core.llm.model_downloader import model_downloader
from engine_2d.image_generator import image_generator
from ui.app.components.avatar_editor import AvatarEditor

logger = get_logger(__name__)

class ModelDownloadThread(QThread):
    """模型下载线程"""
    
    progress_updated = pyqtSignal(int, str)  # progress, status
    download_finished = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, model_name: str, model_url: str):
        super().__init__()
        self.model_name = model_name
        self.model_url = model_url
    
    def run(self):
        """运行下载"""
        try:
            def progress_callback(downloaded: int, total: int, status: str):
                if total > 0:
                    progress = int((downloaded / total) * 100)
                    self.progress_updated.emit(progress, status)
            
            success = model_downloader.download_model(
                self.model_url, 
                self.model_name,
                progress_callback=progress_callback
            )
            
            if success:
                self.download_finished.emit(True, f"模型 {self.model_name} 下载完成")
            else:
                self.download_finished.emit(False, f"模型 {self.model_name} 下载失败")
                
        except Exception as e:
            self.download_finished.emit(False, f"下载出错: {str(e)}")

class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.download_thread = None
        
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 模型设置页
        self.model_tab = self.create_model_tab()
        self.tab_widget.addTab(self.model_tab, "模型设置")
        
        # 生成参数页
        self.generation_tab = self.create_generation_tab()
        self.tab_widget.addTab(self.generation_tab, "生成参数")
        
        # 界面设置页
        self.ui_tab = self.create_ui_tab()
        self.tab_widget.addTab(self.ui_tab, "界面设置")
        
        # 高级设置页
        self.advanced_tab = self.create_advanced_tab()
        self.tab_widget.addTab(self.advanced_tab, "高级设置")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.apply_button = QPushButton("应用")
        self.apply_button.clicked.connect(self.apply_settings)
        button_layout.addWidget(self.apply_button)
        
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept_settings)
        button_layout.addWidget(self.ok_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)

    def on_avatar_changed(self, avatar_type: str, avatar_path: str):
        """头像变化处理"""
        try:
            # 更新配置
            config_manager.set(f"ui.avatars.{avatar_type}", avatar_path)
            logger.info(f"头像设置已更新: {avatar_type} -> {avatar_path}")
        except Exception as e:
            logger.error(f"更新头像设置时出错: {e}")
    
    def create_model_tab(self) -> QWidget:
        """创建模型设置页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 当前模型组
        current_group = QGroupBox("当前模型")
        current_layout = QFormLayout(current_group)
        
        self.current_model_label = QLabel("未加载")
        current_layout.addRow("当前模型:", self.current_model_label)
        
        self.model_combo = QComboBox()
        self.refresh_models_button = QPushButton("刷新")
        self.refresh_models_button.clicked.connect(self.refresh_models)
        
        model_select_layout = QHBoxLayout()
        model_select_layout.addWidget(self.model_combo, 1)
        model_select_layout.addWidget(self.refresh_models_button)
        current_layout.addRow("选择模型:", model_select_layout)
        
        self.load_model_button = QPushButton("加载模型")
        self.load_model_button.clicked.connect(self.load_selected_model)
        current_layout.addRow("", self.load_model_button)
        
        layout.addWidget(current_group)
        
        # 模型下载组
        download_group = QGroupBox("模型下载")
        download_layout = QFormLayout(download_group)
        
        # 预设模型列表
        self.preset_models = {
            "Lucy-128k-4bit": "https://huggingface.co/bartowski/Lucy-128k-GGUF/resolve/main/Lucy-128k-Q4_K_M.gguf",
            "Qwen2.5-7B-4bit": "https://huggingface.co/Qwen/Qwen2.5-7B-Instruct-GGUF/resolve/main/qwen2.5-7b-instruct-q4_k_m.gguf",
            "Llama-3.1-8B-4bit": "https://huggingface.co/bartowski/Meta-Llama-3.1-8B-Instruct-GGUF/resolve/main/Meta-Llama-3.1-8B-Instruct-Q4_K_M.gguf"
        }
        
        self.preset_combo = QComboBox()
        for model_name in self.preset_models.keys():
            self.preset_combo.addItem(model_name)
        download_layout.addRow("预设模型:", self.preset_combo)
        
        # 自定义URL
        self.custom_url_edit = QLineEdit()
        self.custom_url_edit.setPlaceholderText("输入模型下载URL...")
        download_layout.addRow("自定义URL:", self.custom_url_edit)
        
        # 下载按钮和进度条
        self.download_button = QPushButton("下载模型")
        self.download_button.clicked.connect(self.download_model)
        download_layout.addRow("", self.download_button)
        
        self.download_progress = QProgressBar()
        self.download_progress.setVisible(False)
        download_layout.addRow("下载进度:", self.download_progress)
        
        self.download_status = QLabel()
        download_layout.addRow("状态:", self.download_status)
        
        layout.addWidget(download_group)
        
        layout.addStretch()
        return widget
    
    def create_generation_tab(self) -> QWidget:
        """创建生成参数页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 文本生成参数
        text_group = QGroupBox("文本生成参数")
        text_layout = QFormLayout(text_group)
        
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(-1, 999999)  # -1表示无限制
        self.max_tokens_spin.setValue(-1)  # 默认无限制
        self.max_tokens_spin.setSpecialValueText("无限制")  # -1时显示"无限制"
        text_layout.addRow("最大Token数:", self.max_tokens_spin)
        
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.1, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setDecimals(1)
        self.temperature_spin.setValue(0.7)
        text_layout.addRow("温度:", self.temperature_spin)
        
        self.top_p_spin = QDoubleSpinBox()
        self.top_p_spin.setRange(0.1, 1.0)
        self.top_p_spin.setSingleStep(0.1)
        self.top_p_spin.setDecimals(1)
        self.top_p_spin.setValue(0.9)
        text_layout.addRow("Top-p:", self.top_p_spin)
        
        layout.addWidget(text_group)
        
        # 图像生成参数
        image_group = QGroupBox("图像生成参数")
        image_layout = QFormLayout(image_group)

        # T2I模型选择
        self.t2i_model_combo = QComboBox()
        self.refresh_t2i_models_button = QPushButton("刷新")
        self.refresh_t2i_models_button.clicked.connect(self.refresh_t2i_models)

        t2i_model_layout = QHBoxLayout()
        t2i_model_layout.addWidget(self.t2i_model_combo, 1)
        t2i_model_layout.addWidget(self.refresh_t2i_models_button)
        image_layout.addRow("T2I模型:", t2i_model_layout)

        # 采样器选择
        self.sampler_combo = QComboBox()
        self.sampler_combo.addItems([
            "euler", "euler_a", "heun", "dpm_2", "dpm_2_a",
            "lms", "dpm_fast", "dpm_adaptive", "dpmpp_2s_a",
            "dpmpp_2m", "dpmpp_sde", "dpmpp_2m_sde", "ddim", "plms"
        ])
        image_layout.addRow("采样器:", self.sampler_combo)

        # 模型加载模式
        self.model_load_mode_combo = QComboBox()
        self.model_load_mode_combo.addItems(["需要时自动加载", "启动时预加载", "手动管理"])
        image_layout.addRow("加载模式:", self.model_load_mode_combo)

        self.image_width_spin = QSpinBox()
        self.image_width_spin.setRange(256, 2048)
        self.image_width_spin.setSingleStep(64)
        self.image_width_spin.setValue(1024)
        image_layout.addRow("图像宽度:", self.image_width_spin)

        self.image_height_spin = QSpinBox()
        self.image_height_spin.setRange(256, 2048)
        self.image_height_spin.setSingleStep(64)
        self.image_height_spin.setValue(1024)
        image_layout.addRow("图像高度:", self.image_height_spin)
        
        self.image_steps_spin = QSpinBox()
        self.image_steps_spin.setRange(1, 100)
        self.image_steps_spin.setValue(20)
        image_layout.addRow("推理步数:", self.image_steps_spin)
        
        self.guidance_scale_spin = QDoubleSpinBox()
        self.guidance_scale_spin.setRange(1.0, 20.0)
        self.guidance_scale_spin.setSingleStep(0.5)
        self.guidance_scale_spin.setDecimals(1)
        self.guidance_scale_spin.setValue(7.5)
        image_layout.addRow("引导强度:", self.guidance_scale_spin)
        
        layout.addWidget(image_group)
        
        layout.addStretch()
        return widget
    
    def create_ui_tab(self) -> QWidget:
        """创建界面设置页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主题设置
        theme_group = QGroupBox("主题设置")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["深色主题", "浅色主题"])
        theme_layout.addRow("主题:", self.theme_combo)
        
        layout.addWidget(theme_group)
        
        # 字体设置
        font_group = QGroupBox("字体设置")
        font_layout = QFormLayout(font_group)
        
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems([
            "Microsoft YaHei UI", "Segoe UI", "Arial", "Helvetica", "SimHei"
        ])
        font_layout.addRow("字体:", self.font_family_combo)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        font_layout.addRow("字体大小:", self.font_size_spin)
        
        layout.addWidget(font_group)

        # 头像设置
        avatar_group = QGroupBox("头像设置")
        avatar_layout = QVBoxLayout(avatar_group)

        self.avatar_editor = AvatarEditor()
        self.avatar_editor.avatar_changed.connect(self.on_avatar_changed)
        avatar_layout.addWidget(self.avatar_editor)

        layout.addWidget(avatar_group)

        layout.addStretch()
        return widget
    
    def create_advanced_tab(self) -> QWidget:
        """创建高级设置页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 系统设置
        system_group = QGroupBox("系统设置")
        system_layout = QFormLayout(system_group)
        
        self.enable_nsfw_check = QCheckBox()
        system_layout.addRow("启用NSFW内容:", self.enable_nsfw_check)
        
        self.auto_search_check = QCheckBox()
        system_layout.addRow("自动联网搜索:", self.auto_search_check)
        
        self.save_conversations_check = QCheckBox()
        system_layout.addRow("保存对话历史:", self.save_conversations_check)
        
        layout.addWidget(system_group)
        
        # 路径设置
        path_group = QGroupBox("路径设置")
        path_layout = QFormLayout(path_group)
        
        self.model_path_edit = QLineEdit()
        model_path_button = QPushButton("浏览...")
        model_path_button.clicked.connect(self.browse_model_path)
        
        model_path_layout = QHBoxLayout()
        model_path_layout.addWidget(self.model_path_edit, 1)
        model_path_layout.addWidget(model_path_button)
        path_layout.addRow("模型路径:", model_path_layout)
        
        layout.addWidget(path_group)
        
        layout.addStretch()
        return widget
    
    def load_settings(self):
        """加载当前设置"""
        # 加载模型信息
        self.refresh_models()
        current_model = model_manager.get_current_model_info()
        if current_model:
            self.current_model_label.setText(current_model['name'])
        
        # 加载生成参数
        self.max_tokens_spin.setValue(config_manager.get('generation.max_tokens', -1))
        self.temperature_spin.setValue(config_manager.get('generation.temperature', 0.7))
        self.top_p_spin.setValue(config_manager.get('generation.top_p', 0.9))
        
        # 加载T2I模型信息
        self.refresh_t2i_models()

        # 加载图像参数
        self.image_width_spin.setValue(config_manager.get('generation.image.width', 1024))
        self.image_height_spin.setValue(config_manager.get('generation.image.height', 1024))
        self.image_steps_spin.setValue(config_manager.get('generation.image.steps', 20))
        self.guidance_scale_spin.setValue(config_manager.get('generation.image.guidance_scale', 7.5))

        # 加载T2I设置
        current_sampler = config_manager.get('models.t2i.default_sampler', 'euler')
        if current_sampler in [self.sampler_combo.itemText(i) for i in range(self.sampler_combo.count())]:
            self.sampler_combo.setCurrentText(current_sampler)

        load_mode = config_manager.get('models.t2i.load_mode', 'auto')
        mode_map = {'auto': '需要时自动加载', 'preload': '启动时预加载', 'manual': '手动管理'}
        self.model_load_mode_combo.setCurrentText(mode_map.get(load_mode, '需要时自动加载'))
        
        # 加载界面设置
        theme = config_manager.get('ui.theme', 'dark')
        self.theme_combo.setCurrentText("深色主题" if theme == 'dark' else "浅色主题")
        
        font_family = config_manager.get('ui.font_family', 'Microsoft YaHei UI')
        self.font_family_combo.setCurrentText(font_family)
        self.font_size_spin.setValue(config_manager.get('ui.font_size', 12))
        
        # 加载高级设置
        self.enable_nsfw_check.setChecked(config_manager.get('personas.enable_nsfw', True))
        self.auto_search_check.setChecked(config_manager.get('search.auto_search', True))
        self.save_conversations_check.setChecked(config_manager.get('memory.save_conversations', True))
        
        self.model_path_edit.setText(config_manager.get('models.llm.model_path', 'models/llm'))
    
    def refresh_models(self):
        """刷新LLM模型列表"""
        self.model_combo.clear()
        available_models = model_manager.get_available_models()

        for model_name in available_models.keys():
            self.model_combo.addItem(model_name)

    def refresh_t2i_models(self):
        """刷新T2I模型列表"""
        self.t2i_model_combo.clear()
        try:
            available_models = image_generator.list_models()
            current_model = image_generator.get_current_model()

            for model_name, model_info in available_models.items():
                model_type = model_info.get('type', 'unknown')
                display_name = f"{model_name} ({model_type.upper()})"
                self.t2i_model_combo.addItem(display_name, model_name)

                # 设置当前选中的模型
                if model_name == current_model:
                    self.t2i_model_combo.setCurrentText(display_name)

        except Exception as e:
            logger.error(f"刷新T2I模型列表失败: {e}")
            self.t2i_model_combo.addItem("无可用模型")
    
    def load_selected_model(self):
        """加载选中的模型"""
        model_name = self.model_combo.currentText()
        if model_name:
            if model_manager.load_model(model_name):
                self.current_model_label.setText(model_name)
                QMessageBox.information(self, "成功", f"模型 {model_name} 加载成功")
            else:
                QMessageBox.warning(self, "失败", f"模型 {model_name} 加载失败")
    
    def download_model(self):
        """下载模型"""
        if self.download_thread and self.download_thread.isRunning():
            QMessageBox.warning(self, "警告", "已有模型正在下载中")
            return
        
        # 获取下载URL
        if self.custom_url_edit.text().strip():
            model_url = self.custom_url_edit.text().strip()
            model_name = os.path.basename(model_url).split('.')[0]
        else:
            model_name = self.preset_combo.currentText()
            model_url = self.preset_models.get(model_name)
        
        if not model_url:
            QMessageBox.warning(self, "错误", "请选择模型或输入下载URL")
            return
        
        # 开始下载
        self.download_thread = ModelDownloadThread(model_name, model_url)
        self.download_thread.progress_updated.connect(self.update_download_progress)
        self.download_thread.download_finished.connect(self.download_finished)
        
        self.download_button.setEnabled(False)
        self.download_progress.setVisible(True)
        self.download_progress.setValue(0)
        self.download_status.setText("开始下载...")
        
        self.download_thread.start()
    
    def update_download_progress(self, progress: int, status: str):
        """更新下载进度"""
        self.download_progress.setValue(progress)
        self.download_status.setText(status)
    
    def download_finished(self, success: bool, message: str):
        """下载完成"""
        self.download_button.setEnabled(True)
        self.download_progress.setVisible(False)
        self.download_status.setText(message)
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.refresh_models()
        else:
            QMessageBox.warning(self, "失败", message)
    
    def browse_model_path(self):
        """浏览模型路径"""
        path = QFileDialog.getExistingDirectory(self, "选择模型目录")
        if path:
            self.model_path_edit.setText(path)
    
    def apply_settings(self):
        """应用设置"""
        # 保存生成参数
        config_manager.set('generation.max_tokens', self.max_tokens_spin.value())
        config_manager.set('generation.temperature', self.temperature_spin.value())
        config_manager.set('generation.top_p', self.top_p_spin.value())
        
        # 保存图像参数
        config_manager.set('generation.image.width', self.image_width_spin.value())
        config_manager.set('generation.image.height', self.image_height_spin.value())
        config_manager.set('generation.image.steps', self.image_steps_spin.value())
        config_manager.set('generation.image.guidance_scale', self.guidance_scale_spin.value())

        # 保存T2I设置
        config_manager.set('models.t2i.default_sampler', self.sampler_combo.currentText())

        # 保存T2I加载模式
        mode_map = {'需要时自动加载': 'auto', '启动时预加载': 'preload', '手动管理': 'manual'}
        load_mode = mode_map.get(self.model_load_mode_combo.currentText(), 'auto')
        config_manager.set('models.t2i.load_mode', load_mode)
        
        # 保存界面设置
        theme = 'dark' if self.theme_combo.currentText() == "深色主题" else 'light'
        config_manager.set('ui.theme', theme)
        config_manager.set('ui.font_family', self.font_family_combo.currentText())
        config_manager.set('ui.font_size', self.font_size_spin.value())
        
        # 保存高级设置
        config_manager.set('personas.enable_nsfw', self.enable_nsfw_check.isChecked())
        config_manager.set('search.auto_search', self.auto_search_check.isChecked())
        config_manager.set('memory.save_conversations', self.save_conversations_check.isChecked())
        config_manager.set('models.llm.model_path', self.model_path_edit.text())
        
        # 保存配置
        config_manager.save_config()
        
        QMessageBox.information(self, "成功", "设置已保存")
    
    def accept_settings(self):
        """确定并关闭"""
        self.apply_settings()
        self.accept()
