import{a as ie}from"./index-CEGzm7H5.js";/* empty css                                                        */import{B as Ae}from"./BlockTitle-Ct-h8ev5.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{D as De}from"./DropdownArrow-DYWFcSFn.js";import"./index-B7J2Z2jS.js";const{SvelteComponent:Ee,add_render_callback:ge,append:W,attr:E,binding_callbacks:re,check_outros:Ce,create_bidirectional_transition:ue,destroy_each:Te,detach:M,element:X,empty:qe,ensure_array_like:fe,flush:H,group_outros:Se,init:Ne,insert:P,listen:Y,prevent_default:Be,run_all:we,safe_not_equal:ze,set_data:Ue,set_style:N,space:ee,text:He,toggle_class:q,transition_in:x,transition_out:_e}=window.__gradio__svelte__internal,{createEventDispatcher:Je}=window.__gradio__svelte__internal;function ce(l,t,e){const f=l.slice();return f[28]=t[e],f}function he(l){let t,e,f,_,u,r=fe(l[1]),n=[];for(let s=0;s<r.length;s+=1)n[s]=ae(ce(l,r,s));return{c(){t=X("ul");for(let s=0;s<n.length;s+=1)n[s].c();E(t,"class","options svelte-y6qw75"),E(t,"role","listbox"),N(t,"top",l[9]),N(t,"bottom",l[10]),N(t,"max-height",`calc(${l[11]}px - var(--window-padding))`),N(t,"width",l[8]+"px")},m(s,w){P(s,t,w);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(t,null);l[24](t),f=!0,_||(u=[Y(t,"mousedown",Be(l[22])),Y(t,"scroll",l[23])],_=!0)},p(s,w){if(w&307){r=fe(s[1]);let o;for(o=0;o<r.length;o+=1){const d=ce(s,r,o);n[o]?n[o].p(d,w):(n[o]=ae(d),n[o].c(),n[o].m(t,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=r.length}w&512&&N(t,"top",s[9]),w&1024&&N(t,"bottom",s[10]),w&2048&&N(t,"max-height",`calc(${s[11]}px - var(--window-padding))`),w&256&&N(t,"width",s[8]+"px")},i(s){f||(s&&ge(()=>{f&&(e||(e=ue(t,ie,{duration:200,y:5},!0)),e.run(1))}),f=!0)},o(s){s&&(e||(e=ue(t,ie,{duration:200,y:5},!1)),e.run(0)),f=!1},d(s){s&&M(t),Te(n,s),l[24](null),s&&e&&e.end(),_=!1,we(u)}}}function ae(l){let t,e,f,_=l[0][l[28]][0]+"",u,r,n,s,w;return{c(){t=X("li"),e=X("span"),e.textContent="✓",f=ee(),u=He(_),r=ee(),E(e,"class","inner-item svelte-y6qw75"),q(e,"hide",!l[4].includes(l[28])),E(t,"class","item svelte-y6qw75"),E(t,"data-index",n=l[28]),E(t,"aria-label",s=l[0][l[28]][0]),E(t,"data-testid","dropdown-option"),E(t,"role","option"),E(t,"aria-selected",w=l[4].includes(l[28])),q(t,"selected",l[4].includes(l[28])),q(t,"active",l[28]===l[5]),q(t,"bg-gray-100",l[28]===l[5]),q(t,"dark:bg-gray-600",l[28]===l[5]),N(t,"width",l[8]+"px")},m(o,d){P(o,t,d),W(t,e),W(t,f),W(t,u),W(t,r)},p(o,d){d&18&&q(e,"hide",!o[4].includes(o[28])),d&3&&_!==(_=o[0][o[28]][0]+"")&&Ue(u,_),d&2&&n!==(n=o[28])&&E(t,"data-index",n),d&3&&s!==(s=o[0][o[28]][0])&&E(t,"aria-label",s),d&18&&w!==(w=o[4].includes(o[28]))&&E(t,"aria-selected",w),d&18&&q(t,"selected",o[4].includes(o[28])),d&34&&q(t,"active",o[28]===o[5]),d&34&&q(t,"bg-gray-100",o[28]===o[5]),d&34&&q(t,"dark:bg-gray-600",o[28]===o[5]),d&256&&N(t,"width",o[8]+"px")},d(o){o&&M(t)}}}function Le(l){let t,e,f,_,u;ge(l[20]);let r=l[2]&&!l[3]&&he(l);return{c(){t=X("div"),e=ee(),r&&r.c(),f=qe(),E(t,"class","reference")},m(n,s){P(n,t,s),l[21](t),P(n,e,s),r&&r.m(n,s),P(n,f,s),_||(u=[Y(window,"scroll",l[14]),Y(window,"resize",l[20])],_=!0)},p(n,[s]){n[2]&&!n[3]?r?(r.p(n,s),s&12&&x(r,1)):(r=he(n),r.c(),x(r,1),r.m(f.parentNode,f)):r&&(Se(),_e(r,1,1,()=>{r=null}),Ce())},i(n){x(r)},o(n){_e(r)},d(n){n&&(M(t),M(e),M(f)),l[21](null),r&&r.d(n),_=!1,we(u)}}}function Re(l,t,e){let{choices:f}=t,{filtered_indices:_}=t,{show_options:u=!1}=t,{disabled:r=!1}=t,{selected_indices:n=[]}=t,{active_index:s=null}=t,{remember_scroll:w=!1}=t,o,d,p,y,C,h,i,m,k,a,B=0;function z(){const{top:b,bottom:F}=C.getBoundingClientRect();e(17,o=b),e(18,d=a-F)}let O=null;function A(){u&&(O!==null&&clearTimeout(O),O=setTimeout(()=>{z(),O=null},10))}function g(){h?.scrollTo?.(0,B)}const T=Je();function U(){e(12,a=window.innerHeight)}function Q(b){re[b?"unshift":"push"](()=>{C=b,e(6,C)})}const Z=b=>T("change",b),j=b=>e(13,B=b.currentTarget.scrollTop);function v(b){re[b?"unshift":"push"](()=>{h=b,e(7,h)})}return l.$$set=b=>{"choices"in b&&e(0,f=b.choices),"filtered_indices"in b&&e(1,_=b.filtered_indices),"show_options"in b&&e(2,u=b.show_options),"disabled"in b&&e(3,r=b.disabled),"selected_indices"in b&&e(4,n=b.selected_indices),"active_index"in b&&e(5,s=b.active_index),"remember_scroll"in b&&e(16,w=b.remember_scroll)},l.$$.update=()=>{if(l.$$.dirty&983252){if(u&&C){if(w)g();else if(h&&n.length>0){let F=h.querySelectorAll("li");for(const V of Array.from(F))if(V.getAttribute("data-index")===n[0].toString()){h?.scrollTo?.(0,V.offsetTop);break}}z();const b=C.parentElement?.getBoundingClientRect();e(19,p=b?.height||0),e(8,y=b?.width||0)}d>o?(e(9,i=`${o}px`),e(11,k=d),e(10,m=null)):(e(10,m=`${d+p}px`),e(11,k=o-p),e(9,i=null))}},[f,_,u,r,n,s,C,h,y,i,m,k,a,B,A,T,w,o,d,p,U,Q,Z,j,v]}class Ie extends Ee{constructor(t){super(),Ne(this,t,Re,Le,ze,{choices:0,filtered_indices:1,show_options:2,disabled:3,selected_indices:4,active_index:5,remember_scroll:16})}get choices(){return this.$$.ctx[0]}set choices(t){this.$$set({choices:t}),H()}get filtered_indices(){return this.$$.ctx[1]}set filtered_indices(t){this.$$set({filtered_indices:t}),H()}get show_options(){return this.$$.ctx[2]}set show_options(t){this.$$set({show_options:t}),H()}get disabled(){return this.$$.ctx[3]}set disabled(t){this.$$set({disabled:t}),H()}get selected_indices(){return this.$$.ctx[4]}set selected_indices(t){this.$$set({selected_indices:t}),H()}get active_index(){return this.$$.ctx[5]}set active_index(t){this.$$set({active_index:t}),H()}get remember_scroll(){return this.$$.ctx[16]}set remember_scroll(t){this.$$set({remember_scroll:t}),H()}}function je(l,t){return(l%t+t)%t}function de(l,t){return l.reduce((e,f,_)=>((!t||f[0].toLowerCase().includes(t.toLowerCase()))&&e.push(_),e),[])}function Fe(l,t,e){l("change",t),e||l("input")}function Ge(l,t,e){if(l.key==="Escape")return[!1,t];if((l.key==="ArrowDown"||l.key==="ArrowUp")&&e.length>0)if(t===null)t=l.key==="ArrowDown"?e[0]:e[e.length-1];else{const f=e.indexOf(t),_=l.key==="ArrowUp"?-1:1;t=e[je(f+_,e.length)]}return[!0,t]}const{SvelteComponent:Ke,append:J,attr:D,binding_callbacks:Me,check_outros:Pe,create_component:te,destroy_component:le,detach:se,element:R,flush:S,group_outros:Qe,init:Ve,insert:oe,listen:G,mount_component:ne,run_all:We,safe_not_equal:Xe,set_data:Ye,set_input_value:me,space:$,text:Ze,toggle_class:L,transition_in:I,transition_out:K}=window.__gradio__svelte__internal,{createEventDispatcher:ve,afterUpdate:xe}=window.__gradio__svelte__internal;function $e(l){let t;return{c(){t=Ze(l[0])},m(e,f){oe(e,t,f)},p(e,f){f[0]&1&&Ye(t,e[0])},d(e){e&&se(t)}}}function be(l){let t,e,f;return e=new De({}),{c(){t=R("div"),te(e.$$.fragment),D(t,"class","icon-wrap svelte-1hfxrpf")},m(_,u){oe(_,t,u),ne(e,t,null),f=!0},i(_){f||(I(e.$$.fragment,_),f=!0)},o(_){K(e.$$.fragment,_),f=!1},d(_){_&&se(t),le(e)}}}function et(l){let t,e,f,_,u,r,n,s,w,o,d,p,y,C;e=new Ae({props:{show_label:l[4],info:l[1],$$slots:{default:[$e]},$$scope:{ctx:l}}});let h=!l[3]&&be();return d=new Ie({props:{show_options:l[12],choices:l[2],filtered_indices:l[10],disabled:l[3],selected_indices:l[11]===null?[]:[l[11]],active_index:l[14]}}),d.$on("change",l[16]),{c(){t=R("div"),te(e.$$.fragment),f=$(),_=R("div"),u=R("div"),r=R("div"),n=R("input"),w=$(),h&&h.c(),o=$(),te(d.$$.fragment),D(n,"role","listbox"),D(n,"aria-controls","dropdown-options"),D(n,"aria-expanded",l[12]),D(n,"aria-label",l[0]),D(n,"class","border-none svelte-1hfxrpf"),n.disabled=l[3],D(n,"autocomplete","off"),n.readOnly=s=!l[7],L(n,"subdued",!l[13].includes(l[9])&&!l[6]),D(r,"class","secondary-wrap svelte-1hfxrpf"),D(u,"class","wrap-inner svelte-1hfxrpf"),L(u,"show_options",l[12]),D(_,"class","wrap svelte-1hfxrpf"),D(t,"class","svelte-1hfxrpf"),L(t,"container",l[5])},m(i,m){oe(i,t,m),ne(e,t,null),J(t,f),J(t,_),J(_,u),J(u,r),J(r,n),me(n,l[9]),l[29](n),J(r,w),h&&h.m(r,null),J(_,o),ne(d,_,null),p=!0,y||(C=[G(n,"input",l[28]),G(n,"keydown",l[19]),G(n,"keyup",l[30]),G(n,"blur",l[18]),G(n,"focus",l[17])],y=!0)},p(i,m){const k={};m[0]&16&&(k.show_label=i[4]),m[0]&2&&(k.info=i[1]),m[0]&1|m[1]&8&&(k.$$scope={dirty:m,ctx:i}),e.$set(k),(!p||m[0]&4096)&&D(n,"aria-expanded",i[12]),(!p||m[0]&1)&&D(n,"aria-label",i[0]),(!p||m[0]&8)&&(n.disabled=i[3]),(!p||m[0]&128&&s!==(s=!i[7]))&&(n.readOnly=s),m[0]&512&&n.value!==i[9]&&me(n,i[9]),(!p||m[0]&8768)&&L(n,"subdued",!i[13].includes(i[9])&&!i[6]),i[3]?h&&(Qe(),K(h,1,1,()=>{h=null}),Pe()):h?m[0]&8&&I(h,1):(h=be(),h.c(),I(h,1),h.m(r,null)),(!p||m[0]&4096)&&L(u,"show_options",i[12]);const a={};m[0]&4096&&(a.show_options=i[12]),m[0]&4&&(a.choices=i[2]),m[0]&1024&&(a.filtered_indices=i[10]),m[0]&8&&(a.disabled=i[3]),m[0]&2048&&(a.selected_indices=i[11]===null?[]:[i[11]]),m[0]&16384&&(a.active_index=i[14]),d.$set(a),(!p||m[0]&32)&&L(t,"container",i[5])},i(i){p||(I(e.$$.fragment,i),I(h),I(d.$$.fragment,i),p=!0)},o(i){K(e.$$.fragment,i),K(h),K(d.$$.fragment,i),p=!1},d(i){i&&se(t),le(e),l[29](null),h&&h.d(),le(d),y=!1,We(C)}}}function tt(l,t,e){let{label:f}=t,{info:_=void 0}=t,{value:u=void 0}=t,r,{value_is_output:n=!1}=t,{choices:s}=t,w,{disabled:o=!1}=t,{show_label:d}=t,{container:p=!0}=t,{allow_custom_value:y=!1}=t,{filterable:C=!0}=t,h,i=!1,m,k,a="",B="",z=!1,O=[],A=null,g=null,T;const U=ve();u&&(T=s.map(c=>c[1]).indexOf(u),g=T,g===-1?(r=u,g=null):([a,r]=s[g],B=a),j());function Q(){e(13,m=s.map(c=>c[0])),e(24,k=s.map(c=>c[1]))}const Z=typeof window<"u";function j(){Q(),u===void 0||Array.isArray(u)&&u.length===0?(e(9,a=""),e(11,g=null)):k.includes(u)?(e(9,a=m[k.indexOf(u)]),e(11,g=k.indexOf(u))):y?(e(9,a=u),e(11,g=null)):(e(9,a=""),e(11,g=null)),e(27,T=g)}function v(c){if(e(11,g=parseInt(c.detail.target.dataset.index)),isNaN(g)){e(11,g=null);return}e(12,i=!1),e(14,A=null),h.blur()}function b(c){e(10,O=s.map((lt,Oe)=>Oe)),e(12,i=!0),U("focus")}function F(){y?e(20,u=a):e(9,a=m[k.indexOf(u)]),e(12,i=!1),e(14,A=null),U("blur")}function V(c){e(12,[i,A]=Ge(c,A,O),i,(e(14,A),e(2,s),e(23,w),e(6,y),e(9,a),e(10,O),e(8,h),e(25,B),e(11,g),e(27,T),e(26,z),e(24,k))),c.key==="Enter"&&(A!==null?(e(11,g=A),e(12,i=!1),h.blur(),e(14,A=null)):m.includes(a)?(e(11,g=m.indexOf(a)),e(12,i=!1),e(14,A=null),h.blur()):y&&(e(20,u=a),e(11,g=null),e(12,i=!1),e(14,A=null),h.blur()))}xe(()=>{e(21,n=!1),e(26,z=!0)});function pe(){a=this.value,e(9,a),e(11,g),e(27,T),e(26,z),e(2,s),e(24,k)}function ke(c){Me[c?"unshift":"push"](()=>{h=c,e(8,h)})}const ye=c=>U("key_up",{key:c.key,input_value:a});return l.$$set=c=>{"label"in c&&e(0,f=c.label),"info"in c&&e(1,_=c.info),"value"in c&&e(20,u=c.value),"value_is_output"in c&&e(21,n=c.value_is_output),"choices"in c&&e(2,s=c.choices),"disabled"in c&&e(3,o=c.disabled),"show_label"in c&&e(4,d=c.show_label),"container"in c&&e(5,p=c.container),"allow_custom_value"in c&&e(6,y=c.allow_custom_value),"filterable"in c&&e(7,C=c.filterable)},l.$$.update=()=>{l.$$.dirty[0]&218105860&&g!==T&&g!==null&&z&&(e(9,[a,u]=s[g],a,(e(20,u),e(11,g),e(27,T),e(26,z),e(2,s),e(24,k))),e(27,T=g),U("select",{index:g,value:k[g],selected:!0})),l.$$.dirty[0]&7340032&&JSON.stringify(r)!==JSON.stringify(u)&&(j(),Fe(U,u,n),e(22,r=u)),l.$$.dirty[0]&4&&Q(),l.$$.dirty[0]&8390468&&s!==w&&(y||j(),e(23,w=s),e(10,O=de(s,a)),!y&&O.length>0&&e(14,A=O[0]),Z&&h===document.activeElement&&e(12,i=!0)),l.$$.dirty[0]&33556036&&a!==B&&(e(10,O=de(s,a)),e(25,B=a),!y&&O.length>0&&e(14,A=O[0]))},[f,_,s,o,d,p,y,C,h,a,O,g,i,m,A,U,v,b,F,V,u,n,r,w,k,B,z,T,pe,ke,ye]}class ft extends Ke{constructor(t){super(),Ve(this,t,tt,et,Xe,{label:0,info:1,value:20,value_is_output:21,choices:2,disabled:3,show_label:4,container:5,allow_custom_value:6,filterable:7},null,[-1,-1])}get label(){return this.$$.ctx[0]}set label(t){this.$$set({label:t}),S()}get info(){return this.$$.ctx[1]}set info(t){this.$$set({info:t}),S()}get value(){return this.$$.ctx[20]}set value(t){this.$$set({value:t}),S()}get value_is_output(){return this.$$.ctx[21]}set value_is_output(t){this.$$set({value_is_output:t}),S()}get choices(){return this.$$.ctx[2]}set choices(t){this.$$set({choices:t}),S()}get disabled(){return this.$$.ctx[3]}set disabled(t){this.$$set({disabled:t}),S()}get show_label(){return this.$$.ctx[4]}set show_label(t){this.$$set({show_label:t}),S()}get container(){return this.$$.ctx[5]}set container(t){this.$$set({container:t}),S()}get allow_custom_value(){return this.$$.ctx[6]}set allow_custom_value(t){this.$$set({allow_custom_value:t}),S()}get filterable(){return this.$$.ctx[7]}set filterable(t){this.$$set({filterable:t}),S()}}export{Ie as D,Fe as a,Ge as b,ft as c,de as h};
//# sourceMappingURL=Dropdown-vf9-p-La.js.map
