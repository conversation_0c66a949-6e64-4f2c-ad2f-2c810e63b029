import{SvelteComponent as te,init as ne,safe_not_equal as ie,svg_element as $,claim_svg_element as ee,children as D,detach as d,attr as h,insert_hydration as C,append_hydration as N,noop as y,space as X,empty as J,claim_space as Y,transition_in as g,group_outros as O,transition_out as w,check_outros as W,create_component as M,claim_component as Z,mount_component as B,destroy_component as T,ensure_array_like as me,element as A,claim_element as j,destroy_each as Pe,text as oe,claim_text as re,set_style as he,listen as Be,set_data as ke,create<PERSON><PERSON><PERSON><PERSON>patcher as sl,onD<PERSON><PERSON> as bt,bubble as Ze,construct_svelte_component as ue,get_spread_update as Ue,get_spread_object as Oe,assign as We,is_function as Mt,toggle_class as K,stop_propagation as Bt,run_all as wt,add_render_callback as Tt,create_bidirectional_transition as _l,binding_callbacks as Ye,null_to_empty as Ge,set_input_value as cl,get_svelte_dataset as kt,src_url_equal as Ke,onMount as il,action_destroyer as At,bind as jt,add_flush_callback as St,tick as qt}from"../../../svelte/svelte.js";import{slide as hl}from"../../../svelte/svelte-submodules.js";import{u as ml,I as de,H as Re,J as Xe,C as Ft,M as fl,r as ye,h as Zt,G as Pt,B as Ut,S as Ot}from"./2.B2AoQPnG.js";import{E as Wt}from"./Edit.jvyxqbov.js";import{U as Rt}from"./Undo.LhwFM5M8.js";import{I as vt}from"./IconButtonWrapper.D5aGR59h.js";import{F as ul}from"./File.Dl9hvYLG.js";import{d as yt}from"./index.CnqicUFC.js";import{C as Jt}from"./Community.i_uzCNAp.js";import{T as Gt}from"./Trash.D4IfxcH_.js";import{M as pt}from"./Music.BVFRDHso.js";import{B as Kt}from"./BlockLabel.BTSz9r5s.js";function Xt(i){let e,l,t;return{c(){e=$("svg"),l=$("path"),t=$("path"),this.h()},l(n){e=ee(n,"svg",{xmlns:!0,"xmlns:xlink":!0,"aria-hidden":!0,role:!0,class:!0,width:!0,height:!0,preserveAspectRatio:!0,viewBox:!0});var a=D(e);l=ee(a,"path",{fill:!0,d:!0}),D(l).forEach(d),t=ee(a,"path",{fill:!0,d:!0}),D(t).forEach(d),a.forEach(d),this.h()},h(){h(l,"fill","currentColor"),h(l,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),h(t,"fill","currentColor"),h(t,"d","M8 10h16v2H8zm0 6h10v2H8z"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),h(e,"aria-hidden","true"),h(e,"role","img"),h(e,"class","iconify iconify--carbon"),h(e,"width","100%"),h(e,"height","100%"),h(e,"preserveAspectRatio","xMidYMid meet"),h(e,"viewBox","0 0 32 32")},m(n,a){C(n,e,a),N(e,l),N(e,t)},p:y,i:y,o:y,d(n){n&&d(e)}}}class Yt extends te{constructor(e){super(),ne(this,e,null,Xt,ie,{})}}function Qt(i){let e,l,t;return{c(){e=$("svg"),l=$("circle"),t=$("path"),this.h()},l(n){e=ee(n,"svg",{class:!0,xmlns:!0,width:!0,height:!0,viewBox:!0});var a=D(e);l=ee(a,"circle",{cx:!0,cy:!0,r:!0,class:!0}),D(l).forEach(d),t=ee(a,"path",{d:!0}),D(t).forEach(d),a.forEach(d),this.h()},h(){h(l,"cx","9"),h(l,"cy","9"),h(l,"r","8"),h(l,"class","circle svelte-1m886t3"),h(t,"d","M5 8l4 4 4-4z"),h(e,"class","dropdown-arrow svelte-1m886t3"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 18 18")},m(n,a){C(n,e,a),N(e,l),N(e,t)},p:y,i:y,o:y,d(n){n&&d(e)}}}class xt extends te{constructor(e){super(),ne(this,e,null,Qt,ie,{})}}function $t(i){let e,l,t,n,a;return{c(){e=$("svg"),l=$("path"),t=$("path"),n=$("path"),a=$("path"),this.h()},l(o){e=ee(o,"svg",{width:!0,height:!0,"stroke-width":!0,viewBox:!0,fill:!0,xmlns:!0,color:!0});var r=D(e);l=ee(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),D(l).forEach(d),t=ee(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),D(t).forEach(d),n=ee(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),D(n).forEach(d),a=ee(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),D(a).forEach(d),r.forEach(d),this.h()},h(){h(l,"d","M19.1679 9C18.0247 6.46819 15.3006 4.5 11.9999 4.5C8.31459 4.5 5.05104 7.44668 4.54932 11"),h(l,"stroke","currentColor"),h(l,"stroke-width","1.5"),h(l,"stroke-linecap","round"),h(l,"stroke-linejoin","round"),h(t,"d","M16 9H19.4C19.7314 9 20 8.73137 20 8.4V5"),h(t,"stroke","currentColor"),h(t,"stroke-width","1.5"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round"),h(n,"d","M4.88146 15C5.92458 17.5318 8.64874 19.5 12.0494 19.5C15.7347 19.5 18.9983 16.5533 19.5 13"),h(n,"stroke","currentColor"),h(n,"stroke-width","1.5"),h(n,"stroke-linecap","round"),h(n,"stroke-linejoin","round"),h(a,"d","M8.04932 15H4.64932C4.31795 15 4.04932 15.2686 4.04932 15.6V19"),h(a,"stroke","currentColor"),h(a,"stroke-width","1.5"),h(a,"stroke-linecap","round"),h(a,"stroke-linejoin","round"),h(e,"width","100%"),h(e,"height","100%"),h(e,"stroke-width","1.5"),h(e,"viewBox","0 0 24 24"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"color","currentColor")},m(o,r){C(o,e,r),N(e,l),N(e,t),N(e,n),N(e,a)},p:y,i:y,o:y,d(o){o&&d(e)}}}class en extends te{constructor(e){super(),ne(this,e,null,$t,ie,{})}}function ln(i){let e,l;return{c(){e=$("svg"),l=$("path"),this.h()},l(t){e=ee(t,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var n=D(e);l=ee(n,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),D(l).forEach(d),n.forEach(d),this.h()},h(){h(l,"d","M12 20L12 4M12 20L7 15M12 20L17 15"),h(l,"stroke","currentColor"),h(l,"stroke-width","2"),h(l,"stroke-linecap","round"),h(l,"stroke-linejoin","round"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 24 24"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(t,n){C(t,e,n),N(e,l)},p:y,i:y,o:y,d(t){t&&d(e)}}}class tn extends te{constructor(e){super(),ne(this,e,null,ln,ie,{})}}const nn=async(i,e=1800)=>{let l=[...i],t=await al(l);if(t.length>e&&l.length>2){const n=l[0],a=l[l.length-1];l=[n,a],t=await al(l)}return t.length>e&&l.length>0&&(l=l.map(a=>{if(a.type==="text"){const o=Math.floor(e/l.length)-20;if(a.content.length>o)return{...a,content:a.content.substring(0,o)+"..."}}return a}),t=await al(l)),t},al=async i=>(await Promise.all(i.map(async l=>{var a;if(l.role==="system")return"";let t=l.role==="user"?"😃":"🤖",n="";if(l.type==="text"){const o={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};n=l.content;for(let[r,s]of Object.entries(o)){let u;for(;(u=s.exec(l.content))!==null;){const c=u[1]||u[2],_=await ml(c);n=n.replace(c,_)}}}else{if(!l.content.value)return"";const o=l.content.component==="video"?(a=l.content.value)==null?void 0:a.video.path:l.content.value,r=await ml(o);l.content.component==="audio"?n=`<audio controls src="${r}"></audio>`:l.content.component==="video"?n=r:l.content.component==="image"&&(n=`<img src="${r}" />`)}return`${t}: ${n}`}))).filter(l=>l!=="").join(`
`),Et=(i,e)=>i.replace('src="/file',`src="${e}file`);function an(i,e){if(!i){const l=e==null?void 0:e.path;if(l){const t=l.toLowerCase();if(t.endsWith(".glb")||t.endsWith(".gltf")||t.endsWith(".obj")||t.endsWith(".stl")||t.endsWith(".splat")||t.endsWith(".ply"))return"model3d"}return"file"}return i.includes("audio")?"audio":i.includes("video")?"video":i.includes("image")?"image":i.includes("model")?"model3d":"file"}function Ct(i){const e=Array.isArray(i.file)?i.file[0]:i.file;return{component:an(e==null?void 0:e.mime_type,e),value:i.file,alt_text:i.alt_text,constructor_args:{},props:{}}}function on(i,e){if(i===null)return i;const l=new Map;return i.map((t,n)=>{let a=typeof t.content=="string"?{role:t.role,metadata:t.metadata,content:Et(t.content,e),type:"text",index:n,options:t.options}:"file"in t.content?{content:Ct(t.content),metadata:t.metadata,role:t.role,type:"component",index:n,options:t.options}:{type:"component",...t};const{id:o,title:r,parent_id:s}=t.metadata||{};if(s){const u=l.get(String(s));if(u){const c={...a,children:[]};return u.children.push(c),o&&r&&l.set(String(o),c),null}}if(o&&r){const u={...a,children:[]};return l.set(String(o),u),u}return a}).filter(t=>t!==null)}function rn(i,e){return i===null?i:i.flatMap((t,n)=>t.map((a,o)=>{if(a==null)return null;const r=o==0?"user":"assistant";return typeof a=="string"?{role:r,type:"text",content:Et(a,e),metadata:{title:null},index:[n,o]}:"file"in a?{content:Ct(a),role:r,type:"component",index:[n,o]}:{role:r,content:a,type:"component",index:[n,o]}})).filter(t=>t!=null)}function dl(i){return i.type==="component"}function Je(i,e){const l=i[i.length-1].role==="assistant",t=i[i.length-1].index;return JSON.stringify(t)===JSON.stringify(e[e.length-1].index)&&l}function sn(i,e,l=!0){const t=[];let n=[],a=null;for(const o of i)if(o.role==="assistant"||o.role==="user"){if(!l){t.push([o]);continue}o.role===a?n.push(o):(n.length>0&&t.push(n),n=[o],a=o.role)}return n.length>0&&t.push(n),t}async function fn(i,e,l){let t=[],n=[];return i.forEach(r=>{if(e[r]||r==="file")return;const s=r==="dataframe"||r==="model3d"?"component":"base",{name:u,component:c}=l(r,s);t.push(u),n.push(c)}),(await Promise.allSettled(n)).map((r,s)=>r.status==="fulfilled"?[s,r.value]:null).filter(r=>r!==null).forEach(([r,s])=>{e[t[r]]=s.default}),e}function un(i){if(!i)return[];let e=new Set;return i.forEach(l=>{l.type==="component"&&e.add(l.content.component)}),Array.from(e)}function rl(i,e=0){var a,o;let l="";const t="  ".repeat(e);(a=i.metadata)!=null&&a.title&&(l+=`${t}${e>0?"- ":""}${i.metadata.title}
`),typeof i.content=="string"&&(l+=`${t}  ${i.content}
`);const n=i;return((o=n.children)==null?void 0:o.length)>0&&(l+=n.children.map(r=>rl(r,e+1)).join("")),l}function _n(i){var e;return Array.isArray(i)?i.map(l=>{var t;return(t=l.metadata)!=null&&t.title?rl(l):l.content}).join(`
`):(e=i.metadata)!=null&&e.title?rl(i):i.content}function gl(i){return Array.isArray(i)&&i.every(e=>typeof e.content=="string")||!Array.isArray(i)&&typeof i.content=="string"}function cn(i){let e,l;return{c(){e=$("svg"),l=$("path"),this.h()},l(t){e=ee(t,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var n=D(e);l=ee(n,"path",{d:!0,fill:!0}),D(l).forEach(d),n.forEach(d),this.h()},h(){h(l,"d","M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z"),h(l,"fill","currentColor"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 12 12"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(t,n){C(t,e,n),N(e,l)},p:y,i:y,o:y,d(t){t&&d(e)}}}class bl extends te{constructor(e){super(),ne(this,e,null,cn,ie,{})}}function hn(i){let e,l;return{c(){e=$("svg"),l=$("path"),this.h()},l(t){e=ee(t,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var n=D(e);l=ee(n,"path",{d:!0,fill:!0}),D(l).forEach(d),n.forEach(d),this.h()},h(){h(l,"d","M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z"),h(l,"fill","currentColor"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 12 12"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(t,n){C(t,e,n),N(e,l)},p:y,i:y,o:y,d(t){t&&d(e)}}}class wl extends te{constructor(e){super(),ne(this,e,null,hn,ie,{})}}function mn(i){let e,l;return{c(){e=$("svg"),l=$("path"),this.h()},l(t){e=ee(t,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var n=D(e);l=ee(n,"path",{d:!0,fill:!0}),D(l).forEach(d),n.forEach(d),this.h()},h(){h(l,"d","M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z"),h(l,"fill","currentColor"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 12 12"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(t,n){C(t,e,n),N(e,l)},p:y,i:y,o:y,d(t){t&&d(e)}}}class kl extends te{constructor(e){super(),ne(this,e,null,mn,ie,{})}}function dn(i){let e,l;return{c(){e=$("svg"),l=$("path"),this.h()},l(t){e=ee(t,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var n=D(e);l=ee(n,"path",{d:!0,fill:!0}),D(l).forEach(d),n.forEach(d),this.h()},h(){h(l,"d","M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z"),h(l,"fill","currentColor"),h(e,"width","100%"),h(e,"height","100%"),h(e,"viewBox","0 0 12 12"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(t,n){C(t,e,n),N(e,l)},p:y,i:y,o:y,d(t){t&&d(e)}}}class vl extends te{constructor(e){super(),ne(this,e,null,dn,ie,{})}}function gn(i){let e,l;return{c(){e=$("svg"),l=$("path"),this.h()},l(t){e=ee(t,"svg",{id:!0,xmlns:!0,viewBox:!0,fill:!0});var n=D(e);l=ee(n,"path",{fill:!0,d:!0}),D(l).forEach(d),n.forEach(d),this.h()},h(){h(l,"fill","currentColor"),h(l,"d","M6,30H4V2H28l-5.8,9L28,20H6ZM6,18H24.33L19.8,11l4.53-7H6Z"),h(e,"id","icon"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"viewBox","0 0 32 32"),h(e,"fill","none")},m(t,n){C(t,e,n),N(e,l)},p:y,i:y,o:y,d(t){t&&d(e)}}}class pl extends te{constructor(e){super(),ne(this,e,null,gn,ie,{})}}function bn(i){let e,l;return{c(){e=$("svg"),l=$("path"),this.h()},l(t){e=ee(t,"svg",{id:!0,xmlns:!0,viewBox:!0,fill:!0});var n=D(e);l=ee(n,"path",{fill:!0,d:!0}),D(l).forEach(d),n.forEach(d),this.h()},h(){h(l,"fill","currentColor"),h(l,"d","M4,2H28l-5.8,9L28,20H6v10H4V2z"),h(e,"id","icon"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"viewBox","0 0 32 32"),h(e,"fill","none")},m(t,n){C(t,e,n),N(e,l)},p:y,i:y,o:y,d(t){t&&d(e)}}}class El extends te{constructor(e){super(),ne(this,e,null,bn,ie,{})}}function Cl(i,e,l){const t=i.slice();return t[9]=e[l],t}function Vl(i){let e=i[3].includes("Dislike"),l,t=i[3].includes("Like"),n,a,o=e&&Il(i),r=t&&Dl(i);return{c(){o&&o.c(),l=X(),r&&r.c(),n=J()},l(s){o&&o.l(s),l=Y(s),r&&r.l(s),n=J()},m(s,u){o&&o.m(s,u),C(s,l,u),r&&r.m(s,u),C(s,n,u),a=!0},p(s,u){u&8&&(e=s[3].includes("Dislike")),e?o?(o.p(s,u),u&8&&g(o,1)):(o=Il(s),o.c(),g(o,1),o.m(l.parentNode,l)):o&&(O(),w(o,1,1,()=>{o=null}),W()),u&8&&(t=s[3].includes("Like")),t?r?(r.p(s,u),u&8&&g(r,1)):(r=Dl(s),r.c(),g(r,1),r.m(n.parentNode,n)):r&&(O(),w(r,1,1,()=>{r=null}),W())},i(s){a||(g(o),g(r),a=!0)},o(s){w(o),w(r),a=!1},d(s){s&&(d(l),d(n)),o&&o.d(s),r&&r.d(s)}}}function Il(i){let e,l;return e=new de({props:{Icon:i[0]==="Dislike"?bl:wl,label:i[0]==="Dislike"?"clicked dislike":i[1]("chatbot.dislike"),color:i[0]==="Dislike"?"var(--color-accent)":"var(--block-label-text-color)"}}),e.$on("click",i[6]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&1&&(a.Icon=t[0]==="Dislike"?bl:wl),n&3&&(a.label=t[0]==="Dislike"?"clicked dislike":t[1]("chatbot.dislike")),n&1&&(a.color=t[0]==="Dislike"?"var(--color-accent)":"var(--block-label-text-color)"),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Dl(i){let e,l;return e=new de({props:{Icon:i[0]==="Like"?kl:vl,label:i[0]==="Like"?"clicked like":i[1]("chatbot.like"),color:i[0]==="Like"?"var(--color-accent)":"var(--block-label-text-color)"}}),e.$on("click",i[7]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&1&&(a.Icon=t[0]==="Like"?kl:vl),n&3&&(a.label=t[0]==="Like"?"clicked like":t[1]("chatbot.like")),n&1&&(a.color=t[0]==="Like"?"var(--color-accent)":"var(--block-label-text-color)"),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function zl(i){let e,l,t,n,a;l=new de({props:{Icon:i[0]&&i[4].includes(i[0])?El:pl,label:"Feedback",color:i[0]&&i[4].includes(i[0])?"var(--color-accent)":"var(--block-label-text-color)"}});let o=me(i[4]),r=[];for(let s=0;s<o.length;s+=1)r[s]=Hl(Cl(i,o,s));return{c(){e=A("div"),M(l.$$.fragment),t=X(),n=A("div");for(let s=0;s<r.length;s+=1)r[s].c();this.h()},l(s){e=j(s,"DIV",{class:!0});var u=D(e);Z(l.$$.fragment,u),t=Y(u),n=j(u,"DIV",{class:!0});var c=D(n);for(let _=0;_<r.length;_+=1)r[_].l(c);c.forEach(d),u.forEach(d),this.h()},h(){h(n,"class","extra-feedback-options svelte-14rmxes"),h(e,"class","extra-feedback no-border svelte-14rmxes")},m(s,u){C(s,e,u),B(l,e,null),N(e,t),N(e,n);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(n,null);a=!0},p(s,u){const c={};if(u&17&&(c.Icon=s[0]&&s[4].includes(s[0])?El:pl),u&17&&(c.color=s[0]&&s[4].includes(s[0])?"var(--color-accent)":"var(--block-label-text-color)"),l.$set(c),u&53){o=me(s[4]);let _;for(_=0;_<o.length;_+=1){const f=Cl(s,o,_);r[_]?r[_].p(f,u):(r[_]=Hl(f),r[_].c(),r[_].m(n,null))}for(;_<r.length;_+=1)r[_].d(1);r.length=o.length}},i(s){a||(g(l.$$.fragment,s),a=!0)},o(s){w(l.$$.fragment,s),a=!1},d(s){s&&d(e),T(l),Pe(r,s)}}}function Hl(i){let e,l=i[9]+"",t,n,a;function o(){return i[8](i[9])}return{c(){e=A("button"),t=oe(l),this.h()},l(r){e=j(r,"BUTTON",{class:!0});var s=D(e);t=re(s,l),s.forEach(d),this.h()},h(){h(e,"class","extra-feedback-option svelte-14rmxes"),he(e,"font-weight",i[0]===i[9]?"bold":"normal")},m(r,s){C(r,e,s),N(e,t),n||(a=Be(e,"click",o),n=!0)},p(r,s){i=r,s&16&&l!==(l=i[9]+"")&&ke(t,l),s&17&&he(e,"font-weight",i[0]===i[9]?"bold":"normal")},d(r){r&&d(e),n=!1,a()}}}function wn(i){let e=i[3].includes("Like")||i[3].includes("Dislike"),l,t,n,a=e&&Vl(i),o=i[4].length>0&&zl(i);return{c(){a&&a.c(),l=X(),o&&o.c(),t=J()},l(r){a&&a.l(r),l=Y(r),o&&o.l(r),t=J()},m(r,s){a&&a.m(r,s),C(r,l,s),o&&o.m(r,s),C(r,t,s),n=!0},p(r,[s]){s&8&&(e=r[3].includes("Like")||r[3].includes("Dislike")),e?a?(a.p(r,s),s&8&&g(a,1)):(a=Vl(r),a.c(),g(a,1),a.m(l.parentNode,l)):a&&(O(),w(a,1,1,()=>{a=null}),W()),r[4].length>0?o?(o.p(r,s),s&16&&g(o,1)):(o=zl(r),o.c(),g(o,1),o.m(t.parentNode,t)):o&&(O(),w(o,1,1,()=>{o=null}),W())},i(r){n||(g(a),g(o),n=!0)},o(r){w(a),w(o),n=!1},d(r){r&&(d(l),d(t)),a&&a.d(r),o&&o.d(r)}}}function kn(i,e,l){let t,{i18n:n}=e,{handle_action:a}=e,{feedback_options:o}=e,{selected:r=null}=e;function s(f){l(0,r=r===f?null:f),a(r)}const u=()=>s("Dislike"),c=()=>s("Like"),_=f=>{s(f),a(r||null)};return i.$$set=f=>{"i18n"in f&&l(1,n=f.i18n),"handle_action"in f&&l(2,a=f.handle_action),"feedback_options"in f&&l(3,o=f.feedback_options),"selected"in f&&l(0,r=f.selected)},i.$$.update=()=>{i.$$.dirty&8&&l(4,t=o.filter(f=>f!=="Like"&&f!=="Dislike"))},[r,n,a,o,t,s,u,c,_]}class vn extends te{constructor(e){super(),ne(this,e,kn,wn,ie,{i18n:1,handle_action:2,feedback_options:3,selected:0})}}function pn(i){let e,l;return e=new de({props:{label:i[0]?"Copied message":"Copy message",Icon:i[0]?Re:Xe}}),e.$on("click",i[1]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,[n]){const a={};n&1&&(a.label=t[0]?"Copied message":"Copy message"),n&1&&(a.Icon=t[0]?Re:Xe),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function En(i,e,l){const t=sl();let n=!1,{value:a}=e,{watermark:o=null}=e,r;function s(){l(0,n=!0),r&&clearTimeout(r),r=setTimeout(()=>{l(0,n=!1)},2e3)}async function u(){if("clipboard"in navigator){t("copy",{value:a});const c=o?`${a}

${o}`:a;await navigator.clipboard.writeText(c),s()}else{const c=document.createElement("textarea"),_=o?`${a}

${o}`:a;c.value=_,c.style.position="absolute",c.style.left="-999999px",document.body.prepend(c),c.select();try{document.execCommand("copy"),s()}catch(f){console.error(f)}finally{c.remove()}}}return bt(()=>{r&&clearTimeout(r)}),i.$$set=c=>{"value"in c&&l(2,a=c.value),"watermark"in c&&l(3,o=c.watermark)},[n,u,a,o]}class Cn extends te{constructor(e){super(),ne(this,e,En,pn,ie,{value:2,watermark:3})}}function Ll(i){let e,l,t,n;return l=new vt({props:{top_panel:!1,$$slots:{default:[Dn]},$$scope:{ctx:i}}}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(a){e=j(a,"DIV",{class:!0});var o=D(e);Z(l.$$.fragment,o),o.forEach(d),this.h()},h(){h(e,"class",t="message-buttons-"+i[8]+" "+i[13]+" message-buttons "+(i[9]!==null&&"with-avatar")+" svelte-j7nkv7")},m(a,o){C(a,e,o),B(l,e,null),n=!0},p(a,o){const r={};o&33676543&&(r.$$scope={dirty:o,ctx:a}),l.$set(r),(!n||o&8960&&t!==(t="message-buttons-"+a[8]+" "+a[13]+" message-buttons "+(a[9]!==null&&"with-avatar")+" svelte-j7nkv7"))&&h(e,"class",t)},i(a){n||(g(l.$$.fragment,a),n=!0)},o(a){w(l.$$.fragment,a),n=!1},d(a){a&&d(e),T(l)}}}function Vn(i){let e,l,t,n,a,o,r=i[15]&&Nl(i),s=i[3]&&Ml(i),u=i[4]&&Bl(i),c=i[5]&&Tl(i),_=i[1]&&Al(i);return{c(){r&&r.c(),e=X(),s&&s.c(),l=X(),u&&u.c(),t=X(),c&&c.c(),n=X(),_&&_.c(),a=J()},l(f){r&&r.l(f),e=Y(f),s&&s.l(f),l=Y(f),u&&u.l(f),t=Y(f),c&&c.l(f),n=Y(f),_&&_.l(f),a=J()},m(f,m){r&&r.m(f,m),C(f,e,m),s&&s.m(f,m),C(f,l,m),u&&u.m(f,m),C(f,t,m),c&&c.m(f,m),C(f,n,m),_&&_.m(f,m),C(f,a,m),o=!0},p(f,m){f[15]?r?(r.p(f,m),m&32768&&g(r,1)):(r=Nl(f),r.c(),g(r,1),r.m(e.parentNode,e)):r&&(O(),w(r,1,1,()=>{r=null}),W()),f[3]?s?(s.p(f,m),m&8&&g(s,1)):(s=Ml(f),s.c(),g(s,1),s.m(l.parentNode,l)):s&&(O(),w(s,1,1,()=>{s=null}),W()),f[4]?u?(u.p(f,m),m&16&&g(u,1)):(u=Bl(f),u.c(),g(u,1),u.m(t.parentNode,t)):u&&(O(),w(u,1,1,()=>{u=null}),W()),f[5]?c?(c.p(f,m),m&32&&g(c,1)):(c=Tl(f),c.c(),g(c,1),c.m(n.parentNode,n)):c&&(O(),w(c,1,1,()=>{c=null}),W()),f[1]?_?(_.p(f,m),m&2&&g(_,1)):(_=Al(f),_.c(),g(_,1),_.m(a.parentNode,a)):_&&(O(),w(_,1,1,()=>{_=null}),W())},i(f){o||(g(r),g(s),g(u),g(c),g(_),o=!0)},o(f){w(r),w(s),w(u),w(c),w(_),o=!1},d(f){f&&(d(e),d(l),d(t),d(n),d(a)),r&&r.d(f),s&&s.d(f),u&&u.d(f),c&&c.d(f),_&&_.d(f)}}}function In(i){let e,l,t,n;return e=new de({props:{label:i[0]("chatbot.submit"),Icon:Re,disabled:i[10]}}),e.$on("click",i[19]),t=new de({props:{label:i[0]("chatbot.cancel"),Icon:Ft,disabled:i[10]}}),t.$on("click",i[20]),{c(){M(e.$$.fragment),l=X(),M(t.$$.fragment)},l(a){Z(e.$$.fragment,a),l=Y(a),Z(t.$$.fragment,a)},m(a,o){B(e,a,o),C(a,l,o),B(t,a,o),n=!0},p(a,o){const r={};o&1&&(r.label=a[0]("chatbot.submit")),o&1024&&(r.disabled=a[10]),e.$set(r);const s={};o&1&&(s.label=a[0]("chatbot.cancel")),o&1024&&(s.disabled=a[10]),t.$set(s)},i(a){n||(g(e.$$.fragment,a),g(t.$$.fragment,a),n=!0)},o(a){w(e.$$.fragment,a),w(t.$$.fragment,a),n=!1},d(a){a&&d(l),T(e,a),T(t,a)}}}function Nl(i){let e,l;return e=new Cn({props:{value:i[16],watermark:i[7]}}),e.$on("copy",i[21]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&65536&&(a.value=t[16]),n&128&&(a.watermark=t[7]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Ml(i){let e,l;return e=new de({props:{Icon:en,label:i[0]("chatbot.retry"),disabled:i[10]}}),e.$on("click",i[22]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&1&&(a.label=t[0]("chatbot.retry")),n&1024&&(a.disabled=t[10]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Bl(i){let e,l;return e=new de({props:{label:i[0]("chatbot.undo"),Icon:Rt,disabled:i[10]}}),e.$on("click",i[23]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&1&&(a.label=t[0]("chatbot.undo")),n&1024&&(a.disabled=t[10]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Tl(i){let e,l;return e=new de({props:{label:i[0]("chatbot.edit"),Icon:Wt,disabled:i[10]}}),e.$on("click",i[24]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&1&&(a.label=t[0]("chatbot.edit")),n&1024&&(a.disabled=t[10]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Al(i){let e,l;return e=new vn({props:{handle_action:i[12],feedback_options:i[2],selected:i[11],i18n:i[0]}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&4096&&(a.handle_action=t[12]),n&4&&(a.feedback_options=t[2]),n&2048&&(a.selected=t[11]),n&1&&(a.i18n=t[0]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Dn(i){let e,l,t,n;const a=[In,Vn],o=[];function r(s,u){return s[6]?0:1}return e=r(i),l=o[e]=a[e](i),{c(){l.c(),t=J()},l(s){l.l(s),t=J()},m(s,u){o[e].m(s,u),C(s,t,u),n=!0},p(s,u){let c=e;e=r(s),e===c?o[e].p(s,u):(O(),w(o[c],1,1,()=>{o[c]=null}),W(),l=o[e],l?l.p(s,u):(l=o[e]=a[e](s),l.c()),g(l,1),l.m(t.parentNode,t))},i(s){n||(g(l),n=!0)},o(s){w(l),n=!1},d(s){s&&d(t),o[e].d(s)}}}function zn(i){let e,l,t=(i[15]||i[3]||i[4]||i[5]||i[1])&&Ll(i);return{c(){t&&t.c(),e=J()},l(n){t&&t.l(n),e=J()},m(n,a){t&&t.m(n,a),C(n,e,a),l=!0},p(n,[a]){n[15]||n[3]||n[4]||n[5]||n[1]?t?(t.p(n,a),a&32826&&g(t,1)):(t=Ll(n),t.c(),g(t,1),t.m(e.parentNode,e)):t&&(O(),w(t,1,1,()=>{t=null}),W())},i(n){l||(g(t),l=!0)},o(n){w(t),l=!1},d(n){n&&d(e),t&&t.d(n)}}}function Hn(i,e,l){let t,n,{i18n:a}=e,{likeable:o}=e,{feedback_options:r}=e,{show_retry:s}=e,{show_undo:u}=e,{show_edit:c}=e,{in_edit_mode:_}=e,{show_copy_button:f}=e,{watermark:m=null}=e,{message:b}=e,{position:k}=e,{avatar:p}=e,{generating:I}=e,{current_feedback:U}=e,{handle_action:q}=e,{layout:H}=e,{dispatch:F}=e;const z=()=>q("edit_submit"),L=()=>q("edit_cancel"),R=P=>F("copy",P.detail),G=()=>q("retry"),x=()=>q("undo"),le=()=>q("edit");return i.$$set=P=>{"i18n"in P&&l(0,a=P.i18n),"likeable"in P&&l(1,o=P.likeable),"feedback_options"in P&&l(2,r=P.feedback_options),"show_retry"in P&&l(3,s=P.show_retry),"show_undo"in P&&l(4,u=P.show_undo),"show_edit"in P&&l(5,c=P.show_edit),"in_edit_mode"in P&&l(6,_=P.in_edit_mode),"show_copy_button"in P&&l(17,f=P.show_copy_button),"watermark"in P&&l(7,m=P.watermark),"message"in P&&l(18,b=P.message),"position"in P&&l(8,k=P.position),"avatar"in P&&l(9,p=P.avatar),"generating"in P&&l(10,I=P.generating),"current_feedback"in P&&l(11,U=P.current_feedback),"handle_action"in P&&l(12,q=P.handle_action),"layout"in P&&l(13,H=P.layout),"dispatch"in P&&l(14,F=P.dispatch)},i.$$.update=()=>{i.$$.dirty&262144&&l(16,t=gl(b)?_n(b):""),i.$$.dirty&393216&&l(15,n=f&&b&&gl(b))},[a,o,r,s,u,c,_,m,k,p,I,U,q,H,F,n,t,f,b,z,L,R,G,x,le]}class Vt extends te{constructor(e){super(),ne(this,e,Hn,zn,ie,{i18n:0,likeable:1,feedback_options:2,show_retry:3,show_undo:4,show_edit:5,in_edit_mode:6,show_copy_button:17,watermark:7,message:18,position:8,avatar:9,generating:10,current_feedback:11,handle_action:12,layout:13,dispatch:14})}}function Ln(i){let e,l,t;const n=[{value:i[2]},{clear_color:i[5].clear_color},{display_mode:i[5].display_mode},{zoom_speed:i[5].zoom_speed},{pan_speed:i[5].pan_speed},i[5].camera_position!==void 0&&{camera_position:i[5].camera_position},{has_change_history:!0},{show_label:!1},{root:""},{interactive:!1},{label:"chatbot-model3d"},{show_share_button:!0},{gradio:{dispatch:ol,i18n:i[6]}}];var a=i[1][i[0]];function o(r,s){let u={};for(let c=0;c<n.length;c+=1)u=We(u,n[c]);return s!==void 0&&s&100&&(u=We(u,Ue(n,[s&4&&{value:r[2]},s&32&&{clear_color:r[5].clear_color},s&32&&{display_mode:r[5].display_mode},s&32&&{zoom_speed:r[5].zoom_speed},s&32&&{pan_speed:r[5].pan_speed},s&32&&Oe(r[5].camera_position!==void 0&&{camera_position:r[5].camera_position}),n[6],n[7],n[8],n[9],n[10],n[11],s&64&&{gradio:{dispatch:ol,i18n:r[6]}}]))),{props:u}}return a&&(e=ue(a,o(i)),e.$on("load",i[18])),{c(){e&&M(e.$$.fragment),l=J()},l(r){e&&Z(e.$$.fragment,r),l=J()},m(r,s){e&&B(e,r,s),C(r,l,s),t=!0},p(r,s){if(s&3&&a!==(a=r[1][r[0]])){if(e){O();const u=e;w(u.$$.fragment,1,0,()=>{T(u,1)}),W()}a?(e=ue(a,o(r,s)),e.$on("load",r[18]),M(e.$$.fragment),g(e.$$.fragment,1),B(e,l.parentNode,l)):e=null}else if(a){const u=s&100?Ue(n,[s&4&&{value:r[2]},s&32&&{clear_color:r[5].clear_color},s&32&&{display_mode:r[5].display_mode},s&32&&{zoom_speed:r[5].zoom_speed},s&32&&{pan_speed:r[5].pan_speed},s&32&&Oe(r[5].camera_position!==void 0&&{camera_position:r[5].camera_position}),n[6],n[7],n[8],n[9],n[10],n[11],s&64&&{gradio:{dispatch:ol,i18n:r[6]}}]):{};e.$set(u)}},i(r){t||(e&&g(e.$$.fragment,r),t=!0)},o(r){e&&w(e.$$.fragment,r),t=!1},d(r){r&&d(l),e&&T(e,r)}}}function Nn(i){let e,l,t;var n=i[1][i[0]];function a(o,r){return{props:{value:o[2],show_label:!1,label:"chatbot-html",show_share_button:!0,i18n:o[6],gradio:{dispatch:Zn}}}}return n&&(e=ue(n,a(i)),e.$on("load",i[17])),{c(){e&&M(e.$$.fragment),l=J()},l(o){e&&Z(e.$$.fragment,o),l=J()},m(o,r){e&&B(e,o,r),C(o,l,r),t=!0},p(o,r){if(r&3&&n!==(n=o[1][o[0]])){if(e){O();const s=e;w(s.$$.fragment,1,0,()=>{T(s,1)}),W()}n?(e=ue(n,a(o)),e.$on("load",o[17]),M(e.$$.fragment),g(e.$$.fragment,1),B(e,l.parentNode,l)):e=null}else if(n){const s={};r&4&&(s.value=o[2]),r&64&&(s.i18n=o[6]),e.$set(s)}},i(o){t||(e&&g(e.$$.fragment,o),t=!0)},o(o){e&&w(e.$$.fragment,o),t=!1},d(o){o&&d(l),e&&T(e,o)}}}function Mn(i){let e,l,t;var n=i[1][i[0]];function a(o,r){return{props:{value:o[2],show_label:!1,label:"chatbot-image",show_download_button:o[9],display_icon_button_wrapper_top_corner:o[10],i18n:o[6]}}}return n&&(e=ue(n,a(i)),e.$on("load",i[16])),{c(){e&&M(e.$$.fragment),l=J()},l(o){e&&Z(e.$$.fragment,o),l=J()},m(o,r){e&&B(e,o,r),C(o,l,r),t=!0},p(o,r){if(r&3&&n!==(n=o[1][o[0]])){if(e){O();const s=e;w(s.$$.fragment,1,0,()=>{T(s,1)}),W()}n?(e=ue(n,a(o)),e.$on("load",o[16]),M(e.$$.fragment),g(e.$$.fragment,1),B(e,l.parentNode,l)):e=null}else if(n){const s={};r&4&&(s.value=o[2]),r&512&&(s.show_download_button=o[9]),r&1024&&(s.display_icon_button_wrapper_top_corner=o[10]),r&64&&(s.i18n=o[6]),e.$set(s)}},i(o){t||(e&&g(e.$$.fragment,o),t=!0)},o(o){e&&w(e.$$.fragment,o),t=!1},d(o){o&&d(l),e&&T(e,o)}}}function Bn(i){let e,l,t;var n=i[1][i[0]];function a(o,r){return{props:{autoplay:o[5].autoplay,value:o[2].video||o[2],show_label:!1,show_share_button:!0,i18n:o[6],upload:o[7],display_icon_button_wrapper_top_corner:o[10],show_download_button:o[9],$$slots:{default:[qn]},$$scope:{ctx:o}}}}return n&&(e=ue(n,a(i)),e.$on("load",i[15])),{c(){e&&M(e.$$.fragment),l=J()},l(o){e&&Z(e.$$.fragment,o),l=J()},m(o,r){e&&B(e,o,r),C(o,l,r),t=!0},p(o,r){if(r&3&&n!==(n=o[1][o[0]])){if(e){O();const s=e;w(s.$$.fragment,1,0,()=>{T(s,1)}),W()}n?(e=ue(n,a(o)),e.$on("load",o[15]),M(e.$$.fragment),g(e.$$.fragment,1),B(e,l.parentNode,l)):e=null}else if(n){const s={};r&32&&(s.autoplay=o[5].autoplay),r&4&&(s.value=o[2].video||o[2]),r&64&&(s.i18n=o[6]),r&128&&(s.upload=o[7]),r&1024&&(s.display_icon_button_wrapper_top_corner=o[10]),r&512&&(s.show_download_button=o[9]),r&524288&&(s.$$scope={dirty:r,ctx:o}),e.$set(s)}},i(o){t||(e&&g(e.$$.fragment,o),t=!0)},o(o){e&&w(e.$$.fragment,o),t=!1},d(o){o&&d(l),e&&T(e,o)}}}function Tn(i){let e,l,t;var n=i[1][i[0]];function a(o,r){return{props:{value:o[2],show_label:!1,show_share_button:!0,i18n:o[6],label:"",waveform_settings:{autoplay:o[5].autoplay},show_download_button:o[9],display_icon_button_wrapper_top_corner:o[10]}}}return n&&(l=ue(n,a(i)),l.$on("load",i[14])),{c(){e=A("div"),l&&M(l.$$.fragment),this.h()},l(o){e=j(o,"DIV",{style:!0});var r=D(e);l&&Z(l.$$.fragment,r),r.forEach(d),this.h()},h(){he(e,"position","relative")},m(o,r){C(o,e,r),l&&B(l,e,null),t=!0},p(o,r){if(r&3&&n!==(n=o[1][o[0]])){if(l){O();const s=l;w(s.$$.fragment,1,0,()=>{T(s,1)}),W()}n?(l=ue(n,a(o)),l.$on("load",o[14]),M(l.$$.fragment),g(l.$$.fragment,1),B(l,e,null)):l=null}else if(n){const s={};r&4&&(s.value=o[2]),r&64&&(s.i18n=o[6]),r&32&&(s.waveform_settings={autoplay:o[5].autoplay}),r&512&&(s.show_download_button=o[9]),r&1024&&(s.display_icon_button_wrapper_top_corner=o[10]),l.$set(s)}},i(o){t||(l&&g(l.$$.fragment,o),t=!0)},o(o){l&&w(l.$$.fragment,o),t=!1},d(o){o&&d(e),l&&T(l)}}}function An(i){let e,l,t;var n=i[1][i[0]];function a(o,r){return{props:{value:o[2],target:o[3],theme_mode:o[4],bokeh_version:o[5].bokeh_version,caption:"",show_actions_button:!0}}}return n&&(e=ue(n,a(i)),e.$on("load",i[13])),{c(){e&&M(e.$$.fragment),l=J()},l(o){e&&Z(e.$$.fragment,o),l=J()},m(o,r){e&&B(e,o,r),C(o,l,r),t=!0},p(o,r){if(r&3&&n!==(n=o[1][o[0]])){if(e){O();const s=e;w(s.$$.fragment,1,0,()=>{T(s,1)}),W()}n?(e=ue(n,a(o)),e.$on("load",o[13]),M(e.$$.fragment),g(e.$$.fragment,1),B(e,l.parentNode,l)):e=null}else if(n){const s={};r&4&&(s.value=o[2]),r&8&&(s.target=o[3]),r&16&&(s.theme_mode=o[4]),r&32&&(s.bokeh_version=o[5].bokeh_version),e.$set(s)}},i(o){t||(e&&g(e.$$.fragment,o),t=!0)},o(o){e&&w(e.$$.fragment,o),t=!1},d(o){o&&d(l),e&&T(e,o)}}}function jn(i){let e,l,t;var n=i[1][i[0]];function a(o,r){return{props:{value:o[2],show_label:!1,i18n:o[6],label:"",interactive:!1,line_breaks:o[5].line_breaks,wrap:!0,root:"",gradio:{dispatch:jl,i18n:o[6]},datatype:o[5].datatype,latex_delimiters:o[5].latex_delimiters,col_count:o[5].col_count,row_count:o[5].row_count}}}return n&&(e=ue(n,a(i)),e.$on("load",i[12])),{c(){e&&M(e.$$.fragment),l=J()},l(o){e&&Z(e.$$.fragment,o),l=J()},m(o,r){e&&B(e,o,r),C(o,l,r),t=!0},p(o,r){if(r&3&&n!==(n=o[1][o[0]])){if(e){O();const s=e;w(s.$$.fragment,1,0,()=>{T(s,1)}),W()}n?(e=ue(n,a(o)),e.$on("load",o[12]),M(e.$$.fragment),g(e.$$.fragment,1),B(e,l.parentNode,l)):e=null}else if(n){const s={};r&4&&(s.value=o[2]),r&64&&(s.i18n=o[6]),r&32&&(s.line_breaks=o[5].line_breaks),r&64&&(s.gradio={dispatch:jl,i18n:o[6]}),r&32&&(s.datatype=o[5].datatype),r&32&&(s.latex_delimiters=o[5].latex_delimiters),r&32&&(s.col_count=o[5].col_count),r&32&&(s.row_count=o[5].row_count),e.$set(s)}},i(o){t||(e&&g(e.$$.fragment,o),t=!0)},o(o){e&&w(e.$$.fragment,o),t=!1},d(o){o&&d(l),e&&T(e,o)}}}function Sn(i){let e,l,t;var n=i[1][i[0]];function a(o,r){return{props:{value:o[2],display_icon_button_wrapper_top_corner:o[10],show_label:!1,i18n:o[6],label:"",_fetch:o[8],allow_preview:!1,interactive:!1,mode:"minimal",fixed_height:1}}}return n&&(e=ue(n,a(i)),e.$on("load",i[11])),{c(){e&&M(e.$$.fragment),l=J()},l(o){e&&Z(e.$$.fragment,o),l=J()},m(o,r){e&&B(e,o,r),C(o,l,r),t=!0},p(o,r){if(r&3&&n!==(n=o[1][o[0]])){if(e){O();const s=e;w(s.$$.fragment,1,0,()=>{T(s,1)}),W()}n?(e=ue(n,a(o)),e.$on("load",o[11]),M(e.$$.fragment),g(e.$$.fragment,1),B(e,l.parentNode,l)):e=null}else if(n){const s={};r&4&&(s.value=o[2]),r&1024&&(s.display_icon_button_wrapper_top_corner=o[10]),r&64&&(s.i18n=o[6]),r&256&&(s._fetch=o[8]),e.$set(s)}},i(o){t||(e&&g(e.$$.fragment,o),t=!0)},o(o){e&&w(e.$$.fragment,o),t=!1},d(o){o&&d(l),e&&T(e,o)}}}function qn(i){let e;return{c(){e=A("track"),this.h()},l(l){e=j(l,"TRACK",{kind:!0}),this.h()},h(){h(e,"kind","captions")},m(l,t){C(l,e,t)},p:y,d(l){l&&d(e)}}}function Fn(i){let e,l,t,n;const a=[Sn,jn,An,Tn,Bn,Mn,Nn,Ln],o=[];function r(s,u){return s[0]==="gallery"?0:s[0]==="dataframe"?1:s[0]==="plot"?2:s[0]==="audio"?3:s[0]==="video"?4:s[0]==="image"?5:s[0]==="html"?6:s[0]==="model3d"?7:-1}return~(e=r(i))&&(l=o[e]=a[e](i)),{c(){l&&l.c(),t=J()},l(s){l&&l.l(s),t=J()},m(s,u){~e&&o[e].m(s,u),C(s,t,u),n=!0},p(s,[u]){let c=e;e=r(s),e===c?~e&&o[e].p(s,u):(l&&(O(),w(o[c],1,1,()=>{o[c]=null}),W()),~e?(l=o[e],l?l.p(s,u):(l=o[e]=a[e](s),l.c()),g(l,1),l.m(t.parentNode,t)):l=null)},i(s){n||(g(l),n=!0)},o(s){w(l),n=!1},d(s){s&&d(t),~e&&o[e].d(s)}}}const jl=()=>{},Zn=()=>{},ol=()=>{};function Pn(i,e,l){let{type:t}=e,{components:n}=e,{value:a}=e,{target:o}=e,{theme_mode:r}=e,{props:s}=e,{i18n:u}=e,{upload:c}=e,{_fetch:_}=e,{allow_file_downloads:f}=e,{display_icon_button_wrapper_top_corner:m=!1}=e;function b(z){Ze.call(this,i,z)}function k(z){Ze.call(this,i,z)}function p(z){Ze.call(this,i,z)}function I(z){Ze.call(this,i,z)}function U(z){Ze.call(this,i,z)}function q(z){Ze.call(this,i,z)}function H(z){Ze.call(this,i,z)}function F(z){Ze.call(this,i,z)}return i.$$set=z=>{"type"in z&&l(0,t=z.type),"components"in z&&l(1,n=z.components),"value"in z&&l(2,a=z.value),"target"in z&&l(3,o=z.target),"theme_mode"in z&&l(4,r=z.theme_mode),"props"in z&&l(5,s=z.props),"i18n"in z&&l(6,u=z.i18n),"upload"in z&&l(7,c=z.upload),"_fetch"in z&&l(8,_=z._fetch),"allow_file_downloads"in z&&l(9,f=z.allow_file_downloads),"display_icon_button_wrapper_top_corner"in z&&l(10,m=z.display_icon_button_wrapper_top_corner)},[t,n,a,o,r,s,u,c,_,f,m,b,k,p,I,U,q,H,F]}class Un extends te{constructor(e){super(),ne(this,e,Pn,Fn,ie,{type:0,components:1,value:2,target:3,theme_mode:4,props:5,i18n:6,upload:7,_fetch:8,allow_file_downloads:9,display_icon_button_wrapper_top_corner:10})}}function On(i){var I,U,q,H;let e,l,t,n,a,o,r,s=(((I=i[15].content.value)==null?void 0:I.orig_name)||((U=i[15].content.value)==null?void 0:U.path.split("/").pop())||"file")+"",u,c,_,f,m,b=(((q=i[15].content.value)==null?void 0:q.orig_name)||((H=i[15].content.value)==null?void 0:H.path)||"").split(".").pop().toUpperCase()+"",k,p;return t=new ul({}),{c(){e=A("div"),l=A("div"),M(t.$$.fragment),n=X(),a=A("div"),o=A("a"),r=A("span"),u=oe(s),f=X(),m=A("span"),k=oe(b),this.h()},l(F){e=j(F,"DIV",{class:!0});var z=D(e);l=j(z,"DIV",{class:!0});var L=D(l);Z(t.$$.fragment,L),L.forEach(d),n=Y(z),a=j(z,"DIV",{class:!0});var R=D(a);o=j(R,"A",{"data-testid":!0,class:!0,href:!0,target:!0,download:!0});var G=D(o);r=j(G,"SPAN",{class:!0});var x=D(r);u=re(x,s),x.forEach(d),G.forEach(d),f=Y(R),m=j(R,"SPAN",{class:!0});var le=D(m);k=re(le,b),le.forEach(d),R.forEach(d),z.forEach(d),this.h()},h(){var F,z;h(l,"class","file-icon svelte-ulpe0d"),h(r,"class","file-name svelte-ulpe0d"),h(o,"data-testid","chatbot-file"),h(o,"class","file-link svelte-ulpe0d"),h(o,"href",c=i[15].content.value.url),h(o,"target","_blank"),h(o,"download",_=window.__is_colab__?null:((F=i[15].content.value)==null?void 0:F.orig_name)||((z=i[15].content.value)==null?void 0:z.path.split("/").pop())||"file"),h(m,"class","file-type svelte-ulpe0d"),h(a,"class","file-info svelte-ulpe0d"),h(e,"class","file-container svelte-ulpe0d")},m(F,z){C(F,e,z),N(e,l),B(t,l,null),N(e,n),N(e,a),N(a,o),N(o,r),N(r,u),N(a,f),N(a,m),N(m,k),p=!0},p(F,z){var L,R,G,x,le,P;(!p||z&32768)&&s!==(s=(((L=F[15].content.value)==null?void 0:L.orig_name)||((R=F[15].content.value)==null?void 0:R.path.split("/").pop())||"file")+"")&&ke(u,s),(!p||z&32768&&c!==(c=F[15].content.value.url))&&h(o,"href",c),(!p||z&32768&&_!==(_=window.__is_colab__?null:((G=F[15].content.value)==null?void 0:G.orig_name)||((x=F[15].content.value)==null?void 0:x.path.split("/").pop())||"file"))&&h(o,"download",_),(!p||z&32768)&&b!==(b=(((le=F[15].content.value)==null?void 0:le.orig_name)||((P=F[15].content.value)==null?void 0:P.path)||"").split(".").pop().toUpperCase()+"")&&ke(k,b)},i(F){p||(g(t.$$.fragment,F),p=!0)},o(F){w(t.$$.fragment,F),p=!1},d(F){F&&d(e),T(t)}}}function Wn(i){let e,l;return e=new Un({props:{target:i[6],theme_mode:i[7],props:i[15].content.props,type:i[15].content.component,components:i[8],value:i[15].content.value,display_icon_button_wrapper_top_corner:i[13]>0&&i[12],i18n:i[3],upload:i[5],_fetch:i[2],allow_file_downloads:i[11]}}),e.$on("load",i[16]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&64&&(a.target=t[6]),n&128&&(a.theme_mode=t[7]),n&32768&&(a.props=t[15].content.props),n&32768&&(a.type=t[15].content.component),n&256&&(a.components=t[8]),n&32768&&(a.value=t[15].content.value),n&12288&&(a.display_icon_button_wrapper_top_corner=t[13]>0&&t[12]),n&8&&(a.i18n=t[3]),n&32&&(a.upload=t[5]),n&4&&(a._fetch=t[2]),n&2048&&(a.allow_file_downloads=t[11]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Rn(i){let e,l,t;return l=new fl({props:{message:i[15].content,latex_delimiters:i[0],sanitize_html:i[1],render_markdown:i[9],line_breaks:i[4],allow_tags:i[14],theme_mode:i[7]}}),l.$on("load",function(){Mt(i[10])&&i[10].apply(this,arguments)}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(n){e=j(n,"DIV",{class:!0});var a=D(e);Z(l.$$.fragment,a),a.forEach(d),this.h()},h(){h(e,"class","message-content")},m(n,a){C(n,e,a),B(l,e,null),t=!0},p(n,a){i=n;const o={};a&32768&&(o.message=i[15].content),a&1&&(o.latex_delimiters=i[0]),a&2&&(o.sanitize_html=i[1]),a&512&&(o.render_markdown=i[9]),a&16&&(o.line_breaks=i[4]),a&16384&&(o.allow_tags=i[14]),a&128&&(o.theme_mode=i[7]),l.$set(o)},i(n){t||(g(l.$$.fragment,n),t=!0)},o(n){w(l.$$.fragment,n),t=!1},d(n){n&&d(e),T(l)}}}function yn(i){let e,l,t,n;const a=[Rn,Wn,On],o=[];function r(s,u){return s[15].type==="text"?0:s[15].type==="component"&&s[15].content.component in s[8]?1:s[15].type==="component"&&s[15].content.component==="file"?2:-1}return~(e=r(i))&&(l=o[e]=a[e](i)),{c(){l&&l.c(),t=J()},l(s){l&&l.l(s),t=J()},m(s,u){~e&&o[e].m(s,u),C(s,t,u),n=!0},p(s,[u]){let c=e;e=r(s),e===c?~e&&o[e].p(s,u):(l&&(O(),w(o[c],1,1,()=>{o[c]=null}),W()),~e?(l=o[e],l?l.p(s,u):(l=o[e]=a[e](s),l.c()),g(l,1),l.m(t.parentNode,t)):l=null)},i(s){n||(g(l),n=!0)},o(s){w(l),n=!1},d(s){s&&d(t),~e&&o[e].d(s)}}}function Jn(i,e,l){let{latex_delimiters:t}=e,{sanitize_html:n}=e,{_fetch:a}=e,{i18n:o}=e,{line_breaks:r}=e,{upload:s}=e,{target:u}=e,{theme_mode:c}=e,{_components:_}=e,{render_markdown:f}=e,{scroll:m}=e,{allow_file_downloads:b}=e,{display_consecutive_in_same_bubble:k}=e,{thought_index:p}=e,{allow_tags:I=!1}=e,{message:U}=e;const q=()=>m();return i.$$set=H=>{"latex_delimiters"in H&&l(0,t=H.latex_delimiters),"sanitize_html"in H&&l(1,n=H.sanitize_html),"_fetch"in H&&l(2,a=H._fetch),"i18n"in H&&l(3,o=H.i18n),"line_breaks"in H&&l(4,r=H.line_breaks),"upload"in H&&l(5,s=H.upload),"target"in H&&l(6,u=H.target),"theme_mode"in H&&l(7,c=H.theme_mode),"_components"in H&&l(8,_=H._components),"render_markdown"in H&&l(9,f=H.render_markdown),"scroll"in H&&l(10,m=H.scroll),"allow_file_downloads"in H&&l(11,b=H.allow_file_downloads),"display_consecutive_in_same_bubble"in H&&l(12,k=H.display_consecutive_in_same_bubble),"thought_index"in H&&l(13,p=H.thought_index),"allow_tags"in H&&l(14,I=H.allow_tags),"message"in H&&l(15,U=H.message)},[t,n,a,o,r,s,u,c,_,f,m,b,k,p,I,U,q]}class It extends te{constructor(e){super(),ne(this,e,Jn,yn,ie,{latex_delimiters:0,sanitize_html:1,_fetch:2,i18n:3,line_breaks:4,upload:5,target:6,theme_mode:7,_components:8,render_markdown:9,scroll:10,allow_file_downloads:11,display_consecutive_in_same_bubble:12,thought_index:13,allow_tags:14,message:15})}}function Sl(i,e,l){const t=i.slice();return t[27]=e[l],t[29]=l,t}function ql(i){let e;return{c(){e=A("span"),this.h()},l(l){e=j(l,"SPAN",{class:!0}),D(e).forEach(d),this.h()},h(){h(e,"class","loading-spinner svelte-1qn6r4f")},m(l,t){C(l,e,t)},d(l){l&&d(e)}}}function Fl(i){let e,l,t=i[16].metadata.log&&Zl(i),n=i[16].metadata.duration!==void 0&&Pl(i);return{c(){e=A("span"),t&&t.c(),l=X(),n&&n.c(),this.h()},l(a){e=j(a,"SPAN",{class:!0});var o=D(e);t&&t.l(o),l=Y(o),n&&n.l(o),o.forEach(d),this.h()},h(){h(e,"class","duration svelte-1qn6r4f")},m(a,o){C(a,e,o),t&&t.m(e,null),N(e,l),n&&n.m(e,null)},p(a,o){a[16].metadata.log?t?t.p(a,o):(t=Zl(a),t.c(),t.m(e,l)):t&&(t.d(1),t=null),a[16].metadata.duration!==void 0?n?n.p(a,o):(n=Pl(a),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},d(a){a&&d(e),t&&t.d(),n&&n.d()}}}function Zl(i){let e=i[16].metadata.log+"",l;return{c(){l=oe(e)},l(t){l=re(t,e)},m(t,n){C(t,l,n)},p(t,n){n&65536&&e!==(e=t[16].metadata.log+"")&&ke(l,e)},d(t){t&&d(l)}}}function Pl(i){let e,l,t;function n(r,s){return s&65536&&(l=null),l==null&&(l=!!Number.isInteger(r[16].metadata.duration)),l?Xn:r[16].metadata.duration>=.1?Kn:Gn}let a=n(i,-1),o=a(i);return{c(){e=oe("("),o.c(),t=oe(")")},l(r){e=re(r,"("),o.l(r),t=re(r,")")},m(r,s){C(r,e,s),o.m(r,s),C(r,t,s)},p(r,s){a===(a=n(r,s))&&o?o.p(r,s):(o.d(1),o=a(r),o&&(o.c(),o.m(t.parentNode,t)))},d(r){r&&(d(e),d(t)),o.d(r)}}}function Gn(i){let e=(i[16].metadata.duration*1e3).toFixed(1)+"",l,t;return{c(){l=oe(e),t=oe("ms")},l(n){l=re(n,e),t=re(n,"ms")},m(n,a){C(n,l,a),C(n,t,a)},p(n,a){a&65536&&e!==(e=(n[16].metadata.duration*1e3).toFixed(1)+"")&&ke(l,e)},d(n){n&&(d(l),d(t))}}}function Kn(i){let e=i[16].metadata.duration.toFixed(1)+"",l,t;return{c(){l=oe(e),t=oe("s")},l(n){l=re(n,e),t=re(n,"s")},m(n,a){C(n,l,a),C(n,t,a)},p(n,a){a&65536&&e!==(e=n[16].metadata.duration.toFixed(1)+"")&&ke(l,e)},d(n){n&&(d(l),d(t))}}}function Xn(i){let e=i[16].metadata.duration+"",l,t;return{c(){l=oe(e),t=oe("s")},l(n){l=re(n,e),t=re(n,"s")},m(n,a){C(n,l,a),C(n,t,a)},p(n,a){a&65536&&e!==(e=n[16].metadata.duration+"")&&ke(l,e)},d(n){n&&(d(l),d(t))}}}function Ul(i){var u;let e,l,t,n,a,o,r;l=new It({props:{message:i[16],sanitize_html:i[1],allow_tags:i[15],latex_delimiters:i[2],render_markdown:i[3],_components:i[4],upload:i[5],thought_index:i[6],target:i[7],theme_mode:i[8],_fetch:i[9],scroll:i[10],allow_file_downloads:i[11],display_consecutive_in_same_bubble:i[12],i18n:i[13],line_breaks:i[14]}});let s=((u=i[16].children)==null?void 0:u.length)>0&&Ol(i);return{c(){e=A("div"),M(l.$$.fragment),t=X(),s&&s.c(),this.h()},l(c){e=j(c,"DIV",{class:!0});var _=D(e);Z(l.$$.fragment,_),t=Y(_),s&&s.l(_),_.forEach(d),this.h()},h(){var c;h(e,"class","svelte-1qn6r4f"),K(e,"content",i[18]),K(e,"content-preview",!i[18]&&((c=i[16].metadata)==null?void 0:c.status)!=="done")},m(c,_){C(c,e,_),B(l,e,null),N(e,t),s&&s.m(e,null),i[24](e),a=!0,o||(r=Be(e,"scroll",i[20]),o=!0)},p(c,_){var m,b;const f={};_&65536&&(f.message=c[16]),_&2&&(f.sanitize_html=c[1]),_&32768&&(f.allow_tags=c[15]),_&4&&(f.latex_delimiters=c[2]),_&8&&(f.render_markdown=c[3]),_&16&&(f._components=c[4]),_&32&&(f.upload=c[5]),_&64&&(f.thought_index=c[6]),_&128&&(f.target=c[7]),_&256&&(f.theme_mode=c[8]),_&512&&(f._fetch=c[9]),_&1024&&(f.scroll=c[10]),_&2048&&(f.allow_file_downloads=c[11]),_&4096&&(f.display_consecutive_in_same_bubble=c[12]),_&8192&&(f.i18n=c[13]),_&16384&&(f.line_breaks=c[14]),l.$set(f),((m=c[16].children)==null?void 0:m.length)>0?s?(s.p(c,_),_&65536&&g(s,1)):(s=Ol(c),s.c(),g(s,1),s.m(e,null)):s&&(O(),w(s,1,1,()=>{s=null}),W()),(!a||_&262144)&&K(e,"content",c[18]),(!a||_&327680)&&K(e,"content-preview",!c[18]&&((b=c[16].metadata)==null?void 0:b.status)!=="done")},i(c){a||(g(l.$$.fragment,c),g(s),c&&Tt(()=>{a&&(n||(n=_l(e,hl,{},!0)),n.run(1))}),a=!0)},o(c){w(l.$$.fragment,c),w(s),c&&(n||(n=_l(e,hl,{},!1)),n.run(0)),a=!1},d(c){c&&d(e),T(l),s&&s.d(),i[24](null),c&&n&&n.end(),o=!1,r()}}}function Ol(i){let e,l,t=me(i[16].children),n=[];for(let o=0;o<t.length;o+=1)n[o]=Wl(Sl(i,t,o));const a=o=>w(n[o],1,1,()=>{n[o]=null});return{c(){e=A("div");for(let o=0;o<n.length;o+=1)n[o].c();this.h()},l(o){e=j(o,"DIV",{class:!0});var r=D(e);for(let s=0;s<n.length;s+=1)n[s].l(r);r.forEach(d),this.h()},h(){h(e,"class","children svelte-1qn6r4f")},m(o,r){C(o,e,r);for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(e,null);l=!0},p(o,r){if(r&98303){t=me(o[16].children);let s;for(s=0;s<t.length;s+=1){const u=Sl(o,t,s);n[s]?(n[s].p(u,r),g(n[s],1)):(n[s]=Wl(u),n[s].c(),g(n[s],1),n[s].m(e,null))}for(O(),s=t.length;s<n.length;s+=1)a(s);W()}},i(o){if(!l){for(let r=0;r<t.length;r+=1)g(n[r]);l=!0}},o(o){n=n.filter(Boolean);for(let r=0;r<n.length;r+=1)w(n[r]);l=!1},d(o){o&&d(e),Pe(n,o)}}}function Wl(i){let e,l;return e=new Dt({props:{thought:i[27],rtl:i[0],sanitize_html:i[1],latex_delimiters:i[2],render_markdown:i[3],_components:i[4],upload:i[5],thought_index:i[6]+1,target:i[7],theme_mode:i[8],_fetch:i[9],scroll:i[10],allow_file_downloads:i[11],display_consecutive_in_same_bubble:i[12],i18n:i[13],line_breaks:i[14]}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n&65536&&(a.thought=t[27]),n&1&&(a.rtl=t[0]),n&2&&(a.sanitize_html=t[1]),n&4&&(a.latex_delimiters=t[2]),n&8&&(a.render_markdown=t[3]),n&16&&(a._components=t[4]),n&32&&(a.upload=t[5]),n&64&&(a.thought_index=t[6]+1),n&128&&(a.target=t[7]),n&256&&(a.theme_mode=t[8]),n&512&&(a._fetch=t[9]),n&1024&&(a.scroll=t[10]),n&2048&&(a.allow_file_downloads=t[11]),n&4096&&(a.display_consecutive_in_same_bubble=t[12]),n&8192&&(a.i18n=t[13]),n&16384&&(a.line_breaks=t[14]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Yn(i){var I,U,q,H,F,z;let e,l,t,n,a,o,r,s,u,c,_,f,m;n=new de({props:{Icon:xt}}),o=new fl({props:{message:((I=i[16].metadata)==null?void 0:I.title)||"",render_markdown:i[3],latex_delimiters:i[2],sanitize_html:i[1],allow_tags:i[15]}});let b=((U=i[16].metadata)==null?void 0:U.status)==="pending"&&ql(),k=(((H=(q=i[16])==null?void 0:q.metadata)==null?void 0:H.log)||((z=(F=i[16])==null?void 0:F.metadata)==null?void 0:z.duration))&&Fl(i),p=i[18]&&Ul(i);return{c(){e=A("div"),l=A("div"),t=A("span"),M(n.$$.fragment),a=X(),M(o.$$.fragment),r=X(),b&&b.c(),s=X(),k&&k.c(),c=X(),p&&p.c(),this.h()},l(L){e=j(L,"DIV",{class:!0});var R=D(e);l=j(R,"DIV",{class:!0,"aria-busy":!0,role:!0,tabindex:!0});var G=D(l);t=j(G,"SPAN",{class:!0});var x=D(t);Z(n.$$.fragment,x),x.forEach(d),a=Y(G),Z(o.$$.fragment,G),r=Y(G),b&&b.l(G),s=Y(G),k&&k.l(G),G.forEach(d),c=Y(R),p&&p.l(R),R.forEach(d),this.h()},h(){h(t,"class","arrow svelte-1qn6r4f"),he(t,"transform",i[18]?"rotate(180deg)":"rotate(0deg)"),h(l,"class","title svelte-1qn6r4f"),h(l,"aria-busy",u=i[16].content===""||i[16].content===null),h(l,"role","button"),h(l,"tabindex","0"),K(l,"expanded",i[18]),h(e,"class","thought-group svelte-1qn6r4f")},m(L,R){C(L,e,R),N(e,l),N(l,t),B(n,t,null),N(l,a),B(o,l,null),N(l,r),b&&b.m(l,null),N(l,s),k&&k.m(l,null),N(e,c),p&&p.m(e,null),_=!0,f||(m=[Be(l,"click",Bt(i[19])),Be(l,"keydown",i[23])],f=!0)},p(L,[R]){var x,le,P,ce,S,Q;R&262144&&he(t,"transform",L[18]?"rotate(180deg)":"rotate(0deg)");const G={};R&65536&&(G.message=((x=L[16].metadata)==null?void 0:x.title)||""),R&8&&(G.render_markdown=L[3]),R&4&&(G.latex_delimiters=L[2]),R&2&&(G.sanitize_html=L[1]),R&32768&&(G.allow_tags=L[15]),o.$set(G),((le=L[16].metadata)==null?void 0:le.status)==="pending"?b||(b=ql(),b.c(),b.m(l,s)):b&&(b.d(1),b=null),(ce=(P=L[16])==null?void 0:P.metadata)!=null&&ce.log||(Q=(S=L[16])==null?void 0:S.metadata)!=null&&Q.duration?k?k.p(L,R):(k=Fl(L),k.c(),k.m(l,null)):k&&(k.d(1),k=null),(!_||R&65536&&u!==(u=L[16].content===""||L[16].content===null))&&h(l,"aria-busy",u),(!_||R&262144)&&K(l,"expanded",L[18]),L[18]?p?(p.p(L,R),R&262144&&g(p,1)):(p=Ul(L),p.c(),g(p,1),p.m(e,null)):p&&(O(),w(p,1,1,()=>{p=null}),W())},i(L){_||(g(n.$$.fragment,L),g(o.$$.fragment,L),g(p),_=!0)},o(L){w(n.$$.fragment,L),w(o.$$.fragment,L),w(p),_=!1},d(L){L&&d(e),T(n),T(o),b&&b.d(),k&&k.d(),p&&p.d(),f=!1,wt(m)}}}function Qn(i){return"children"in i}function xn(i,e,l){let{thought:t}=e,{rtl:n=!1}=e,{sanitize_html:a}=e,{latex_delimiters:o}=e,{render_markdown:r}=e,{_components:s}=e,{upload:u}=e,{thought_index:c}=e,{target:_}=e,{theme_mode:f}=e,{_fetch:m}=e,{scroll:b}=e,{allow_file_downloads:k}=e,{display_consecutive_in_same_bubble:p}=e,{i18n:I}=e,{line_breaks:U}=e,{allow_tags:q=!1}=e,H,F=!1,z=!1,L,R=!1;function G(){l(18,F=!F),l(22,z=!0)}function x(){L&&!R&&l(17,L.scrollTop=L.scrollHeight,L)}function le(){L&&(L.scrollHeight-L.scrollTop<=L.clientHeight+10||(R=!0))}const P=S=>S.key==="Enter"&&G();function ce(S){Ye[S?"unshift":"push"](()=>{L=S,l(17,L)})}return i.$$set=S=>{"thought"in S&&l(21,t=S.thought),"rtl"in S&&l(0,n=S.rtl),"sanitize_html"in S&&l(1,a=S.sanitize_html),"latex_delimiters"in S&&l(2,o=S.latex_delimiters),"render_markdown"in S&&l(3,r=S.render_markdown),"_components"in S&&l(4,s=S._components),"upload"in S&&l(5,u=S.upload),"thought_index"in S&&l(6,c=S.thought_index),"target"in S&&l(7,_=S.target),"theme_mode"in S&&l(8,f=S.theme_mode),"_fetch"in S&&l(9,m=S._fetch),"scroll"in S&&l(10,b=S.scroll),"allow_file_downloads"in S&&l(11,k=S.allow_file_downloads),"display_consecutive_in_same_bubble"in S&&l(12,p=S.display_consecutive_in_same_bubble),"i18n"in S&&l(13,I=S.i18n),"line_breaks"in S&&l(14,U=S.line_breaks),"allow_tags"in S&&l(15,q=S.allow_tags)},i.$$.update=()=>{var S,Q;i.$$.dirty&2097152&&l(16,H={...t,children:Qn(t)?t.children:[]}),i.$$.dirty&4259840&&(z||l(18,F=((S=H==null?void 0:H.metadata)==null?void 0:S.status)!=="done")),i.$$.dirty&196608&&H.content&&L&&((Q=H.metadata)==null?void 0:Q.status)!=="done"&&setTimeout(x,0)},[n,a,o,r,s,u,c,_,f,m,b,k,p,I,U,q,H,L,F,G,le,t,z,P,ce]}class Dt extends te{constructor(e){super(),ne(this,e,xn,Yn,ie,{thought:21,rtl:0,sanitize_html:1,latex_delimiters:2,render_markdown:3,_components:4,upload:5,thought_index:6,target:7,theme_mode:8,_fetch:9,scroll:10,allow_file_downloads:11,display_consecutive_in_same_bubble:12,i18n:13,line_breaks:14,allow_tags:15})}}function Rl(i,e,l){const t=i.slice();return t[47]=e[l],t[48]=e,t[49]=l,t}function yl(i){var n;let e,l,t;return l=new ye({props:{class:"avatar-image",src:(n=i[2])==null?void 0:n.url,alt:i[4]+" avatar"}}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(a){e=j(a,"DIV",{class:!0});var o=D(e);Z(l.$$.fragment,o),o.forEach(d),this.h()},h(){h(e,"class","avatar-container svelte-1csv61q")},m(a,o){C(a,e,o),B(l,e,null),t=!0},p(a,o){var s;const r={};o[0]&4&&(r.src=(s=a[2])==null?void 0:s.url),o[0]&16&&(r.alt=a[4]+" avatar"),l.$set(r)},i(a){t||(g(l.$$.fragment,a),t=!0)},o(a){w(l.$$.fragment,a),t=!1},d(a){a&&d(e),T(l)}}}function $n(i){let e,l,t,n,a,o=i[49],r,s,u;const c=[ti,li],_=[];function f(I,U){var q,H;return(H=(q=I[47])==null?void 0:q.metadata)!=null&&H.title?0:1}l=f(i),t=_[l]=c[l](i);const m=()=>i[43](e,o),b=()=>i[43](null,o);function k(){return i[44](i[47])}function p(...I){return i[45](i[47],...I)}return{c(){e=A("div"),t.c(),this.h()},l(I){e=j(I,"DIV",{"data-testid":!0,dir:!0,"aria-label":!0,class:!0});var U=D(e);t.l(U),U.forEach(d),this.h()},h(){h(e,"data-testid",i[4]),h(e,"dir",n=i[12]?"rtl":"ltr"),h(e,"aria-label",a=i[4]+"'s message: "+Xl(i[47])),h(e,"class","svelte-1csv61q"),K(e,"latest",i[20]===i[1].length-1),K(e,"message-markdown-disabled",!i[7]),K(e,"selectable",i[10]),he(e,"user-select","text"),he(e,"cursor",i[10]?"pointer":"auto"),he(e,"text-align",i[12]?"right":"left")},m(I,U){C(I,e,U),_[l].m(e,null),m(),r=!0,s||(u=[Be(e,"click",k),Be(e,"keydown",p)],s=!0)},p(I,U){i=I;let q=l;l=f(i),l===q?_[l].p(i,U):(O(),w(_[q],1,1,()=>{_[q]=null}),W(),t=_[l],t?t.p(i,U):(t=_[l]=c[l](i),t.c()),g(t,1),t.m(e,null)),(!r||U[0]&16)&&h(e,"data-testid",i[4]),(!r||U[0]&4096&&n!==(n=i[12]?"rtl":"ltr"))&&h(e,"dir",n),(!r||U[0]&48&&a!==(a=i[4]+"'s message: "+Xl(i[47])))&&h(e,"aria-label",a),o!==i[49]&&(b(),o=i[49],m()),(!r||U[0]&1048578)&&K(e,"latest",i[20]===i[1].length-1),(!r||U[0]&128)&&K(e,"message-markdown-disabled",!i[7]),(!r||U[0]&1024)&&K(e,"selectable",i[10]),U[0]&1024&&he(e,"cursor",i[10]?"pointer":"auto"),U[0]&4096&&he(e,"text-align",i[12]?"right":"left")},i(I){r||(g(t),r=!0)},o(I){w(t),r=!1},d(I){I&&d(e),_[l].d(),b(),s=!1,wt(u)}}}function ei(i){let e,l,t;function n(){i[42].call(e,i[49])}return{c(){e=A("textarea"),this.h()},l(a){e=j(a,"TEXTAREA",{class:!0}),D(e).forEach(d),this.h()},h(){h(e,"class","edit-textarea svelte-1csv61q"),e.autofocus=!0,he(e,"width",`max(${i[29][i[49]]}px, 160px)`),he(e,"min-height",`${i[30][i[49]]}px`)},m(a,o){C(a,e,o),cl(e,i[0][i[49]]),e.focus(),l||(t=Be(e,"input",n),l=!0)},p(a,o){i=a,o[0]&1&&cl(e,i[0][i[49]]),o[0]&536870912&&he(e,"width",`max(${i[29][i[49]]}px, 160px)`),o[0]&1073741824&&he(e,"min-height",`${i[30][i[49]]}px`)},i:y,o:y,d(a){a&&d(e),l=!1,t()}}}function li(i){let e,l;return e=new It({props:{message:i[47],sanitize_html:i[9],allow_tags:i[26],latex_delimiters:i[8],render_markdown:i[7],_components:i[19],upload:i[16],thought_index:i[49],target:i[17],theme_mode:i[18],_fetch:i[11],scroll:i[21],allow_file_downloads:i[22],display_consecutive_in_same_bubble:i[24],i18n:i[14],line_breaks:i[15]}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&32&&(a.message=t[47]),n[0]&512&&(a.sanitize_html=t[9]),n[0]&67108864&&(a.allow_tags=t[26]),n[0]&256&&(a.latex_delimiters=t[8]),n[0]&128&&(a.render_markdown=t[7]),n[0]&524288&&(a._components=t[19]),n[0]&65536&&(a.upload=t[16]),n[0]&131072&&(a.target=t[17]),n[0]&262144&&(a.theme_mode=t[18]),n[0]&2048&&(a._fetch=t[11]),n[0]&2097152&&(a.scroll=t[21]),n[0]&4194304&&(a.allow_file_downloads=t[22]),n[0]&16777216&&(a.display_consecutive_in_same_bubble=t[24]),n[0]&16384&&(a.i18n=t[14]),n[0]&32768&&(a.line_breaks=t[15]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function ti(i){let e,l;return e=new Dt({props:{thought:i[47],rtl:i[12],sanitize_html:i[9],allow_tags:i[26],latex_delimiters:i[8],render_markdown:i[7],_components:i[19],upload:i[16],thought_index:i[49],target:i[17],theme_mode:i[18],_fetch:i[11],scroll:i[21],allow_file_downloads:i[22],display_consecutive_in_same_bubble:i[24],i18n:i[14],line_breaks:i[15]}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&32&&(a.thought=t[47]),n[0]&4096&&(a.rtl=t[12]),n[0]&512&&(a.sanitize_html=t[9]),n[0]&67108864&&(a.allow_tags=t[26]),n[0]&256&&(a.latex_delimiters=t[8]),n[0]&128&&(a.render_markdown=t[7]),n[0]&524288&&(a._components=t[19]),n[0]&65536&&(a.upload=t[16]),n[0]&131072&&(a.target=t[17]),n[0]&262144&&(a.theme_mode=t[18]),n[0]&2048&&(a._fetch=t[11]),n[0]&2097152&&(a.scroll=t[21]),n[0]&4194304&&(a.allow_file_downloads=t[22]),n[0]&16777216&&(a.display_consecutive_in_same_bubble=t[24]),n[0]&16384&&(a.i18n=t[14]),n[0]&32768&&(a.line_breaks=t[15]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Jl(i){let e,l;const t=[i[31],{current_feedback:i[25]},{watermark:i[27]},{i18n:i[14]}];let n={};for(let a=0;a<t.length;a+=1)n=We(n,t[a]);return e=new Vt({props:n}),e.$on("copy",i[46]),{c(){M(e.$$.fragment)},l(a){Z(e.$$.fragment,a)},m(a,o){B(e,a,o),l=!0},p(a,o){const r=o[0]&167788544|o[1]&1?Ue(t,[o[1]&1&&Oe(a[31]),o[0]&33554432&&{current_feedback:a[25]},o[0]&134217728&&{watermark:a[27]},o[0]&16384&&{i18n:a[14]}]):{};e.$set(r)},i(a){l||(g(e.$$.fragment,a),l=!0)},o(a){w(e.$$.fragment,a),l=!1},d(a){T(e,a)}}}function Gl(i){let e,l,t,n,a,o,r;const s=[ei,$n],u=[];function c(f,m){return f[23]&&f[47].type==="text"?0:1}l=c(i),t=u[l]=s[l](i);let _=i[6]==="panel"&&Jl(i);return{c(){e=A("div"),t.c(),a=X(),_&&_.c(),o=J(),this.h()},l(f){e=j(f,"DIV",{class:!0});var m=D(e);t.l(m),m.forEach(d),a=Y(f),_&&_.l(f),o=J(),this.h()},h(){h(e,"class",n="message "+(i[24]?"":i[4])+" svelte-1csv61q"),K(e,"panel-full-width",!0),K(e,"message-markdown-disabled",!i[7]),K(e,"component",i[47].type==="component"),K(e,"html",dl(i[47])&&i[47].content.component==="html"),K(e,"thought",i[49]>0)},m(f,m){C(f,e,m),u[l].m(e,null),C(f,a,m),_&&_.m(f,m),C(f,o,m),r=!0},p(f,m){let b=l;l=c(f),l===b?u[l].p(f,m):(O(),w(u[b],1,1,()=>{u[b]=null}),W(),t=u[l],t?t.p(f,m):(t=u[l]=s[l](f),t.c()),g(t,1),t.m(e,null)),(!r||m[0]&16777232&&n!==(n="message "+(f[24]?"":f[4])+" svelte-1csv61q"))&&h(e,"class",n),(!r||m[0]&16777232)&&K(e,"panel-full-width",!0),(!r||m[0]&16777360)&&K(e,"message-markdown-disabled",!f[7]),(!r||m[0]&16777264)&&K(e,"component",f[47].type==="component"),(!r||m[0]&16777264)&&K(e,"html",dl(f[47])&&f[47].content.component==="html"),(!r||m[0]&16777232)&&K(e,"thought",f[49]>0),f[6]==="panel"?_?(_.p(f,m),m[0]&64&&g(_,1)):(_=Jl(f),_.c(),g(_,1),_.m(o.parentNode,o)):_&&(O(),w(_,1,1,()=>{_=null}),W())},i(f){r||(g(t),g(_),r=!0)},o(f){w(t),w(_),r=!1},d(f){f&&(d(e),d(a),d(o)),u[l].d(),_&&_.d(f)}}}function Kl(i){let e,l;const t=[i[31],{i18n:i[14]}];let n={};for(let a=0;a<t.length;a+=1)n=We(n,t[a]);return e=new Vt({props:n}),{c(){M(e.$$.fragment)},l(a){Z(e.$$.fragment,a)},m(a,o){B(e,a,o),l=!0},p(a,o){const r=o[0]&16384|o[1]&1?Ue(t,[o[1]&1&&Oe(a[31]),o[0]&16384&&{i18n:a[14]}]):{};e.$set(r)},i(a){l||(g(e.$$.fragment,a),l=!0)},o(a){w(e.$$.fragment,a),l=!1},d(a){T(e,a)}}}function ni(i){let e,l,t,n,a,o,r,s,u,c=i[2]!==null&&yl(i),_=me(i[5]),f=[];for(let k=0;k<_.length;k+=1)f[k]=Gl(Rl(i,_,k));const m=k=>w(f[k],1,1,()=>{f[k]=null});let b=i[6]==="bubble"&&Kl(i);return{c(){e=A("div"),c&&c.c(),l=X(),t=A("div"),n=A("div");for(let k=0;k<f.length;k+=1)f[k].c();r=X(),b&&b.c(),s=J(),this.h()},l(k){e=j(k,"DIV",{class:!0});var p=D(e);c&&c.l(p),l=Y(p),t=j(p,"DIV",{class:!0});var I=D(t);n=j(I,"DIV",{class:!0});var U=D(n);for(let q=0;q<f.length;q+=1)f[q].l(U);U.forEach(d),I.forEach(d),p.forEach(d),r=Y(k),b&&b.l(k),s=J(),this.h()},h(){h(n,"class",a=Ge(i[24]?i[4]:"")+" svelte-1csv61q"),K(n,"message",i[24]),h(t,"class","flex-wrap svelte-1csv61q"),K(t,"role",i[4]),K(t,"component-wrap",i[5][0].type==="component"),h(e,"class",o="message-row "+i[6]+" "+i[4]+"-row svelte-1csv61q"),K(e,"with_avatar",i[2]!==null),K(e,"with_opposite_avatar",i[3]!==null)},m(k,p){C(k,e,p),c&&c.m(e,null),N(e,l),N(e,t),N(t,n);for(let I=0;I<f.length;I+=1)f[I]&&f[I].m(n,null);C(k,r,p),b&&b.m(k,p),C(k,s,p),u=!0},p(k,p){if(k[2]!==null?c?(c.p(k,p),p[0]&4&&g(c,1)):(c=yl(k),c.c(),g(c,1),c.m(e,l)):c&&(O(),w(c,1,1,()=>{c=null}),W()),p[0]&2147483635|p[1]&3){_=me(k[5]);let I;for(I=0;I<_.length;I+=1){const U=Rl(k,_,I);f[I]?(f[I].p(U,p),g(f[I],1)):(f[I]=Gl(U),f[I].c(),g(f[I],1),f[I].m(n,null))}for(O(),I=_.length;I<f.length;I+=1)m(I);W()}(!u||p[0]&16777232&&a!==(a=Ge(k[24]?k[4]:"")+" svelte-1csv61q"))&&h(n,"class",a),(!u||p[0]&16777232)&&K(n,"message",k[24]),(!u||p[0]&16)&&K(t,"role",k[4]),(!u||p[0]&32)&&K(t,"component-wrap",k[5][0].type==="component"),(!u||p[0]&80&&o!==(o="message-row "+k[6]+" "+k[4]+"-row svelte-1csv61q"))&&h(e,"class",o),(!u||p[0]&84)&&K(e,"with_avatar",k[2]!==null),(!u||p[0]&88)&&K(e,"with_opposite_avatar",k[3]!==null),k[6]==="bubble"?b?(b.p(k,p),p[0]&64&&g(b,1)):(b=Kl(k),b.c(),g(b,1),b.m(s.parentNode,s)):b&&(O(),w(b,1,1,()=>{b=null}),W())},i(k){if(!u){g(c);for(let p=0;p<_.length;p+=1)g(f[p]);g(b),u=!0}},o(k){w(c),f=f.filter(Boolean);for(let p=0;p<f.length;p+=1)w(f[p]);w(b),u=!1},d(k){k&&(d(e),d(r),d(s)),c&&c.d(),Pe(f,k),b&&b.d(k)}}}let ii=!1;function Xl(i){var e,l,t,n;return i.type==="text"?i.content:i.type==="component"&&i.content.component==="file"?Array.isArray(i.content.value)?`file of extension type: ${(e=i.content.value[0].orig_name)==null?void 0:e.split(".").pop()}`:`file of extension type: ${(t=(l=i.content.value)==null?void 0:l.orig_name)==null?void 0:t.split(".").pop()}`+(((n=i.content.value)==null?void 0:n.orig_name)??""):`a component of type ${i.content.component??"unknown"}`}function ai(i,e,l){let{value:t}=e,{avatar_img:n}=e,{opposite_avatar_img:a=null}=e,{role:o="user"}=e,{messages:r=[]}=e,{layout:s}=e,{render_markdown:u}=e,{latex_delimiters:c}=e,{sanitize_html:_}=e,{selectable:f}=e,{_fetch:m}=e,{rtl:b}=e,{dispatch:k}=e,{i18n:p}=e,{line_breaks:I}=e,{upload:U}=e,{target:q}=e,{theme_mode:H}=e,{_components:F}=e,{i:z}=e,{show_copy_button:L}=e,{generating:R}=e,{feedback_options:G}=e,{show_like:x}=e,{show_edit:le}=e,{show_retry:P}=e,{show_undo:ce}=e,{msg_format:S}=e,{handle_action:Q}=e,{scroll:Te}=e,{allow_file_downloads:Ae}=e,{in_edit_mode:Ce}=e,{edit_messages:ve}=e,{display_consecutive_in_same_bubble:Ve}=e,{current_feedback:Ie=null}=e,{allow_tags:je=!1}=e,{watermark:De=null}=e,ge=[],ze=Array(r.length).fill(160),He=Array(r.length).fill(0);function Le(V,_e){k("select",{index:_e.index,value:_e.content})}let Se;function Ne(V){ve[V]=this.value,l(0,ve)}function pe(V,_e){Ye[V?"unshift":"push"](()=>{ge[_e]=V,l(28,ge)})}const ae=V=>Le(z,V),qe=(V,_e)=>{_e.key==="Enter"&&Le(z,V)},se=V=>k("copy",V.detail);return i.$$set=V=>{"value"in V&&l(1,t=V.value),"avatar_img"in V&&l(2,n=V.avatar_img),"opposite_avatar_img"in V&&l(3,a=V.opposite_avatar_img),"role"in V&&l(4,o=V.role),"messages"in V&&l(5,r=V.messages),"layout"in V&&l(6,s=V.layout),"render_markdown"in V&&l(7,u=V.render_markdown),"latex_delimiters"in V&&l(8,c=V.latex_delimiters),"sanitize_html"in V&&l(9,_=V.sanitize_html),"selectable"in V&&l(10,f=V.selectable),"_fetch"in V&&l(11,m=V._fetch),"rtl"in V&&l(12,b=V.rtl),"dispatch"in V&&l(13,k=V.dispatch),"i18n"in V&&l(14,p=V.i18n),"line_breaks"in V&&l(15,I=V.line_breaks),"upload"in V&&l(16,U=V.upload),"target"in V&&l(17,q=V.target),"theme_mode"in V&&l(18,H=V.theme_mode),"_components"in V&&l(19,F=V._components),"i"in V&&l(20,z=V.i),"show_copy_button"in V&&l(33,L=V.show_copy_button),"generating"in V&&l(34,R=V.generating),"feedback_options"in V&&l(35,G=V.feedback_options),"show_like"in V&&l(36,x=V.show_like),"show_edit"in V&&l(37,le=V.show_edit),"show_retry"in V&&l(38,P=V.show_retry),"show_undo"in V&&l(39,ce=V.show_undo),"msg_format"in V&&l(40,S=V.msg_format),"handle_action"in V&&l(41,Q=V.handle_action),"scroll"in V&&l(21,Te=V.scroll),"allow_file_downloads"in V&&l(22,Ae=V.allow_file_downloads),"in_edit_mode"in V&&l(23,Ce=V.in_edit_mode),"edit_messages"in V&&l(0,ve=V.edit_messages),"display_consecutive_in_same_bubble"in V&&l(24,Ve=V.display_consecutive_in_same_bubble),"current_feedback"in V&&l(25,Ie=V.current_feedback),"allow_tags"in V&&l(26,je=V.allow_tags),"watermark"in V&&l(27,De=V.watermark)},i.$$.update=()=>{var V,_e;if(i.$$.dirty[0]&276824096&&Ce&&!ii){const Fe=ge.length-r.length;for(let be=Fe;be<ge.length;be++)be>=0&&(l(29,ze[be-Fe]=(V=ge[be])==null?void 0:V.clientWidth,ze),l(30,He[be-Fe]=(_e=ge[be])==null?void 0:_e.clientHeight,He))}i.$$.dirty[0]&176169076|i.$$.dirty[1]&2044&&l(31,Se={handle_action:Q,likeable:x,feedback_options:G,show_retry:P,show_undo:ce,show_edit:le,in_edit_mode:Ce,generating:R,show_copy_button:L,message:S==="tuples"?r[0]:r,position:o==="user"?"right":"left",avatar:n,layout:s,dispatch:k,current_feedback:Ie,watermark:De})},[ve,t,n,a,o,r,s,u,c,_,f,m,b,k,p,I,U,q,H,F,z,Te,Ae,Ce,Ve,Ie,je,De,ge,ze,He,Se,Le,L,R,G,x,le,P,ce,S,Q,Ne,pe,ae,qe,se]}class oi extends te{constructor(e){super(),ne(this,e,ai,ni,ie,{value:1,avatar_img:2,opposite_avatar_img:3,role:4,messages:5,layout:6,render_markdown:7,latex_delimiters:8,sanitize_html:9,selectable:10,_fetch:11,rtl:12,dispatch:13,i18n:14,line_breaks:15,upload:16,target:17,theme_mode:18,_components:19,i:20,show_copy_button:33,generating:34,feedback_options:35,show_like:36,show_edit:37,show_retry:38,show_undo:39,msg_format:40,handle_action:41,scroll:21,allow_file_downloads:22,in_edit_mode:23,edit_messages:0,display_consecutive_in_same_bubble:24,current_feedback:25,allow_tags:26,watermark:27},null,[-1,-1])}}function Yl(i){let e,l,t;return l=new ye({props:{class:"avatar-image",src:i[1][1].url,alt:"bot avatar"}}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(n){e=j(n,"DIV",{class:!0});var a=D(e);Z(l.$$.fragment,a),a.forEach(d),this.h()},h(){h(e,"class","avatar-container svelte-134ihlx")},m(n,a){C(n,e,a),B(l,e,null),t=!0},p(n,a){const o={};a&2&&(o.src=n[1][1].url),l.$set(o)},i(n){t||(g(l.$$.fragment,n),t=!0)},o(n){w(l.$$.fragment,n),t=!1},d(n){n&&d(e),T(l)}}}function ri(i){let e,l,t,n,a='<span class="sr-only">Loading content</span> <div class="dots svelte-134ihlx"><div class="dot svelte-134ihlx"></div> <div class="dot svelte-134ihlx"></div> <div class="dot svelte-134ihlx"></div></div>',o,r,s=i[1][1]!==null&&Yl(i);return{c(){e=A("div"),s&&s.c(),l=X(),t=A("div"),n=A("div"),n.innerHTML=a,this.h()},l(u){e=j(u,"DIV",{class:!0});var c=D(e);s&&s.l(c),l=Y(c),t=j(c,"DIV",{class:!0,role:!0,"aria-label":!0,"aria-live":!0});var _=D(t);n=j(_,"DIV",{class:!0,"data-svelte-h":!0}),kt(n)!=="svelte-1vfby8"&&(n.innerHTML=a),_.forEach(d),c.forEach(d),this.h()},h(){h(n,"class","message-content svelte-134ihlx"),h(t,"class",o="message bot pending "+i[0]+" svelte-134ihlx"),h(t,"role","status"),h(t,"aria-label","Loading response"),h(t,"aria-live","polite"),K(t,"with_avatar",i[1][1]!==null),K(t,"with_opposite_avatar",i[1][0]!==null),h(e,"class","container svelte-134ihlx")},m(u,c){C(u,e,c),s&&s.m(e,null),N(e,l),N(e,t),N(t,n),r=!0},p(u,[c]){u[1][1]!==null?s?(s.p(u,c),c&2&&g(s,1)):(s=Yl(u),s.c(),g(s,1),s.m(e,l)):s&&(O(),w(s,1,1,()=>{s=null}),W()),(!r||c&1&&o!==(o="message bot pending "+u[0]+" svelte-134ihlx"))&&h(t,"class",o),(!r||c&3)&&K(t,"with_avatar",u[1][1]!==null),(!r||c&3)&&K(t,"with_opposite_avatar",u[1][0]!==null)},i(u){r||(g(s),r=!0)},o(u){w(s),r=!1},d(u){u&&d(e),s&&s.d()}}}function si(i,e,l){let{layout:t="bubble"}=e,{avatar_images:n=[null,null]}=e;return i.$$set=a=>{"layout"in a&&l(0,t=a.layout),"avatar_images"in a&&l(1,n=a.avatar_images)},[t,n]}class zt extends te{constructor(e){super(),ne(this,e,si,ri,ie,{layout:0,avatar_images:1})}}function Ql(i,e,l){const t=i.slice();return t[6]=e[l],t[8]=l,t}function xl(i,e,l){const t=i.slice();return t[9]=e[l],t[8]=l,t}function $l(i){let e,l,t;return l=new fl({props:{message:i[1],latex_delimiters:i[2]}}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(n){e=j(n,"DIV",{class:!0});var a=D(e);Z(l.$$.fragment,a),a.forEach(d),this.h()},h(){h(e,"class","placeholder svelte-9pi8y1")},m(n,a){C(n,e,a),B(l,e,null),t=!0},p(n,a){const o={};a&2&&(o.message=n[1]),a&4&&(o.latex_delimiters=n[2]),l.$set(o)},i(n){t||(g(l.$$.fragment,n),t=!0)},o(n){w(l.$$.fragment,n),t=!1},d(n){n&&d(e),T(l)}}}function et(i){let e,l,t=me(i[0]),n=[];for(let o=0;o<t.length;o+=1)n[o]=at(Ql(i,t,o));const a=o=>w(n[o],1,1,()=>{n[o]=null});return{c(){e=A("div");for(let o=0;o<n.length;o+=1)n[o].c();this.h()},l(o){e=j(o,"DIV",{class:!0,role:!0});var r=D(e);for(let s=0;s<n.length;s+=1)n[s].l(r);r.forEach(d),this.h()},h(){h(e,"class","examples svelte-9pi8y1"),h(e,"role","list")},m(o,r){C(o,e,r);for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(e,null);l=!0},p(o,r){if(r&9){t=me(o[0]);let s;for(s=0;s<t.length;s+=1){const u=Ql(o,t,s);n[s]?(n[s].p(u,r),g(n[s],1)):(n[s]=at(u),n[s].c(),g(n[s],1),n[s].m(e,null))}for(O(),s=t.length;s<n.length;s+=1)a(s);W()}},i(o){if(!l){for(let r=0;r<t.length;r+=1)g(n[r]);l=!0}},o(o){n=n.filter(Boolean);for(let r=0;r<n.length;r+=1)w(n[r]);l=!1},d(o){o&&d(e),Pe(n,o)}}}function fi(i){let e,l,t,n,a,o,r;const s=[gi,di,mi,hi,ci],u=[];function c(_,f){var m,b,k;return f&1&&(e=null),f&1&&(l=null),f&1&&(t=null),_[6].files.length>1?0:(e==null&&(e=!!((m=_[6].files[0].mime_type)!=null&&m.includes("image"))),e?1:(l==null&&(l=!!((b=_[6].files[0].mime_type)!=null&&b.includes("video"))),l?2:(t==null&&(t=!!((k=_[6].files[0].mime_type)!=null&&k.includes("audio"))),t?3:4)))}return n=c(i,-1),a=u[n]=s[n](i),{c(){a.c(),o=J()},l(_){a.l(_),o=J()},m(_,f){u[n].m(_,f),C(_,o,f),r=!0},p(_,f){let m=n;n=c(_,f),n===m?u[n].p(_,f):(O(),w(u[m],1,1,()=>{u[m]=null}),W(),a=u[n],a?a.p(_,f):(a=u[n]=s[n](_),a.c()),g(a,1),a.m(o.parentNode,o))},i(_){r||(g(a),r=!0)},o(_){w(a),r=!1},d(_){_&&d(o),u[n].d(_)}}}function ui(i){let e,l='<span class="text-icon-aa svelte-9pi8y1">Aa</span>';return{c(){e=A("div"),e.innerHTML=l,this.h()},l(t){e=j(t,"DIV",{class:!0,"aria-hidden":!0,"data-svelte-h":!0}),kt(e)!=="svelte-15cq9iz"&&(e.innerHTML=l),this.h()},h(){h(e,"class","example-icon svelte-9pi8y1"),h(e,"aria-hidden","true")},m(t,n){C(t,e,n)},p:y,i:y,o:y,d(t){t&&d(e)}}}function _i(i){let e,l,t;return l=new ye({props:{class:"example-image",src:i[6].icon.url,alt:"Example icon"}}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(n){e=j(n,"DIV",{class:!0});var a=D(e);Z(l.$$.fragment,a),a.forEach(d),this.h()},h(){h(e,"class","example-image-container svelte-9pi8y1")},m(n,a){C(n,e,a),B(l,e,null),t=!0},p(n,a){const o={};a&1&&(o.src=n[6].icon.url),l.$set(o)},i(n){t||(g(l.$$.fragment,n),t=!0)},o(n){w(l.$$.fragment,n),t=!1},d(n){n&&d(e),T(l)}}}function ci(i){let e,l,t,n;return l=new ul({}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(a){e=j(a,"DIV",{class:!0,"aria-label":!0});var o=D(e);Z(l.$$.fragment,o),o.forEach(d),this.h()},h(){h(e,"class","example-icon svelte-9pi8y1"),h(e,"aria-label",t=`File: ${i[6].files[0].orig_name}`)},m(a,o){C(a,e,o),B(l,e,null),n=!0},p(a,o){(!n||o&1&&t!==(t=`File: ${a[6].files[0].orig_name}`))&&h(e,"aria-label",t)},i(a){n||(g(l.$$.fragment,a),n=!0)},o(a){w(l.$$.fragment,a),n=!1},d(a){a&&d(e),T(l)}}}function hi(i){let e,l,t,n;return l=new pt({}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(a){e=j(a,"DIV",{class:!0,"aria-label":!0});var o=D(e);Z(l.$$.fragment,o),o.forEach(d),this.h()},h(){h(e,"class","example-icon svelte-9pi8y1"),h(e,"aria-label",t=`File: ${i[6].files[0].orig_name}`)},m(a,o){C(a,e,o),B(l,e,null),n=!0},p(a,o){(!n||o&1&&t!==(t=`File: ${a[6].files[0].orig_name}`))&&h(e,"aria-label",t)},i(a){n||(g(l.$$.fragment,a),n=!0)},o(a){w(l.$$.fragment,a),n=!1},d(a){a&&d(e),T(l)}}}function mi(i){let e,l,t;return{c(){e=A("div"),l=A("video"),this.h()},l(n){e=j(n,"DIV",{class:!0});var a=D(e);l=j(a,"VIDEO",{class:!0,src:!0,"aria-hidden":!0}),D(l).forEach(d),a.forEach(d),this.h()},h(){h(l,"class","example-image"),Ke(l.src,t=i[6].files[0].url)||h(l,"src",t),h(l,"aria-hidden","true"),h(e,"class","example-image-container svelte-9pi8y1")},m(n,a){C(n,e,a),N(e,l)},p(n,a){a&1&&!Ke(l.src,t=n[6].files[0].url)&&h(l,"src",t)},i:y,o:y,d(n){n&&d(e)}}}function di(i){let e,l,t;return l=new ye({props:{class:"example-image",src:i[6].files[0].url,alt:i[6].files[0].orig_name||"Example image"}}),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(n){e=j(n,"DIV",{class:!0});var a=D(e);Z(l.$$.fragment,a),a.forEach(d),this.h()},h(){h(e,"class","example-image-container svelte-9pi8y1")},m(n,a){C(n,e,a),B(l,e,null),t=!0},p(n,a){const o={};a&1&&(o.src=n[6].files[0].url),a&1&&(o.alt=n[6].files[0].orig_name||"Example image"),l.$set(o)},i(n){t||(g(l.$$.fragment,n),t=!0)},o(n){w(l.$$.fragment,n),t=!1},d(n){n&&d(e),T(l)}}}function gi(i){let e,l,t,n=me(i[6].files.slice(0,4)),a=[];for(let s=0;s<n.length;s+=1)a[s]=nt(xl(i,n,s));const o=s=>w(a[s],1,1,()=>{a[s]=null});let r=i[6].files.length>4&&it(i);return{c(){e=A("div");for(let s=0;s<a.length;s+=1)a[s].c();l=X(),r&&r.c(),this.h()},l(s){e=j(s,"DIV",{class:!0,role:!0,"aria-label":!0});var u=D(e);for(let c=0;c<a.length;c+=1)a[c].l(u);l=Y(u),r&&r.l(u),u.forEach(d),this.h()},h(){h(e,"class","example-icons-grid svelte-9pi8y1"),h(e,"role","group"),h(e,"aria-label","Example attachments")},m(s,u){C(s,e,u);for(let c=0;c<a.length;c+=1)a[c]&&a[c].m(e,null);N(e,l),r&&r.m(e,null),t=!0},p(s,u){if(u&1){n=me(s[6].files.slice(0,4));let c;for(c=0;c<n.length;c+=1){const _=xl(s,n,c);a[c]?(a[c].p(_,u),g(a[c],1)):(a[c]=nt(_),a[c].c(),g(a[c],1),a[c].m(e,l))}for(O(),c=n.length;c<a.length;c+=1)o(c);W()}s[6].files.length>4?r?r.p(s,u):(r=it(s),r.c(),r.m(e,null)):r&&(r.d(1),r=null)},i(s){if(!t){for(let u=0;u<n.length;u+=1)g(a[u]);t=!0}},o(s){a=a.filter(Boolean);for(let u=0;u<a.length;u+=1)w(a[u]);t=!1},d(s){s&&d(e),Pe(a,s),r&&r.d()}}}function bi(i){let e,l,t,n,a,o;const r=[pi,vi],s=[];function u(c,_){var f;return _&1&&(l=null),l==null&&(l=!!((f=c[9].mime_type)!=null&&f.includes("audio"))),l?0:1}return t=u(i,-1),n=s[t]=r[t](i),{c(){e=A("div"),n.c(),this.h()},l(c){e=j(c,"DIV",{class:!0,"aria-label":!0});var _=D(e);n.l(_),_.forEach(d),this.h()},h(){h(e,"class","example-icon svelte-9pi8y1"),h(e,"aria-label",a=`File: ${i[9].orig_name}`)},m(c,_){C(c,e,_),s[t].m(e,null),o=!0},p(c,_){let f=t;t=u(c,_),t!==f&&(O(),w(s[f],1,1,()=>{s[f]=null}),W(),n=s[t],n||(n=s[t]=r[t](c),n.c()),g(n,1),n.m(e,null)),(!o||_&1&&a!==(a=`File: ${c[9].orig_name}`))&&h(e,"aria-label",a)},i(c){o||(g(n),o=!0)},o(c){w(n),o=!1},d(c){c&&d(e),s[t].d()}}}function wi(i){let e,l,t,n,a=i[8]===3&&i[6].files.length>4&&lt(i);return{c(){e=A("div"),l=A("video"),n=X(),a&&a.c(),this.h()},l(o){e=j(o,"DIV",{class:!0});var r=D(e);l=j(r,"VIDEO",{class:!0,src:!0,"aria-hidden":!0}),D(l).forEach(d),n=Y(r),a&&a.l(r),r.forEach(d),this.h()},h(){h(l,"class","example-image"),Ke(l.src,t=i[9].url)||h(l,"src",t),h(l,"aria-hidden","true"),h(e,"class","example-image-container svelte-9pi8y1")},m(o,r){C(o,e,r),N(e,l),N(e,n),a&&a.m(e,null)},p(o,r){r&1&&!Ke(l.src,t=o[9].url)&&h(l,"src",t),o[8]===3&&o[6].files.length>4?a?a.p(o,r):(a=lt(o),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i:y,o:y,d(o){o&&d(e),a&&a.d()}}}function ki(i){let e,l,t,n;l=new ye({props:{class:"example-image",src:i[9].url,alt:i[9].orig_name||`Example image ${i[8]+1}`}});let a=i[8]===3&&i[6].files.length>4&&tt(i);return{c(){e=A("div"),M(l.$$.fragment),t=X(),a&&a.c(),this.h()},l(o){e=j(o,"DIV",{class:!0});var r=D(e);Z(l.$$.fragment,r),t=Y(r),a&&a.l(r),r.forEach(d),this.h()},h(){h(e,"class","example-image-container svelte-9pi8y1")},m(o,r){C(o,e,r),B(l,e,null),N(e,t),a&&a.m(e,null),n=!0},p(o,r){const s={};r&1&&(s.src=o[9].url),r&1&&(s.alt=o[9].orig_name||`Example image ${o[8]+1}`),l.$set(s),o[8]===3&&o[6].files.length>4?a?a.p(o,r):(a=tt(o),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i(o){n||(g(l.$$.fragment,o),n=!0)},o(o){w(l.$$.fragment,o),n=!1},d(o){o&&d(e),T(l),a&&a.d()}}}function vi(i){let e,l;return e=new ul({}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function pi(i){let e,l;return e=new pt({}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function lt(i){let e,l,t=i[6].files.length-4+"",n,a;return{c(){e=A("div"),l=oe("+"),n=oe(t),this.h()},l(o){e=j(o,"DIV",{class:!0,role:!0,"aria-label":!0});var r=D(e);l=re(r,"+"),n=re(r,t),r.forEach(d),this.h()},h(){h(e,"class","image-overlay svelte-9pi8y1"),h(e,"role","status"),h(e,"aria-label",a=`${i[6].files.length-4} more files`)},m(o,r){C(o,e,r),N(e,l),N(e,n)},p(o,r){r&1&&t!==(t=o[6].files.length-4+"")&&ke(n,t),r&1&&a!==(a=`${o[6].files.length-4} more files`)&&h(e,"aria-label",a)},d(o){o&&d(e)}}}function tt(i){let e,l,t=i[6].files.length-4+"",n,a;return{c(){e=A("div"),l=oe("+"),n=oe(t),this.h()},l(o){e=j(o,"DIV",{class:!0,role:!0,"aria-label":!0});var r=D(e);l=re(r,"+"),n=re(r,t),r.forEach(d),this.h()},h(){h(e,"class","image-overlay svelte-9pi8y1"),h(e,"role","status"),h(e,"aria-label",a=`${i[6].files.length-4} more files`)},m(o,r){C(o,e,r),N(e,l),N(e,n)},p(o,r){r&1&&t!==(t=o[6].files.length-4+"")&&ke(n,t),r&1&&a!==(a=`${o[6].files.length-4} more files`)&&h(e,"aria-label",a)},d(o){o&&d(e)}}}function nt(i){let e,l,t,n,a,o;const r=[ki,wi,bi],s=[];function u(c,_){var f,m;return _&1&&(e=null),_&1&&(l=null),e==null&&(e=!!((f=c[9].mime_type)!=null&&f.includes("image"))),e?0:(l==null&&(l=!!((m=c[9].mime_type)!=null&&m.includes("video"))),l?1:2)}return t=u(i,-1),n=s[t]=r[t](i),{c(){n.c(),a=J()},l(c){n.l(c),a=J()},m(c,_){s[t].m(c,_),C(c,a,_),o=!0},p(c,_){let f=t;t=u(c,_),t===f?s[t].p(c,_):(O(),w(s[f],1,1,()=>{s[f]=null}),W(),n=s[t],n?n.p(c,_):(n=s[t]=r[t](c),n.c()),g(n,1),n.m(a.parentNode,a))},i(c){o||(g(n),o=!0)},o(c){w(n),o=!1},d(c){c&&d(a),s[t].d(c)}}}function it(i){let e,l,t,n=i[6].files.length-4+"",a,o;return{c(){e=A("div"),l=A("div"),t=oe("+"),a=oe(n),this.h()},l(r){e=j(r,"DIV",{class:!0});var s=D(e);l=j(s,"DIV",{class:!0,role:!0,"aria-label":!0});var u=D(l);t=re(u,"+"),a=re(u,n),u.forEach(d),s.forEach(d),this.h()},h(){h(l,"class","file-overlay svelte-9pi8y1"),h(l,"role","status"),h(l,"aria-label",o=`${i[6].files.length-4} more files`),h(e,"class","example-icon svelte-9pi8y1")},m(r,s){C(r,e,s),N(e,l),N(l,t),N(l,a)},p(r,s){s&1&&n!==(n=r[6].files.length-4+"")&&ke(a,n),s&1&&o!==(o=`${r[6].files.length-4} more files`)&&h(l,"aria-label",o)},d(r){r&&d(e)}}}function at(i){let e,l,t,n,a,o,r,s=(i[6].display_text||i[6].text)+"",u,c,_,f,m,b;const k=[_i,ui,fi],p=[];function I(q,H){var F,z,L,R;return(z=(F=q[6])==null?void 0:F.icon)!=null&&z.url?0:((R=(L=q[6])==null?void 0:L.icon)==null?void 0:R.mime_type)==="text"?1:q[6].files!==void 0&&q[6].files.length>0?2:-1}~(t=I(i))&&(n=p[t]=k[t](i));function U(){return i[4](i[8],i[6])}return{c(){e=A("button"),l=A("div"),n&&n.c(),a=X(),o=A("div"),r=A("span"),u=oe(s),c=X(),this.h()},l(q){e=j(q,"BUTTON",{class:!0,"aria-label":!0});var H=D(e);l=j(H,"DIV",{class:!0});var F=D(l);n&&n.l(F),a=Y(F),o=j(F,"DIV",{class:!0});var z=D(o);r=j(z,"SPAN",{class:!0});var L=D(r);u=re(L,s),L.forEach(d),z.forEach(d),F.forEach(d),c=Y(H),H.forEach(d),this.h()},h(){h(r,"class","example-text svelte-9pi8y1"),h(o,"class","example-text-content svelte-9pi8y1"),h(l,"class","example-content svelte-9pi8y1"),h(e,"class","example svelte-9pi8y1"),h(e,"aria-label",_=`Select example ${i[8]+1}: ${i[6].display_text||i[6].text}`)},m(q,H){C(q,e,H),N(e,l),~t&&p[t].m(l,null),N(l,a),N(l,o),N(o,r),N(r,u),N(e,c),f=!0,m||(b=Be(e,"click",U),m=!0)},p(q,H){i=q;let F=t;t=I(i),t===F?~t&&p[t].p(i,H):(n&&(O(),w(p[F],1,1,()=>{p[F]=null}),W()),~t?(n=p[t],n?n.p(i,H):(n=p[t]=k[t](i),n.c()),g(n,1),n.m(l,a)):n=null),(!f||H&1)&&s!==(s=(i[6].display_text||i[6].text)+"")&&ke(u,s),(!f||H&1&&_!==(_=`Select example ${i[8]+1}: ${i[6].display_text||i[6].text}`))&&h(e,"aria-label",_)},i(q){f||(g(n),f=!0)},o(q){w(n),f=!1},d(q){q&&d(e),~t&&p[t].d(),m=!1,b()}}}function Ei(i){let e,l,t,n=i[1]!==null&&$l(i),a=i[0]!==null&&et(i);return{c(){e=A("div"),n&&n.c(),l=X(),a&&a.c(),this.h()},l(o){e=j(o,"DIV",{class:!0,role:!0});var r=D(e);n&&n.l(r),l=Y(r),a&&a.l(r),r.forEach(d),this.h()},h(){h(e,"class","placeholder-content svelte-9pi8y1"),h(e,"role","complementary")},m(o,r){C(o,e,r),n&&n.m(e,null),N(e,l),a&&a.m(e,null),t=!0},p(o,[r]){o[1]!==null?n?(n.p(o,r),r&2&&g(n,1)):(n=$l(o),n.c(),g(n,1),n.m(e,l)):n&&(O(),w(n,1,1,()=>{n=null}),W()),o[0]!==null?a?(a.p(o,r),r&1&&g(a,1)):(a=et(o),a.c(),g(a,1),a.m(e,null)):a&&(O(),w(a,1,1,()=>{a=null}),W())},i(o){t||(g(n),g(a),t=!0)},o(o){w(n),w(a),t=!1},d(o){o&&d(e),n&&n.d(),a&&a.d()}}}function Ci(i,e,l){let{examples:t=null}=e,{placeholder:n=null}=e,{latex_delimiters:a}=e;const o=sl();function r(u,c){const _=typeof c=="string"?{text:c}:c;o("example_select",{index:u,value:{text:_.text,files:_.files}})}const s=(u,c)=>r(u,typeof c=="string"?{text:c}:c);return i.$$set=u=>{"examples"in u&&l(0,t=u.examples),"placeholder"in u&&l(1,n=u.placeholder),"latex_delimiters"in u&&l(2,a=u.latex_delimiters)},[t,n,a,r,s]}class Vi extends te{constructor(e){super(),ne(this,e,Ci,Ei,ie,{examples:0,placeholder:1,latex_delimiters:2})}}function Ii(i){let e,l;return e=new de({props:{Icon:i[0]?Re:Xe,label:i[0]?"Copied conversation":"Copy conversation"}}),e.$on("click",i[1]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,[n]){const a={};n&1&&(a.Icon=t[0]?Re:Xe),n&1&&(a.label=t[0]?"Copied conversation":"Copy conversation"),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Di(i,e,l){let t=!1,{value:n}=e,{watermark:a=null}=e,o;function r(){l(0,t=!0),o&&clearTimeout(o),o=setTimeout(()=>{l(0,t=!1)},1e3)}const s=()=>{if(n){const c=n.map(f=>f.type==="text"?`${f.role}: ${f.content}`:`${f.role}: ${f.content.value.url}`).join(`

`),_=a?`${c}

${a}`:c;navigator.clipboard.writeText(_).catch(f=>{console.error("Failed to copy conversation: ",f)})}};async function u(){"clipboard"in navigator&&(s(),r())}return bt(()=>{o&&clearTimeout(o)}),i.$$set=c=>{"value"in c&&l(2,n=c.value),"watermark"in c&&l(3,a=c.watermark)},[t,u,n,a]}class zi extends te{constructor(e){super(),ne(this,e,Di,Ii,ie,{value:2,watermark:3})}}function ot(i,e,l){const t=i.slice();return t[61]=e[l],t[63]=l,t}function rt(i,e,l){const t=i.slice();t[64]=e[l],t[71]=l;const n=t[64][0].role==="user"?"user":"bot";t[65]=n;const a=t[16][t[65]==="user"?0:1];t[66]=a;const o=t[16][t[65]==="user"?0:1];t[67]=o;const r=t[39].slice(0,t[71]).filter(u=>u[0].role==="assistant").length;t[68]=r;const s=t[65]==="bot"&&t[10]&&t[10][t[68]]?t[10][t[68]]:null;return t[69]=s,t}function st(i){let e,l;return e=new vt({props:{$$slots:{default:[Hi]},$$scope:{ctx:i}}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&2109441|n[1]&1|n[2]&1024&&(a.$$scope={dirty:n,ctx:t}),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function ft(i){let e,l;return e=new de({props:{Icon:Jt}}),e.$on("click",i[48]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p:y,i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function ut(i){let e,l;return e=new zi({props:{value:i[0],watermark:i[31]}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&1&&(a.value=t[0]),n[1]&1&&(a.watermark=t[31]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Hi(i){let e,l,t,n,a,o=i[12]&&ft(i);l=new de({props:{Icon:Gt,label:i[21]("chatbot.clear")}}),l.$on("click",i[49]);let r=i[13]&&ut(i);return{c(){o&&o.c(),e=X(),M(l.$$.fragment),t=X(),r&&r.c(),n=J()},l(s){o&&o.l(s),e=Y(s),Z(l.$$.fragment,s),t=Y(s),r&&r.l(s),n=J()},m(s,u){o&&o.m(s,u),C(s,e,u),B(l,s,u),C(s,t,u),r&&r.m(s,u),C(s,n,u),a=!0},p(s,u){s[12]?o?(o.p(s,u),u[0]&4096&&g(o,1)):(o=ft(s),o.c(),g(o,1),o.m(e.parentNode,e)):o&&(O(),w(o,1,1,()=>{o=null}),W());const c={};u[0]&2097152&&(c.label=s[21]("chatbot.clear")),l.$set(c),s[13]?r?(r.p(s,u),u[0]&8192&&g(r,1)):(r=ut(s),r.c(),g(r,1),r.m(n.parentNode,n)):r&&(O(),w(r,1,1,()=>{r=null}),W())},i(s){a||(g(o),g(l.$$.fragment,s),g(r),a=!0)},o(s){w(o),w(l.$$.fragment,s),w(r),a=!1},d(s){s&&(d(e),d(t),d(n)),o&&o.d(s),T(l,s),r&&r.d(s)}}}function Li(i){let e,l;return e=new Vi({props:{examples:i[26],placeholder:i[23],latex_delimiters:i[4]}}),e.$on("example_select",i[54]),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&67108864&&(a.examples=t[26]),n[0]&8388608&&(a.placeholder=t[23]),n[0]&16&&(a.latex_delimiters=t[4]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Ni(i){let e,l,t,n,a,o,r,s=me(i[39]),u=[];for(let b=0;b<s.length;b+=1)u[b]=ct(rt(i,s,b));const c=b=>w(u[b],1,1,()=>{u[b]=null}),_=[Bi,Mi],f=[];function m(b,k){return b[32]!=="hidden"&&b[5]?0:b[40]?1:-1}return~(t=m(i))&&(n=f[t]=_[t](i)),{c(){e=A("div");for(let b=0;b<u.length;b+=1)u[b].c();l=X(),n&&n.c(),this.h()},l(b){e=j(b,"DIV",{class:!0});var k=D(e);for(let p=0;p<u.length;p+=1)u[p].l(k);l=Y(k),n&&n.l(k),k.forEach(d),this.h()},h(){h(e,"class","message-wrap svelte-gjtrl6")},m(b,k){C(b,e,k);for(let p=0;p<u.length;p+=1)u[p]&&u[p].m(e,null);N(e,l),~t&&f[t].m(e,null),a=!0,o||(r=At(Pt.call(null,e)),o=!0)},p(b,k){if(k[0]&2071973855|k[1]&11583){s=me(b[39]);let I;for(I=0;I<s.length;I+=1){const U=rt(b,s,I);u[I]?(u[I].p(U,k),g(u[I],1)):(u[I]=ct(U),u[I].c(),g(u[I],1),u[I].m(e,l))}for(O(),I=s.length;I<u.length;I+=1)c(I);W()}let p=t;t=m(b),t===p?~t&&f[t].p(b,k):(n&&(O(),w(f[p],1,1,()=>{f[p]=null}),W()),~t?(n=f[t],n?n.p(b,k):(n=f[t]=_[t](b),n.c()),g(n,1),n.m(e,null)):n=null)},i(b){if(!a){for(let k=0;k<s.length;k+=1)g(u[k]);g(n),a=!0}},o(b){u=u.filter(Boolean);for(let k=0;k<u.length;k+=1)w(u[k]);w(n),a=!1},d(b){b&&d(e),Pe(u,b),~t&&f[t].d(),o=!1,r()}}}function _t(i){let e,l;return e=new zt({props:{layout:i[22],avatar_images:i[16]}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&4194304&&(a.layout=t[22]),n[0]&65536&&(a.avatar_images=t[16]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function ct(i){var c;let e,l,t,n,a;function o(..._){return i[50](i[64],i[71],..._)}function r(_){i[51](_)}let s={messages:i[64],display_consecutive_in_same_bubble:i[3],opposite_avatar_img:i[67],avatar_img:i[66],role:i[65],layout:i[22],dispatch:i[42],i18n:i[21],_fetch:i[1],line_breaks:i[19],theme_mode:i[20],target:i[34],upload:i[24],selectable:i[7],sanitize_html:i[17],render_markdown:i[18],rtl:i[14],i:i[71],value:i[0],latex_delimiters:i[4],_components:i[33],generating:i[6],msg_format:i[25],feedback_options:i[9],current_feedback:i[69],allow_tags:i[30],watermark:i[31],show_like:i[65]==="user"?i[8]&&i[29]:i[8],show_retry:i[27]&&Je(i[64],i[0]),show_undo:i[28]&&Je(i[64],i[0]),show_edit:i[11]==="all"||i[11]=="user"&&i[65]==="user"&&i[64].length>0&&i[64][i[64].length-1].type=="text",in_edit_mode:i[35]===i[71],show_copy_button:i[15],handle_action:o,scroll:i[41]?scroll:Ai,allow_file_downloads:i[2]};i[36]!==void 0&&(s.edit_messages=i[36]),e=new oi({props:s}),Ye.push(()=>jt(e,"edit_messages",r)),e.$on("copy",i[52]);let u=i[32]!=="hidden"&&i[6]&&i[64][i[64].length-1].role==="assistant"&&((c=i[64][i[64].length-1].metadata)==null?void 0:c.status)==="done"&&_t(i);return{c(){M(e.$$.fragment),t=X(),u&&u.c(),n=J()},l(_){Z(e.$$.fragment,_),t=Y(_),u&&u.l(_),n=J()},m(_,f){B(e,_,f),C(_,t,f),u&&u.m(_,f),C(_,n,f),a=!0},p(_,f){var b;i=_;const m={};f[1]&256&&(m.messages=i[64]),f[0]&8&&(m.display_consecutive_in_same_bubble=i[3]),f[0]&65536|f[1]&256&&(m.opposite_avatar_img=i[67]),f[0]&65536|f[1]&256&&(m.avatar_img=i[66]),f[1]&256&&(m.role=i[65]),f[0]&4194304&&(m.layout=i[22]),f[0]&2097152&&(m.i18n=i[21]),f[0]&2&&(m._fetch=i[1]),f[0]&524288&&(m.line_breaks=i[19]),f[0]&1048576&&(m.theme_mode=i[20]),f[1]&8&&(m.target=i[34]),f[0]&16777216&&(m.upload=i[24]),f[0]&128&&(m.selectable=i[7]),f[0]&131072&&(m.sanitize_html=i[17]),f[0]&262144&&(m.render_markdown=i[18]),f[0]&16384&&(m.rtl=i[14]),f[0]&1&&(m.value=i[0]),f[0]&16&&(m.latex_delimiters=i[4]),f[1]&4&&(m._components=i[33]),f[0]&64&&(m.generating=i[6]),f[0]&33554432&&(m.msg_format=i[25]),f[0]&512&&(m.feedback_options=i[9]),f[0]&1024|f[1]&256&&(m.current_feedback=i[69]),f[0]&1073741824&&(m.allow_tags=i[30]),f[1]&1&&(m.watermark=i[31]),f[0]&536871168|f[1]&256&&(m.show_like=i[65]==="user"?i[8]&&i[29]:i[8]),f[0]&134217729|f[1]&256&&(m.show_retry=i[27]&&Je(i[64],i[0])),f[0]&268435457|f[1]&256&&(m.show_undo=i[28]&&Je(i[64],i[0])),f[0]&2048|f[1]&256&&(m.show_edit=i[11]==="all"||i[11]=="user"&&i[65]==="user"&&i[64].length>0&&i[64][i[64].length-1].type=="text"),f[1]&16&&(m.in_edit_mode=i[35]===i[71]),f[0]&32768&&(m.show_copy_button=i[15]),f[1]&288&&(m.handle_action=o),f[0]&4&&(m.allow_file_downloads=i[2]),!l&&f[1]&32&&(l=!0,m.edit_messages=i[36],St(()=>l=!1)),e.$set(m),i[32]!=="hidden"&&i[6]&&i[64][i[64].length-1].role==="assistant"&&((b=i[64][i[64].length-1].metadata)==null?void 0:b.status)==="done"?u?(u.p(i,f),f[0]&64|f[1]&258&&g(u,1)):(u=_t(i),u.c(),g(u,1),u.m(n.parentNode,n)):u&&(O(),w(u,1,1,()=>{u=null}),W())},i(_){a||(g(e.$$.fragment,_),g(u),a=!0)},o(_){w(e.$$.fragment,_),w(u),a=!1},d(_){_&&(d(t),d(n)),T(e,_),u&&u.d(_)}}}function Mi(i){let e,l=me(i[40]),t=[];for(let n=0;n<l.length;n+=1)t[n]=ht(ot(i,l,n));return{c(){e=A("div");for(let n=0;n<t.length;n+=1)t[n].c();this.h()},l(n){e=j(n,"DIV",{class:!0});var a=D(e);for(let o=0;o<t.length;o+=1)t[o].l(a);a.forEach(d),this.h()},h(){h(e,"class","options svelte-gjtrl6")},m(n,a){C(n,e,a);for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(e,null)},p(n,a){if(a[1]&2560){l=me(n[40]);let o;for(o=0;o<l.length;o+=1){const r=ot(n,l,o);t[o]?t[o].p(r,a):(t[o]=ht(r),t[o].c(),t[o].m(e,null))}for(;o<t.length;o+=1)t[o].d(1);t.length=l.length}},i:y,o:y,d(n){n&&d(e),Pe(t,n)}}}function Bi(i){let e,l;return e=new zt({props:{layout:i[22],avatar_images:i[16]}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&4194304&&(a.layout=t[22]),n[0]&65536&&(a.avatar_images=t[16]),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function ht(i){let e,l=(i[61].label||i[61].value)+"",t,n,a,o;function r(){return i[53](i[63],i[61])}return{c(){e=A("button"),t=oe(l),n=X(),this.h()},l(s){e=j(s,"BUTTON",{class:!0});var u=D(e);t=re(u,l),n=Y(u),u.forEach(d),this.h()},h(){h(e,"class","option svelte-gjtrl6")},m(s,u){C(s,e,u),N(e,t),N(e,n),a||(o=Be(e,"click",r),a=!0)},p(s,u){i=s,u[1]&512&&l!==(l=(i[61].label||i[61].value)+"")&&ke(t,l)},d(s){s&&d(e),a=!1,o()}}}function mt(i){let e,l,t;return l=new de({props:{Icon:tn,label:"Scroll down",size:"large"}}),l.$on("click",i[43]),{c(){e=A("div"),M(l.$$.fragment),this.h()},l(n){e=j(n,"DIV",{class:!0});var a=D(e);Z(l.$$.fragment,a),a.forEach(d),this.h()},h(){h(e,"class","scroll-down-button-container svelte-gjtrl6")},m(n,a){C(n,e,a),B(l,e,null),t=!0},p:y,i(n){t||(g(l.$$.fragment,n),t=!0)},o(n){w(l.$$.fragment,n),t=!1},d(n){n&&d(e),T(l)}}}function Ti(i){let e,l,t,n,a,o,r,s,u=i[0]!==null&&i[0].length>0&&st(i);const c=[Ni,Li],_=[];function f(b,k){return b[0]!==null&&b[0].length>0&&b[39]!==null?0:1}t=f(i),n=_[t]=c[t](i);let m=i[38]&&mt(i);return{c(){u&&u.c(),e=X(),l=A("div"),n.c(),o=X(),m&&m.c(),r=J(),this.h()},l(b){u&&u.l(b),e=Y(b),l=j(b,"DIV",{class:!0,role:!0,"aria-label":!0,"aria-live":!0});var k=D(l);n.l(k),k.forEach(d),o=Y(b),m&&m.l(b),r=J(),this.h()},h(){h(l,"class",a=Ge(i[22]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-gjtrl6"),h(l,"role","log"),h(l,"aria-label","chatbot conversation"),h(l,"aria-live","polite")},m(b,k){u&&u.m(b,k),C(b,e,k),C(b,l,k),_[t].m(l,null),i[55](l),C(b,o,k),m&&m.m(b,k),C(b,r,k),s=!0},p(b,k){b[0]!==null&&b[0].length>0?u?(u.p(b,k),k[0]&1&&g(u,1)):(u=st(b),u.c(),g(u,1),u.m(e.parentNode,e)):u&&(O(),w(u,1,1,()=>{u=null}),W());let p=t;t=f(b),t===p?_[t].p(b,k):(O(),w(_[p],1,1,()=>{_[p]=null}),W(),n=_[t],n?n.p(b,k):(n=_[t]=c[t](b),n.c()),g(n,1),n.m(l,null)),(!s||k[0]&4194304&&a!==(a=Ge(b[22]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-gjtrl6"))&&h(l,"class",a),b[38]?m?(m.p(b,k),k[1]&128&&g(m,1)):(m=mt(b),m.c(),g(m,1),m.m(r.parentNode,r)):m&&(O(),w(m,1,1,()=>{m=null}),W())},i(b){s||(g(u),g(n),g(m),s=!0)},o(b){w(u),w(n),w(m),s=!1},d(b){b&&(d(e),d(l),d(o),d(r)),u&&u.d(b),_[t].d(),i[55](null),m&&m.d(b)}}}const Ai=()=>{};function ji(i,e,l){let t,n,{value:a=[]}=e,o=null,{_fetch:r}=e,{load_component:s}=e,{allow_file_downloads:u}=e,{display_consecutive_in_same_bubble:c}=e,_={};const f=typeof window<"u";async function m(){l(33,_=await fn(un(a),_,s))}let{latex_delimiters:b}=e,{pending_message:k=!1}=e,{generating:p=!1}=e,{selectable:I=!1}=e,{likeable:U=!1}=e,{feedback_options:q}=e,{feedback_value:H=null}=e,{editable:F=null}=e,{show_share_button:z=!1}=e,{show_copy_all_button:L=!1}=e,{rtl:R=!1}=e,{show_copy_button:G=!1}=e,{avatar_images:x=[null,null]}=e,{sanitize_html:le=!0}=e,{render_markdown:P=!0}=e,{line_breaks:ce=!0}=e,{autoscroll:S=!0}=e,{theme_mode:Q}=e,{i18n:Te}=e,{layout:Ae="bubble"}=e,{placeholder:Ce=null}=e,{upload:ve}=e,{msg_format:Ve="tuples"}=e,{examples:Ie=null}=e,{_retryable:je=!1}=e,{_undoable:De=!1}=e,{like_user_message:ge=!1}=e,{allow_tags:ze=!1}=e,{watermark:He=null}=e,{show_progress:Le="full"}=e,Se=null,Ne=null,pe=[];il(()=>{l(34,Se=document.querySelector("div.gradio-container"))});let ae,qe=!1;const se=sl();function V(){return ae&&ae.offsetHeight+ae.scrollTop>ae.scrollHeight-100}function _e(){ae&&(ae.scrollTo(0,ae.scrollHeight),l(38,qe=!1))}async function Fe(){S&&V()&&(await qt(),await new Promise(E=>setTimeout(E,300)),_e())}il(()=>{S&&_e(),Fe()}),il(()=>{function E(){V()?l(38,qe=!1):l(38,qe=!0)}return ae==null||ae.addEventListener("scroll",E),()=>{ae==null||ae.removeEventListener("scroll",E)}});function be(E,we,fe){if(fe==="undo"||fe==="retry"){const Me=a;let Ee=Me.length-1;for(;Me[Ee].role==="assistant";)Ee--;se(fe,{index:Me[Ee].index,value:Me[Ee].content})}else if(fe=="edit")l(35,Ne=E),pe.push(we.content);else if(fe=="edit_cancel")l(35,Ne=null);else if(fe=="edit_submit")l(35,Ne=null),se("edit",{index:we.index,value:pe[E].slice(),previous_value:we.content});else{let Me=fe==="Like"?!0:fe==="Dislike"?!1:fe||"";if(Ve==="tuples")se("like",{index:we.index,value:we.content,liked:Me});else{if(!t)return;const Ee=t[E],[Lt,Ui]=[Ee[0],Ee[Ee.length-1]];se("like",{index:Lt.index,value:Ee.map(Nt=>Nt.content),liked:Me})}}}function Qe(){if(!a||!t||t.length===0)return;const E=t[t.length-1];if(E[0].role==="assistant")return E[E.length-1].options}const xe=async()=>{try{const E=await nn(a);se("share",{description:E})}catch(E){console.error(E);let we=E instanceof Zt?E.message:"Share failed.";se("error",we)}},$e=()=>se("clear"),el=(E,we,fe)=>{fe=="edit"&&pe.splice(0,pe.length),fe==="edit"||fe==="edit_submit"?E.forEach((Me,Ee)=>{be(fe==="edit"?we:Ee,Me,fe)}):be(we,E[0],fe)};function ll(E){pe=E,l(36,pe)}const tl=E=>se("copy",E.detail),nl=(E,we)=>se("option_select",{index:E,value:we.value}),v=E=>se("example_select",E.detail);function Ht(E){Ye[E?"unshift":"push"](()=>{ae=E,l(37,ae)})}return i.$$set=E=>{"value"in E&&l(0,a=E.value),"_fetch"in E&&l(1,r=E._fetch),"load_component"in E&&l(45,s=E.load_component),"allow_file_downloads"in E&&l(2,u=E.allow_file_downloads),"display_consecutive_in_same_bubble"in E&&l(3,c=E.display_consecutive_in_same_bubble),"latex_delimiters"in E&&l(4,b=E.latex_delimiters),"pending_message"in E&&l(5,k=E.pending_message),"generating"in E&&l(6,p=E.generating),"selectable"in E&&l(7,I=E.selectable),"likeable"in E&&l(8,U=E.likeable),"feedback_options"in E&&l(9,q=E.feedback_options),"feedback_value"in E&&l(10,H=E.feedback_value),"editable"in E&&l(11,F=E.editable),"show_share_button"in E&&l(12,z=E.show_share_button),"show_copy_all_button"in E&&l(13,L=E.show_copy_all_button),"rtl"in E&&l(14,R=E.rtl),"show_copy_button"in E&&l(15,G=E.show_copy_button),"avatar_images"in E&&l(16,x=E.avatar_images),"sanitize_html"in E&&l(17,le=E.sanitize_html),"render_markdown"in E&&l(18,P=E.render_markdown),"line_breaks"in E&&l(19,ce=E.line_breaks),"autoscroll"in E&&l(46,S=E.autoscroll),"theme_mode"in E&&l(20,Q=E.theme_mode),"i18n"in E&&l(21,Te=E.i18n),"layout"in E&&l(22,Ae=E.layout),"placeholder"in E&&l(23,Ce=E.placeholder),"upload"in E&&l(24,ve=E.upload),"msg_format"in E&&l(25,Ve=E.msg_format),"examples"in E&&l(26,Ie=E.examples),"_retryable"in E&&l(27,je=E._retryable),"_undoable"in E&&l(28,De=E._undoable),"like_user_message"in E&&l(29,ge=E.like_user_message),"allow_tags"in E&&l(30,ze=E.allow_tags),"watermark"in E&&l(31,He=E.watermark),"show_progress"in E&&l(32,Le=E.show_progress)},i.$$.update=()=>{i.$$.dirty[0]&1&&m(),i.$$.dirty[0]&33|i.$$.dirty[1]&4&&(a||k||_)&&Fe(),i.$$.dirty[0]&1|i.$$.dirty[1]&65536&&(yt(a,o)||(l(47,o=a),se("change"))),i.$$.dirty[0]&33554441&&l(39,t=a&&sn(a,Ve,c)),i.$$.dirty[0]&1&&l(40,n=a&&Qe())},[a,r,u,c,b,k,p,I,U,q,H,F,z,L,R,G,x,le,P,ce,Q,Te,Ae,Ce,ve,Ve,Ie,je,De,ge,ze,He,Le,_,Se,Ne,pe,ae,qe,t,n,f,se,_e,be,s,S,o,xe,$e,el,ll,tl,nl,v,Ht]}class Si extends te{constructor(e){super(),ne(this,e,ji,Ti,ie,{value:0,_fetch:1,load_component:45,allow_file_downloads:2,display_consecutive_in_same_bubble:3,latex_delimiters:4,pending_message:5,generating:6,selectable:7,likeable:8,feedback_options:9,feedback_value:10,editable:11,show_share_button:12,show_copy_all_button:13,rtl:14,show_copy_button:15,avatar_images:16,sanitize_html:17,render_markdown:18,line_breaks:19,autoscroll:46,theme_mode:20,i18n:21,layout:22,placeholder:23,upload:24,msg_format:25,examples:26,_retryable:27,_undoable:28,like_user_message:29,allow_tags:30,watermark:31,show_progress:32},null,[-1,-1,-1])}}const qi=Si;function dt(i){let e,l;const t=[{autoscroll:i[27].autoscroll},{i18n:i[27].i18n},i[30],{show_progress:i[30].show_progress==="hidden"?"hidden":"minimal"}];let n={};for(let a=0;a<t.length;a+=1)n=We(n,t[a]);return e=new Ot({props:n}),e.$on("clear_status",i[43]),{c(){M(e.$$.fragment)},l(a){Z(e.$$.fragment,a)},m(a,o){B(e,a,o),l=!0},p(a,o){const r=o[0]&1207959552?Ue(t,[o[0]&134217728&&{autoscroll:a[27].autoscroll},o[0]&134217728&&{i18n:a[27].i18n},o[0]&1073741824&&Oe(a[30]),o[0]&1073741824&&{show_progress:a[30].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(r)},i(a){l||(g(e.$$.fragment,a),l=!0)},o(a){w(e.$$.fragment,a),l=!1},d(a){T(e,a)}}}function gt(i){let e,l;return e=new Kt({props:{show_label:i[7],Icon:Yt,float:!0,label:i[6]||"Chatbot"}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&128&&(a.show_label=t[7]),n[0]&64&&(a.label=t[6]||"Chatbot"),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Fi(i){var s,u,c;let e,l,t,n,a,o=i[30]&&dt(i),r=i[7]&&gt(i);return n=new qi({props:{i18n:i[27].i18n,selectable:i[8],likeable:i[9],feedback_options:i[10],feedback_value:i[11],show_share_button:i[12],show_copy_all_button:i[15],value:i[41],latex_delimiters:i[26],display_consecutive_in_same_bubble:i[24],render_markdown:i[19],theme_mode:i[38],editable:i[35],pending_message:((s=i[30])==null?void 0:s.status)==="pending",generating:((u=i[30])==null?void 0:u.status)==="generating",rtl:i[13],show_copy_button:i[14],like_user_message:i[29],show_progress:((c=i[30])==null?void 0:c.show_progress)||"full",avatar_images:i[28],sanitize_html:i[16],line_breaks:i[20],autoscroll:i[21],layout:i[17],placeholder:i[36],examples:i[37],_retryable:i[22],_undoable:i[23],upload:i[44],_fetch:i[45],load_component:i[27].load_component,msg_format:i[18],allow_file_downloads:i[39],allow_tags:i[25],watermark:i[40]}}),n.$on("change",i[46]),n.$on("select",i[47]),n.$on("like",i[48]),n.$on("share",i[49]),n.$on("error",i[50]),n.$on("example_select",i[51]),n.$on("option_select",i[52]),n.$on("retry",i[53]),n.$on("undo",i[54]),n.$on("clear",i[55]),n.$on("copy",i[56]),n.$on("edit",i[57]),{c(){o&&o.c(),e=X(),l=A("div"),r&&r.c(),t=X(),M(n.$$.fragment),this.h()},l(_){o&&o.l(_),e=Y(_),l=j(_,"DIV",{class:!0});var f=D(l);r&&r.l(f),t=Y(f),Z(n.$$.fragment,f),f.forEach(d),this.h()},h(){h(l,"class","wrapper svelte-g3p8na")},m(_,f){o&&o.m(_,f),C(_,e,f),C(_,l,f),r&&r.m(l,null),N(l,t),B(n,l,null),a=!0},p(_,f){var b,k,p;_[30]?o?(o.p(_,f),f[0]&1073741824&&g(o,1)):(o=dt(_),o.c(),g(o,1),o.m(e.parentNode,e)):o&&(O(),w(o,1,1,()=>{o=null}),W()),_[7]?r?(r.p(_,f),f[0]&128&&g(r,1)):(r=gt(_),r.c(),g(r,1),r.m(l,t)):r&&(O(),w(r,1,1,()=>{r=null}),W());const m={};f[0]&134217728&&(m.i18n=_[27].i18n),f[0]&256&&(m.selectable=_[8]),f[0]&512&&(m.likeable=_[9]),f[0]&1024&&(m.feedback_options=_[10]),f[0]&2048&&(m.feedback_value=_[11]),f[0]&4096&&(m.show_share_button=_[12]),f[0]&32768&&(m.show_copy_all_button=_[15]),f[1]&1024&&(m.value=_[41]),f[0]&67108864&&(m.latex_delimiters=_[26]),f[0]&16777216&&(m.display_consecutive_in_same_bubble=_[24]),f[0]&524288&&(m.render_markdown=_[19]),f[1]&128&&(m.theme_mode=_[38]),f[1]&16&&(m.editable=_[35]),f[0]&1073741824&&(m.pending_message=((b=_[30])==null?void 0:b.status)==="pending"),f[0]&1073741824&&(m.generating=((k=_[30])==null?void 0:k.status)==="generating"),f[0]&8192&&(m.rtl=_[13]),f[0]&16384&&(m.show_copy_button=_[14]),f[0]&536870912&&(m.like_user_message=_[29]),f[0]&1073741824&&(m.show_progress=((p=_[30])==null?void 0:p.show_progress)||"full"),f[0]&268435456&&(m.avatar_images=_[28]),f[0]&65536&&(m.sanitize_html=_[16]),f[0]&1048576&&(m.line_breaks=_[20]),f[0]&2097152&&(m.autoscroll=_[21]),f[0]&131072&&(m.layout=_[17]),f[1]&32&&(m.placeholder=_[36]),f[1]&64&&(m.examples=_[37]),f[0]&4194304&&(m._retryable=_[22]),f[0]&8388608&&(m._undoable=_[23]),f[0]&134217728&&(m.upload=_[44]),f[0]&134217728&&(m._fetch=_[45]),f[0]&134217728&&(m.load_component=_[27].load_component),f[0]&262144&&(m.msg_format=_[18]),f[1]&256&&(m.allow_file_downloads=_[39]),f[0]&33554432&&(m.allow_tags=_[25]),f[1]&512&&(m.watermark=_[40]),n.$set(m)},i(_){a||(g(o),g(r),g(n.$$.fragment,_),a=!0)},o(_){w(o),w(r),w(n.$$.fragment,_),a=!1},d(_){_&&(d(e),d(l)),o&&o.d(_),r&&r.d(),T(n)}}}function Zi(i){let e,l;return e=new Ut({props:{elem_id:i[1],elem_classes:i[2],visible:i[3],padding:!1,scale:i[4],min_width:i[5],height:i[31],resizable:i[32],min_height:i[33],max_height:i[34],allow_overflow:!0,flex:!0,overflow_behavior:"auto",$$slots:{default:[Fi]},$$scope:{ctx:i}}}),{c(){M(e.$$.fragment)},l(t){Z(e.$$.fragment,t)},m(t,n){B(e,t,n),l=!0},p(t,n){const a={};n[0]&2&&(a.elem_id=t[1]),n[0]&4&&(a.elem_classes=t[2]),n[0]&8&&(a.visible=t[3]),n[0]&16&&(a.scale=t[4]),n[0]&32&&(a.min_width=t[5]),n[1]&1&&(a.height=t[31]),n[1]&2&&(a.resizable=t[32]),n[1]&4&&(a.min_height=t[33]),n[1]&8&&(a.max_height=t[34]),n[0]&2147483585|n[1]&134219760&&(a.$$scope={dirty:n,ctx:t}),e.$set(a)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){T(e,t)}}}function Pi(i,e,l){let{elem_id:t=""}=e,{elem_classes:n=[]}=e,{visible:a=!0}=e,{value:o=[]}=e,{scale:r=null}=e,{min_width:s=void 0}=e,{label:u}=e,{show_label:c=!0}=e,{root:_}=e,{_selectable:f=!1}=e,{likeable:m=!1}=e,{feedback_options:b=["Like","Dislike"]}=e,{feedback_value:k=null}=e,{show_share_button:p=!1}=e,{rtl:I=!1}=e,{show_copy_button:U=!0}=e,{show_copy_all_button:q=!1}=e,{sanitize_html:H=!0}=e,{layout:F="bubble"}=e,{type:z="tuples"}=e,{render_markdown:L=!0}=e,{line_breaks:R=!0}=e,{autoscroll:G=!0}=e,{_retryable:x=!1}=e,{_undoable:le=!1}=e,{group_consecutive_messages:P=!0}=e,{allow_tags:ce=!1}=e,{latex_delimiters:S}=e,{gradio:Q}=e,Te=[],{avatar_images:Ae=[null,null]}=e,{like_user_message:Ce=!1}=e,{loading_status:ve=void 0}=e,{height:Ve}=e,{resizable:Ie}=e,{min_height:je}=e,{max_height:De}=e,{editable:ge=null}=e,{placeholder:ze=null}=e,{examples:He=null}=e,{theme_mode:Le}=e,{allow_file_downloads:Se=!0}=e,{watermark:Ne=null}=e;const pe=()=>Q.dispatch("clear_status",ve),ae=(...v)=>Q.client.upload(...v),qe=(...v)=>Q.client.fetch(...v),se=()=>Q.dispatch("change",o),V=v=>Q.dispatch("select",v.detail),_e=v=>Q.dispatch("like",v.detail),Fe=v=>Q.dispatch("share",v.detail),be=v=>Q.dispatch("error",v.detail),Qe=v=>Q.dispatch("example_select",v.detail),xe=v=>Q.dispatch("option_select",v.detail),$e=v=>Q.dispatch("retry",v.detail),el=v=>Q.dispatch("undo",v.detail),ll=()=>{l(0,o=[]),Q.dispatch("clear")},tl=v=>Q.dispatch("copy",v.detail),nl=v=>{o===null||o.length===0||(z==="messages"?l(0,o[v.detail.index].content=v.detail.value,o):l(0,o[v.detail.index[0]][v.detail.index[1]]=v.detail.value,o),l(0,o),Q.dispatch("edit",v.detail))};return i.$$set=v=>{"elem_id"in v&&l(1,t=v.elem_id),"elem_classes"in v&&l(2,n=v.elem_classes),"visible"in v&&l(3,a=v.visible),"value"in v&&l(0,o=v.value),"scale"in v&&l(4,r=v.scale),"min_width"in v&&l(5,s=v.min_width),"label"in v&&l(6,u=v.label),"show_label"in v&&l(7,c=v.show_label),"root"in v&&l(42,_=v.root),"_selectable"in v&&l(8,f=v._selectable),"likeable"in v&&l(9,m=v.likeable),"feedback_options"in v&&l(10,b=v.feedback_options),"feedback_value"in v&&l(11,k=v.feedback_value),"show_share_button"in v&&l(12,p=v.show_share_button),"rtl"in v&&l(13,I=v.rtl),"show_copy_button"in v&&l(14,U=v.show_copy_button),"show_copy_all_button"in v&&l(15,q=v.show_copy_all_button),"sanitize_html"in v&&l(16,H=v.sanitize_html),"layout"in v&&l(17,F=v.layout),"type"in v&&l(18,z=v.type),"render_markdown"in v&&l(19,L=v.render_markdown),"line_breaks"in v&&l(20,R=v.line_breaks),"autoscroll"in v&&l(21,G=v.autoscroll),"_retryable"in v&&l(22,x=v._retryable),"_undoable"in v&&l(23,le=v._undoable),"group_consecutive_messages"in v&&l(24,P=v.group_consecutive_messages),"allow_tags"in v&&l(25,ce=v.allow_tags),"latex_delimiters"in v&&l(26,S=v.latex_delimiters),"gradio"in v&&l(27,Q=v.gradio),"avatar_images"in v&&l(28,Ae=v.avatar_images),"like_user_message"in v&&l(29,Ce=v.like_user_message),"loading_status"in v&&l(30,ve=v.loading_status),"height"in v&&l(31,Ve=v.height),"resizable"in v&&l(32,Ie=v.resizable),"min_height"in v&&l(33,je=v.min_height),"max_height"in v&&l(34,De=v.max_height),"editable"in v&&l(35,ge=v.editable),"placeholder"in v&&l(36,ze=v.placeholder),"examples"in v&&l(37,He=v.examples),"theme_mode"in v&&l(38,Le=v.theme_mode),"allow_file_downloads"in v&&l(39,Se=v.allow_file_downloads),"watermark"in v&&l(40,Ne=v.watermark)},i.$$.update=()=>{i.$$.dirty[0]&262145|i.$$.dirty[1]&2048&&l(41,Te=z==="tuples"?rn(o,_):on(o,_))},[o,t,n,a,r,s,u,c,f,m,b,k,p,I,U,q,H,F,z,L,R,G,x,le,P,ce,S,Q,Ae,Ce,ve,Ve,Ie,je,De,ge,ze,He,Le,Se,Ne,Te,_,pe,ae,qe,se,V,_e,Fe,be,Qe,xe,$e,el,ll,tl,nl]}class ea extends te{constructor(e){super(),ne(this,e,Pi,Zi,ie,{elem_id:1,elem_classes:2,visible:3,value:0,scale:4,min_width:5,label:6,show_label:7,root:42,_selectable:8,likeable:9,feedback_options:10,feedback_value:11,show_share_button:12,rtl:13,show_copy_button:14,show_copy_all_button:15,sanitize_html:16,layout:17,type:18,render_markdown:19,line_breaks:20,autoscroll:21,_retryable:22,_undoable:23,group_consecutive_messages:24,allow_tags:25,latex_delimiters:26,gradio:27,avatar_images:28,like_user_message:29,loading_status:30,height:31,resizable:32,min_height:33,max_height:34,editable:35,placeholder:36,examples:37,theme_mode:38,allow_file_downloads:39,watermark:40},null,[-1,-1])}}export{qi as BaseChatBot,ea as default};
//# sourceMappingURL=Index.Bv2l-SRz.js.map
