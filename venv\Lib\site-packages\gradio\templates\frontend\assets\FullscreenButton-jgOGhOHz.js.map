{"version": 3, "file": "FullscreenButton-jgOGhOHz.js", "sources": ["../../../../js/icons/src/Maximize.svelte", "../../../../js/icons/src/Minimize.svelte", "../../../../js/atoms/src/FullscreenButton.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-maximize\"\n\twidth=\"100%\"\n\theight=\"100%\"\n>\n\t<path\n\t\td=\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\"\n\t>\n\t</path>\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-minimize\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\t><path\n\t\td=\"M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3\"\n\t></path></svg\n>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Maximize, Minimize } from \"@gradio/icons\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tfullscreen: boolean;\n\t}>();\n\n\texport let fullscreen;\n</script>\n\n{#if fullscreen}\n\t<IconButton\n\t\tIcon={Minimize}\n\t\tlabel=\"Exit fullscreen mode\"\n\t\ton:click={() => dispatch(\"fullscreen\", false)}\n\t/>\n{:else}\n\t<IconButton\n\t\tIcon={Maximize}\n\t\tlabel=\"Fullscreen\"\n\t\ton:click={() => dispatch(\"fullscreen\", true)}\n\t/>\n{/if}\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "Maximize", "Minimize", "ctx", "dispatch", "createEventDispatcher", "fullscreen", "$$props"], "mappings": "8xBAAAA,EAgBKC,EAAAC,EAAAC,CAAA,EAJJC,EAGMF,EAAAG,CAAA,4qBCfPL,EAcAC,EAAAC,EAAAC,CAAA,EAHEC,EAEOF,EAAAG,CAAA,0WCZ8B,EAAA,OAAA,6EAmB/BC,EAAQ,MAAA,YAAA,0MANRC,0PAFHC,EAAU,CAAA,EAAA,gUAPRC,EAAWC,IAIN,GAAA,CAAA,WAAAC,CAAA,EAAAC,cAOMH,EAAS,aAAc,EAAK,QAM5BA,EAAS,aAAc,EAAI"}