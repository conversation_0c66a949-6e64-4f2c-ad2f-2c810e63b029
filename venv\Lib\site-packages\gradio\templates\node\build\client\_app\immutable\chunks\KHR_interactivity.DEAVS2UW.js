const __vite__fileDeps=["./flowGraphPlayAnimationBlock.DMpd9AzC.js","./declarationMapper.UBCwU7BT.js","./index.BoI39RQH.js","./preload-helper.D6kgxu3v.js","./animationGroup.CYmPDV4x.js","./bone.F-7hGbXp.js","./flowGraphStopAnimationBlock.DRdSCS1A.js","./flowGraphPauseAnimationBlock.tGoUJuwx.js","./flowGraphInterpolationBlock.Clp4-F7o.js","./flowGraphSceneReadyEventBlock.D16NDYdp.js","./flowGraphSceneTickEventBlock.Bwibrsmg.js","./flowGraphSendCustomEventBlock.ChWfcsQb.js","./flowGraphReceiveCustomEventBlock.Zl1e7Elp.js","./flowGraphMeshPickEventBlock.Bkgm0TYl.js","./flowGraphMathBlocks.BgQ6jpID.js","./flowGraphBinaryOperationBlock.CI0NxKdM.js","./flowGraphCachedOperationBlock.CtP7sxiu.js","./flowGraphUnaryOperationBlock.ZI1fHq8c.js","./flowGraphTernaryOperationBlock.DiGI0Nsv.js","./flowGraphVectorMathBlocks.B5YfnEFA.js","./flowGraphMatrixMathBlocks.DxazJ9N_.js","./flowGraphBranchBlock.B5KPMpSk.js","./flowGraphSetDelayBlock.NAhfwlgz.js","./flowGraphCancelDelayBlock.Bo445-z6.js","./flowGraphCounterBlock.B4fjt7HE.js","./flowGraphDebounceBlock.BBFhWo9P.js","./flowGraphThrottleBlock.D9nMlAUr.js","./flowGraphDoNBlock.DCMR9_vT.js","./flowGraphFlipFlopBlock.CA4_-zai.js","./flowGraphForLoopBlock.BWvb8YvA.js","./flowGraphMultiGateBlock.KML6_2T-.js","./flowGraphSequenceBlock.CEbTwj2T.js","./flowGraphSwitchBlock.rlj1f3Pb.js","./flowGraphWaitAllBlock.Bj0x3SHG.js","./flowGraphWhileLoopBlock.CE8CW0jy.js","./flowGraphConsoleLogBlock.cIadvORr.js","./flowGraphConditionalDataBlock.BpscsBV2.js","./flowGraphConstantBlock.Cf9AYaf0.js","./flowGraphTransformCoordinatesSystemBlock.DdG5wdid.js","./flowGraphGetAssetBlock.B9sydsa6.js","./flowGraphGetPropertyBlock.CvJub6Jr.js","./flowGraphSetPropertyBlock.BrAMshGe.js","./flowGraphGetVariableBlock.C49Sb2GW.js","./flowGraphSetVariableBlock.CWwy6I9C.js","./flowGraphJsonPointerParserBlock.B04B8uAV.js","./flowGraphMathCombineExtractBlocks.DpvecDoq.js","./flowGraphTypeToTypeBlocks.DFY5E9Xa.js","./flowGraphEasingBlock.1niIOCug.js","./flowGraphBezierCurveEasingBlock.BqfcWA2V.js","./flowGraphPointerOverEventBlock.I7XsC6gG.js","./flowGraphPointerOutEventBlock.B3OaQD4T.js","./flowGraphContextBlock.DHb7kEHn.js","./flowGraphArrayIndexBlock.B03sfcvL.js","./flowGraphCodeExecutionBlock.BITVoFWe.js","./flowGraphIndexOfBlock.cBkAYfIL.js","./flowGraphFunctionReferenceBlock.DYL4rOb8.js","./flowGraphDataSwitchBlock.CRZGNJmJ.js","./flowGraphGLTFDataProvider.BfhrUZfi.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as a}from"./preload-helper.D6kgxu3v.js";import{M as yt,ay as kt,V as ut,az as vt,Q as X,C as Gt,aS as Bt,h as p,_ as It,s as Ft,e as A,O as W,R as pt,ax as nt,an as Tt,ao as Vt}from"./index.BoI39RQH.js";import{n as bt,p as Pt,F as Ct,g as Ot,r as xt,s as at}from"./declarationMapper.UBCwU7BT.js";import{G as Dt,A as x}from"./objectModelMapping.ha_8hIyl.js";function At(n){return n==="Mesh"||n==="AbstractMesh"||n==="GroundMesh"||n==="InstanceMesh"||n==="LinesMesh"||n==="GoldbergMesh"||n==="GreasedLineMesh"||n==="TrailMesh"}function ht(n){return n==="Vector2"||n==="Vector3"||n==="Vector4"||n==="Quaternion"||n==="Color3"||n==="Color4"}function Rt(n){return n==="Matrix"||n==="Matrix2D"||n==="Matrix3D"}function Lt(n){return n==="AnimationGroup"}function Nt(n,t,e=!1){if(n==="Vector2")return kt.FromArray(t);if(n==="Vector3")return e&&(t[2]*=-1),ut.FromArray(t);if(n==="Vector4")return vt.FromArray(t);if(n==="Quaternion")return e&&(t[2]*=-1,t[3]*=-1),X.FromArray(t);if(n==="Color3")return new Gt(t[0],t[1],t[2]);if(n==="Color4")return new Bt(t[0],t[1],t[2],t[3]);throw new Error(`Unknown vector class name ${n}`)}function tt(n,t,e){var r;const o=((r=t==null?void 0:t.getClassName)==null?void 0:r.call(t))??"";if(ht(o)||Rt(o))e[n]={value:t.asArray(),className:o};else if(o==="FlowGraphInteger")e[n]={value:t.value,className:o};else if(o&&(t.id||t.name))e[n]={id:t.id,name:t.name,className:o};else if(typeof t!="object")e[n]=t;else throw new Error(`Could not serialize value ${t}`)}function Q(n,t,e,o){const r=t[n];let i;const s=(r==null?void 0:r.type)??(r==null?void 0:r.className);if(At(s)){let l=o.meshes.filter(c=>r.id?c.id===r.id:c.name===r.name);l.length===0&&(l=o.transformNodes.filter(c=>r.id?c.id===r.id:c.name===r.name)),i=r.uniqueId?l.find(c=>c.uniqueId===r.uniqueId):l[0]}else if(ht(s))i=Nt(s,r.value);else if(Lt(s)){const l=o.animationGroups.filter(c=>c.name===r.name);i=l.length===1?l[0]:l.find(c=>c.uniqueId===r.uniqueId)}else s==="Matrix"?i=yt.FromArray(r.value):s==="Matrix2D"?i=new bt(r.value):s==="Matrix3D"?i=new Pt(r.value):s==="FlowGraphInteger"?i=Ct.FromValue(r.value):s==="number"||s==="string"||s==="boolean"?i=r.value[0]:r&&r.value!==void 0?i=r.value:Array.isArray(r)?i=r.reduce((l,c)=>(c.eventData&&(l[c.id]={type:Ot(c.type)},typeof c.value<"u"&&(l[c.id].value=Q("value",c,e,o))),l),{}):i=r;return i}function St(n){return n==="FlowGraphJsonPointerParserBlock"}var it;(function(n){n.Animation="Animation",n.AnimationGroup="AnimationGroup",n.Mesh="Mesh",n.Material="Material",n.Camera="Camera",n.Light="Light"})(it||(it={}));function Mt(n,t,e,o){switch(t){case"Animation":return o?n.animations.find(r=>r.uniqueId===e)??null:n.animations[e]??null;case"AnimationGroup":return o?n.animationGroups.find(r=>r.uniqueId===e)??null:n.animationGroups[e]??null;case"Mesh":return o?n.meshes.find(r=>r.uniqueId===e)??null:n.meshes[e]??null;case"Material":return o?n.materials.find(r=>r.uniqueId===e)??null:n.materials[e]??null;case"Camera":return o?n.cameras.find(r=>r.uniqueId===e)??null:n.cameras[e]??null;case"Light":return o?n.lights.find(r=>r.uniqueId===e)??null:n.lights[e]??null;default:return null}}var st;(function(n){n.ExecuteBlock="ExecuteBlock",n.ExecuteEvent="ExecuteEvent",n.TriggerConnection="TriggerConnection",n.ContextVariableSet="ContextVariableSet",n.GlobalVariableSet="GlobalVariableSet",n.GlobalVariableDelete="GlobalVariableDelete",n.GlobalVariableGet="GlobalVariableGet",n.AddConnection="AddConnection",n.GetConnectionValue="GetConnectionValue",n.SetConnectionValue="SetConnectionValue",n.ActivateSignal="ActivateSignal",n.ContextVariableGet="ContextVariableGet"})(st||(st={}));class qt{constructor(){this.logToConsole=!1,this.log=[]}addLogItem(t){var e;if(t.time||(t.time=Date.now()),this.log.push(t),this.logToConsole){const o=(e=t.payload)==null?void 0:e.value;typeof o=="object"&&o.getClassName?p.Log(`[FGLog] ${t.className}:${t.uniqueId.split("-")[0]} ${t.action} - ${JSON.stringify(o.getClassName())}: ${o.toString()}`):p.Log(`[FGLog] ${t.className}:${t.uniqueId.split("-")[0]} ${t.action} - ${JSON.stringify(t.payload)}`)}}getItemsOfType(t){return this.log.filter(e=>e.action===t)}}class _t{get enableLogging(){return this._enableLogging}set enableLogging(t){this._enableLogging!==t&&(this._enableLogging=t,this._enableLogging?(this.logger=new qt,this.logger.logToConsole=!0):this.logger=null)}constructor(t){this.uniqueId=A(),this._userVariables={},this._executionVariables={},this._globalContextVariables={},this._connectionValues={},this._pendingBlocks=[],this._executionId=0,this.onNodeExecutedObservable=new W,this.treatDataAsRightHanded=!1,this._enableLogging=!1,this._configuration=t,this.assetsContext=t.assetsContext??t.scene}hasVariable(t){return t in this._userVariables}setVariable(t,e){var o;this._userVariables[t]=e,(o=this.logger)==null||o.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"ContextVariableSet",payload:{name:t,value:e}})}getAsset(t,e){return Mt(this.assetsContext,t,e)}getVariable(t){var e;return(e=this.logger)==null||e.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"ContextVariableGet",payload:{name:t,value:this._userVariables[t]}}),this._userVariables[t]}get userVariables(){return this._userVariables}getScene(){return this._configuration.scene}_getUniqueIdPrefixedName(t,e){return`${t.uniqueId}_${e}`}_getGlobalContextVariable(t,e){var o;return(o=this.logger)==null||o.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"GlobalVariableGet",payload:{name:t,defaultValue:e,possibleValue:this._globalContextVariables[t]}}),this._hasGlobalContextVariable(t)?this._globalContextVariables[t]:e}_setGlobalContextVariable(t,e){var o;(o=this.logger)==null||o.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"GlobalVariableSet",payload:{name:t,value:e}}),this._globalContextVariables[t]=e}_deleteGlobalContextVariable(t){var e;(e=this.logger)==null||e.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"GlobalVariableDelete",payload:{name:t}}),delete this._globalContextVariables[t]}_hasGlobalContextVariable(t){return t in this._globalContextVariables}_setExecutionVariable(t,e,o){this._executionVariables[this._getUniqueIdPrefixedName(t,e)]=o}_getExecutionVariable(t,e,o){return this._hasExecutionVariable(t,e)?this._executionVariables[this._getUniqueIdPrefixedName(t,e)]:o}_deleteExecutionVariable(t,e){delete this._executionVariables[this._getUniqueIdPrefixedName(t,e)]}_hasExecutionVariable(t,e){return this._getUniqueIdPrefixedName(t,e)in this._executionVariables}_hasConnectionValue(t){return t.uniqueId in this._connectionValues}_setConnectionValue(t,e){var o;this._connectionValues[t.uniqueId]=e,(o=this.logger)==null||o.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"SetConnectionValue",payload:{connectionPointId:t.uniqueId,value:e}})}_setConnectionValueByKey(t,e){this._connectionValues[t]=e}_getConnectionValue(t){var e;return(e=this.logger)==null||e.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"GetConnectionValue",payload:{connectionPointId:t.uniqueId,value:this._connectionValues[t.uniqueId]}}),this._connectionValues[t.uniqueId]}get configuration(){return this._configuration}get hasPendingBlocks(){return this._pendingBlocks.length>0}_addPendingBlock(t){this._pendingBlocks.includes(t)||(this._pendingBlocks.push(t),this._pendingBlocks.sort((e,o)=>e.priority-o.priority))}_removePendingBlock(t){const e=this._pendingBlocks.indexOf(t);e!==-1&&this._pendingBlocks.splice(e,1)}_clearPendingBlocks(){for(const t of this._pendingBlocks)t._cancelPendingTasks(this);this._pendingBlocks.length=0}_notifyExecuteNode(t){var e;this.onNodeExecutedObservable.notifyObservers(t),(e=this.logger)==null||e.addLogItem({time:Date.now(),className:t.getClassName(),uniqueId:t.uniqueId,action:"ExecuteBlock"})}_notifyOnTick(t){var e;this._setGlobalContextVariable("timeSinceStart",t.timeSinceStart),this._setGlobalContextVariable("deltaTime",t.deltaTime);for(const o of this._pendingBlocks)(e=o._executeOnTick)==null||e.call(o,this)}_increaseExecutionId(){this._executionId++}get executionId(){return this._executionId}serialize(t={},e=tt){var o;t.uniqueId=this.uniqueId,t._userVariables={};for(const r in this._userVariables)e(r,this._userVariables[r],t._userVariables);t._connectionValues={};for(const r in this._connectionValues)e(r,this._connectionValues[r],t._connectionValues);this.assetsContext!==this.getScene()&&(t._assetsContext={meshes:this.assetsContext.meshes.map(r=>r.id),materials:this.assetsContext.materials.map(r=>r.id),textures:this.assetsContext.textures.map(r=>r.name),animations:this.assetsContext.animations.map(r=>r.name),lights:this.assetsContext.lights.map(r=>r.id),cameras:this.assetsContext.cameras.map(r=>r.id),sounds:(o=this.assetsContext.sounds)==null?void 0:o.map(r=>r.name),skeletons:this.assetsContext.skeletons.map(r=>r.id),particleSystems:this.assetsContext.particleSystems.map(r=>r.name),geometries:this.assetsContext.geometries.map(r=>r.id),multiMaterials:this.assetsContext.multiMaterials.map(r=>r.id),transformNodes:this.assetsContext.transformNodes.map(r=>r.id)})}getClassName(){return"FlowGraphContext"}}It([Ft()],_t.prototype,"uniqueId",void 0);var lt;(function(n){n[n.Input=0]="Input",n[n.Output=1]="Output"})(lt||(lt={}));class dt{constructor(t,e,o){this._ownerBlock=o,this._connectedPoint=[],this.uniqueId=A(),this.connectedPointIds=[],this.name=t,this._connectionType=e}get connectionType(){return this._connectionType}_isSingularConnection(){return!0}isConnected(){return this._connectedPoint.length>0}connectTo(t){if(this._connectionType===t._connectionType)throw new Error(`Cannot connect two points of type ${this.connectionType}`);if(this._isSingularConnection()&&this._connectedPoint.length>0||t._isSingularConnection()&&t._connectedPoint.length>0)throw new Error("Max number of connections for point reached");this._connectedPoint.push(t),t._connectedPoint.push(this)}disconnectFrom(t,e=!0){const o=this._connectedPoint.indexOf(t),r=t._connectedPoint.indexOf(this);o===-1||r===-1||(e&&this._connectedPoint.splice(o,1),t._connectedPoint.splice(r,1))}disconnectFromAll(){for(const t of this._connectedPoint)this.disconnectFrom(t,!1);this._connectedPoint.length=0}dispose(){for(const t of this._connectedPoint)this.disconnectFrom(t)}serialize(t={}){t.uniqueId=this.uniqueId,t.name=this.name,t._connectionType=this._connectionType,t.connectedPointIds=[],t.className=this.getClassName();for(const e of this._connectedPoint)t.connectedPointIds.push(e.uniqueId)}getClassName(){return"FGConnection"}deserialize(t){this.uniqueId=t.uniqueId,this.name=t.name,this._connectionType=t._connectionType,this.connectedPointIds=t.connectedPointIds}}class Y extends dt{constructor(t,e,o,r,i=r.defaultValue,s=!1){super(t,e,o),this.richType=r,this._defaultValue=i,this._optional=s,this._isDisabled=!1,this._lastValue=null,this.dataTransformer=null,this.onValueChangedObservable=new W}get optional(){return this._optional}get isDisabled(){return this._isDisabled}set isDisabled(t){this._isDisabled!==t&&(this._isDisabled=t,this._isDisabled&&this.disconnectFromAll())}_isSingularConnection(){return this.connectionType===0}setValue(t,e){e._getConnectionValue(this)!==t&&(e._setConnectionValue(this,t),this.onValueChangedObservable.notifyObservers(t))}resetToDefaultValue(t){t._setConnectionValue(this,this._defaultValue)}connectTo(t){this._isDisabled||super.connectTo(t)}_getValueOrDefault(t){const e=t._getConnectionValue(this)??this._defaultValue;return this.dataTransformer?this.dataTransformer(e):e}getValue(t){if(this.connectionType===1){t._notifyExecuteNode(this._ownerBlock),this._ownerBlock._updateOutputs(t);const o=this._getValueOrDefault(t);return this._lastValue=o,this.richType.typeTransformer?this.richType.typeTransformer(o):o}const e=this.isConnected()?this._connectedPoint[0].getValue(t):this._getValueOrDefault(t);return this._lastValue=e,this.richType.typeTransformer?this.richType.typeTransformer(e):e}_getLastValue(){return this._lastValue}getClassName(){return"FlowGraphDataConnection"}serialize(t={}){super.serialize(t),t.richType={},this.richType.serialize(t.richType),t.optional=this._optional,tt("defaultValue",this._defaultValue,t)}}pt("FlowGraphDataConnection",Y);class $t{constructor(t){var e;this.config=t,this.uniqueId=A(),this.name=((e=this.config)==null?void 0:e.name)??this.getClassName(),this.dataInputs=[],this.dataOutputs=[]}_updateOutputs(t){}registerDataInput(t,e,o){const r=new Y(t,0,this,e,o);return this.dataInputs.push(r),r}registerDataOutput(t,e,o){const r=new Y(t,1,this,e,o);return this.dataOutputs.push(r),r}getDataInput(t){return this.dataInputs.find(e=>e.name===t)}getDataOutput(t){return this.dataOutputs.find(e=>e.name===t)}serialize(t={},e=tt){if(t.uniqueId=this.uniqueId,t.config={},this.config){const o=this.config;Object.keys(this.config).forEach(r=>{e(r,o[r],t.config)})}t.dataInputs=[],t.dataOutputs=[],t.className=this.getClassName();for(const o of this.dataInputs){const r={};o.serialize(r),t.dataInputs.push(r)}for(const o of this.dataOutputs){const r={};o.serialize(r),t.dataOutputs.push(r)}}deserialize(t){}_log(t,e,o){var r;(r=t.logger)==null||r.addLogItem({action:e,payload:o,className:this.getClassName(),uniqueId:this.uniqueId})}getClassName(){return"FlowGraphBlock"}}class z extends dt{constructor(){super(...arguments),this.priority=0}_isSingularConnection(){return!1}connectTo(t){super.connectTo(t),this._connectedPoint.sort((e,o)=>o.priority-e.priority)}_activateSignal(t){var e;if((e=t.logger)==null||e.addLogItem({action:"ActivateSignal",className:this._ownerBlock.getClassName(),uniqueId:this._ownerBlock.uniqueId,payload:{connectionType:this.connectionType,name:this.name}}),this.connectionType===0)t._notifyExecuteNode(this._ownerBlock),this._ownerBlock._execute(t,this),t._increaseExecutionId();else for(const o of this._connectedPoint)o._activateSignal(t)}}pt("FlowGraphSignalConnection",z);class J extends $t{constructor(t){super(t),this.priority=0,this.signalInputs=[],this.signalOutputs=[],this.in=this._registerSignalInput("in"),this.error=this._registerSignalOutput("error")}_registerSignalInput(t){const e=new z(t,0,this);return this.signalInputs.push(e),e}_registerSignalOutput(t){const e=new z(t,1,this);return this.signalOutputs.push(e),e}_unregisterSignalInput(t){const e=this.signalInputs.findIndex(o=>o.name===t);e!==-1&&(this.signalInputs[e].dispose(),this.signalInputs.splice(e,1))}_unregisterSignalOutput(t){const e=this.signalOutputs.findIndex(o=>o.name===t);e!==-1&&(this.signalOutputs[e].dispose(),this.signalOutputs.splice(e,1))}_reportError(t,e){this.error.payload=typeof e=="string"?new Error(e):e,this.error._activateSignal(t)}getSignalInput(t){return this.signalInputs.find(e=>e.name===t)}getSignalOutput(t){return this.signalOutputs.find(e=>e.name===t)}serialize(t={}){super.serialize(t),t.signalInputs=[],t.signalOutputs=[];for(const e of this.signalInputs){const o={};e.serialize(o),t.signalInputs.push(o)}for(const e of this.signalOutputs){const o={};e.serialize(o),t.signalOutputs.push(o)}}deserialize(t){for(let e=0;e<t.signalInputs.length;e++){const o=this.getSignalInput(t.signalInputs[e].name);if(o)o.deserialize(t.signalInputs[e]);else throw new Error("Could not find signal input with name "+t.signalInputs[e].name+" in block "+t.className)}for(let e=0;e<t.signalOutputs.length;e++){const o=this.getSignalOutput(t.signalOutputs[e].name);if(o)o.deserialize(t.signalOutputs[e]);else throw new Error("Could not find signal output with name "+t.signalOutputs[e].name+" in block "+t.className)}}getClassName(){return"FlowGraphExecutionBlock"}}class Kt{constructor(t){this.onEventTriggeredObservable=new W,this.sceneReadyTriggered=!1,this._pointerUnderMeshState={},this._startingTime=0,this._scene=t,this._initialize()}_initialize(){this._sceneReadyObserver=this._scene.onReadyObservable.add(()=>{this.sceneReadyTriggered||(this.onEventTriggeredObservable.notifyObservers({type:"SceneReady"}),this.sceneReadyTriggered=!0)}),this._sceneDisposeObserver=this._scene.onDisposeObservable.add(()=>{this.onEventTriggeredObservable.notifyObservers({type:"SceneDispose"})}),this._sceneOnBeforeRenderObserver=this._scene.onBeforeRenderObservable.add(()=>{const t=this._scene.getEngine().getDeltaTime()/1e3;this.onEventTriggeredObservable.notifyObservers({type:"SceneBeforeRender",payload:{timeSinceStart:this._startingTime,deltaTime:t}}),this._startingTime+=t}),this._meshPickedObserver=this._scene.onPointerObservable.add(t=>{this.onEventTriggeredObservable.notifyObservers({type:"MeshPick",payload:t})},nt.POINTERPICK),this._meshUnderPointerObserver=this._scene.onMeshUnderPointerUpdatedObservable.add(t=>{const e=t.pointerId,o=t.mesh,r=this._pointerUnderMeshState[e];!r&&o?this.onEventTriggeredObservable.notifyObservers({type:"PointerOver",payload:{pointerId:e,mesh:o}}):r&&!o?this.onEventTriggeredObservable.notifyObservers({type:"PointerOut",payload:{pointerId:e,mesh:r}}):r&&o&&r!==o&&(this.onEventTriggeredObservable.notifyObservers({type:"PointerOut",payload:{pointerId:e,mesh:r,over:o}}),this.onEventTriggeredObservable.notifyObservers({type:"PointerOver",payload:{pointerId:e,mesh:o,out:r}})),this._pointerUnderMeshState[e]=o},nt.POINTERMOVE)}dispose(){var t,e,o,r,i;(t=this._sceneDisposeObserver)==null||t.remove(),(e=this._sceneReadyObserver)==null||e.remove(),(o=this._sceneOnBeforeRenderObserver)==null||o.remove(),(r=this._meshPickedObserver)==null||r.remove(),(i=this._meshUnderPointerObserver)==null||i.remove(),this.onEventTriggeredObservable.clear()}}function mt(n,t){return!!(n.parent&&(n.parent===t||mt(n.parent,t)))}function ce(n){if(n.getClassName)return n.getClassName()}function ue(n,t){return n===t&&(n==="Vector2"||n==="Vector3"||n==="Vector4")}function pe(n,t){return n===t&&(n==="Matrix"||n==="Matrix2D"||n==="Matrix3D")}function he(n,t){return n==="FlowGraphInteger"&&t==="FlowGraphInteger"}function _e(n,t){const e=typeof n=="number"||typeof(n==null?void 0:n.value)=="number";return e&&!t?!isNaN(Ut(n)):e}function Ut(n){return typeof n=="number"?n:n.value}var ct;(function(n){n[n.Stopped=0]="Stopped",n[n.Started=1]="Started"})(ct||(ct={}));class Wt{get state(){return this._state}set state(t){this._state=t,this.onStateChangedObservable.notifyObservers(t)}constructor(t){this.onStateChangedObservable=new W,this._eventBlocks={SceneReady:[],SceneDispose:[],SceneBeforeRender:[],MeshPick:[],PointerDown:[],PointerUp:[],PointerMove:[],PointerOver:[],PointerOut:[],SceneAfterRender:[],NoTrigger:[]},this._executionContexts=[],this._state=0,this._scene=t.scene,this._sceneEventCoordinator=new Kt(this._scene),this._coordinator=t.coordinator,this._eventObserver=this._sceneEventCoordinator.onEventTriggeredObservable.add(e=>{for(const o of this._executionContexts){const r=this._getContextualOrder(e.type,o);for(const i of r)if(!i._executeEvent(o,e.payload))break}switch(e.type){case"SceneReady":this._sceneEventCoordinator.sceneReadyTriggered=!0;break;case"SceneBeforeRender":for(const o of this._executionContexts)o._notifyOnTick(e.payload);break;case"SceneDispose":this.dispose();break}})}createContext(){const t=new _t({scene:this._scene,coordinator:this._coordinator});return this._executionContexts.push(t),t}getContext(t){return this._executionContexts[t]}addEventBlock(t){if((t.type==="PointerOver"||t.type==="PointerOut")&&(this._scene.constantlyUpdateMeshUnderPointer=!0),t.type!=="NoTrigger"&&this._eventBlocks[t.type].push(t),this.state===1)for(const e of this._executionContexts)t._startPendingTasks(e);else this.onStateChangedObservable.addOnce(e=>{if(e===1)for(const o of this._executionContexts)t._startPendingTasks(o)})}start(){this.state!==1&&(this._executionContexts.length===0&&this.createContext(),this.onStateChangedObservable.add(t=>{t===1&&(this._startPendingEvents(),this._scene.isReady(!0)&&this._sceneEventCoordinator.onEventTriggeredObservable.notifyObservers({type:"SceneReady"}))}),this.state=1)}_startPendingEvents(){for(const t of this._executionContexts)for(const e in this._eventBlocks){const o=this._getContextualOrder(e,t);for(const r of o)r._startPendingTasks(t)}}_getContextualOrder(t,e){const o=this._eventBlocks[t].sort((r,i)=>i.initPriority-r.initPriority);if(t==="MeshPick"){const r=[];for(const i of o){const s=i.asset.getValue(e);let l=0;for(;l<o.length;l++){const d=o[l].asset.getValue(e);if(s&&d&&mt(s,d))break}r.splice(l,0,i)}return r}return o}dispose(){var t;if(this.state!==0){this.state=0;for(const e of this._executionContexts)e._clearPendingBlocks();this._executionContexts.length=0;for(const e in this._eventBlocks)this._eventBlocks[e].length=0;(t=this._eventObserver)==null||t.remove(),this._sceneEventCoordinator.dispose()}}visitAllBlocks(t){const e=[],o=new Set;for(const r in this._eventBlocks)for(const i of this._eventBlocks[r])e.push(i),o.add(i.uniqueId);for(;e.length>0;){const r=e.pop();t(r);for(const i of r.dataInputs)for(const s of i._connectedPoint)o.has(s._ownerBlock.uniqueId)||(e.push(s._ownerBlock),o.add(s._ownerBlock.uniqueId));if(r instanceof J)for(const i of r.signalOutputs)for(const s of i._connectedPoint)o.has(s._ownerBlock.uniqueId)||(e.push(s._ownerBlock),o.add(s._ownerBlock.uniqueId))}}serialize(t={},e){t.allBlocks=[],this.visitAllBlocks(o=>{const r={};o.serialize(r),t.allBlocks.push(r)}),t.executionContexts=[];for(const o of this._executionContexts){const r={};o.serialize(r,e),t.executionContexts.push(r)}}}class B{constructor(t){this.config=t,this.dispatchEventsSynchronously=!0,this._flowGraphs=[],this._customEventsMap=new Map,this._eventExecutionCounter=new Map,this._executeOnNextFrame=[],this._eventUniqueId=0,this._disposeObserver=this.config.scene.onDisposeObservable.add(()=>{this.dispose()}),this._onBeforeRenderObserver=this.config.scene.onBeforeRenderObservable.add(()=>{this._eventExecutionCounter.clear();const o=this._executeOnNextFrame.slice(0);o.length&&o.forEach(r=>{this.notifyCustomEvent(r.id,r.data,!1);const i=this._executeOnNextFrame.findIndex(s=>s.uniqueId===r.uniqueId);i!==-1&&this._executeOnNextFrame.splice(i,1)})}),(B.SceneCoordinators.get(this.config.scene)??[]).push(this)}createGraph(){const t=new Wt({scene:this.config.scene,coordinator:this});return this._flowGraphs.push(t),t}removeGraph(t){const e=this._flowGraphs.indexOf(t);e!==-1&&(t.dispose(),this._flowGraphs.splice(e,1))}start(){this._flowGraphs.forEach(t=>t.start())}dispose(){var o,r;this._flowGraphs.forEach(i=>i.dispose()),this._flowGraphs.length=0,(o=this._disposeObserver)==null||o.remove(),(r=this._onBeforeRenderObserver)==null||r.remove();const t=B.SceneCoordinators.get(this.config.scene)??[],e=t.indexOf(this);e!==-1&&t.splice(e,1)}serialize(t,e){t._flowGraphs=[],this._flowGraphs.forEach(o=>{const r={};o.serialize(r,e),t._flowGraphs.push(r)}),t.dispatchEventsSynchronously=this.dispatchEventsSynchronously}get flowGraphs(){return this._flowGraphs}getCustomEventObservable(t){let e=this._customEventsMap.get(t);return e||(e=new W,this._customEventsMap.set(t,e)),e}notifyCustomEvent(t,e,o=!this.dispatchEventsSynchronously){if(o){this._executeOnNextFrame.push({id:t,data:e,uniqueId:this._eventUniqueId++});return}if(this._eventExecutionCounter.has(t)){const i=this._eventExecutionCounter.get(t);if(this._eventExecutionCounter.set(t,i+1),i>=B.MaxEventTypeExecutionPerFrame){i===B.MaxEventTypeExecutionPerFrame&&p.Warn(`FlowGraphCoordinator: Too many executions of event "${t}".`);return}}else this._eventExecutionCounter.set(t,1);const r=this._customEventsMap.get(t);r&&r.notifyObservers(e)}}B.MaxEventsPerType=30;B.MaxEventTypeExecutionPerFrame=30;B.SceneCoordinators=new Map;const j={};function Ht(n,t,e){j[`${n}/${t}`]=e}function Qt(n){switch(n){case"FlowGraphPlayAnimationBlock":return async()=>(await a(()=>import("./flowGraphPlayAnimationBlock.DMpd9AzC.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).FlowGraphPlayAnimationBlock;case"FlowGraphStopAnimationBlock":return async()=>(await a(()=>import("./flowGraphStopAnimationBlock.DRdSCS1A.js"),__vite__mapDeps([6,1,2,3]),import.meta.url)).FlowGraphStopAnimationBlock;case"FlowGraphPauseAnimationBlock":return async()=>(await a(()=>import("./flowGraphPauseAnimationBlock.tGoUJuwx.js"),__vite__mapDeps([7,1,2,3]),import.meta.url)).FlowGraphPauseAnimationBlock;case"FlowGraphInterpolationBlock":return async()=>(await a(()=>import("./flowGraphInterpolationBlock.Clp4-F7o.js"),__vite__mapDeps([8,1,2,3]),import.meta.url)).FlowGraphInterpolationBlock;case"FlowGraphSceneReadyEventBlock":return async()=>(await a(()=>import("./flowGraphSceneReadyEventBlock.D16NDYdp.js"),__vite__mapDeps([9,2,3]),import.meta.url)).FlowGraphSceneReadyEventBlock;case"FlowGraphSceneTickEventBlock":return async()=>(await a(()=>import("./flowGraphSceneTickEventBlock.Bwibrsmg.js"),__vite__mapDeps([10,2,3,1]),import.meta.url)).FlowGraphSceneTickEventBlock;case"FlowGraphSendCustomEventBlock":return async()=>(await a(()=>import("./flowGraphSendCustomEventBlock.ChWfcsQb.js"),__vite__mapDeps([11,2,3]),import.meta.url)).FlowGraphSendCustomEventBlock;case"FlowGraphReceiveCustomEventBlock":return async()=>(await a(()=>import("./flowGraphReceiveCustomEventBlock.Zl1e7Elp.js"),__vite__mapDeps([12,2,3]),import.meta.url)).FlowGraphReceiveCustomEventBlock;case"FlowGraphMeshPickEventBlock":return async()=>(await a(()=>import("./flowGraphMeshPickEventBlock.Bkgm0TYl.js"),__vite__mapDeps([13,2,3,1]),import.meta.url)).FlowGraphMeshPickEventBlock;case"FlowGraphEBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphEBlock;case"FlowGraphPIBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphPiBlock;case"FlowGraphInfBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphInfBlock;case"FlowGraphNaNBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphNaNBlock;case"FlowGraphRandomBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphRandomBlock;case"FlowGraphAddBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAddBlock;case"FlowGraphSubtractBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphSubtractBlock;case"FlowGraphMultiplyBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphMultiplyBlock;case"FlowGraphDivideBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphDivideBlock;case"FlowGraphAbsBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAbsBlock;case"FlowGraphSignBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphSignBlock;case"FlowGraphTruncBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphTruncBlock;case"FlowGraphFloorBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphFloorBlock;case"FlowGraphCeilBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphCeilBlock;case"FlowGraphRoundBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphRoundBlock;case"FlowGraphFractBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphFractionBlock;case"FlowGraphNegationBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphNegationBlock;case"FlowGraphModuloBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphModuloBlock;case"FlowGraphMinBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphMinBlock;case"FlowGraphMaxBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphMaxBlock;case"FlowGraphClampBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphClampBlock;case"FlowGraphSaturateBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphSaturateBlock;case"FlowGraphMathInterpolationBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphMathInterpolationBlock;case"FlowGraphEqualityBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphEqualityBlock;case"FlowGraphLessThanBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphLessThanBlock;case"FlowGraphLessThanOrEqualBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphLessThanOrEqualBlock;case"FlowGraphGreaterThanBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphGreaterThanBlock;case"FlowGraphGreaterThanOrEqualBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphGreaterThanOrEqualBlock;case"FlowGraphIsNaNBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphIsNanBlock;case"FlowGraphIsInfBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphIsInfinityBlock;case"FlowGraphDegToRadBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphDegToRadBlock;case"FlowGraphRadToDegBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphRadToDegBlock;case"FlowGraphSinBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphSinBlock;case"FlowGraphCosBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphCosBlock;case"FlowGraphTanBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphTanBlock;case"FlowGraphASinBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAsinBlock;case"FlowGraphACosBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAcosBlock;case"FlowGraphATanBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAtanBlock;case"FlowGraphATan2Block":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAtan2Block;case"FlowGraphSinhBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphSinhBlock;case"FlowGraphCoshBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphCoshBlock;case"FlowGraphTanhBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphTanhBlock;case"FlowGraphASinhBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAsinhBlock;case"FlowGraphACoshBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAcoshBlock;case"FlowGraphATanhBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphAtanhBlock;case"FlowGraphExponentialBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphExpBlock;case"FlowGraphLogBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphLogBlock;case"FlowGraphLog2Block":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphLog2Block;case"FlowGraphLog10Block":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphLog10Block;case"FlowGraphSquareRootBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphSquareRootBlock;case"FlowGraphPowerBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphPowerBlock;case"FlowGraphCubeRootBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphCubeRootBlock;case"FlowGraphBitwiseAndBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphBitwiseAndBlock;case"FlowGraphBitwiseOrBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphBitwiseOrBlock;case"FlowGraphBitwiseNotBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphBitwiseNotBlock;case"FlowGraphBitwiseXorBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphBitwiseXorBlock;case"FlowGraphBitwiseLeftShiftBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphBitwiseLeftShiftBlock;case"FlowGraphBitwiseRightShiftBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphBitwiseRightShiftBlock;case"FlowGraphLengthBlock":return async()=>(await a(()=>import("./flowGraphVectorMathBlocks.B5YfnEFA.js"),__vite__mapDeps([19,1,2,3,15,16,17,18]),import.meta.url)).FlowGraphLengthBlock;case"FlowGraphNormalizeBlock":return async()=>(await a(()=>import("./flowGraphVectorMathBlocks.B5YfnEFA.js"),__vite__mapDeps([19,1,2,3,15,16,17,18]),import.meta.url)).FlowGraphNormalizeBlock;case"FlowGraphDotBlock":return async()=>(await a(()=>import("./flowGraphVectorMathBlocks.B5YfnEFA.js"),__vite__mapDeps([19,1,2,3,15,16,17,18]),import.meta.url)).FlowGraphDotBlock;case"FlowGraphCrossBlock":return async()=>(await a(()=>import("./flowGraphVectorMathBlocks.B5YfnEFA.js"),__vite__mapDeps([19,1,2,3,15,16,17,18]),import.meta.url)).FlowGraphCrossBlock;case"FlowGraphRotate2DBlock":return async()=>(await a(()=>import("./flowGraphVectorMathBlocks.B5YfnEFA.js"),__vite__mapDeps([19,1,2,3,15,16,17,18]),import.meta.url)).FlowGraphRotate2DBlock;case"FlowGraphRotate3DBlock":return async()=>(await a(()=>import("./flowGraphVectorMathBlocks.B5YfnEFA.js"),__vite__mapDeps([19,1,2,3,15,16,17,18]),import.meta.url)).FlowGraphRotate3DBlock;case"FlowGraphTransposeBlock":return async()=>(await a(()=>import("./flowGraphMatrixMathBlocks.DxazJ9N_.js"),__vite__mapDeps([20,1,2,3,17,16,15]),import.meta.url)).FlowGraphTransposeBlock;case"FlowGraphDeterminantBlock":return async()=>(await a(()=>import("./flowGraphMatrixMathBlocks.DxazJ9N_.js"),__vite__mapDeps([20,1,2,3,17,16,15]),import.meta.url)).FlowGraphDeterminantBlock;case"FlowGraphInvertMatrixBlock":return async()=>(await a(()=>import("./flowGraphMatrixMathBlocks.DxazJ9N_.js"),__vite__mapDeps([20,1,2,3,17,16,15]),import.meta.url)).FlowGraphInvertMatrixBlock;case"FlowGraphMatrixMultiplicationBlock":return async()=>(await a(()=>import("./flowGraphMatrixMathBlocks.DxazJ9N_.js"),__vite__mapDeps([20,1,2,3,17,16,15]),import.meta.url)).FlowGraphMatrixMultiplicationBlock;case"FlowGraphBranchBlock":return async()=>(await a(()=>import("./flowGraphBranchBlock.B5KPMpSk.js"),__vite__mapDeps([21,1,2,3]),import.meta.url)).FlowGraphBranchBlock;case"FlowGraphSetDelayBlock":return async()=>(await a(()=>import("./flowGraphSetDelayBlock.NAhfwlgz.js"),__vite__mapDeps([22,1,2,3]),import.meta.url)).FlowGraphSetDelayBlock;case"FlowGraphCancelDelayBlock":return async()=>(await a(()=>import("./flowGraphCancelDelayBlock.Bo445-z6.js"),__vite__mapDeps([23,2,3,1]),import.meta.url)).FlowGraphCancelDelayBlock;case"FlowGraphCallCounterBlock":return async()=>(await a(()=>import("./flowGraphCounterBlock.B4fjt7HE.js"),__vite__mapDeps([24,1,2,3]),import.meta.url)).FlowGraphCallCounterBlock;case"FlowGraphDebounceBlock":return async()=>(await a(()=>import("./flowGraphDebounceBlock.BBFhWo9P.js"),__vite__mapDeps([25,1,2,3]),import.meta.url)).FlowGraphDebounceBlock;case"FlowGraphThrottleBlock":return async()=>(await a(()=>import("./flowGraphThrottleBlock.D9nMlAUr.js"),__vite__mapDeps([26,1,2,3]),import.meta.url)).FlowGraphThrottleBlock;case"FlowGraphDoNBlock":return async()=>(await a(()=>import("./flowGraphDoNBlock.DCMR9_vT.js"),__vite__mapDeps([27,1,2,3]),import.meta.url)).FlowGraphDoNBlock;case"FlowGraphFlipFlopBlock":return async()=>(await a(()=>import("./flowGraphFlipFlopBlock.CA4_-zai.js"),__vite__mapDeps([28,1,2,3]),import.meta.url)).FlowGraphFlipFlopBlock;case"FlowGraphForLoopBlock":return async()=>(await a(()=>import("./flowGraphForLoopBlock.BWvb8YvA.js"),__vite__mapDeps([29,1,2,3]),import.meta.url)).FlowGraphForLoopBlock;case"FlowGraphMultiGateBlock":return async()=>(await a(()=>import("./flowGraphMultiGateBlock.KML6_2T-.js"),__vite__mapDeps([30,2,3,1]),import.meta.url)).FlowGraphMultiGateBlock;case"FlowGraphSequenceBlock":return async()=>(await a(()=>import("./flowGraphSequenceBlock.CEbTwj2T.js"),__vite__mapDeps([31,2,3]),import.meta.url)).FlowGraphSequenceBlock;case"FlowGraphSwitchBlock":return async()=>(await a(()=>import("./flowGraphSwitchBlock.rlj1f3Pb.js"),__vite__mapDeps([32,1,2,3]),import.meta.url)).FlowGraphSwitchBlock;case"FlowGraphWaitAllBlock":return async()=>(await a(()=>import("./flowGraphWaitAllBlock.Bj0x3SHG.js"),__vite__mapDeps([33,2,3,1]),import.meta.url)).FlowGraphWaitAllBlock;case"FlowGraphWhileLoopBlock":return async()=>(await a(()=>import("./flowGraphWhileLoopBlock.CE8CW0jy.js"),__vite__mapDeps([34,1,2,3]),import.meta.url)).FlowGraphWhileLoopBlock;case"FlowGraphConsoleLogBlock":return async()=>(await a(()=>import("./flowGraphConsoleLogBlock.cIadvORr.js"),__vite__mapDeps([35,1,2,3]),import.meta.url)).FlowGraphConsoleLogBlock;case"FlowGraphConditionalBlock":return async()=>(await a(()=>import("./flowGraphConditionalDataBlock.BpscsBV2.js"),__vite__mapDeps([36,1,2,3]),import.meta.url)).FlowGraphConditionalDataBlock;case"FlowGraphConstantBlock":return async()=>(await a(()=>import("./flowGraphConstantBlock.Cf9AYaf0.js"),__vite__mapDeps([37,1,2,3]),import.meta.url)).FlowGraphConstantBlock;case"FlowGraphTransformCoordinatesSystemBlock":return async()=>(await a(()=>import("./flowGraphTransformCoordinatesSystemBlock.DdG5wdid.js"),__vite__mapDeps([38,1,2,3]),import.meta.url)).FlowGraphTransformCoordinatesSystemBlock;case"FlowGraphGetAssetBlock":return async()=>(await a(()=>import("./flowGraphGetAssetBlock.B9sydsa6.js"),__vite__mapDeps([39,1,2,3]),import.meta.url)).FlowGraphGetAssetBlock;case"FlowGraphGetPropertyBlock":return async()=>(await a(()=>import("./flowGraphGetPropertyBlock.CvJub6Jr.js"),__vite__mapDeps([40,1,2,3,16]),import.meta.url)).FlowGraphGetPropertyBlock;case"FlowGraphSetPropertyBlock":return async()=>(await a(()=>import("./flowGraphSetPropertyBlock.BrAMshGe.js"),__vite__mapDeps([41,1,2,3]),import.meta.url)).FlowGraphSetPropertyBlock;case"FlowGraphGetVariableBlock":return async()=>(await a(()=>import("./flowGraphGetVariableBlock.C49Sb2GW.js"),__vite__mapDeps([42,1,2,3]),import.meta.url)).FlowGraphGetVariableBlock;case"FlowGraphSetVariableBlock":return async()=>(await a(()=>import("./flowGraphSetVariableBlock.CWwy6I9C.js"),__vite__mapDeps([43,2,3,1]),import.meta.url)).FlowGraphSetVariableBlock;case"FlowGraphJsonPointerParserBlock":return async()=>(await a(()=>import("./flowGraphJsonPointerParserBlock.B04B8uAV.js"),__vite__mapDeps([44,1,2,3,16]),import.meta.url)).FlowGraphJsonPointerParserBlock;case"FlowGraphLeadingZerosBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphLeadingZerosBlock;case"FlowGraphTrailingZerosBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphTrailingZerosBlock;case"FlowGraphOneBitsCounterBlock":return async()=>(await a(()=>import("./flowGraphMathBlocks.BgQ6jpID.js"),__vite__mapDeps([14,2,3,1,15,16,17,18]),import.meta.url)).FlowGraphOneBitsCounterBlock;case"FlowGraphCombineVector2Block":return async()=>(await a(()=>import("./flowGraphMathCombineExtractBlocks.DpvecDoq.js"),__vite__mapDeps([45,16,1,2,3]),import.meta.url)).FlowGraphCombineVector2Block;case"FlowGraphCombineVector3Block":return async()=>(await a(()=>import("./flowGraphMathCombineExtractBlocks.DpvecDoq.js"),__vite__mapDeps([45,16,1,2,3]),import.meta.url)).FlowGraphCombineVector3Block;case"FlowGraphCombineVector4Block":return async()=>(await a(()=>import("./flowGraphMathCombineExtractBlocks.DpvecDoq.js"),__vite__mapDeps([45,16,1,2,3]),import.meta.url)).FlowGraphCombineVector4Block;case"FlowGraphCombineMatrixBlock":return async()=>(await a(()=>import("./flowGraphMathCombineExtractBlocks.DpvecDoq.js"),__vite__mapDeps([45,16,1,2,3]),import.meta.url)).FlowGraphCombineMatrixBlock;case"FlowGraphExtractVector2Block":return async()=>(await a(()=>import("./flowGraphMathCombineExtractBlocks.DpvecDoq.js"),__vite__mapDeps([45,16,1,2,3]),import.meta.url)).FlowGraphExtractVector2Block;case"FlowGraphExtractVector3Block":return async()=>(await a(()=>import("./flowGraphMathCombineExtractBlocks.DpvecDoq.js"),__vite__mapDeps([45,16,1,2,3]),import.meta.url)).FlowGraphExtractVector3Block;case"FlowGraphExtractVector4Block":return async()=>(await a(()=>import("./flowGraphMathCombineExtractBlocks.DpvecDoq.js"),__vite__mapDeps([45,16,1,2,3]),import.meta.url)).FlowGraphExtractVector4Block;case"FlowGraphExtractMatrixBlock":return async()=>(await a(()=>import("./flowGraphMathCombineExtractBlocks.DpvecDoq.js"),__vite__mapDeps([45,16,1,2,3]),import.meta.url)).FlowGraphExtractMatrixBlock;case"FlowGraphTransformVectorBlock":return async()=>(await a(()=>import("./flowGraphVectorMathBlocks.B5YfnEFA.js"),__vite__mapDeps([19,1,2,3,15,16,17,18]),import.meta.url)).FlowGraphTransformBlock;case"FlowGraphTransformCoordinatesBlock":return async()=>(await a(()=>import("./flowGraphVectorMathBlocks.B5YfnEFA.js"),__vite__mapDeps([19,1,2,3,15,16,17,18]),import.meta.url)).FlowGraphTransformCoordinatesBlock;case"FlowGraphMatrixDecompose":return async()=>(await a(()=>import("./flowGraphMatrixMathBlocks.DxazJ9N_.js"),__vite__mapDeps([20,1,2,3,17,16,15]),import.meta.url)).FlowGraphMatrixDecomposeBlock;case"FlowGraphMatrixCompose":return async()=>(await a(()=>import("./flowGraphMatrixMathBlocks.DxazJ9N_.js"),__vite__mapDeps([20,1,2,3,17,16,15]),import.meta.url)).FlowGraphMatrixComposeBlock;case"FlowGraphBooleanToFloat":return async()=>(await a(()=>import("./flowGraphTypeToTypeBlocks.DFY5E9Xa.js"),__vite__mapDeps([46,17,16,1,2,3]),import.meta.url)).FlowGraphBooleanToFloat;case"FlowGraphBooleanToInt":return async()=>(await a(()=>import("./flowGraphTypeToTypeBlocks.DFY5E9Xa.js"),__vite__mapDeps([46,17,16,1,2,3]),import.meta.url)).FlowGraphBooleanToInt;case"FlowGraphFloatToBoolean":return async()=>(await a(()=>import("./flowGraphTypeToTypeBlocks.DFY5E9Xa.js"),__vite__mapDeps([46,17,16,1,2,3]),import.meta.url)).FlowGraphFloatToBoolean;case"FlowGraphIntToBoolean":return async()=>(await a(()=>import("./flowGraphTypeToTypeBlocks.DFY5E9Xa.js"),__vite__mapDeps([46,17,16,1,2,3]),import.meta.url)).FlowGraphIntToBoolean;case"FlowGraphIntToFloat":return async()=>(await a(()=>import("./flowGraphTypeToTypeBlocks.DFY5E9Xa.js"),__vite__mapDeps([46,17,16,1,2,3]),import.meta.url)).FlowGraphIntToFloat;case"FlowGraphFloatToInt":return async()=>(await a(()=>import("./flowGraphTypeToTypeBlocks.DFY5E9Xa.js"),__vite__mapDeps([46,17,16,1,2,3]),import.meta.url)).FlowGraphFloatToInt;case"FlowGraphEasingBlock":return async()=>(await a(()=>import("./flowGraphEasingBlock.1niIOCug.js"),__vite__mapDeps([47,2,3,1]),import.meta.url)).FlowGraphEasingBlock;case"FlowGraphBezierCurveEasing":return async()=>(await a(()=>import("./flowGraphBezierCurveEasingBlock.BqfcWA2V.js"),__vite__mapDeps([48,2,3,1]),import.meta.url)).FlowGraphBezierCurveEasingBlock;case"FlowGraphPointerOverEventBlock":return async()=>(await a(()=>import("./flowGraphPointerOverEventBlock.I7XsC6gG.js"),__vite__mapDeps([49,1,2,3]),import.meta.url)).FlowGraphPointerOverEventBlock;case"FlowGraphPointerOutEventBlock":return async()=>(await a(()=>import("./flowGraphPointerOutEventBlock.B3OaQD4T.js"),__vite__mapDeps([50,1,2,3]),import.meta.url)).FlowGraphPointerOutEventBlock;case"FlowGraphContextBlock":return async()=>(await a(()=>import("./flowGraphContextBlock.DHb7kEHn.js"),__vite__mapDeps([51,1,2,3]),import.meta.url)).FlowGraphContextBlock;case"FlowGraphArrayIndexBlock":return async()=>(await a(()=>import("./flowGraphArrayIndexBlock.B03sfcvL.js"),__vite__mapDeps([52,1,2,3]),import.meta.url)).FlowGraphArrayIndexBlock;case"FlowGraphCodeExecutionBlock":return async()=>(await a(()=>import("./flowGraphCodeExecutionBlock.BITVoFWe.js"),__vite__mapDeps([53,1,2,3]),import.meta.url)).FlowGraphCodeExecutionBlock;case"FlowGraphIndexOfBlock":return async()=>(await a(()=>import("./flowGraphIndexOfBlock.cBkAYfIL.js"),__vite__mapDeps([54,1,2,3]),import.meta.url)).FlowGraphIndexOfBlock;case"FlowGraphFunctionReference":return async()=>(await a(()=>import("./flowGraphFunctionReferenceBlock.DYL4rOb8.js"),__vite__mapDeps([55,1,2,3]),import.meta.url)).FlowGraphFunctionReferenceBlock;case"FlowGraphDataSwitchBlock":return async()=>(await a(()=>import("./flowGraphDataSwitchBlock.CRZGNJmJ.js"),__vite__mapDeps([56,1,2,3]),import.meta.url)).FlowGraphDataSwitchBlock;default:if(j[n])return j[n];throw new Error(`Unknown block name ${n}`)}}class Jt extends J{constructor(t){super(t),this.out=this._registerSignalOutput("out")}}class Zt extends Jt{constructor(t,e){super(t),this._eventsSignalOutputs={},this.done=this._registerSignalOutput("done"),e==null||e.forEach(o=>{this._eventsSignalOutputs[o]=this._registerSignalOutput(o+"Event")})}_executeOnTick(t){}_startPendingTasks(t){t._getExecutionVariable(this,"_initialized",!1)&&(this._cancelPendingTasks(t),this._resetAfterCanceled(t)),this._preparePendingTasks(t),t._addPendingBlock(this),this.out._activateSignal(t),t._setExecutionVariable(this,"_initialized",!0)}_resetAfterCanceled(t){t._deleteExecutionVariable(this,"_initialized"),t._removePendingBlock(this)}}class Xt extends Zt{constructor(){super(...arguments),this.initPriority=0,this.type="NoTrigger"}_execute(t){t._notifyExecuteNode(this),this.done._activateSignal(t)}}function Yt(n,t){for(const e of n)for(const o of e.dataOutputs)if(o.uniqueId===t)return o;throw new Error("Could not find data out connection with unique id "+t)}function zt(n,t){for(const e of n)if(e instanceof J){for(const o of e.signalInputs)if(o.uniqueId===t)return o}throw new Error("Could not find signal in connection with unique id "+t)}async function jt(n,t){const e=await Promise.all(n.allBlocks.map(async o=>Qt(o.className)()));return te(n,t,e)}function te(n,t,e){const o=t.coordinator.createGraph(),r=[],i=t.valueParseFunction??Q;for(let s=0;s<n.allBlocks.length;s++){const l=n.allBlocks[s],c=oe(l,{scene:t.coordinator.config.scene,pathConverter:t.pathConverter,assetsContainer:t.coordinator.config.scene,valueParseFunction:i},e[s]);r.push(c),c instanceof Xt&&o.addEventBlock(c)}for(const s of r){for(const l of s.dataInputs)for(const c of l.connectedPointIds){const d=Yt(r,c);l.connectTo(d)}if(s instanceof J)for(const l of s.signalOutputs)for(const c of l.connectedPointIds){const d=zt(r,c);l.connectTo(d)}}for(const s of n.executionContexts)ee(s,{graph:o,valueParseFunction:i},n.rightHanded);return o}function ee(n,t,e){var s,l,c,d,f,I,v,R,L,N;const o=t.graph.createContext();n.enableLogging&&(o.enableLogging=!0),o.treatDataAsRightHanded=e||!1;const r=t.valueParseFunction??Q;o.uniqueId=n.uniqueId;const i=o.getScene();if(n._assetsContext){const h=n._assetsContext,F={meshes:(s=h.meshes)==null?void 0:s.map(u=>i.getMeshById(u)),lights:(l=h.lights)==null?void 0:l.map(u=>i.getLightByName(u)),cameras:(c=h.cameras)==null?void 0:c.map(u=>i.getCameraByName(u)),materials:(d=h.materials)==null?void 0:d.map(u=>i.getMaterialById(u)),textures:(f=h.textures)==null?void 0:f.map(u=>i.getTextureByName(u)),animations:(I=h.animations)==null?void 0:I.map(u=>i.animations.find(H=>H.name===u)),skeletons:(v=h.skeletons)==null?void 0:v.map(u=>i.getSkeletonByName(u)),particleSystems:(R=h.particleSystems)==null?void 0:R.map(u=>i.getParticleSystemById(u)),animationGroups:(L=h.animationGroups)==null?void 0:L.map(u=>i.getAnimationGroupByName(u)),transformNodes:(N=h.transformNodes)==null?void 0:N.map(u=>i.getTransformNodeById(u)),rootNodes:[],multiMaterials:[],morphTargetManagers:[],geometries:[],actionManagers:[],environmentTexture:null,postProcesses:[],sounds:null,effectLayers:[],layers:[],reflectionProbes:[],lensFlareSystems:[],proceduralTextures:[],getNodes:function(){throw new Error("Function not implemented.")}};o.assetsContext=F}for(const h in n._userVariables){const F=r(h,n._userVariables,o.assetsContext,i);o.userVariables[h]=F}for(const h in n._connectionValues){const F=r(h,n._connectionValues,o.assetsContext,i);o._setConnectionValueByKey(h,F)}return o}function oe(n,t,e){const o={},r=t.valueParseFunction??Q;if(n.config)for(const s in n.config)o[s]=r(s,n.config,t.assetsContainer||t.scene,t.scene);if(St(n.className)){if(!t.pathConverter)throw new Error("Path converter is required for this block");o.pathConverter=t.pathConverter}const i=new e(o);i.uniqueId=n.uniqueId;for(let s=0;s<n.dataInputs.length;s++){const l=i.getDataInput(n.dataInputs[s].name);if(l)l.deserialize(n.dataInputs[s]);else throw new Error("Could not find data input with name "+n.dataInputs[s].name+" in block "+n.className)}for(let s=0;s<n.dataOutputs.length;s++){const l=i.getDataOutput(n.dataOutputs[s].name);if(l)l.deserialize(n.dataOutputs[s]);else throw new Error("Could not find data output with name "+n.dataOutputs[s].name+" in block "+n.className)}return i.metadata=n.metadata,i.deserialize&&i.deserialize(n),i}const re={float:{length:1,flowGraphType:"number",elementType:"number"},bool:{length:1,flowGraphType:"boolean",elementType:"boolean"},float2:{length:2,flowGraphType:"Vector2",elementType:"number"},float3:{length:3,flowGraphType:"Vector3",elementType:"number"},float4:{length:4,flowGraphType:"Vector4",elementType:"number"},float4x4:{length:16,flowGraphType:"Matrix",elementType:"number"},float2x2:{length:4,flowGraphType:"Matrix2D",elementType:"number"},float3x3:{length:9,flowGraphType:"Matrix3D",elementType:"number"},int:{length:1,flowGraphType:"FlowGraphInteger",elementType:"number"}};class ne{constructor(t,e,o){this._interactivityGraph=t,this._gltf=e,this._loader=o,this._types=[],this._mappings=[],this._staticVariables=[],this._events=[],this._internalEventsCounter=0,this._nodes=[],this._parseTypes(),this._parseDeclarations(),this._parseVariables(),this._parseEvents(),this._parseNodes()}get arrays(){return{types:this._types,mappings:this._mappings,staticVariables:this._staticVariables,events:this._events,nodes:this._nodes}}_parseTypes(){if(this._interactivityGraph.types)for(const t of this._interactivityGraph.types)this._types.push(re[t.signature])}_parseDeclarations(){if(this._interactivityGraph.declarations)for(const t of this._interactivityGraph.declarations){const e=xt(t);if(!e)throw p.Error(["No mapping found for declaration",t]),new Error("Error parsing declarations");this._mappings.push({flowGraphMapping:e,fullOperationName:t.extension?t.op+":"+t.extension:t.op})}}_parseVariables(){if(this._interactivityGraph.variables)for(const t of this._interactivityGraph.variables){const e=this._parseVariable(t);this._staticVariables.push(e)}}_parseVariable(t,e){const o=this._types[t.type];if(!o)throw p.Error(["No type found for variable",t]),new Error("Error parsing variables");if(t.value&&t.value.length!==o.length)throw p.Error(["Invalid value length for variable",t,o]),new Error("Error parsing variables");const r=t.value||[];if(!r.length)switch(o.flowGraphType){case"boolean":r.push(!1);break;case"FlowGraphInteger":r.push(0);break;case"number":r.push(NaN);break;case"Vector2":r.push(NaN,NaN);break;case"Vector3":r.push(NaN,NaN,NaN);break;case"Vector4":case"Matrix2D":case"Quaternion":r.fill(NaN,0,4);break;case"Matrix":r.fill(NaN,0,16);break;case"Matrix3D":r.fill(NaN,0,9);break}return{type:o.flowGraphType,value:e?e(r,this):r}}_parseEvents(){if(this._interactivityGraph.events)for(const t of this._interactivityGraph.events){const e={eventId:t.id||"internalEvent_"+this._internalEventsCounter++};t.values&&(e.eventData=Object.keys(t.values).map(o=>{var l;const r=(l=t.values)==null?void 0:l[o];if(!r)throw p.Error(["No value found for event key",o]),new Error("Error parsing events");const i=this._types[r.type];if(!i)throw p.Error(["No type found for event value",r]),new Error("Error parsing events");const s=typeof r.value<"u"?this._parseVariable(r):void 0;return{id:o,type:i.flowGraphType,eventData:!0,value:s}})),this._events.push(e)}}_parseNodes(){if(this._interactivityGraph.nodes)for(const t of this._interactivityGraph.nodes){if(typeof t.declaration!="number")throw p.Error(["No declaration found for node",t]),new Error("Error parsing nodes");const e=this._mappings[t.declaration];if(!e)throw p.Error(["No mapping found for node",t]),new Error("Error parsing nodes");if(e.flowGraphMapping.validation&&!e.flowGraphMapping.validation(t,this._interactivityGraph,this._gltf))throw new Error(`Error validating interactivity node ${t}`);const o=[];for(const r of e.flowGraphMapping.blocks){const i=this._getEmptyBlock(r,e.fullOperationName);this._parseNodeConfiguration(t,i,e.flowGraphMapping,r),o.push(i)}this._nodes.push({blocks:o,fullOperationName:e.fullOperationName})}}_getEmptyBlock(t,e){return{uniqueId:A(),className:t,dataInputs:[],dataOutputs:[],signalInputs:[],signalOutputs:[],config:{},type:e,metadata:{}}}_parseNodeConfiguration(t,e,o,r){const i=e.config;t.configuration&&Object.keys(t.configuration).forEach(s=>{var f,I;const l=(f=t.configuration)==null?void 0:f[s];if(!l)throw p.Error(["No value found for node configuration",s]),new Error("Error parsing node configuration");const c=(I=o.configuration)==null?void 0:I[s];if(c&&c.toBlock?c.toBlock===r:o.blocks.indexOf(r)===0){const v=(c==null?void 0:c.name)||s;(!l||typeof l.value>"u")&&typeof(c==null?void 0:c.defaultValue)<"u"?i[v]={value:c.defaultValue}:l.value.length>=1?i[v]={value:l.value.length===1?l.value[0]:l.value}:p.Warn(["Invalid value for node configuration",l]),c&&c.dataTransformer&&(i[v].value=c.dataTransformer([i[v].value],this)[0])}})}_parseNodeConnections(t){var e,o,r,i,s,l,c,d,f,I,v,R,L,N,h,F,u,H,et;for(let S=0;S<this._nodes.length;S++){const T=(e=this._interactivityGraph.nodes)==null?void 0:e[S];if(!T)throw p.Error(["No node found for interactivity node",this._nodes[S]]),new Error("Error parsing node connections");const V=this._nodes[S],G=this._mappings[T.declaration];if(!G)throw p.Error(["No mapping found for node",T]),new Error("Error parsing node connections");const ot=T.flows||{},gt=Object.keys(ot).sort();for(const m of gt){const w=ot[m],_=(r=(o=G.flowGraphMapping.outputs)==null?void 0:o.flows)==null?void 0:r[m],D=(_==null?void 0:_.name)||m,M=this._createNewSocketConnection(D,!0);(_&&_.toBlock&&V.blocks.find(k=>k.className===_.toBlock)||V.blocks[0]).signalOutputs.push(M);const Z=w.node,g=this._nodes[Z];if(!g)throw p.Error(["No node found for input node id",Z]),new Error("Error parsing node connections");const b=at(g.fullOperationName);if(!b)throw p.Error(["No mapping found for input node",g]),new Error("Error parsing node connections");let E=(s=(i=b.inputs)==null?void 0:i.flows)==null?void 0:s[w.socket||"in"],C=!1;if(!E)for(const k in(l=b.inputs)==null?void 0:l.flows)k.startsWith("[")&&k.endsWith("]")&&(C=!0,E=(d=(c=b.inputs)==null?void 0:c.flows)==null?void 0:d[k]);const y=E?C?E.name.replace("$1",w.socket||""):E.name:w.socket||"in",$=E&&E.toBlock&&g.blocks.find(k=>k.className===E.toBlock)||g.blocks[0];let P=$.signalInputs.find(k=>k.name===y);P||(P=this._createNewSocketConnection(y),$.signalInputs.push(P)),P.connectedPointIds.push(M.uniqueId),M.connectedPointIds.push(P.uniqueId)}const rt=T.values||{},Et=Object.keys(rt);for(const m of Et){const w=rt[m];let _=(I=(f=G.flowGraphMapping.inputs)==null?void 0:f.values)==null?void 0:I[m],D=!1;if(!_)for(const g in(v=G.flowGraphMapping.inputs)==null?void 0:v.values)g.startsWith("[")&&g.endsWith("]")&&(D=!0,_=(L=(R=G.flowGraphMapping.inputs)==null?void 0:R.values)==null?void 0:L[g]);const M=_?D?_.name.replace("$1",m):_.name:m,q=this._createNewSocketConnection(M);if((_&&_.toBlock&&V.blocks.find(g=>g.className===_.toBlock)||V.blocks[0]).dataInputs.push(q),w.value!==void 0){const g=this._parseVariable(w,_&&_.dataTransformer);t._connectionValues[q.uniqueId]=g}else if(typeof w.node<"u"){const g=w.node,b=w.socket||"value",E=this._nodes[g];if(!E)throw p.Error(["No node found for output socket reference",w]),new Error("Error parsing node connections");const C=at(E.fullOperationName);if(!C)throw p.Error(["No mapping found for output socket reference",w]),new Error("Error parsing node connections");let y=(h=(N=C.outputs)==null?void 0:N.values)==null?void 0:h[b],$=!1;if(!y)for(const O in(F=C.outputs)==null?void 0:F.values)O.startsWith("[")&&O.endsWith("]")&&($=!0,y=(H=(u=C.outputs)==null?void 0:u.values)==null?void 0:H[O]);const P=y?$?y.name.replace("$1",b):y==null?void 0:y.name:b,k=y&&y.toBlock&&E.blocks.find(O=>O.className===y.toBlock)||E.blocks[0];let K=k.dataOutputs.find(O=>O.name===P);K||(K=this._createNewSocketConnection(P,!0),k.dataOutputs.push(K)),q.connectedPointIds.push(K.uniqueId),K.connectedPointIds.push(q.uniqueId)}else throw p.Error(["Invalid value for value connection",w]),new Error("Error parsing node connections")}if(G.flowGraphMapping.interBlockConnectors)for(const m of G.flowGraphMapping.interBlockConnectors){const w=m.input,_=m.output,D=m.isVariable;this._connectFlowGraphNodes(w,_,V.blocks[m.inputBlockIndex],V.blocks[m.outputBlockIndex],D)}if(G.flowGraphMapping.extraProcessor){const m=(et=this._interactivityGraph.declarations)==null?void 0:et[T.declaration];if(!m)throw p.Error(["No declaration found for extra processor",T]),new Error("Error parsing node connections");V.blocks=G.flowGraphMapping.extraProcessor(T,m,G.flowGraphMapping,this,V.blocks,t,this._gltf)}}}_createNewSocketConnection(t,e){return{uniqueId:A(),name:t,_connectionType:e?1:0,connectedPointIds:[]}}_connectFlowGraphNodes(t,e,o,r,i){const s=i?o.dataInputs:o.signalInputs,l=i?r.dataOutputs:r.signalOutputs,c=s.find(f=>f.name===t)||this._createNewSocketConnection(t),d=l.find(f=>f.name===e)||this._createNewSocketConnection(e,!0);s.find(f=>f.name===t)||s.push(c),l.find(f=>f.name===e)||l.push(d),c.connectedPointIds.push(d.uniqueId),d.connectedPointIds.push(c.uniqueId)}getVariableName(t){return"staticVariable_"+t}serializeToFlowGraph(){const t={uniqueId:A(),_userVariables:{},_connectionValues:{}};this._parseNodeConnections(t);for(let o=0;o<this._staticVariables.length;o++){const r=this._staticVariables[o];t._userVariables[this.getVariableName(o)]=r}return{rightHanded:!0,allBlocks:this._nodes.reduce((o,r)=>o.concat(r.blocks),[]),executionContexts:[t]}}}const U="KHR_interactivity";class ft{constructor(t){this._loader=t,this.name=U,this.enabled=this._loader.isExtensionUsed(U),this._pathConverter=Dt(this._loader.gltf),t._skipStartAnimationStep=!0;const e=t.babylonScene;e&&wt(e)}dispose(){this._loader=null,delete this._pathConverter}async onReady(){var i;if(!this._loader.babylonScene||!this._pathConverter)return;const t=this._loader.babylonScene,e=(i=this._loader.gltf.extensions)==null?void 0:i.KHR_interactivity;if(!e)return;const o=new B({scene:t});o.dispatchEventsSynchronously=!1;const r=e.graphs.map(s=>new ne(s,this._loader.gltf,this._loader).serializeToFlowGraph());await Promise.all(r.map(s=>jt(s,{coordinator:o,pathConverter:this._pathConverter}))),o.start()}}function wt(n){x("/extensions/KHR_interactivity/?/activeCamera/rotation",{get:()=>n.activeCamera?X.FromRotationMatrix(n.activeCamera.getWorldMatrix()).normalize():new X(NaN,NaN,NaN,NaN),type:"Quaternion",getTarget:()=>n.activeCamera}),x("/extensions/KHR_interactivity/?/activeCamera/position",{get:()=>n.activeCamera?n.activeCamera.position:new ut(NaN,NaN,NaN),type:"Vector3",getTarget:()=>n.activeCamera}),x("/animations/{}/extensions/KHR_interactivity/isPlaying",{get:t=>{var e;return((e=t._babylonAnimationGroup)==null?void 0:e.isPlaying)??!1},type:"boolean",getTarget:t=>t._babylonAnimationGroup}),x("/animations/{}/extensions/KHR_interactivity/minTime",{get:t=>{var e;return(((e=t._babylonAnimationGroup)==null?void 0:e.from)??0)/60},type:"number",getTarget:t=>t._babylonAnimationGroup}),x("/animations/{}/extensions/KHR_interactivity/maxTime",{get:t=>{var e;return(((e=t._babylonAnimationGroup)==null?void 0:e.to)??0)/60},type:"number",getTarget:t=>t._babylonAnimationGroup}),x("/animations/{}/extensions/KHR_interactivity/playhead",{get:t=>{var e;return(((e=t._babylonAnimationGroup)==null?void 0:e.getCurrentFrame())??0)/60},type:"number",getTarget:t=>t._babylonAnimationGroup}),x("/animations/{}/extensions/KHR_interactivity/virtualPlayhead",{get:t=>{var e;return(((e=t._babylonAnimationGroup)==null?void 0:e.getCurrentFrame())??0)/60},type:"number",getTarget:t=>t._babylonAnimationGroup})}Ht(U,"FlowGraphGLTFDataProvider",async()=>(await a(()=>import("./flowGraphGLTFDataProvider.BfhrUZfi.js"),__vite__mapDeps([57,1,2,3]),import.meta.url)).FlowGraphGLTFDataProvider);Tt(U);Vt(U,!0,n=>new ft(n));const de=Object.freeze(Object.defineProperty({__proto__:null,KHR_interactivity:ft,_AddInteractivityObjectModel:wt},Symbol.toStringTag,{value:"Module"}));export{$t as F,Mt as G,de as K,mt as _,Zt as a,Jt as b,Xt as c,B as d,ce as e,J as f,Ut as g,tt as h,_e as i,ue as j,pe as k,he as l};
//# sourceMappingURL=KHR_interactivity.DEAVS2UW.js.map
