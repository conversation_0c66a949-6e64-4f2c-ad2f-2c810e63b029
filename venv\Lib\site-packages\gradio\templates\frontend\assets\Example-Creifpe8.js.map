{"version": 3, "file": "Example-Creifpe8.js", "sources": ["../../../../js/nativeplot/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let title: string | null;\n\texport let x: string;\n\texport let y: string;\n</script>\n\n{#if title}\n\t{title}\n{:else}\n\t{x} x {y}\n{/if}\n"], "names": ["ctx", "create_if_block", "title", "$$props", "x", "y"], "mappings": "qLASEA,EAAC,CAAA,CAAA,MAAC,KAAG,MAACA,EAAC,CAAA,CAAA,sDAAPA,EAAC,CAAA,CAAA,WAAKA,EAAC,CAAA,CAAA,iEAFPA,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,6DADFA,EAAK,CAAA,EAAAC,0MALE,GAAA,CAAA,MAAAC,CAAA,EAAAC,EACA,CAAA,EAAAC,CAAA,EAAAD,EACA,CAAA,EAAAE,CAAA,EAAAF"}