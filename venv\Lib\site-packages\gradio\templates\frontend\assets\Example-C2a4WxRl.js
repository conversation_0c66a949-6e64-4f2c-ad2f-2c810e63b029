const{SvelteComponent:f,attr:d,detach:g,element:v,flush:i,init:_,insert:h,noop:r,safe_not_equal:o,toggle_class:a}=window.__gradio__svelte__internal;function y(s){let e;return{c(){e=v("div"),d(e,"class","prose svelte-zvfedn"),a(e,"table",s[1]==="table"),a(e,"gallery",s[1]==="gallery"),a(e,"selected",s[2])},m(t,l){h(t,e,l),e.innerHTML=s[0]},p(t,[l]){l&1&&(e.innerHTML=t[0]),l&2&&a(e,"table",t[1]==="table"),l&2&&a(e,"gallery",t[1]==="gallery"),l&4&&a(e,"selected",t[2])},i:r,o:r,d(t){t&&g(e)}}}function m(s,e,t){let{value:l}=e,{type:u}=e,{selected:c=!1}=e;return s.$$set=n=>{"value"in n&&t(0,l=n.value),"type"in n&&t(1,u=n.type),"selected"in n&&t(2,c=n.selected)},[l,u,c]}class b extends f{constructor(e){super(),_(this,e,m,y,o,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),i()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),i()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),i()}}export{b as default};
//# sourceMappingURL=Example-C2a4WxRl.js.map
