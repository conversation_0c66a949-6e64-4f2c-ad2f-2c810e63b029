import{F as p,g as s,G as o}from"./KHR_interactivity-DTxiAnOo.js";import{R as e,F as l}from"./declarationMapper-BZjsjg7g.js";import{R as u}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class h extends p{constructor(t){super(t),this.config=t,this.type=this.registerDataInput("type",e,t.type),this.value=this.registerDataOutput("value",e),this.index=this.registerDataInput("index",e,new l(s(t.index??-1)))}_updateOutputs(t){const a=this.type.getValue(t),r=this.index.getValue(t),i=o(t.assetsContext,a,s(r),this.config.useIndexAsUniqueId);this.value.setValue(i,t)}getClassName(){return"FlowGraphGetAssetBlock"}}u("FlowGraphGetAssetBlock",h);export{h as FlowGraphGetAssetBlock};
//# sourceMappingURL=flowGraphGetAssetBlock-DjXwA5iQ.js.map
