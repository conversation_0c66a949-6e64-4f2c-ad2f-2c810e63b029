import { c as create_ssr_component } from './ssr-C3HYbsxA.js';
import { t, S as SvelteComponentDev } from './ssr-BO6k2UBO.js';
import './Component-NmRBwSfF.js';

const s=typeof window<"u";if(s){const o={SvelteComponent:SvelteComponentDev};for(const e in t)e!=="SvelteComponent"&&(e==="SvelteComponentDev"?o[e]=o.SvelteComponent:o[e]=t[e]);window.__gradio__svelte__internal=o,window.__gradio__svelte__internal.globals={},window.globals=window;}const a={code:"body{background:var(--body-background-fill);color:var(--body-text-color)}@media(prefers-color-scheme: dark){body:not(.theme-loaded){background:var(--neutral-950)}}",map:'{"version":3,"file":"+layout.svelte","sources":["+layout.svelte"],"sourcesContent":["<script>\\n\\timport \\"@gradio/theme/reset.css\\";\\n\\timport \\"@gradio/theme/global.css\\";\\n\\timport \\"@gradio/theme/pollen.css\\";\\n\\timport \\"@gradio/theme/typography.css\\";\\n\\timport \\"./svelte_init\\";\\n<\/script>\\n\\n<slot></slot>\\n\\n<style>\\n\\t:global(body) {\\n\\t\\tbackground: var(--body-background-fill);\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\n\\t@media (prefers-color-scheme: dark) {\\n\\t\\t:global(body:not(.theme-loaded)) {\\n\\t\\t\\tbackground: var(--neutral-950);\\n\\t\\t}\\n\\t}</style>\\n"],"names":[],"mappings":"AAWS,IAAM,CACb,UAAU,CAAE,IAAI,sBAAsB,CAAC,CACvC,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,MAAO,uBAAuB,IAAI,CAAE,CAC3B,uBAAyB,CAChC,UAAU,CAAE,IAAI,aAAa,CAC9B,CACD"}'},d=create_ssr_component((o,e,l,n)=>(o.css.add(a),`${n.default?n.default({}):""}`));

export { d as default };
//# sourceMappingURL=_layout.svelte-DtUVntm9.js.map
