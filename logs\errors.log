2025-08-01 15:22:15 | ERROR    | core.ai_engine:generate_response:65 | AI回复生成失败: 'SearchManager' object has no attribute 'search'
2025-08-01 15:25:25 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://zhuanlan.zhihu.com/p/30178868633
2025-08-01 15:33:46 | ERROR    | ui.app.components.sidebar:load_chat_history:204 | 加载对话历史失败: 'ContextEngine' object has no attribute 'get_all_sessions'
2025-08-01 15:35:46 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://ticket.louvre.fr/
2025-08-01 15:39:34 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 503 Server Error: Service Unavailable for url: https://beta.finance.yahoo.com/quote/AMZN/
2025-08-01 15:39:35 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 503 Server Error: Service Unavailable for url: https://finance.yahoo.com/quotes/AMZN/
2025-08-01 15:39:37 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 503 Server Error: Service Unavailable for url: https://finance.yahoo.com/quote/AMZN/analysis/
2025-08-01 15:39:38 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 503 Server Error: Service Unavailable for url: https://finance.yahoo.com/chart/AMZN
2025-08-01 15:40:52 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://zhuanlan.zhihu.com/p/103033552
2025-08-01 15:40:54 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://www.zhihu.com/question/566203859
2025-08-01 15:40:59 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://zhuanlan.zhihu.com/p/178490624
2025-08-01 15:41:14 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://zhuanlan.zhihu.com/p/27117691
2025-08-01 16:14:14 | ERROR    | ui.app.components.sidebar:load_chat_history:204 | 加载对话历史失败: 'ContextEngine' object has no attribute 'get_all_sessions'
2025-08-01 16:20:05 | ERROR    | __main__:main:91 | Web界面启动失败: Couldn't start the app because 'http://127.0.0.1:7861/gradio_api/startup-events' failed (code 502). Check your network or proxy settings to ensure localhost is accessible.
2025-08-01 16:20:28 | ERROR    | __main__:main:91 | Web界面启动失败: Couldn't start the app because 'http://127.0.0.1:7860/gradio_api/startup-events' failed (code 502). Check your network or proxy settings to ensure localhost is accessible.
2025-08-01 17:20:04 | ERROR    | core.llm.model_manager:load_model:126 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 17:30:25 | ERROR    | core.llm.model_manager:load_model:158 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 17:30:25 | ERROR    | core.llm.model_manager:load_model:159 | 错误类型: OSError
2025-08-01 17:36:53 | ERROR    | core.llm.model_manager:load_model:158 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 17:36:53 | ERROR    | core.llm.model_manager:load_model:159 | 错误类型: OSError
2025-08-01 17:49:27 | ERROR    | core.llm.model_manager:load_model:158 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 17:49:27 | ERROR    | core.llm.model_manager:load_model:159 | 错误类型: OSError
2025-08-01 18:10:12 | ERROR    | core.llm.model_manager:load_model:188 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 18:10:12 | ERROR    | core.llm.model_manager:load_model:189 | 错误类型: OSError
2025-08-01 19:08:42 | ERROR    | core.llm.model_manager:load_model:188 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 19:08:42 | ERROR    | core.llm.model_manager:load_model:189 | 错误类型: OSError
2025-08-01 19:29:33 | ERROR    | core.llm.model_manager:load_model:188 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 19:29:33 | ERROR    | core.llm.model_manager:load_model:189 | 错误类型: OSError
2025-08-07 13:51:27 | ERROR    | core.llm.model_manager:load_model:188 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-07 13:51:27 | ERROR    | core.llm.model_manager:load_model:189 | 错误类型: OSError
2025-08-07 17:16:27 | ERROR    | core.ai_engine:generate_response:67 | AI回复生成失败: 'locale' codec can't encode character '\u5e74' in position 2: encoding error
2025-08-07 17:16:32 | ERROR    | core.llm.model_manager:generate_text_complete:155 | 文本生成失败: Error code: 502
2025-08-07 21:21:24 | ERROR    | ui.app.components.sidebar:delete_chat:371 | 删除对话失败: 'ContextEngine' object has no attribute 'delete_session'
2025-08-07 21:21:34 | ERROR    | core.ai_engine:generate_response:74 | AI回复生成失败: 'locale' codec can't encode character '\u5e74' in position 2: encoding error
2025-08-07 21:44:55 | ERROR    | core.ai_engine:generate_response:78 | AI回复生成失败: 'locale' codec can't encode character '\u5e74' in position 2: encoding error
2025-08-07 22:03:31 | ERROR    | core.ai_engine:generate_response:78 | AI回复生成失败: 'locale' codec can't encode character '\u5e74' in position 2: encoding error
