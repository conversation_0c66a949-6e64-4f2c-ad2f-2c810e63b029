import{ar as d,C as l,an as u,ao as h}from"./index.BoI39RQH.js";import{GLTFLoader as p}from"./glTFLoader.BetPWe9U.js";const t="KHR_materials_unlit";class c{constructor(r){this.name=t,this.order=210,this._loader=r,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,o,e){return p.LoadExtensionAsync(r,o,this.name,()=>this._loadUnlitPropertiesAsync(r,o,e))}_loadUnlitPropertiesAsync(r,o,e){if(!(e instanceof d))throw new Error(`${r}: Material type not supported`);const n=new Array;e.unlit=!0;const s=o.pbrMetallicRoughness;return s&&(s.baseColorFactor?(e.albedoColor=l.FromArray(s.baseColorFactor),e.alpha=s.baseColorFactor[3]):e.albedoColor=l.<PERSON>(),s.baseColorTexture&&n.push(this._loader.loadTextureInfoAsync(`${r}/baseColorTexture`,s.baseColorTexture,a=>{a.name=`${e.name} (Base Color)`,e.albedoTexture=a}))),o.doubleSided&&(e.backFaceCulling=!1,e.twoSidedLighting=!0),this._loader.loadMaterialAlphaProperties(r,o,e),Promise.all(n).then(()=>{})}}u(t);h(t,!0,i=>new c(i));export{c as KHR_materials_unlit};
//# sourceMappingURL=KHR_materials_unlit.I1FWdR44.js.map
