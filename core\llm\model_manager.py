"""
LLM模型管理器
通过连接到 llama.cpp 服务器来管理和使用 LLM 模型。
"""

import os
import yaml
import threading
from typing import Optional, Dict, Any, Generator, List
from pathlib import Path
import logging
from dataclasses import dataclass

# 使用 `openai` 库与 llama.cpp 服务器进行交互
from openai import OpenAI

from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class ModelConfig:
    """模型配置类"""
    name: str
    # 不再需要 path，因为模型由服务器加载
    context_length: int = 8000
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 40
    repetition_penalty: float = 1.1
    max_tokens: int = 2048

class LLMModelManager:
    """LLM模型管理器（客户端模式）"""
    
    def __init__(self, config_path: str = "config/settings/app_config.yaml"):
        self.config_path = config_path
        self.client: Optional[OpenAI] = None
        self.current_config: Optional[ModelConfig] = None
        self.model_lock = threading.Lock()
        self.available_models: Dict[str, str] = {}
        
        self._load_config()
        self._scan_models()  # 扫描本地模型文件以供用户选择
        self._initialize_client() # 初始化客户端

    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                self.llm_config = config.get('models', {}).get('llm', {})
                logger.info("配置文件加载成功")
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            self.llm_config = {}
    
    def _scan_models(self):
        """扫描本地模型目录，让用户知道有哪些模型可以启动服务"""
        model_path = self.llm_config.get('model_path', 'models/llm')
        if not os.path.exists(model_path):
            logger.warning(f"模型目录不存在: {model_path}")
            return
        
        for file in os.listdir(model_path):
            if file.endswith('.gguf'):
                model_name = file.replace('.gguf', '')
                self.available_models[model_name] = os.path.join(model_path, file)
                logger.info(f"发现可用模型文件: {model_name}")

    def _initialize_client(self):
        """初始化OpenAI客户端以连接到llama.cpp服务器"""
        server_url = self.llm_config.get('server_url', 'http://localhost:8080/v1')
        with self.model_lock:
            try:
                self.client = OpenAI(
                    base_url=server_url,
                    api_key="not-needed"  # llama.cpp服务器不需要API密钥
                )
                logger.info(f"已连接到LLM服务器: {server_url}")
            except Exception as e:
                logger.error(f"连接LLM服务器失败: {e}")
                self.client = None

    def get_available_models(self) -> Dict[str, str]:
        """获取可用模型文件列表"""
        return self.available_models.copy()

    def load_model(self, model_name: str, **kwargs) -> bool:
        """设置当前活动模型（实际上只是设置配置）"""
        if model_name not in self.available_models:
            logger.error(f"模型配置不存在: {model_name}")
            return False
        
        with self.model_lock:
            self.current_config = ModelConfig(
                name=model_name,
                context_length=kwargs.get('context_length', self.llm_config.get('max_context_length', 8000)),
                temperature=kwargs.get('temperature', self.llm_config.get('temperature', 0.7)),
                top_p=kwargs.get('top_p', self.llm_config.get('top_p', 0.9)),
                top_k=kwargs.get('top_k', self.llm_config.get('top_k', 40)),
                repetition_penalty=kwargs.get('repetition_penalty', self.llm_config.get('repetition_penalty', 1.1)),
                max_tokens=kwargs.get('max_tokens', self.llm_config.get('max_new_tokens', 2048)),
            )
            logger.info(f"已切换到模型配置: {model_name}")
        return True

    def generate_text(self, messages: List[Dict[str, str]], **kwargs) -> Generator[str, None, None]:
        """通过API生成文本（流式输出）"""
        if not self.client or not self.current_config:
            yield "错误: LLM客户端未初始化或未选择模型"
            return

        try:
            max_tokens = kwargs.get('max_tokens', self.current_config.max_tokens)
            if max_tokens == -1:
                max_tokens = None # OpenAI SDK 中 None 表示无限制

            stream = self.client.chat.completions.create(
                model=self.current_config.name, # 模型名称需要与服务器加载的匹配
                messages=messages,
                temperature=kwargs.get('temperature', self.current_config.temperature),
                top_p=kwargs.get('top_p', self.current_config.top_p),
                max_tokens=max_tokens,
                stream=True
            )
            for chunk in stream:
                content = chunk.choices[0].delta.content
                if content:
                    yield content
        except Exception as e:
            error_msg = str(e)
            # 处理编码错误和网络错误
            try:
                error_msg = error_msg.encode('utf-8', errors='ignore').decode('utf-8')
            except:
                error_msg = "文本生成时出现未知错误"
            
            # 特殊处理502错误
            if "502" in error_msg or "Bad Gateway" in error_msg:
                error_msg = "服务器连接失败(502)，请检查llama.cpp服务器是否正常运行"
            elif "Connection" in error_msg:
                error_msg = "无法连接到LLM服务器，请检查服务器状态"
            
            logger.error(f"文本生成失败: {error_msg}")
            yield f"错误: {error_msg}"

    def generate_text_complete(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """通过API生成完整文本（非流式）"""
        if not self.client or not self.current_config:
            return "错误: LLM客户端未初始化或未选择模型"

        try:
            max_tokens = kwargs.get('max_tokens', self.current_config.max_tokens)
            if max_tokens == -1:
                max_tokens = None # OpenAI SDK 中 None 表示无限制

            completion = self.client.chat.completions.create(
                model=self.current_config.name,
                messages=messages,
                temperature=kwargs.get('temperature', self.current_config.temperature),
                top_p=kwargs.get('top_p', self.current_config.top_p),
                max_tokens=max_tokens,
                stream=False
            )
            return completion.choices[0].message.content
        except Exception as e:
            error_msg = str(e)
            # 处理编码错误和网络错误
            try:
                error_msg = error_msg.encode('utf-8', errors='ignore').decode('utf-8')
            except:
                error_msg = "文本生成时出现未知错误"
            
            # 特殊处理502错误
            if "502" in error_msg or "Bad Gateway" in error_msg:
                error_msg = "服务器连接失败(502)，请检查llama.cpp服务器是否正常运行"
            elif "Connection" in error_msg:
                error_msg = "无法连接到LLM服务器，请检查服务器状态"
            
            logger.error(f"文本生成失败: {error_msg}")
            return f"错误: {error_msg}"

    def get_current_model_info(self) -> Optional[Dict[str, Any]]:
        """获取当前模型配置信息"""
        if not self.current_config:
            return None
        
        return self.current_config.__dict__

    def unload_model(self):
        """断开与服务器的连接"""
        with self.model_lock:
            if self.client:
                self.client.close()
                self.client = None
                self.current_config = None
                logger.info("已与LLM服务器断开连接")

    def reload_models(self):
        """重新扫描模型并重新连接客户端"""
        self.available_models.clear()
        self._scan_models()
        self._initialize_client()
        logger.info("模型列表已刷新并已重新连接客户端")

# 全局模型管理器实例
model_manager = LLMModelManager()
