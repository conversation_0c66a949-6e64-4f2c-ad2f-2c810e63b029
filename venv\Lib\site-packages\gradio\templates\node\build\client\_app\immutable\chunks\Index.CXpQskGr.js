import{SvelteComponent as T,init as k,safe_not_equal as v,binding_callbacks as B,bind as S,create_component as q,claim_component as w,mount_component as A,add_flush_callback as C,transition_in as d,transition_out as r,destroy_component as D,createEventDispatcher as E,create_slot as I,update_slot_base as j,get_all_dirty_from_scope as z,get_slot_changes as F}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{T as G}from"./Tabs.DkGgrUWn.js";import{a as R}from"./Tabs.DkGgrUWn.js";function H(s){let e;const a=s[6].default,l=I(a,s,s[10],null);return{c(){l&&l.c()},l(i){l&&l.l(i)},m(i,f){l&&l.m(i,f),e=!0},p(i,f){l&&l.p&&(!e||f&1024)&&j(l,a,i,i[10],e?F(a,i[10],f,null):z(i[10]),null)},i(i){e||(d(l,i),e=!0)},o(i){r(l,i),e=!1},d(i){l&&l.d(i)}}}function J(s){let e,a,l;function i(t){s[7](t)}let f={visible:s[1],elem_id:s[2],elem_classes:s[3],initial_tabs:s[4],$$slots:{default:[H]},$$scope:{ctx:s}};return s[0]!==void 0&&(f.selected=s[0]),e=new G({props:f}),B.push(()=>S(e,"selected",i)),e.$on("change",s[8]),e.$on("select",s[9]),{c(){q(e.$$.fragment)},l(t){w(e.$$.fragment,t)},m(t,_){A(e,t,_),l=!0},p(t,[_]){const c={};_&2&&(c.visible=t[1]),_&4&&(c.elem_id=t[2]),_&8&&(c.elem_classes=t[3]),_&16&&(c.initial_tabs=t[4]),_&1024&&(c.$$scope={dirty:_,ctx:t}),!a&&_&1&&(a=!0,c.selected=t[0],C(()=>a=!1)),e.$set(c)},i(t){l||(d(e.$$.fragment,t),l=!0)},o(t){r(e.$$.fragment,t),l=!1},d(t){D(e,t)}}}function K(s,e,a){let{$$slots:l={},$$scope:i}=e;const f=E();let{visible:t=!0}=e,{elem_id:_=""}=e,{elem_classes:c=[]}=e,{selected:u}=e,{initial_tabs:m=[]}=e,{gradio:o}=e;function b(n){u=n,a(0,u)}const g=()=>o==null?void 0:o.dispatch("change"),h=n=>o==null?void 0:o.dispatch("select",n.detail);return s.$$set=n=>{"visible"in n&&a(1,t=n.visible),"elem_id"in n&&a(2,_=n.elem_id),"elem_classes"in n&&a(3,c=n.elem_classes),"selected"in n&&a(0,u=n.selected),"initial_tabs"in n&&a(4,m=n.initial_tabs),"gradio"in n&&a(5,o=n.gradio),"$$scope"in n&&a(10,i=n.$$scope)},s.$$.update=()=>{s.$$.dirty&1&&f("prop_change",{selected:u})},[u,t,_,c,m,o,l,b,g,h,i]}class O extends T{constructor(e){super(),k(this,e,K,J,v,{visible:1,elem_id:2,elem_classes:3,selected:0,initial_tabs:4,gradio:5})}}export{G as BaseTabs,R as TABS,O as default};
//# sourceMappingURL=Index.CXpQskGr.js.map
