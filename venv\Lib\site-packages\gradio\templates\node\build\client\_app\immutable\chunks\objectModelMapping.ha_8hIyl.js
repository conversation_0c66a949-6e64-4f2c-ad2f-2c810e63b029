import{_ as c,c as S,s as A,aq as M,M as g,V as a,b1 as y,a as P,N as d,T as I,l as C,R as b,ay as p,aS as D,Q as O}from"./index.BoI39RQH.js";class r{}r.AUTOSAMPLERSUFFIX="Sampler";r.DISABLEUA="#define DISABLE_UNIFORMITY_ANALYSIS";r.ALPHA_DISABLE=0;r.ALPHA_ADD=1;r.ALPHA_COMBINE=2;r.ALPHA_SUBTRACT=3;r.ALPHA_MULTIPLY=4;r.AL<PERSON>HA_MAXIMIZED=5;r.ALPHA_ONEONE=6;r.<PERSON><PERSON><PERSON>_PREMULTIPLIED=7;r.ALPHA_PREMULTIPLIED_PORTERDUFF=8;r.ALPHA_INTERPOLATE=9;r.ALPHA_SCREENMODE=10;r.ALPHA_ONEONE_ONEONE=11;r.ALP<PERSON>_ALPHATOCOLOR=12;r.<PERSON><PERSON><PERSON>_REVERSEONEMINUS=13;r.<PERSON>_DSTONEMINUSSRCALPHA=14;r.<PERSON><PERSON><PERSON>_<PERSON>ONE_ONEZERO=15;r.ALPHA_EXCLUSION=16;r.ALPHA_LAYER_ACCUMULATE=17;r.ALPHA_EQUATION_ADD=0;r.ALPHA_EQUATION_SUBSTRACT=1;r.ALPHA_EQUATION_REVERSE_SUBTRACT=2;r.ALPHA_EQUATION_MAX=3;r.ALPHA_EQUATION_MIN=4;r.ALPHA_EQUATION_DARKEN=5;r.DELAYLOADSTATE_NONE=0;r.DELAYLOADSTATE_LOADED=1;r.DELAYLOADSTATE_LOADING=2;r.DELAYLOADSTATE_NOTLOADED=4;r.NEVER=512;r.ALWAYS=519;r.LESS=513;r.EQUAL=514;r.LEQUAL=515;r.GREATER=516;r.GEQUAL=518;r.NOTEQUAL=517;r.KEEP=7680;r.ZERO=0;r.REPLACE=7681;r.INCR=7682;r.DECR=7683;r.INVERT=5386;r.INCR_WRAP=34055;r.DECR_WRAP=34056;r.TEXTURE_CLAMP_ADDRESSMODE=0;r.TEXTURE_WRAP_ADDRESSMODE=1;r.TEXTURE_MIRROR_ADDRESSMODE=2;r.TEXTURE_CREATIONFLAG_STORAGE=1;r.TEXTUREFORMAT_ALPHA=0;r.TEXTUREFORMAT_LUMINANCE=1;r.TEXTUREFORMAT_LUMINANCE_ALPHA=2;r.TEXTUREFORMAT_RGB=4;r.TEXTUREFORMAT_RGBA=5;r.TEXTUREFORMAT_RED=6;r.TEXTUREFORMAT_R=6;r.TEXTUREFORMAT_R16_UNORM=33322;r.TEXTUREFORMAT_RG16_UNORM=33324;r.TEXTUREFORMAT_RGB16_UNORM=32852;r.TEXTUREFORMAT_RGBA16_UNORM=32859;r.TEXTUREFORMAT_R16_SNORM=36760;r.TEXTUREFORMAT_RG16_SNORM=36761;r.TEXTUREFORMAT_RGB16_SNORM=36762;r.TEXTUREFORMAT_RGBA16_SNORM=36763;r.TEXTUREFORMAT_RG=7;r.TEXTUREFORMAT_RED_INTEGER=8;r.TEXTUREFORMAT_R_INTEGER=8;r.TEXTUREFORMAT_RG_INTEGER=9;r.TEXTUREFORMAT_RGB_INTEGER=10;r.TEXTUREFORMAT_RGBA_INTEGER=11;r.TEXTUREFORMAT_BGRA=12;r.TEXTUREFORMAT_DEPTH24_STENCIL8=13;r.TEXTUREFORMAT_DEPTH32_FLOAT=14;r.TEXTUREFORMAT_DEPTH16=15;r.TEXTUREFORMAT_DEPTH24=16;r.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8=17;r.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8=18;r.TEXTUREFORMAT_STENCIL8=19;r.TEXTUREFORMAT_UNDEFINED=4294967295;r.TEXTUREFORMAT_COMPRESSED_RGBA_BPTC_UNORM=36492;r.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_BPTC_UNORM=36493;r.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT=36495;r.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_SIGNED_FLOAT=36494;r.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT5=33779;r.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919;r.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT3=33778;r.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918;r.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT1=33777;r.TEXTUREFORMAT_COMPRESSED_RGB_S3TC_DXT1=33776;r.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917;r.TEXTUREFORMAT_COMPRESSED_SRGB_S3TC_DXT1_EXT=35916;r.TEXTUREFORMAT_COMPRESSED_RGBA_ASTC_4x4=37808;r.TEXTUREFORMAT_COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR=37840;r.TEXTUREFORMAT_COMPRESSED_RGB_ETC1_WEBGL=36196;r.TEXTUREFORMAT_COMPRESSED_RGB8_ETC2=37492;r.TEXTUREFORMAT_COMPRESSED_SRGB8_ETC2=37493;r.TEXTUREFORMAT_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494;r.TEXTUREFORMAT_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495;r.TEXTUREFORMAT_COMPRESSED_RGBA8_ETC2_EAC=37496;r.TEXTUREFORMAT_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497;r.TEXTURETYPE_UNSIGNED_BYTE=0;r.TEXTURETYPE_UNSIGNED_INT=0;r.TEXTURETYPE_FLOAT=1;r.TEXTURETYPE_HALF_FLOAT=2;r.TEXTURETYPE_BYTE=3;r.TEXTURETYPE_SHORT=4;r.TEXTURETYPE_UNSIGNED_SHORT=5;r.TEXTURETYPE_INT=6;r.TEXTURETYPE_UNSIGNED_INTEGER=7;r.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4=8;r.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1=9;r.TEXTURETYPE_UNSIGNED_SHORT_5_6_5=10;r.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV=11;r.TEXTURETYPE_UNSIGNED_INT_24_8=12;r.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV=13;r.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV=14;r.TEXTURETYPE_FLOAT_32_UNSIGNED_INT_24_8_REV=15;r.TEXTURETYPE_UNDEFINED=16;r.TEXTURE_2D=3553;r.TEXTURE_2D_ARRAY=35866;r.TEXTURE_CUBE_MAP=34067;r.TEXTURE_CUBE_MAP_ARRAY=3735928559;r.TEXTURE_3D=32879;r.TEXTURE_NEAREST_SAMPLINGMODE=1;r.TEXTURE_NEAREST_NEAREST=1;r.TEXTURE_BILINEAR_SAMPLINGMODE=2;r.TEXTURE_LINEAR_LINEAR=2;r.TEXTURE_TRILINEAR_SAMPLINGMODE=3;r.TEXTURE_LINEAR_LINEAR_MIPLINEAR=3;r.TEXTURE_NEAREST_NEAREST_MIPNEAREST=4;r.TEXTURE_NEAREST_LINEAR_MIPNEAREST=5;r.TEXTURE_NEAREST_LINEAR_MIPLINEAR=6;r.TEXTURE_NEAREST_LINEAR=7;r.TEXTURE_NEAREST_NEAREST_MIPLINEAR=8;r.TEXTURE_LINEAR_NEAREST_MIPNEAREST=9;r.TEXTURE_LINEAR_NEAREST_MIPLINEAR=10;r.TEXTURE_LINEAR_LINEAR_MIPNEAREST=11;r.TEXTURE_LINEAR_NEAREST=12;r.TEXTURE_EXPLICIT_MODE=0;r.TEXTURE_SPHERICAL_MODE=1;r.TEXTURE_PLANAR_MODE=2;r.TEXTURE_CUBIC_MODE=3;r.TEXTURE_PROJECTION_MODE=4;r.TEXTURE_SKYBOX_MODE=5;r.TEXTURE_INVCUBIC_MODE=6;r.TEXTURE_EQUIRECTANGULAR_MODE=7;r.TEXTURE_FIXED_EQUIRECTANGULAR_MODE=8;r.TEXTURE_FIXED_EQUIRECTANGULAR_MIRRORED_MODE=9;r.TEXTURE_FILTERING_QUALITY_OFFLINE=4096;r.TEXTURE_FILTERING_QUALITY_HIGH=64;r.TEXTURE_FILTERING_QUALITY_MEDIUM=16;r.TEXTURE_FILTERING_QUALITY_LOW=8;r.SCALEMODE_FLOOR=1;r.SCALEMODE_NEAREST=2;r.SCALEMODE_CEILING=3;r.MATERIAL_TextureDirtyFlag=1;r.MATERIAL_LightDirtyFlag=2;r.MATERIAL_FresnelDirtyFlag=4;r.MATERIAL_AttributesDirtyFlag=8;r.MATERIAL_MiscDirtyFlag=16;r.MATERIAL_PrePassDirtyFlag=32;r.MATERIAL_ImageProcessingDirtyFlag=64;r.MATERIAL_AllDirtyFlag=127;r.MATERIAL_TriangleFillMode=0;r.MATERIAL_WireFrameFillMode=1;r.MATERIAL_PointFillMode=2;r.MATERIAL_PointListDrawMode=3;r.MATERIAL_LineListDrawMode=4;r.MATERIAL_LineLoopDrawMode=5;r.MATERIAL_LineStripDrawMode=6;r.MATERIAL_TriangleStripDrawMode=7;r.MATERIAL_TriangleFanDrawMode=8;r.MATERIAL_ClockWiseSideOrientation=0;r.MATERIAL_CounterClockWiseSideOrientation=1;r.ACTION_NothingTrigger=0;r.ACTION_OnPickTrigger=1;r.ACTION_OnLeftPickTrigger=2;r.ACTION_OnRightPickTrigger=3;r.ACTION_OnCenterPickTrigger=4;r.ACTION_OnPickDownTrigger=5;r.ACTION_OnDoublePickTrigger=6;r.ACTION_OnPickUpTrigger=7;r.ACTION_OnPickOutTrigger=16;r.ACTION_OnLongPressTrigger=8;r.ACTION_OnPointerOverTrigger=9;r.ACTION_OnPointerOutTrigger=10;r.ACTION_OnEveryFrameTrigger=11;r.ACTION_OnIntersectionEnterTrigger=12;r.ACTION_OnIntersectionExitTrigger=13;r.ACTION_OnKeyDownTrigger=14;r.ACTION_OnKeyUpTrigger=15;r.PARTICLES_BILLBOARDMODE_Y=2;r.PARTICLES_BILLBOARDMODE_ALL=7;r.PARTICLES_BILLBOARDMODE_STRETCHED=8;r.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL=9;r.MESHES_CULLINGSTRATEGY_STANDARD=0;r.MESHES_CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY=1;r.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION=2;r.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY=3;r.SCENELOADER_NO_LOGGING=0;r.SCENELOADER_MINIMAL_LOGGING=1;r.SCENELOADER_SUMMARY_LOGGING=2;r.SCENELOADER_DETAILED_LOGGING=3;r.PREPASS_IRRADIANCE_TEXTURE_TYPE=0;r.PREPASS_POSITION_TEXTURE_TYPE=1;r.PREPASS_VELOCITY_TEXTURE_TYPE=2;r.PREPASS_REFLECTIVITY_TEXTURE_TYPE=3;r.PREPASS_COLOR_TEXTURE_TYPE=4;r.PREPASS_DEPTH_TEXTURE_TYPE=5;r.PREPASS_NORMAL_TEXTURE_TYPE=6;r.PREPASS_ALBEDO_SQRT_TEXTURE_TYPE=7;r.PREPASS_WORLD_NORMAL_TEXTURE_TYPE=8;r.PREPASS_LOCAL_POSITION_TEXTURE_TYPE=9;r.PREPASS_SCREENSPACE_DEPTH_TEXTURE_TYPE=10;r.PREPASS_VELOCITY_LINEAR_TEXTURE_TYPE=11;r.PREPASS_ALBEDO_TEXTURE_TYPE=12;r.BUFFER_CREATIONFLAG_READ=1;r.BUFFER_CREATIONFLAG_WRITE=2;r.BUFFER_CREATIONFLAG_READWRITE=3;r.BUFFER_CREATIONFLAG_UNIFORM=4;r.BUFFER_CREATIONFLAG_VERTEX=8;r.BUFFER_CREATIONFLAG_INDEX=16;r.BUFFER_CREATIONFLAG_STORAGE=32;r.BUFFER_CREATIONFLAG_INDIRECT=64;r.RENDERPASS_MAIN=0;r.INPUT_ALT_KEY=18;r.INPUT_CTRL_KEY=17;r.INPUT_META_KEY1=91;r.INPUT_META_KEY2=92;r.INPUT_META_KEY3=93;r.INPUT_SHIFT_KEY=16;r.SNAPSHOTRENDERING_STANDARD=0;r.SNAPSHOTRENDERING_FAST=1;r.PERSPECTIVE_CAMERA=0;r.ORTHOGRAPHIC_CAMERA=1;r.FOVMODE_VERTICAL_FIXED=0;r.FOVMODE_HORIZONTAL_FIXED=1;r.RIG_MODE_NONE=0;r.RIG_MODE_STEREOSCOPIC_ANAGLYPH=10;r.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL=11;r.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED=12;r.RIG_MODE_STEREOSCOPIC_OVERUNDER=13;r.RIG_MODE_STEREOSCOPIC_INTERLACED=14;r.RIG_MODE_VR=20;r.RIG_MODE_CUSTOM=22;r.MAX_SUPPORTED_UV_SETS=6;r.GL_ALPHA_EQUATION_ADD=32774;r.GL_ALPHA_EQUATION_MIN=32775;r.GL_ALPHA_EQUATION_MAX=32776;r.GL_ALPHA_EQUATION_SUBTRACT=32778;r.GL_ALPHA_EQUATION_REVERSE_SUBTRACT=32779;r.GL_ALPHA_FUNCTION_SRC=768;r.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_COLOR=769;r.GL_ALPHA_FUNCTION_SRC_ALPHA=770;r.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_ALPHA=771;r.GL_ALPHA_FUNCTION_DST_ALPHA=772;r.GL_ALPHA_FUNCTION_ONE_MINUS_DST_ALPHA=773;r.GL_ALPHA_FUNCTION_DST_COLOR=774;r.GL_ALPHA_FUNCTION_ONE_MINUS_DST_COLOR=775;r.GL_ALPHA_FUNCTION_SRC_ALPHA_SATURATED=776;r.GL_ALPHA_FUNCTION_CONSTANT_COLOR=32769;r.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_COLOR=32770;r.GL_ALPHA_FUNCTION_CONSTANT_ALPHA=32771;r.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_ALPHA=32772;r.GL_ALPHA_FUNCTION_SRC1_COLOR=35065;r.GL_ALPHA_FUNCTION_ONE_MINUS_SRC1_COLOR=35066;r.GL_ALPHA_FUNCTION_SRC1_ALPHA=34185;r.GL_ALPHA_FUNCTION_ONE_MINUS_SRC1_ALPHA=35067;r.SnippetUrl="https://snippet.babylonjs.com";r.FOGMODE_NONE=0;r.FOGMODE_EXP=1;r.FOGMODE_EXP2=2;r.FOGMODE_LINEAR=3;r.BYTE=5120;r.UNSIGNED_BYTE=5121;r.SHORT=5122;r.UNSIGNED_SHORT=5123;r.INT=5124;r.UNSIGNED_INT=5125;r.FLOAT=5126;r.PositionKind="position";r.NormalKind="normal";r.TangentKind="tangent";r.UVKind="uv";r.UV2Kind="uv2";r.UV3Kind="uv3";r.UV4Kind="uv4";r.UV5Kind="uv5";r.UV6Kind="uv6";r.ColorKind="color";r.ColorInstanceKind="instanceColor";r.MatricesIndicesKind="matricesIndices";r.MatricesWeightsKind="matricesWeights";r.MatricesIndicesExtraKind="matricesIndicesExtra";r.MatricesWeightsExtraKind="matricesWeightsExtra";r.ANIMATIONTYPE_FLOAT=0;r.ANIMATIONTYPE_VECTOR3=1;r.ANIMATIONTYPE_QUATERNION=2;r.ANIMATIONTYPE_MATRIX=3;r.ANIMATIONTYPE_COLOR3=4;r.ANIMATIONTYPE_COLOR4=7;r.ANIMATIONTYPE_VECTOR2=5;r.ANIMATIONTYPE_SIZE=6;r.ShadowMinZ=0;r.ShadowMaxZ=1e4;class f extends M{constructor(){super(...arguments),this._needProjectionMatrixCompute=!0,this._viewMatrix=g.Identity(),this._projectionMatrix=g.Identity()}_setPosition(e){this._position=e}get position(){return this._position}set position(e){this._setPosition(e)}_setDirection(e){this._direction=e}get direction(){return this._direction}set direction(e){this._setDirection(e)}get shadowMinZ(){return this._shadowMinZ}set shadowMinZ(e){this._shadowMinZ=e,this.forceProjectionMatrixCompute()}get shadowMaxZ(){return this._shadowMaxZ}set shadowMaxZ(e){this._shadowMaxZ=e,this.forceProjectionMatrixCompute()}computeTransformedInformation(){return this.parent&&this.parent.getWorldMatrix?(this.transformedPosition||(this.transformedPosition=a.Zero()),a.TransformCoordinatesToRef(this.position,this.parent.getWorldMatrix(),this.transformedPosition),this.direction&&(this.transformedDirection||(this.transformedDirection=a.Zero()),a.TransformNormalToRef(this.direction,this.parent.getWorldMatrix(),this.transformedDirection)),!0):!1}getDepthScale(){return 50}getShadowDirection(e){return this.transformedDirection?this.transformedDirection:this.direction}getAbsolutePosition(){return this.transformedPosition?this.transformedPosition:this.position}setDirectionToTarget(e){return this.direction=a.Normalize(e.subtract(this.position)),this.direction}getRotation(){this.direction.normalize();const e=a.Cross(this.direction,y.Y),i=a.Cross(e,this.direction);return a.RotationFromAxis(e,i,this.direction)}needCube(){return!1}needProjectionMatrixCompute(){return this._needProjectionMatrixCompute}forceProjectionMatrixCompute(){this._needProjectionMatrixCompute=!0}_initCache(){super._initCache(),this._cache.position=a.Zero()}_isSynchronized(){return!!this._cache.position.equals(this.position)}computeWorldMatrix(e){return!e&&this.isSynchronized()?(this._currentRenderId=this.getScene().getRenderId(),this._worldMatrix):(this._updateCache(),this._cache.position.copyFrom(this.position),this._worldMatrix||(this._worldMatrix=g.Identity()),g.TranslationToRef(this.position.x,this.position.y,this.position.z,this._worldMatrix),this.parent&&this.parent.getWorldMatrix&&(this._worldMatrix.multiplyToRef(this.parent.getWorldMatrix(),this._worldMatrix),this._markSyncedWithParent()),this._worldMatrixDeterminantIsDirty=!0,this._worldMatrix)}getDepthMinZ(e){return this.shadowMinZ!==void 0?this.shadowMinZ:(e==null?void 0:e.minZ)||0}getDepthMaxZ(e){return this.shadowMaxZ!==void 0?this.shadowMaxZ:(e==null?void 0:e.maxZ)||1e4}setShadowProjectionMatrix(e,i,o){return this.customProjectionMatrixBuilder?this.customProjectionMatrixBuilder(i,o,e):this._setDefaultShadowProjectionMatrix(e,i,o),this}_syncParentEnabledState(){super._syncParentEnabledState(),(!this.parent||!this.parent.getWorldMatrix)&&(this.transformedPosition=null,this.transformedDirection=null)}getViewMatrix(e){const i=P.Vector3[0];let o=this.position;this.computeTransformedInformation()&&(o=this.transformedPosition),a.NormalizeToRef(this.getShadowDirection(e),i),Math.abs(a.Dot(i,a.Up()))===1&&(i.z=1e-13);const s=P.Vector3[1];return o.addToRef(i,s),g.LookAtLHToRef(o,s,a.Up(),this._viewMatrix),this._viewMatrix}getProjectionMatrix(e,i){return this.setShadowProjectionMatrix(this._projectionMatrix,e??this._viewMatrix,i??[]),this._projectionMatrix}}c([S()],f.prototype,"position",null);c([S()],f.prototype,"direction",null);c([A()],f.prototype,"shadowMinZ",null);c([A()],f.prototype,"shadowMaxZ",null);d.AddNodeConstructor("Light_Type_2",(t,e)=>()=>new T(t,a.Zero(),a.Zero(),0,0,e));class T extends f{get iesProfileTexture(){return this._iesProfileTexture}set iesProfileTexture(e){this._iesProfileTexture!==e&&(this._iesProfileTexture=e,this._iesProfileTexture&&T._IsTexture(this._iesProfileTexture)&&this._iesProfileTexture.onLoadObservable.addOnce(()=>{this._markMeshesAsLightDirty()}))}get angle(){return this._angle}set angle(e){this._angle=e,this._cosHalfAngle=Math.cos(e*.5),this._projectionTextureProjectionLightDirty=!0,this.forceProjectionMatrixCompute(),this._computeAngleValues()}get innerAngle(){return this._innerAngle}set innerAngle(e){this._innerAngle=e,this._computeAngleValues()}get shadowAngleScale(){return this._shadowAngleScale}set shadowAngleScale(e){this._shadowAngleScale=e,this.forceProjectionMatrixCompute()}get projectionTextureMatrix(){return this._projectionTextureMatrix}get projectionTextureLightNear(){return this._projectionTextureLightNear}set projectionTextureLightNear(e){this._projectionTextureLightNear=e,this._projectionTextureProjectionLightDirty=!0}get projectionTextureLightFar(){return this._projectionTextureLightFar}set projectionTextureLightFar(e){this._projectionTextureLightFar=e,this._projectionTextureProjectionLightDirty=!0}get projectionTextureUpDirection(){return this._projectionTextureUpDirection}set projectionTextureUpDirection(e){this._projectionTextureUpDirection=e,this._projectionTextureProjectionLightDirty=!0}get projectionTexture(){return this._projectionTexture}set projectionTexture(e){this._projectionTexture!==e&&(this._projectionTexture=e,this._projectionTextureDirty=!0,this._projectionTexture&&!this._projectionTexture.isReady()&&(T._IsProceduralTexture(this._projectionTexture)?this._projectionTexture.getEffect().executeWhenCompiled(()=>{this._markMeshesAsLightDirty()}):T._IsTexture(this._projectionTexture)&&this._projectionTexture.onLoadObservable.addOnce(()=>{this._markMeshesAsLightDirty()})))}static _IsProceduralTexture(e){return e.onGeneratedObservable!==void 0}static _IsTexture(e){return e.onLoadObservable!==void 0}get projectionTextureProjectionLightMatrix(){return this._projectionTextureProjectionLightMatrix}set projectionTextureProjectionLightMatrix(e){this._projectionTextureProjectionLightMatrix=e,this._projectionTextureProjectionLightDirty=!1,this._projectionTextureDirty=!0}constructor(e,i,o,s,_,R){super(e,R),this._innerAngle=0,this._iesProfileTexture=null,this._projectionTextureMatrix=g.Zero(),this._projectionTextureLightNear=1e-6,this._projectionTextureLightFar=1e3,this._projectionTextureUpDirection=a.Up(),this._projectionTextureViewLightDirty=!0,this._projectionTextureProjectionLightDirty=!0,this._projectionTextureDirty=!0,this._projectionTextureViewTargetVector=a.Zero(),this._projectionTextureViewLightMatrix=g.Zero(),this._projectionTextureProjectionLightMatrix=g.Zero(),this._projectionTextureScalingMatrix=g.FromValues(.5,0,0,0,0,.5,0,0,0,0,.5,0,.5,.5,.5,1),this.position=i,this.direction=o,this.angle=s,this.exponent=_}getClassName(){return"SpotLight"}getTypeID(){return M.LIGHTTYPEID_SPOTLIGHT}_setDirection(e){super._setDirection(e),this._projectionTextureViewLightDirty=!0}_setPosition(e){super._setPosition(e),this._projectionTextureViewLightDirty=!0}_setDefaultShadowProjectionMatrix(e,i,o){const s=this.getScene().activeCamera;if(!s)return;this._shadowAngleScale=this._shadowAngleScale||1;const _=this._shadowAngleScale*this._angle,R=this.shadowMinZ!==void 0?this.shadowMinZ:s.minZ,l=this.shadowMaxZ!==void 0?this.shadowMaxZ:s.maxZ,u=this.getScene().getEngine().useReverseDepthBuffer;g.PerspectiveFovLHToRef(_,1,u?l:R,u?R:l,e,!0,this._scene.getEngine().isNDCHalfZRange,void 0,u)}_computeProjectionTextureViewLightMatrix(){this._projectionTextureViewLightDirty=!1,this._projectionTextureDirty=!0,this.getAbsolutePosition().addToRef(this.getShadowDirection(),this._projectionTextureViewTargetVector),g.LookAtLHToRef(this.getAbsolutePosition(),this._projectionTextureViewTargetVector,this._projectionTextureUpDirection,this._projectionTextureViewLightMatrix)}_computeProjectionTextureProjectionLightMatrix(){this._projectionTextureProjectionLightDirty=!1,this._projectionTextureDirty=!0;const e=this.projectionTextureLightFar,i=this.projectionTextureLightNear,o=e/(e-i),s=-o*i,_=1/Math.tan(this._angle/2);g.FromValuesToRef(_/1,0,0,0,0,_,0,0,0,0,o,1,0,0,s,0,this._projectionTextureProjectionLightMatrix)}_computeProjectionTextureMatrix(){if(this._projectionTextureDirty=!1,this._projectionTextureViewLightMatrix.multiplyToRef(this._projectionTextureProjectionLightMatrix,this._projectionTextureMatrix),this._projectionTexture instanceof I){const e=this._projectionTexture.uScale/2,i=this._projectionTexture.vScale/2;g.FromValuesToRef(e,0,0,0,0,i,0,0,0,0,.5,0,.5,.5,.5,1,this._projectionTextureScalingMatrix)}this._projectionTextureMatrix.multiplyToRef(this._projectionTextureScalingMatrix,this._projectionTextureMatrix)}_buildUniformLayout(){this._uniformBuffer.addUniform("vLightData",4),this._uniformBuffer.addUniform("vLightDiffuse",4),this._uniformBuffer.addUniform("vLightSpecular",4),this._uniformBuffer.addUniform("vLightDirection",3),this._uniformBuffer.addUniform("vLightFalloff",4),this._uniformBuffer.addUniform("shadowsInfo",3),this._uniformBuffer.addUniform("depthValues",2),this._uniformBuffer.create()}_computeAngleValues(){this._lightAngleScale=1/Math.max(.001,Math.cos(this._innerAngle*.5)-this._cosHalfAngle),this._lightAngleOffset=-this._cosHalfAngle*this._lightAngleScale}transferTexturesToEffect(e,i){return this.projectionTexture&&this.projectionTexture.isReady()&&(this._projectionTextureViewLightDirty&&this._computeProjectionTextureViewLightMatrix(),this._projectionTextureProjectionLightDirty&&this._computeProjectionTextureProjectionLightMatrix(),this._projectionTextureDirty&&this._computeProjectionTextureMatrix(),e.setMatrix("textureProjectionMatrix"+i,this._projectionTextureMatrix),e.setTexture("projectionLightTexture"+i,this.projectionTexture)),this._iesProfileTexture&&this._iesProfileTexture.isReady()&&e.setTexture("iesLightTexture"+i,this._iesProfileTexture),this}transferToEffect(e,i){let o;return this.computeTransformedInformation()?(this._uniformBuffer.updateFloat4("vLightData",this.transformedPosition.x,this.transformedPosition.y,this.transformedPosition.z,this.exponent,i),o=a.Normalize(this.transformedDirection)):(this._uniformBuffer.updateFloat4("vLightData",this.position.x,this.position.y,this.position.z,this.exponent,i),o=a.Normalize(this.direction)),this._uniformBuffer.updateFloat4("vLightDirection",o.x,o.y,o.z,this._cosHalfAngle,i),this._uniformBuffer.updateFloat4("vLightFalloff",this.range,this._inverseSquaredRange,this._lightAngleScale,this._lightAngleOffset,i),this}transferToNodeMaterialEffect(e,i){let o;return this.computeTransformedInformation()?o=a.Normalize(this.transformedDirection):o=a.Normalize(this.direction),this.getScene().useRightHandedSystem?e.setFloat3(i,-o.x,-o.y,-o.z):e.setFloat3(i,o.x,o.y,o.z),this}dispose(){super.dispose(),this._projectionTexture&&this._projectionTexture.dispose(),this._iesProfileTexture&&(this._iesProfileTexture.dispose(),this._iesProfileTexture=null)}getDepthMinZ(e){const i=this._scene.getEngine(),o=this.shadowMinZ!==void 0?this.shadowMinZ:(e==null?void 0:e.minZ)??0;return i.useReverseDepthBuffer&&i.isNDCHalfZRange?o:this._scene.getEngine().isNDCHalfZRange?0:o}getDepthMaxZ(e){const i=this._scene.getEngine(),o=this.shadowMaxZ!==void 0?this.shadowMaxZ:(e==null?void 0:e.maxZ)??1e4;return i.useReverseDepthBuffer&&i.isNDCHalfZRange?0:o}prepareLightSpecificDefines(e,i){e["SPOTLIGHT"+i]=!0,e["PROJECTEDLIGHTTEXTURE"+i]=!!(this.projectionTexture&&this.projectionTexture.isReady()),e["IESLIGHTTEXTURE"+i]=!!(this._iesProfileTexture&&this._iesProfileTexture.isReady())}}c([A()],T.prototype,"angle",null);c([A()],T.prototype,"innerAngle",null);c([A()],T.prototype,"shadowAngleScale",null);c([A()],T.prototype,"exponent",void 0);c([A()],T.prototype,"projectionTextureLightNear",null);c([A()],T.prototype,"projectionTextureLightFar",null);c([A()],T.prototype,"projectionTextureUpDirection",null);c([C("projectedLightTexture")],T.prototype,"_projectionTexture",void 0);b("BABYLON.SpotLight",T);class U{constructor(e,i){this._gltf=e,this._infoTree=i}convert(e){let i=this._gltf,o=this._infoTree,s;if(!e.startsWith("/"))throw new Error("Path must start with a /");const _=e.split("/");if(_.shift(),_[_.length-1].includes(".length")){const u=_[_.length-1].split(".");_.pop(),_.push(...u)}let R=!1;for(const l of _){const u=l==="length";if(u&&!o.__array__)throw new Error(`Path ${e} is invalid`);if(o.__ignoreObjectTree__&&(R=!0),o.__array__&&!u)o=o.__array__;else if(o=o[l],!o)throw new Error(`Path ${e} is invalid`);if(!R){if(i===void 0)throw new Error(`Path ${e} is invalid`);u||(i=i==null?void 0:i[l])}(o.__target__||u)&&(s=i)}return{object:s,info:o}}}const F={length:{type:"number",get:t=>t.length,getTarget:t=>t.map(e=>e._babylonTransformNode),getPropertyName:[()=>"length"]},__array__:{__target__:!0,translation:{type:"Vector3",get:t=>{var e;return(e=t._babylonTransformNode)==null?void 0:e.position},set:(t,e)=>{var i;return(i=e._babylonTransformNode)==null?void 0:i.position.copyFrom(t)},getTarget:t=>t._babylonTransformNode,getPropertyName:[()=>"position"]},rotation:{type:"Quaternion",get:t=>{var e;return(e=t._babylonTransformNode)==null?void 0:e.rotationQuaternion},set:(t,e)=>{var i,o;return(o=(i=e._babylonTransformNode)==null?void 0:i.rotationQuaternion)==null?void 0:o.copyFrom(t)},getTarget:t=>t._babylonTransformNode,getPropertyName:[()=>"rotationQuaternion"]},scale:{type:"Vector3",get:t=>{var e;return(e=t._babylonTransformNode)==null?void 0:e.scaling},set:(t,e)=>{var i;return(i=e._babylonTransformNode)==null?void 0:i.scaling.copyFrom(t)},getTarget:t=>t._babylonTransformNode,getPropertyName:[()=>"scaling"]},weights:{length:{type:"number",get:t=>t._numMorphTargets,getTarget:t=>t._babylonTransformNode,getPropertyName:[()=>"influence"]},__array__:{__target__:!0,type:"number",get:(t,e)=>{var i,o;return e!==void 0?(o=(i=t._primitiveBabylonMeshes)==null?void 0:i[0].morphTargetManager)==null?void 0:o.getTarget(e).influence:void 0},getTarget:t=>t._babylonTransformNode,getPropertyName:[()=>"influence"]},type:"number[]",get:(t,e)=>[0],getTarget:t=>t._babylonTransformNode,getPropertyName:[()=>"influence"]},matrix:{type:"Matrix",get:t=>{var e,i,o;return g.Compose((e=t._babylonTransformNode)==null?void 0:e.scaling,(i=t._babylonTransformNode)==null?void 0:i.rotationQuaternion,(o=t._babylonTransformNode)==null?void 0:o.position)},getTarget:t=>t._babylonTransformNode,isReadOnly:!0},globalMatrix:{type:"Matrix",get:t=>{var s,_,R,l,u,m,x;const e=g.Identity();let i=t.parent;for(;i&&i.parent;)i=i.parent;const o=((s=t._babylonTransformNode)==null?void 0:s.position._isDirty)||((R=(_=t._babylonTransformNode)==null?void 0:_.rotationQuaternion)==null?void 0:R._isDirty)||((l=t._babylonTransformNode)==null?void 0:l.scaling._isDirty);if(i){const L=(u=i._babylonTransformNode)==null?void 0:u.computeWorldMatrix(!0).invert();L&&((x=(m=t._babylonTransformNode)==null?void 0:m.computeWorldMatrix(o))==null||x.multiplyToRef(L,e))}else t._babylonTransformNode&&e.copyFrom(t._babylonTransformNode.computeWorldMatrix(o));return e},getTarget:t=>t._babylonTransformNode,isReadOnly:!0},extensions:{EXT_lights_ies:{multiplier:{type:"number",get:t=>{var e,i;return(i=(e=t._babylonTransformNode)==null?void 0:e.getChildren(o=>o instanceof T,!0)[0])==null?void 0:i.intensity},getTarget:t=>{var e;return(e=t._babylonTransformNode)==null?void 0:e.getChildren(i=>i instanceof T,!0)[0]},set:(t,e)=>{if(e._babylonTransformNode){const i=e._babylonTransformNode.getChildren(o=>o instanceof T,!0)[0];i&&(i.intensity=t)}}},color:{type:"Color3",get:t=>{var e,i;return(i=(e=t._babylonTransformNode)==null?void 0:e.getChildren(o=>o instanceof T,!0)[0])==null?void 0:i.diffuse},getTarget:t=>{var e;return(e=t._babylonTransformNode)==null?void 0:e.getChildren(i=>i instanceof T,!0)[0]},set:(t,e)=>{if(e._babylonTransformNode){const i=e._babylonTransformNode.getChildren(o=>o instanceof T,!0)[0];i&&(i.diffuse=t)}}}}}}},G={length:{type:"number",get:t=>t.length,getTarget:t=>t.map(e=>e._babylonAnimationGroup),getPropertyName:[()=>"length"]},__array__:{}},H={length:{type:"number",get:t=>t.length,getTarget:t=>t.map(e=>{var i;return(i=e.primitives[0]._instanceData)==null?void 0:i.babylonSourceMesh}),getPropertyName:[()=>"length"]},__array__:{}},X={__array__:{__target__:!0,orthographic:{xmag:{componentsCount:2,type:"Vector2",get:t=>{var e,i;return new p(((e=t._babylonCamera)==null?void 0:e.orthoLeft)??0,((i=t._babylonCamera)==null?void 0:i.orthoRight)??0)},set:(t,e)=>{e._babylonCamera&&(e._babylonCamera.orthoLeft=t.x,e._babylonCamera.orthoRight=t.y)},getTarget:t=>t,getPropertyName:[()=>"orthoLeft",()=>"orthoRight"]},ymag:{componentsCount:2,type:"Vector2",get:t=>{var e,i;return new p(((e=t._babylonCamera)==null?void 0:e.orthoBottom)??0,((i=t._babylonCamera)==null?void 0:i.orthoTop)??0)},set:(t,e)=>{e._babylonCamera&&(e._babylonCamera.orthoBottom=t.x,e._babylonCamera.orthoTop=t.y)},getTarget:t=>t,getPropertyName:[()=>"orthoBottom",()=>"orthoTop"]},zfar:{type:"number",get:t=>{var e;return(e=t._babylonCamera)==null?void 0:e.maxZ},set:(t,e)=>{e._babylonCamera&&(e._babylonCamera.maxZ=t)},getTarget:t=>t,getPropertyName:[()=>"maxZ"]},znear:{type:"number",get:t=>{var e;return(e=t._babylonCamera)==null?void 0:e.minZ},set:(t,e)=>{e._babylonCamera&&(e._babylonCamera.minZ=t)},getTarget:t=>t,getPropertyName:[()=>"minZ"]}},perspective:{aspectRatio:{type:"number",get:t=>{var e;return(e=t._babylonCamera)==null?void 0:e.getEngine().getAspectRatio(t._babylonCamera)},getTarget:t=>t,getPropertyName:[()=>"aspectRatio"],isReadOnly:!0},yfov:{type:"number",get:t=>{var e;return(e=t._babylonCamera)==null?void 0:e.fov},set:(t,e)=>{e._babylonCamera&&(e._babylonCamera.fov=t)},getTarget:t=>t,getPropertyName:[()=>"fov"]},zfar:{type:"number",get:t=>{var e;return(e=t._babylonCamera)==null?void 0:e.maxZ},set:(t,e)=>{e._babylonCamera&&(e._babylonCamera.maxZ=t)},getTarget:t=>t,getPropertyName:[()=>"maxZ"]},znear:{type:"number",get:t=>{var e;return(e=t._babylonCamera)==null?void 0:e.minZ},set:(t,e)=>{e._babylonCamera&&(e._babylonCamera.minZ=t)},getTarget:t=>t,getPropertyName:[()=>"minZ"]}}}},j={__array__:{__target__:!0,emissiveFactor:{type:"Color3",get:(t,e,i)=>n(t,e,i).emissiveColor,set:(t,e,i,o)=>n(e,i,o).emissiveColor.copyFrom(t),getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"emissiveColor"]},emissiveTexture:{extensions:{KHR_texture_transform:E("emissiveTexture")}},normalTexture:{scale:{type:"number",get:(t,e,i)=>{var o;return(o=h(t,i,"bumpTexture"))==null?void 0:o.level},set:(t,e,i,o)=>{const s=h(e,o,"bumpTexture");s&&(s.level=t)},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"level"]},extensions:{KHR_texture_transform:E("bumpTexture")}},occlusionTexture:{strength:{type:"number",get:(t,e,i)=>n(t,e,i).ambientTextureStrength,set:(t,e,i,o)=>{const s=n(e,i,o);s&&(s.ambientTextureStrength=t)},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"ambientTextureStrength"]},extensions:{KHR_texture_transform:E("ambientTexture")}},pbrMetallicRoughness:{baseColorFactor:{type:"Color4",get:(t,e,i)=>{const o=n(t,e,i);return D.FromColor3(o.albedoColor,o.alpha)},set:(t,e,i,o)=>{const s=n(e,i,o);s.albedoColor.set(t.r,t.g,t.b),s.alpha=t.a},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"albedoColor",()=>"alpha"]},baseColorTexture:{extensions:{KHR_texture_transform:E("albedoTexture")}},metallicFactor:{type:"number",get:(t,e,i)=>n(t,e,i).metallic,set:(t,e,i,o)=>{const s=n(e,i,o);s&&(s.metallic=t)},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"metallic"]},roughnessFactor:{type:"number",get:(t,e,i)=>n(t,e,i).roughness,set:(t,e,i,o)=>{const s=n(e,i,o);s&&(s.roughness=t)},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"roughness"]},metallicRoughnessTexture:{extensions:{KHR_texture_transform:E("metallicTexture")}}},extensions:{KHR_materials_anisotropy:{anisotropyStrength:{type:"number",get:(t,e,i)=>n(t,e,i).anisotropy.intensity,set:(t,e,i,o)=>{n(e,i,o).anisotropy.intensity=t},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"anisotropy.intensity"]},anisotropyRotation:{type:"number",get:(t,e,i)=>n(t,e,i).anisotropy.angle,set:(t,e,i,o)=>{n(e,i,o).anisotropy.angle=t},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"anisotropy.angle"]},anisotropyTexture:{extensions:{KHR_texture_transform:E("anisotropy","texture")}}},KHR_materials_clearcoat:{clearcoatFactor:{type:"number",get:(t,e,i)=>n(t,e,i).clearCoat.intensity,set:(t,e,i,o)=>{n(e,i,o).clearCoat.intensity=t},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"clearCoat.intensity"]},clearcoatRoughnessFactor:{type:"number",get:(t,e,i)=>n(t,e,i).clearCoat.roughness,set:(t,e,i,o)=>{n(e,i,o).clearCoat.roughness=t},getTarget:(t,e,i)=>n(t,e,i),getPropertyName:[()=>"clearCoat.roughness"]},clearcoatTexture:{extensions:{KHR_texture_transform:E("clearCoat","texture")}},clearcoatNormalTexture:{scale:{type:"number",get:(t,e,i)=>{var o;return(o=n(t,e,i).clearCoat.bumpTexture)==null?void 0:o.level},getTarget:n,set:(t,e,i,o)=>n(e,i,o).clearCoat.bumpTexture.level=t},extensions:{KHR_texture_transform:E("clearCoat","bumpTexture")}},clearcoatRoughnessTexture:{extensions:{KHR_texture_transform:E("clearCoat","textureRoughness")}}},KHR_materials_dispersion:{dispersion:{type:"number",get:(t,e,i)=>n(t,e,i).subSurface.dispersion,getTarget:n,set:(t,e,i,o)=>n(e,i,o).subSurface.dispersion=t}},KHR_materials_emissive_strength:{emissiveStrength:{type:"number",get:(t,e,i)=>n(t,e,i).emissiveIntensity,getTarget:n,set:(t,e,i,o)=>n(e,i,o).emissiveIntensity=t}},KHR_materials_ior:{ior:{type:"number",get:(t,e,i)=>n(t,e,i).indexOfRefraction,getTarget:n,set:(t,e,i,o)=>n(e,i,o).indexOfRefraction=t}},KHR_materials_iridescence:{iridescenceFactor:{type:"number",get:(t,e,i)=>n(t,e,i).iridescence.intensity,getTarget:n,set:(t,e,i,o)=>n(e,i,o).iridescence.intensity=t},iridescenceIor:{type:"number",get:(t,e,i)=>n(t,e,i).iridescence.indexOfRefraction,getTarget:n,set:(t,e,i,o)=>n(e,i,o).iridescence.indexOfRefraction=t},iridescenceTexture:{extensions:{KHR_texture_transform:E("iridescence","texture")}},iridescenceThicknessMaximum:{type:"number",get:(t,e,i)=>n(t,e,i).iridescence.maximumThickness,getTarget:n,set:(t,e,i,o)=>n(e,i,o).iridescence.maximumThickness=t},iridescenceThicknessMinimum:{type:"number",get:(t,e,i)=>n(t,e,i).iridescence.minimumThickness,getTarget:n,set:(t,e,i,o)=>n(e,i,o).iridescence.minimumThickness=t},iridescenceThicknessTexture:{extensions:{KHR_texture_transform:E("iridescence","thicknessTexture")}}},KHR_materials_sheen:{sheenColorFactor:{type:"Color3",get:(t,e,i)=>n(t,e,i).sheen.color,getTarget:n,set:(t,e,i,o)=>n(e,i,o).sheen.color.copyFrom(t)},sheenColorTexture:{extensions:{KHR_texture_transform:E("sheen","texture")}},sheenRoughnessFactor:{type:"number",get:(t,e,i)=>n(t,e,i).sheen.intensity,getTarget:n,set:(t,e,i,o)=>n(e,i,o).sheen.intensity=t},sheenRoughnessTexture:{extensions:{KHR_texture_transform:E("sheen","thicknessTexture")}}},KHR_materials_specular:{specularFactor:{type:"number",get:(t,e,i)=>n(t,e,i).metallicF0Factor,getTarget:n,set:(t,e,i,o)=>n(e,i,o).metallicF0Factor=t,getPropertyName:[()=>"metallicF0Factor"]},specularColorFactor:{type:"Color3",get:(t,e,i)=>n(t,e,i).metallicReflectanceColor,getTarget:n,set:(t,e,i,o)=>n(e,i,o).metallicReflectanceColor.copyFrom(t),getPropertyName:[()=>"metallicReflectanceColor"]},specularTexture:{extensions:{KHR_texture_transform:E("metallicReflectanceTexture")}},specularColorTexture:{extensions:{KHR_texture_transform:E("reflectanceTexture")}}},KHR_materials_transmission:{transmissionFactor:{type:"number",get:(t,e,i)=>n(t,e,i).subSurface.refractionIntensity,getTarget:n,set:(t,e,i,o)=>n(e,i,o).subSurface.refractionIntensity=t,getPropertyName:[()=>"subSurface.refractionIntensity"]},transmissionTexture:{extensions:{KHR_texture_transform:E("subSurface","refractionIntensityTexture")}}},KHR_materials_diffuse_transmission:{diffuseTransmissionFactor:{type:"number",get:(t,e,i)=>n(t,e,i).subSurface.translucencyIntensity,getTarget:n,set:(t,e,i,o)=>n(e,i,o).subSurface.translucencyIntensity=t},diffuseTransmissionTexture:{extensions:{KHR_texture_transform:E("subSurface","translucencyIntensityTexture")}},diffuseTransmissionColorFactor:{type:"Color3",get:(t,e,i)=>n(t,e,i).subSurface.translucencyColor,getTarget:n,set:(t,e,i,o)=>{var s;return t&&((s=n(e,i,o).subSurface.translucencyColor)==null?void 0:s.copyFrom(t))}},diffuseTransmissionColorTexture:{extensions:{KHR_texture_transform:E("subSurface","translucencyColorTexture")}}},KHR_materials_volume:{attenuationColor:{type:"Color3",get:(t,e,i)=>n(t,e,i).subSurface.tintColor,getTarget:n,set:(t,e,i,o)=>n(e,i,o).subSurface.tintColor.copyFrom(t)},attenuationDistance:{type:"number",get:(t,e,i)=>n(t,e,i).subSurface.tintColorAtDistance,getTarget:n,set:(t,e,i,o)=>n(e,i,o).subSurface.tintColorAtDistance=t},thicknessFactor:{type:"number",get:(t,e,i)=>n(t,e,i).subSurface.maximumThickness,getTarget:n,set:(t,e,i,o)=>n(e,i,o).subSurface.maximumThickness=t},thicknessTexture:{extensions:{KHR_texture_transform:E("subSurface","thicknessTexture")}}}}}},B={KHR_lights_punctual:{lights:{length:{type:"number",get:t=>t.length,getTarget:t=>t.map(e=>e._babylonLight),getPropertyName:[t=>"length"]},__array__:{__target__:!0,color:{type:"Color3",get:t=>{var e;return(e=t._babylonLight)==null?void 0:e.diffuse},set:(t,e)=>{var i;return(i=e._babylonLight)==null?void 0:i.diffuse.copyFrom(t)},getTarget:t=>t._babylonLight,getPropertyName:[t=>"diffuse"]},intensity:{type:"number",get:t=>{var e;return(e=t._babylonLight)==null?void 0:e.intensity},set:(t,e)=>e._babylonLight?e._babylonLight.intensity=t:void 0,getTarget:t=>t._babylonLight,getPropertyName:[t=>"intensity"]},range:{type:"number",get:t=>{var e;return(e=t._babylonLight)==null?void 0:e.range},set:(t,e)=>e._babylonLight?e._babylonLight.range=t:void 0,getTarget:t=>t._babylonLight,getPropertyName:[t=>"range"]},spot:{innerConeAngle:{type:"number",get:t=>{var e;return(e=t._babylonLight)==null?void 0:e.innerAngle},set:(t,e)=>e._babylonLight?e._babylonLight.innerAngle=t:void 0,getTarget:t=>t._babylonLight,getPropertyName:[t=>"innerConeAngle"]},outerConeAngle:{type:"number",get:t=>{var e;return(e=t._babylonLight)==null?void 0:e.angle},set:(t,e)=>e._babylonLight?e._babylonLight.angle=t:void 0,getTarget:t=>t._babylonLight,getPropertyName:[t=>"outerConeAngle"]}}}}},EXT_lights_ies:{lights:{length:{type:"number",get:t=>t.length,getTarget:t=>t.map(e=>e._babylonLight),getPropertyName:[t=>"length"]}}},EXT_lights_image_based:{lights:{length:{type:"number",get:t=>t.length,getTarget:t=>t.map(e=>e._babylonTexture),getPropertyName:[t=>"length"]},__array__:{__target__:!0,intensity:{type:"number",get:t=>{var e;return(e=t._babylonTexture)==null?void 0:e.level},set:(t,e)=>{e._babylonTexture&&(e._babylonTexture.level=t)},getTarget:t=>t._babylonTexture},rotation:{type:"Quaternion",get:t=>{var e;return t._babylonTexture&&O.FromRotationMatrix((e=t._babylonTexture)==null?void 0:e.getReflectionTextureMatrix())},set:(t,e)=>{var i;e._babylonTexture&&((i=e._babylonTexture.getScene())!=null&&i.useRightHandedSystem||(t=O.Inverse(t)),g.FromQuaternionToRef(t,e._babylonTexture.getReflectionTextureMatrix()))},getTarget:t=>t._babylonTexture}}}}};function h(t,e,i,o){const s=n(t);return o?s[i][o]:s[i]}function n(t,e,i){var o,s;return(s=(o=t._data)==null?void 0:o[(i==null?void 0:i.fillMode)??r.MATERIAL_TriangleFillMode])==null?void 0:s.babylonMaterial}function E(t,e){return{offset:{componentsCount:2,type:"Vector2",get:(i,o,s)=>{const _=h(i,s,t,e);return new p(_==null?void 0:_.uOffset,_==null?void 0:_.vOffset)},getTarget:n,set:(i,o,s,_)=>{const R=h(o,_,t,e);R.uOffset=i.x,R.vOffset=i.y},getPropertyName:[()=>`${t}${e?"."+e:""}.uOffset`,()=>`${t}${e?"."+e:""}.vOffset`]},rotation:{type:"number",get:(i,o,s)=>{var _;return(_=h(i,s,t,e))==null?void 0:_.wAng},getTarget:n,set:(i,o,s,_)=>h(o,_,t,e).wAng=i,getPropertyName:[()=>`${t}${e?"."+e:""}.wAng`]},scale:{componentsCount:2,type:"Vector2",get:(i,o,s)=>{const _=h(i,s,t,e);return new p(_==null?void 0:_.uScale,_==null?void 0:_.vScale)},getTarget:n,set:(i,o,s,_)=>{const R=h(o,_,t,e);R.uScale=i.x,R.vScale=i.y},getPropertyName:[()=>`${t}${e?"."+e:""}.uScale`,()=>`${t}${e?"."+e:""}.vScale`]}}}const N={cameras:X,nodes:F,materials:j,extensions:B,animations:G,meshes:H};function Y(t){return new U(t,N)}function Z(t){const e=t.split("/").map(o=>o.replace(/{}/g,"__array__"));let i=N;for(const o of e)o&&(i=i[o]);if(i&&i.type&&i.get)return i}function V(t,e){const i=t.split("/").map(s=>s.replace(/{}/g,"__array__"));let o=N;for(const s of i)s&&(o=o[s]);o&&o.type&&o.get&&(o.interpolation=e)}function K(t,e){const i=t.split("/").map(s=>s.replace(/{}/g,"__array__"));let o=N;for(const s of i)if(s){if(!o[s]){if(s==="?"){o.__ignoreObjectTree__=!0;continue}o[s]={},s==="__array__"&&(o[s].__target__=!0)}o=o[s]}Object.assign(o,e)}export{K as A,r as C,Y as G,V as S,T as a,f as b,Z as c};
//# sourceMappingURL=objectModelMapping.ha_8hIyl.js.map
