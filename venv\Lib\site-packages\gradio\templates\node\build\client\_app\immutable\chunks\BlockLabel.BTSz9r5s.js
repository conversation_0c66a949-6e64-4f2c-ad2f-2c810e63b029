import{SvelteComponent as w,init as I,safe_not_equal as q,element as h,create_component as B,space as E,text as L,claim_element as d,children as g,claim_component as k,detach as u,claim_space as A,claim_text as S,attr as c,toggle_class as r,insert_hydration as v,append_hydration as m,mount_component as C,set_data as N,transition_in as P,transition_out as j,destroy_component as y}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.B2AoQPnG.js";function z(i){let e,a,o,_,f,s,n;return o=new i[1]({}),{c(){e=h("label"),a=h("span"),B(o.$$.fragment),_=E(),f=L(i[0]),this.h()},l(t){e=d(t,"LABEL",{for:!0,"data-testid":!0,dir:!0,class:!0});var l=g(e);a=d(l,"SPAN",{class:!0});var b=g(a);k(o.$$.fragment,b),b.forEach(u),_=A(l),f=S(l,i[0]),l.forEach(u),this.h()},h(){c(a,"class","svelte-1to105q"),c(e,"for",""),c(e,"data-testid","block-label"),c(e,"dir",s=i[5]?"rtl":"ltr"),c(e,"class","svelte-1to105q"),r(e,"hide",!i[2]),r(e,"sr-only",!i[2]),r(e,"float",i[4]),r(e,"hide-label",i[3])},m(t,l){v(t,e,l),m(e,a),C(o,a,null),m(e,_),m(e,f),n=!0},p(t,[l]){(!n||l&1)&&N(f,t[0]),(!n||l&32&&s!==(s=t[5]?"rtl":"ltr"))&&c(e,"dir",s),(!n||l&4)&&r(e,"hide",!t[2]),(!n||l&4)&&r(e,"sr-only",!t[2]),(!n||l&16)&&r(e,"float",t[4]),(!n||l&8)&&r(e,"hide-label",t[3])},i(t){n||(P(o.$$.fragment,t),n=!0)},o(t){j(o.$$.fragment,t),n=!1},d(t){t&&u(e),y(o)}}}function D(i,e,a){let{label:o=null}=e,{Icon:_}=e,{show_label:f=!0}=e,{disable:s=!1}=e,{float:n=!0}=e,{rtl:t=!1}=e;return i.$$set=l=>{"label"in l&&a(0,o=l.label),"Icon"in l&&a(1,_=l.Icon),"show_label"in l&&a(2,f=l.show_label),"disable"in l&&a(3,s=l.disable),"float"in l&&a(4,n=l.float),"rtl"in l&&a(5,t=l.rtl)},[o,_,f,s,n,t]}class J extends w{constructor(e){super(),I(this,e,D,z,q,{label:0,Icon:1,show_label:2,disable:3,float:4,rtl:5})}}export{J as B};
//# sourceMappingURL=BlockLabel.BTSz9r5s.js.map
