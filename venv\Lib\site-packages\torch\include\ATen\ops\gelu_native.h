#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/gelu_meta.h>

namespace at {
namespace native {
struct TORCH_API structured_gelu_out_cpu : public at::meta::structured_gelu {
void impl(const at::Tensor & self, c10::string_view approximate, const at::Tensor & out);
};
struct TORCH_API structured_gelu_out_cuda : public at::meta::structured_gelu {
void impl(const at::Tensor & self, c10::string_view approximate, const at::Tensor & out);
};
TORCH_API at::Tensor NestedTensor_gelu(const at::Tensor & self, c10::string_view approximate="none");
TORCH_API at::Tensor & NestedTensor_gelu_(at::Tensor & self, c10::string_view approximate="none");
TORCH_API at::Tensor mkldnn_gelu(const at::Tensor & self, c10::string_view approximate="none");
TORCH_API at::Tensor gelu_quantized_cpu(const at::Tensor & self, c10::string_view approximate="none");
TORCH_API at::Tensor & gelu_quantized_cpu_(at::Tensor & self, c10::string_view approximate="none");
TORCH_API at::Tensor gelu_quantized_cuda(const at::Tensor & self, c10::string_view approximate="none");
} // namespace native
} // namespace at
