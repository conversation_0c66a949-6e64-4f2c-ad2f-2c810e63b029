import"./index-B7J2Z2jS.js";import{u as pl,S as Ho,a as Lo}from"./utils-BsGrhMNe.js";import{I as Qt}from"./Image-CnqB5dbD.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                   */import{I as ue}from"./IconButton-C_HS7fTi.js";import{C as Gt}from"./Check-CEkiXcyC.js";import{C as fn}from"./Copy-CxQ9EyK2.js";import{C as Mo}from"./Clear-By3xiIwg.js";import{E as No}from"./Edit-BpRIf5rU.js";import{U as jo}from"./Undo-DCjBnnSO.js";import{I as io}from"./IconButtonWrapper--EIOWuEM.js";import{F as ol}from"./File-BQ_9P3Ye.js";import{M as sl}from"./MarkdownCode-CkSMBRHJ.js";import{s as vl}from"./index-CEGzm7H5.js";import{d as Vo}from"./index-CnqicUFC.js";import{C as Do}from"./Community-Dw1micSV.js";import{T as Io}from"./Trash-RbZEwH-j.js";import{M as oo}from"./Music-CDm0RGMk.js";import{B as Bo}from"./Block-CJdXVpa7.js";import{B as Eo}from"./BlockLabel-3KxTaaiM.js";import{S as To}from"./index-B1FJGuzG.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import"./svelte/svelte.js";import"./file-url-DoxvUUVV.js";import"./prism-python-MMh3z1bK.js";const{SvelteComponent:Ao,append:$l,attr:be,detach:Fo,init:Zo,insert:Uo,noop:jn,safe_not_equal:Wo,svg_element:Vn}=window.__gradio__svelte__internal;function Po(l){let e,t,n;return{c(){e=Vn("svg"),t=Vn("path"),n=Vn("path"),be(t,"fill","currentColor"),be(t,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),be(n,"fill","currentColor"),be(n,"d","M8 10h16v2H8zm0 6h10v2H8z"),be(e,"xmlns","http://www.w3.org/2000/svg"),be(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),be(e,"aria-hidden","true"),be(e,"role","img"),be(e,"class","iconify iconify--carbon"),be(e,"width","100%"),be(e,"height","100%"),be(e,"preserveAspectRatio","xMidYMid meet"),be(e,"viewBox","0 0 32 32")},m(i,o){Uo(i,e,o),$l(e,t),$l(e,n)},p:jn,i:jn,o:jn,d(i){i&&Fo(e)}}}class Oo extends Ao{constructor(e){super(),Zo(this,e,null,Po,Wo,{})}}const{SvelteComponent:Ro,append:Cl,attr:je,detach:Jo,init:Go,insert:Yo,noop:Dn,safe_not_equal:Ko,svg_element:In}=window.__gradio__svelte__internal;function Qo(l){let e,t,n;return{c(){e=In("svg"),t=In("circle"),n=In("path"),je(t,"cx","9"),je(t,"cy","9"),je(t,"r","8"),je(t,"class","circle svelte-1m886t3"),je(n,"d","M5 8l4 4 4-4z"),je(e,"class","dropdown-arrow svelte-1m886t3"),je(e,"xmlns","http://www.w3.org/2000/svg"),je(e,"width","100%"),je(e,"height","100%"),je(e,"viewBox","0 0 18 18")},m(i,o){Yo(i,e,o),Cl(e,t),Cl(e,n)},p:Dn,i:Dn,o:Dn,d(i){i&&Jo(e)}}}class Xo extends Ro{constructor(e){super(),Go(this,e,null,Qo,Ko,{})}}const{SvelteComponent:xo,append:ln,attr:D,detach:es,init:ts,insert:ns,noop:Bn,safe_not_equal:ls,svg_element:Rt}=window.__gradio__svelte__internal;function is(l){let e,t,n,i,o;return{c(){e=Rt("svg"),t=Rt("path"),n=Rt("path"),i=Rt("path"),o=Rt("path"),D(t,"d","M19.1679 9C18.0247 6.46819 15.3006 4.5 11.9999 4.5C8.31459 4.5 5.05104 7.44668 4.54932 11"),D(t,"stroke","currentColor"),D(t,"stroke-width","1.5"),D(t,"stroke-linecap","round"),D(t,"stroke-linejoin","round"),D(n,"d","M16 9H19.4C19.7314 9 20 8.73137 20 8.4V5"),D(n,"stroke","currentColor"),D(n,"stroke-width","1.5"),D(n,"stroke-linecap","round"),D(n,"stroke-linejoin","round"),D(i,"d","M4.88146 15C5.92458 17.5318 8.64874 19.5 12.0494 19.5C15.7347 19.5 18.9983 16.5533 19.5 13"),D(i,"stroke","currentColor"),D(i,"stroke-width","1.5"),D(i,"stroke-linecap","round"),D(i,"stroke-linejoin","round"),D(o,"d","M8.04932 15H4.64932C4.31795 15 4.04932 15.2686 4.04932 15.6V19"),D(o,"stroke","currentColor"),D(o,"stroke-width","1.5"),D(o,"stroke-linecap","round"),D(o,"stroke-linejoin","round"),D(e,"width","100%"),D(e,"height","100%"),D(e,"stroke-width","1.5"),D(e,"viewBox","0 0 24 24"),D(e,"fill","none"),D(e,"xmlns","http://www.w3.org/2000/svg"),D(e,"color","currentColor")},m(s,r){ns(s,e,r),ln(e,t),ln(e,n),ln(e,i),ln(e,o)},p:Bn,i:Bn,o:Bn,d(s){s&&es(e)}}}class os extends xo{constructor(e){super(),ts(this,e,null,is,ls,{})}}const{SvelteComponent:ss,append:as,attr:Ve,detach:rs,init:_s,insert:fs,noop:En,safe_not_equal:us,svg_element:yl}=window.__gradio__svelte__internal;function cs(l){let e,t;return{c(){e=yl("svg"),t=yl("path"),Ve(t,"d","M12 20L12 4M12 20L7 15M12 20L17 15"),Ve(t,"stroke","currentColor"),Ve(t,"stroke-width","2"),Ve(t,"stroke-linecap","round"),Ve(t,"stroke-linejoin","round"),Ve(e,"width","100%"),Ve(e,"height","100%"),Ve(e,"viewBox","0 0 24 24"),Ve(e,"fill","none"),Ve(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){fs(n,e,i),as(e,t)},p:En,i:En,o:En,d(n){n&&rs(e)}}}class ms extends ss{constructor(e){super(),_s(this,e,null,cs,us,{})}}const hs=async(l,e=1800)=>{let t=[...l],n=await Tn(t);if(n.length>e&&t.length>2){const i=t[0],o=t[t.length-1];t=[i,o],n=await Tn(t)}return n.length>e&&t.length>0&&(t=t.map(o=>{if(o.type==="text"){const s=Math.floor(e/t.length)-20;if(o.content.length>s)return{...o,content:o.content.substring(0,s)+"..."}}return o}),n=await Tn(t)),n},Tn=async l=>(await Promise.all(l.map(async t=>{if(t.role==="system")return"";let n=t.role==="user"?"😃":"🤖",i="";if(t.type==="text"){const o={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};i=t.content;for(let[s,r]of Object.entries(o)){let a;for(;(a=r.exec(t.content))!==null;){const _=a[1]||a[2],f=await pl(_);i=i.replace(_,f)}}}else{if(!t.content.value)return"";const o=t.content.component==="video"?t.content.value?.video.path:t.content.value,s=await pl(o);t.content.component==="audio"?i=`<audio controls src="${s}"></audio>`:t.content.component==="video"?i=s:t.content.component==="image"&&(i=`<img src="${s}" />`)}return`${n}: ${i}`}))).filter(t=>t!=="").join(`
`),so=(l,e)=>l.replace('src="/file',`src="${e}file`);function ds(l,e){if(!l){const t=e?.path;if(t){const n=t.toLowerCase();if(n.endsWith(".glb")||n.endsWith(".gltf")||n.endsWith(".obj")||n.endsWith(".stl")||n.endsWith(".splat")||n.endsWith(".ply"))return"model3d"}return"file"}return l.includes("audio")?"audio":l.includes("video")?"video":l.includes("image")?"image":l.includes("model")?"model3d":"file"}function ao(l){const e=Array.isArray(l.file)?l.file[0]:l.file;return{component:ds(e?.mime_type,e),value:l.file,alt_text:l.alt_text,constructor_args:{},props:{}}}function gs(l,e){if(l===null)return l;const t=new Map;return l.map((n,i)=>{let o=typeof n.content=="string"?{role:n.role,metadata:n.metadata,content:so(n.content,e),type:"text",index:i,options:n.options}:"file"in n.content?{content:ao(n.content),metadata:n.metadata,role:n.role,type:"component",index:i,options:n.options}:{type:"component",...n};const{id:s,title:r,parent_id:a}=n.metadata||{};if(a){const _=t.get(String(a));if(_){const f={...o,children:[]};return _.children.push(f),s&&r&&t.set(String(s),f),null}}if(s&&r){const _={...o,children:[]};return t.set(String(s),_),_}return o}).filter(n=>n!==null)}function bs(l,e){return l===null?l:l.flatMap((n,i)=>n.map((o,s)=>{if(o==null)return null;const r=s==0?"user":"assistant";return typeof o=="string"?{role:r,type:"text",content:so(o,e),metadata:{title:null},index:[i,s]}:"file"in o?{content:ao(o),role:r,type:"component",index:[i,s]}:{role:r,content:o,type:"component",index:[i,s]}})).filter(n=>n!=null)}function zl(l){return l.type==="component"}function on(l,e){const t=l[l.length-1].role==="assistant",n=l[l.length-1].index;return JSON.stringify(n)===JSON.stringify(e[e.length-1].index)&&t}function ws(l,e,t=!0){const n=[];let i=[],o=null;for(const s of l)if(s.role==="assistant"||s.role==="user"){if(!t){n.push([s]);continue}s.role===o?i.push(s):(i.length>0&&n.push(i),i=[s],o=s.role)}return i.length>0&&n.push(i),n}async function ks(l,e,t){let n=[],i=[];return l.forEach(r=>{if(e[r]||r==="file")return;const a=r==="dataframe"||r==="model3d"?"component":"base",{name:_,component:f}=t(r,a);n.push(_),i.push(f)}),(await Promise.allSettled(i)).map((r,a)=>r.status==="fulfilled"?[a,r.value]:null).filter(r=>r!==null).forEach(([r,a])=>{e[n[r]]=a.default}),e}function ps(l){if(!l)return[];let e=new Set;return l.forEach(t=>{t.type==="component"&&e.add(t.content.component)}),Array.from(e)}function Kn(l,e=0){let t="";const n="  ".repeat(e);l.metadata?.title&&(t+=`${n}${e>0?"- ":""}${l.metadata.title}
`),typeof l.content=="string"&&(t+=`${n}  ${l.content}
`);const i=l;return i.children?.length>0&&(t+=i.children.map(o=>Kn(o,e+1)).join("")),t}function vs(l){return Array.isArray(l)?l.map(e=>e.metadata?.title?Kn(e):e.content).join(`
`):l.metadata?.title?Kn(l):l.content}function ql(l){return Array.isArray(l)&&l.every(e=>typeof e.content=="string")||!Array.isArray(l)&&typeof l.content=="string"}const{SvelteComponent:$s,append:Cs,attr:yt,detach:ys,init:zs,insert:qs,noop:An,safe_not_equal:Ss,svg_element:Sl}=window.__gradio__svelte__internal;function Hs(l){let e,t;return{c(){e=Sl("svg"),t=Sl("path"),yt(t,"d","M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z"),yt(t,"fill","currentColor"),yt(e,"width","100%"),yt(e,"height","100%"),yt(e,"viewBox","0 0 12 12"),yt(e,"fill","none"),yt(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){qs(n,e,i),Cs(e,t)},p:An,i:An,o:An,d(n){n&&ys(e)}}}class Hl extends $s{constructor(e){super(),zs(this,e,null,Hs,Ss,{})}}const{SvelteComponent:Ls,append:Ms,attr:zt,detach:Ns,init:js,insert:Vs,noop:Fn,safe_not_equal:Ds,svg_element:Ll}=window.__gradio__svelte__internal;function Is(l){let e,t;return{c(){e=Ll("svg"),t=Ll("path"),zt(t,"d","M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z"),zt(t,"fill","currentColor"),zt(e,"width","100%"),zt(e,"height","100%"),zt(e,"viewBox","0 0 12 12"),zt(e,"fill","none"),zt(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){Vs(n,e,i),Ms(e,t)},p:Fn,i:Fn,o:Fn,d(n){n&&Ns(e)}}}class Ml extends Ls{constructor(e){super(),js(this,e,null,Is,Ds,{})}}const{SvelteComponent:Bs,append:Es,attr:qt,detach:Ts,init:As,insert:Fs,noop:Zn,safe_not_equal:Zs,svg_element:Nl}=window.__gradio__svelte__internal;function Us(l){let e,t;return{c(){e=Nl("svg"),t=Nl("path"),qt(t,"d","M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z"),qt(t,"fill","currentColor"),qt(e,"width","100%"),qt(e,"height","100%"),qt(e,"viewBox","0 0 12 12"),qt(e,"fill","none"),qt(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){Fs(n,e,i),Es(e,t)},p:Zn,i:Zn,o:Zn,d(n){n&&Ts(e)}}}class jl extends Bs{constructor(e){super(),As(this,e,null,Us,Zs,{})}}const{SvelteComponent:Ws,append:Ps,attr:St,detach:Os,init:Rs,insert:Js,noop:Un,safe_not_equal:Gs,svg_element:Vl}=window.__gradio__svelte__internal;function Ys(l){let e,t;return{c(){e=Vl("svg"),t=Vl("path"),St(t,"d","M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z"),St(t,"fill","currentColor"),St(e,"width","100%"),St(e,"height","100%"),St(e,"viewBox","0 0 12 12"),St(e,"fill","none"),St(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){Js(n,e,i),Ps(e,t)},p:Un,i:Un,o:Un,d(n){n&&Os(e)}}}class Dl extends Ws{constructor(e){super(),Rs(this,e,null,Ys,Gs,{})}}const{SvelteComponent:Ks,append:Qs,attr:Bt,detach:Xs,init:xs,insert:ea,noop:Wn,safe_not_equal:ta,svg_element:Il}=window.__gradio__svelte__internal;function na(l){let e,t;return{c(){e=Il("svg"),t=Il("path"),Bt(t,"fill","currentColor"),Bt(t,"d","M6,30H4V2H28l-5.8,9L28,20H6ZM6,18H24.33L19.8,11l4.53-7H6Z"),Bt(e,"id","icon"),Bt(e,"xmlns","http://www.w3.org/2000/svg"),Bt(e,"viewBox","0 0 32 32"),Bt(e,"fill","none")},m(n,i){ea(n,e,i),Qs(e,t)},p:Wn,i:Wn,o:Wn,d(n){n&&Xs(e)}}}class Bl extends Ks{constructor(e){super(),xs(this,e,null,na,ta,{})}}const{SvelteComponent:la,append:ia,attr:Et,detach:oa,init:sa,insert:aa,noop:Pn,safe_not_equal:ra,svg_element:El}=window.__gradio__svelte__internal;function _a(l){let e,t;return{c(){e=El("svg"),t=El("path"),Et(t,"fill","currentColor"),Et(t,"d","M4,2H28l-5.8,9L28,20H6v10H4V2z"),Et(e,"id","icon"),Et(e,"xmlns","http://www.w3.org/2000/svg"),Et(e,"viewBox","0 0 32 32"),Et(e,"fill","none")},m(n,i){aa(n,e,i),ia(e,t)},p:Pn,i:Pn,o:Pn,d(n){n&&oa(e)}}}class Tl extends la{constructor(e){super(),sa(this,e,null,_a,ra,{})}}const{SvelteComponent:fa,append:Qn,attr:Xn,check_outros:un,create_component:al,destroy_component:rl,destroy_each:ua,detach:Ut,element:xn,empty:ro,ensure_array_like:Al,flush:sn,group_outros:cn,init:ca,insert:Wt,listen:ma,mount_component:_l,safe_not_equal:ha,set_data:da,set_style:Fl,space:fl,text:ga,transition_in:re,transition_out:Le}=window.__gradio__svelte__internal;function Zl(l,e,t){const n=l.slice();return n[9]=e[t],n}function Ul(l){let e=l[3].includes("Dislike"),t,n=l[3].includes("Like"),i,o,s=e&&Wl(l),r=n&&Pl(l);return{c(){s&&s.c(),t=fl(),r&&r.c(),i=ro()},m(a,_){s&&s.m(a,_),Wt(a,t,_),r&&r.m(a,_),Wt(a,i,_),o=!0},p(a,_){_&8&&(e=a[3].includes("Dislike")),e?s?(s.p(a,_),_&8&&re(s,1)):(s=Wl(a),s.c(),re(s,1),s.m(t.parentNode,t)):s&&(cn(),Le(s,1,1,()=>{s=null}),un()),_&8&&(n=a[3].includes("Like")),n?r?(r.p(a,_),_&8&&re(r,1)):(r=Pl(a),r.c(),re(r,1),r.m(i.parentNode,i)):r&&(cn(),Le(r,1,1,()=>{r=null}),un())},i(a){o||(re(s),re(r),o=!0)},o(a){Le(s),Le(r),o=!1},d(a){a&&(Ut(t),Ut(i)),s&&s.d(a),r&&r.d(a)}}}function Wl(l){let e,t;return e=new ue({props:{Icon:l[0]==="Dislike"?Hl:Ml,label:l[0]==="Dislike"?"clicked dislike":l[1]("chatbot.dislike"),color:l[0]==="Dislike"?"var(--color-accent)":"var(--block-label-text-color)"}}),e.$on("click",l[6]),{c(){al(e.$$.fragment)},m(n,i){_l(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.Icon=n[0]==="Dislike"?Hl:Ml),i&3&&(o.label=n[0]==="Dislike"?"clicked dislike":n[1]("chatbot.dislike")),i&1&&(o.color=n[0]==="Dislike"?"var(--color-accent)":"var(--block-label-text-color)"),e.$set(o)},i(n){t||(re(e.$$.fragment,n),t=!0)},o(n){Le(e.$$.fragment,n),t=!1},d(n){rl(e,n)}}}function Pl(l){let e,t;return e=new ue({props:{Icon:l[0]==="Like"?jl:Dl,label:l[0]==="Like"?"clicked like":l[1]("chatbot.like"),color:l[0]==="Like"?"var(--color-accent)":"var(--block-label-text-color)"}}),e.$on("click",l[7]),{c(){al(e.$$.fragment)},m(n,i){_l(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.Icon=n[0]==="Like"?jl:Dl),i&3&&(o.label=n[0]==="Like"?"clicked like":n[1]("chatbot.like")),i&1&&(o.color=n[0]==="Like"?"var(--color-accent)":"var(--block-label-text-color)"),e.$set(o)},i(n){t||(re(e.$$.fragment,n),t=!0)},o(n){Le(e.$$.fragment,n),t=!1},d(n){rl(e,n)}}}function Ol(l){let e,t,n,i,o;t=new ue({props:{Icon:l[0]&&l[4].includes(l[0])?Tl:Bl,label:"Feedback",color:l[0]&&l[4].includes(l[0])?"var(--color-accent)":"var(--block-label-text-color)"}});let s=Al(l[4]),r=[];for(let a=0;a<s.length;a+=1)r[a]=Rl(Zl(l,s,a));return{c(){e=xn("div"),al(t.$$.fragment),n=fl(),i=xn("div");for(let a=0;a<r.length;a+=1)r[a].c();Xn(i,"class","extra-feedback-options svelte-14rmxes"),Xn(e,"class","extra-feedback no-border svelte-14rmxes")},m(a,_){Wt(a,e,_),_l(t,e,null),Qn(e,n),Qn(e,i);for(let f=0;f<r.length;f+=1)r[f]&&r[f].m(i,null);o=!0},p(a,_){const f={};if(_&17&&(f.Icon=a[0]&&a[4].includes(a[0])?Tl:Bl),_&17&&(f.color=a[0]&&a[4].includes(a[0])?"var(--color-accent)":"var(--block-label-text-color)"),t.$set(f),_&53){s=Al(a[4]);let u;for(u=0;u<s.length;u+=1){const c=Zl(a,s,u);r[u]?r[u].p(c,_):(r[u]=Rl(c),r[u].c(),r[u].m(i,null))}for(;u<r.length;u+=1)r[u].d(1);r.length=s.length}},i(a){o||(re(t.$$.fragment,a),o=!0)},o(a){Le(t.$$.fragment,a),o=!1},d(a){a&&Ut(e),rl(t),ua(r,a)}}}function Rl(l){let e,t=l[9]+"",n,i,o;function s(){return l[8](l[9])}return{c(){e=xn("button"),n=ga(t),Xn(e,"class","extra-feedback-option svelte-14rmxes"),Fl(e,"font-weight",l[0]===l[9]?"bold":"normal")},m(r,a){Wt(r,e,a),Qn(e,n),i||(o=ma(e,"click",s),i=!0)},p(r,a){l=r,a&16&&t!==(t=l[9]+"")&&da(n,t),a&17&&Fl(e,"font-weight",l[0]===l[9]?"bold":"normal")},d(r){r&&Ut(e),i=!1,o()}}}function ba(l){let e=l[3].includes("Like")||l[3].includes("Dislike"),t,n,i,o=e&&Ul(l),s=l[4].length>0&&Ol(l);return{c(){o&&o.c(),t=fl(),s&&s.c(),n=ro()},m(r,a){o&&o.m(r,a),Wt(r,t,a),s&&s.m(r,a),Wt(r,n,a),i=!0},p(r,[a]){a&8&&(e=r[3].includes("Like")||r[3].includes("Dislike")),e?o?(o.p(r,a),a&8&&re(o,1)):(o=Ul(r),o.c(),re(o,1),o.m(t.parentNode,t)):o&&(cn(),Le(o,1,1,()=>{o=null}),un()),r[4].length>0?s?(s.p(r,a),a&16&&re(s,1)):(s=Ol(r),s.c(),re(s,1),s.m(n.parentNode,n)):s&&(cn(),Le(s,1,1,()=>{s=null}),un())},i(r){i||(re(o),re(s),i=!0)},o(r){Le(o),Le(s),i=!1},d(r){r&&(Ut(t),Ut(n)),o&&o.d(r),s&&s.d(r)}}}function wa(l,e,t){let n,{i18n:i}=e,{handle_action:o}=e,{feedback_options:s}=e,{selected:r=null}=e;function a(c){t(0,r=r===c?null:c),o(r)}const _=()=>a("Dislike"),f=()=>a("Like"),u=c=>{a(c),o(r||null)};return l.$$set=c=>{"i18n"in c&&t(1,i=c.i18n),"handle_action"in c&&t(2,o=c.handle_action),"feedback_options"in c&&t(3,s=c.feedback_options),"selected"in c&&t(0,r=c.selected)},l.$$.update=()=>{l.$$.dirty&8&&t(4,n=s.filter(c=>c!=="Like"&&c!=="Dislike"))},[r,i,o,s,n,a,_,f,u]}class ka extends fa{constructor(e){super(),ca(this,e,wa,ba,ha,{i18n:1,handle_action:2,feedback_options:3,selected:0})}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),sn()}get handle_action(){return this.$$.ctx[2]}set handle_action(e){this.$$set({handle_action:e}),sn()}get feedback_options(){return this.$$.ctx[3]}set feedback_options(e){this.$$set({feedback_options:e}),sn()}get selected(){return this.$$.ctx[0]}set selected(e){this.$$set({selected:e}),sn()}}const{SvelteComponent:pa,create_component:va,destroy_component:$a,flush:Jl,init:Ca,mount_component:ya,safe_not_equal:za,transition_in:qa,transition_out:Sa}=window.__gradio__svelte__internal,{createEventDispatcher:Ha}=window.__gradio__svelte__internal,{onDestroy:La}=window.__gradio__svelte__internal;function Ma(l){let e,t;return e=new ue({props:{label:l[0]?"Copied message":"Copy message",Icon:l[0]?Gt:fn}}),e.$on("click",l[1]),{c(){va(e.$$.fragment)},m(n,i){ya(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.label=n[0]?"Copied message":"Copy message"),i&1&&(o.Icon=n[0]?Gt:fn),e.$set(o)},i(n){t||(qa(e.$$.fragment,n),t=!0)},o(n){Sa(e.$$.fragment,n),t=!1},d(n){$a(e,n)}}}function Na(l,e,t){const n=Ha();let i=!1,{value:o}=e,{watermark:s=null}=e,r;function a(){t(0,i=!0),r&&clearTimeout(r),r=setTimeout(()=>{t(0,i=!1)},2e3)}async function _(){if("clipboard"in navigator){n("copy",{value:o});const f=s?`${o}

${s}`:o;await navigator.clipboard.writeText(f),a()}else{const f=document.createElement("textarea"),u=s?`${o}

${s}`:o;f.value=u,f.style.position="absolute",f.style.left="-999999px",document.body.prepend(f),f.select();try{document.execCommand("copy"),a()}catch(c){console.error(c)}finally{f.remove()}}}return La(()=>{r&&clearTimeout(r)}),l.$$set=f=>{"value"in f&&t(2,o=f.value),"watermark"in f&&t(3,s=f.watermark)},[i,_,o,s]}class ja extends pa{constructor(e){super(),Ca(this,e,Na,Ma,za,{value:2,watermark:3})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),Jl()}get watermark(){return this.$$.ctx[3]}set watermark(e){this.$$set({watermark:e}),Jl()}}const{SvelteComponent:Va,attr:Gl,check_outros:Lt,create_component:dt,destroy_component:gt,detach:Re,element:Da,empty:ul,flush:K,group_outros:Mt,init:Ia,insert:Je,mount_component:bt,safe_not_equal:Ba,space:Jt,transition_in:j,transition_out:U}=window.__gradio__svelte__internal;function Yl(l){let e,t,n,i;return t=new io({props:{top_panel:!1,$$slots:{default:[Aa]},$$scope:{ctx:l}}}),{c(){e=Da("div"),dt(t.$$.fragment),Gl(e,"class",n="message-buttons-"+l[8]+" "+l[13]+" message-buttons "+(l[9]!==null&&"with-avatar")+" svelte-j7nkv7")},m(o,s){Je(o,e,s),bt(t,e,null),i=!0},p(o,s){const r={};s&33676543&&(r.$$scope={dirty:s,ctx:o}),t.$set(r),(!i||s&8960&&n!==(n="message-buttons-"+o[8]+" "+o[13]+" message-buttons "+(o[9]!==null&&"with-avatar")+" svelte-j7nkv7"))&&Gl(e,"class",n)},i(o){i||(j(t.$$.fragment,o),i=!0)},o(o){U(t.$$.fragment,o),i=!1},d(o){o&&Re(e),gt(t)}}}function Ea(l){let e,t,n,i,o,s,r=l[15]&&Kl(l),a=l[3]&&Ql(l),_=l[4]&&Xl(l),f=l[5]&&xl(l),u=l[1]&&ei(l);return{c(){r&&r.c(),e=Jt(),a&&a.c(),t=Jt(),_&&_.c(),n=Jt(),f&&f.c(),i=Jt(),u&&u.c(),o=ul()},m(c,w){r&&r.m(c,w),Je(c,e,w),a&&a.m(c,w),Je(c,t,w),_&&_.m(c,w),Je(c,n,w),f&&f.m(c,w),Je(c,i,w),u&&u.m(c,w),Je(c,o,w),s=!0},p(c,w){c[15]?r?(r.p(c,w),w&32768&&j(r,1)):(r=Kl(c),r.c(),j(r,1),r.m(e.parentNode,e)):r&&(Mt(),U(r,1,1,()=>{r=null}),Lt()),c[3]?a?(a.p(c,w),w&8&&j(a,1)):(a=Ql(c),a.c(),j(a,1),a.m(t.parentNode,t)):a&&(Mt(),U(a,1,1,()=>{a=null}),Lt()),c[4]?_?(_.p(c,w),w&16&&j(_,1)):(_=Xl(c),_.c(),j(_,1),_.m(n.parentNode,n)):_&&(Mt(),U(_,1,1,()=>{_=null}),Lt()),c[5]?f?(f.p(c,w),w&32&&j(f,1)):(f=xl(c),f.c(),j(f,1),f.m(i.parentNode,i)):f&&(Mt(),U(f,1,1,()=>{f=null}),Lt()),c[1]?u?(u.p(c,w),w&2&&j(u,1)):(u=ei(c),u.c(),j(u,1),u.m(o.parentNode,o)):u&&(Mt(),U(u,1,1,()=>{u=null}),Lt())},i(c){s||(j(r),j(a),j(_),j(f),j(u),s=!0)},o(c){U(r),U(a),U(_),U(f),U(u),s=!1},d(c){c&&(Re(e),Re(t),Re(n),Re(i),Re(o)),r&&r.d(c),a&&a.d(c),_&&_.d(c),f&&f.d(c),u&&u.d(c)}}}function Ta(l){let e,t,n,i;return e=new ue({props:{label:l[0]("chatbot.submit"),Icon:Gt,disabled:l[10]}}),e.$on("click",l[19]),n=new ue({props:{label:l[0]("chatbot.cancel"),Icon:Mo,disabled:l[10]}}),n.$on("click",l[20]),{c(){dt(e.$$.fragment),t=Jt(),dt(n.$$.fragment)},m(o,s){bt(e,o,s),Je(o,t,s),bt(n,o,s),i=!0},p(o,s){const r={};s&1&&(r.label=o[0]("chatbot.submit")),s&1024&&(r.disabled=o[10]),e.$set(r);const a={};s&1&&(a.label=o[0]("chatbot.cancel")),s&1024&&(a.disabled=o[10]),n.$set(a)},i(o){i||(j(e.$$.fragment,o),j(n.$$.fragment,o),i=!0)},o(o){U(e.$$.fragment,o),U(n.$$.fragment,o),i=!1},d(o){o&&Re(t),gt(e,o),gt(n,o)}}}function Kl(l){let e,t;return e=new ja({props:{value:l[16],watermark:l[7]}}),e.$on("copy",l[21]),{c(){dt(e.$$.fragment)},m(n,i){bt(e,n,i),t=!0},p(n,i){const o={};i&65536&&(o.value=n[16]),i&128&&(o.watermark=n[7]),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){gt(e,n)}}}function Ql(l){let e,t;return e=new ue({props:{Icon:os,label:l[0]("chatbot.retry"),disabled:l[10]}}),e.$on("click",l[22]),{c(){dt(e.$$.fragment)},m(n,i){bt(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.label=n[0]("chatbot.retry")),i&1024&&(o.disabled=n[10]),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){gt(e,n)}}}function Xl(l){let e,t;return e=new ue({props:{label:l[0]("chatbot.undo"),Icon:jo,disabled:l[10]}}),e.$on("click",l[23]),{c(){dt(e.$$.fragment)},m(n,i){bt(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.label=n[0]("chatbot.undo")),i&1024&&(o.disabled=n[10]),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){gt(e,n)}}}function xl(l){let e,t;return e=new ue({props:{label:l[0]("chatbot.edit"),Icon:No,disabled:l[10]}}),e.$on("click",l[24]),{c(){dt(e.$$.fragment)},m(n,i){bt(e,n,i),t=!0},p(n,i){const o={};i&1&&(o.label=n[0]("chatbot.edit")),i&1024&&(o.disabled=n[10]),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){gt(e,n)}}}function ei(l){let e,t;return e=new ka({props:{handle_action:l[12],feedback_options:l[2],selected:l[11],i18n:l[0]}}),{c(){dt(e.$$.fragment)},m(n,i){bt(e,n,i),t=!0},p(n,i){const o={};i&4096&&(o.handle_action=n[12]),i&4&&(o.feedback_options=n[2]),i&2048&&(o.selected=n[11]),i&1&&(o.i18n=n[0]),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){U(e.$$.fragment,n),t=!1},d(n){gt(e,n)}}}function Aa(l){let e,t,n,i;const o=[Ta,Ea],s=[];function r(a,_){return a[6]?0:1}return e=r(l),t=s[e]=o[e](l),{c(){t.c(),n=ul()},m(a,_){s[e].m(a,_),Je(a,n,_),i=!0},p(a,_){let f=e;e=r(a),e===f?s[e].p(a,_):(Mt(),U(s[f],1,1,()=>{s[f]=null}),Lt(),t=s[e],t?t.p(a,_):(t=s[e]=o[e](a),t.c()),j(t,1),t.m(n.parentNode,n))},i(a){i||(j(t),i=!0)},o(a){U(t),i=!1},d(a){a&&Re(n),s[e].d(a)}}}function Fa(l){let e,t,n=(l[15]||l[3]||l[4]||l[5]||l[1])&&Yl(l);return{c(){n&&n.c(),e=ul()},m(i,o){n&&n.m(i,o),Je(i,e,o),t=!0},p(i,[o]){i[15]||i[3]||i[4]||i[5]||i[1]?n?(n.p(i,o),o&32826&&j(n,1)):(n=Yl(i),n.c(),j(n,1),n.m(e.parentNode,e)):n&&(Mt(),U(n,1,1,()=>{n=null}),Lt())},i(i){t||(j(n),t=!0)},o(i){U(n),t=!1},d(i){i&&Re(e),n&&n.d(i)}}}function Za(l,e,t){let n,i,{i18n:o}=e,{likeable:s}=e,{feedback_options:r}=e,{show_retry:a}=e,{show_undo:_}=e,{show_edit:f}=e,{in_edit_mode:u}=e,{show_copy_button:c}=e,{watermark:w=null}=e,{message:m}=e,{position:h}=e,{avatar:k}=e,{generating:d}=e,{current_feedback:v}=e,{handle_action:S}=e,{layout:$}=e,{dispatch:F}=e;const C=()=>S("edit_submit"),E=()=>S("edit_cancel"),_e=q=>F("copy",q.detail),fe=()=>S("retry"),he=()=>S("undo"),de=()=>S("edit");return l.$$set=q=>{"i18n"in q&&t(0,o=q.i18n),"likeable"in q&&t(1,s=q.likeable),"feedback_options"in q&&t(2,r=q.feedback_options),"show_retry"in q&&t(3,a=q.show_retry),"show_undo"in q&&t(4,_=q.show_undo),"show_edit"in q&&t(5,f=q.show_edit),"in_edit_mode"in q&&t(6,u=q.in_edit_mode),"show_copy_button"in q&&t(17,c=q.show_copy_button),"watermark"in q&&t(7,w=q.watermark),"message"in q&&t(18,m=q.message),"position"in q&&t(8,h=q.position),"avatar"in q&&t(9,k=q.avatar),"generating"in q&&t(10,d=q.generating),"current_feedback"in q&&t(11,v=q.current_feedback),"handle_action"in q&&t(12,S=q.handle_action),"layout"in q&&t(13,$=q.layout),"dispatch"in q&&t(14,F=q.dispatch)},l.$$.update=()=>{l.$$.dirty&262144&&t(16,n=ql(m)?vs(m):""),l.$$.dirty&393216&&t(15,i=c&&m&&ql(m))},[o,s,r,a,_,f,u,w,h,k,d,v,S,$,F,i,n,c,m,C,E,_e,fe,he,de]}class _o extends Va{constructor(e){super(),Ia(this,e,Za,Fa,Ba,{i18n:0,likeable:1,feedback_options:2,show_retry:3,show_undo:4,show_edit:5,in_edit_mode:6,show_copy_button:17,watermark:7,message:18,position:8,avatar:9,generating:10,current_feedback:11,handle_action:12,layout:13,dispatch:14})}get i18n(){return this.$$.ctx[0]}set i18n(e){this.$$set({i18n:e}),K()}get likeable(){return this.$$.ctx[1]}set likeable(e){this.$$set({likeable:e}),K()}get feedback_options(){return this.$$.ctx[2]}set feedback_options(e){this.$$set({feedback_options:e}),K()}get show_retry(){return this.$$.ctx[3]}set show_retry(e){this.$$set({show_retry:e}),K()}get show_undo(){return this.$$.ctx[4]}set show_undo(e){this.$$set({show_undo:e}),K()}get show_edit(){return this.$$.ctx[5]}set show_edit(e){this.$$set({show_edit:e}),K()}get in_edit_mode(){return this.$$.ctx[6]}set in_edit_mode(e){this.$$set({in_edit_mode:e}),K()}get show_copy_button(){return this.$$.ctx[17]}set show_copy_button(e){this.$$set({show_copy_button:e}),K()}get watermark(){return this.$$.ctx[7]}set watermark(e){this.$$set({watermark:e}),K()}get message(){return this.$$.ctx[18]}set message(e){this.$$set({message:e}),K()}get position(){return this.$$.ctx[8]}set position(e){this.$$set({position:e}),K()}get avatar(){return this.$$.ctx[9]}set avatar(e){this.$$set({avatar:e}),K()}get generating(){return this.$$.ctx[10]}set generating(e){this.$$set({generating:e}),K()}get current_feedback(){return this.$$.ctx[11]}set current_feedback(e){this.$$set({current_feedback:e}),K()}get handle_action(){return this.$$.ctx[12]}set handle_action(e){this.$$set({handle_action:e}),K()}get layout(){return this.$$.ctx[13]}set layout(e){this.$$set({layout:e}),K()}get dispatch(){return this.$$.ctx[14]}set dispatch(e){this.$$set({dispatch:e}),K()}}const{SvelteComponent:Ua,assign:ti,attr:Wa,bubble:_t,check_outros:xe,construct_svelte_component:ie,create_component:oe,destroy_component:se,detach:Be,element:fo,empty:pt,flush:Se,get_spread_object:ni,get_spread_update:li,group_outros:et,init:Pa,insert:Ee,mount_component:ae,noop:Oa,safe_not_equal:Ra,set_style:Ja,transition_in:G,transition_out:Y}=window.__gradio__svelte__internal;function Ga(l){let e,t,n;const i=[{value:l[2]},{clear_color:l[5].clear_color},{display_mode:l[5].display_mode},{zoom_speed:l[5].zoom_speed},{pan_speed:l[5].pan_speed},l[5].camera_position!==void 0&&{camera_position:l[5].camera_position},{has_change_history:!0},{show_label:!1},{root:""},{interactive:!1},{label:"chatbot-model3d"},{show_share_button:!0},{gradio:{dispatch:On,i18n:l[6]}}];var o=l[1][l[0]];function s(r,a){let _={};for(let f=0;f<i.length;f+=1)_=ti(_,i[f]);return a!==void 0&&a&100&&(_=ti(_,li(i,[a&4&&{value:r[2]},a&32&&{clear_color:r[5].clear_color},a&32&&{display_mode:r[5].display_mode},a&32&&{zoom_speed:r[5].zoom_speed},a&32&&{pan_speed:r[5].pan_speed},a&32&&ni(r[5].camera_position!==void 0&&{camera_position:r[5].camera_position}),i[6],i[7],i[8],i[9],i[10],i[11],a&64&&{gradio:{dispatch:On,i18n:r[6]}}]))),{props:_}}return o&&(e=ie(o,s(l)),e.$on("load",l[18])),{c(){e&&oe(e.$$.fragment),t=pt()},m(r,a){e&&ae(e,r,a),Ee(r,t,a),n=!0},p(r,a){if(a&3&&o!==(o=r[1][r[0]])){if(e){et();const _=e;Y(_.$$.fragment,1,0,()=>{se(_,1)}),xe()}o?(e=ie(o,s(r,a)),e.$on("load",r[18]),oe(e.$$.fragment),G(e.$$.fragment,1),ae(e,t.parentNode,t)):e=null}else if(o){const _=a&100?li(i,[a&4&&{value:r[2]},a&32&&{clear_color:r[5].clear_color},a&32&&{display_mode:r[5].display_mode},a&32&&{zoom_speed:r[5].zoom_speed},a&32&&{pan_speed:r[5].pan_speed},a&32&&ni(r[5].camera_position!==void 0&&{camera_position:r[5].camera_position}),i[6],i[7],i[8],i[9],i[10],i[11],a&64&&{gradio:{dispatch:On,i18n:r[6]}}]):{};e.$set(_)}},i(r){n||(e&&G(e.$$.fragment,r),n=!0)},o(r){e&&Y(e.$$.fragment,r),n=!1},d(r){r&&Be(t),e&&se(e,r)}}}function Ya(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],show_label:!1,label:"chatbot-html",show_share_button:!0,i18n:s[6],gradio:{dispatch:ir}}}}return i&&(e=ie(i,o(l)),e.$on("load",l[17])),{c(){e&&oe(e.$$.fragment),t=pt()},m(s,r){e&&ae(e,s,r),Ee(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){et();const a=e;Y(a.$$.fragment,1,0,()=>{se(a,1)}),xe()}i?(e=ie(i,o(s)),e.$on("load",s[17]),oe(e.$$.fragment),G(e.$$.fragment,1),ae(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&64&&(a.i18n=s[6]),e.$set(a)}},i(s){n||(e&&G(e.$$.fragment,s),n=!0)},o(s){e&&Y(e.$$.fragment,s),n=!1},d(s){s&&Be(t),e&&se(e,s)}}}function Ka(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],show_label:!1,label:"chatbot-image",show_download_button:s[9],display_icon_button_wrapper_top_corner:s[10],i18n:s[6]}}}return i&&(e=ie(i,o(l)),e.$on("load",l[16])),{c(){e&&oe(e.$$.fragment),t=pt()},m(s,r){e&&ae(e,s,r),Ee(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){et();const a=e;Y(a.$$.fragment,1,0,()=>{se(a,1)}),xe()}i?(e=ie(i,o(s)),e.$on("load",s[16]),oe(e.$$.fragment),G(e.$$.fragment,1),ae(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&512&&(a.show_download_button=s[9]),r&1024&&(a.display_icon_button_wrapper_top_corner=s[10]),r&64&&(a.i18n=s[6]),e.$set(a)}},i(s){n||(e&&G(e.$$.fragment,s),n=!0)},o(s){e&&Y(e.$$.fragment,s),n=!1},d(s){s&&Be(t),e&&se(e,s)}}}function Qa(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{autoplay:s[5].autoplay,value:s[2].video||s[2],show_label:!1,show_share_button:!0,i18n:s[6],upload:s[7],display_icon_button_wrapper_top_corner:s[10],show_download_button:s[9],$$slots:{default:[nr]},$$scope:{ctx:s}}}}return i&&(e=ie(i,o(l)),e.$on("load",l[15])),{c(){e&&oe(e.$$.fragment),t=pt()},m(s,r){e&&ae(e,s,r),Ee(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){et();const a=e;Y(a.$$.fragment,1,0,()=>{se(a,1)}),xe()}i?(e=ie(i,o(s)),e.$on("load",s[15]),oe(e.$$.fragment),G(e.$$.fragment,1),ae(e,t.parentNode,t)):e=null}else if(i){const a={};r&32&&(a.autoplay=s[5].autoplay),r&4&&(a.value=s[2].video||s[2]),r&64&&(a.i18n=s[6]),r&128&&(a.upload=s[7]),r&1024&&(a.display_icon_button_wrapper_top_corner=s[10]),r&512&&(a.show_download_button=s[9]),r&524288&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)}},i(s){n||(e&&G(e.$$.fragment,s),n=!0)},o(s){e&&Y(e.$$.fragment,s),n=!1},d(s){s&&Be(t),e&&se(e,s)}}}function Xa(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],show_label:!1,show_share_button:!0,i18n:s[6],label:"",waveform_settings:{autoplay:s[5].autoplay},show_download_button:s[9],display_icon_button_wrapper_top_corner:s[10]}}}return i&&(t=ie(i,o(l)),t.$on("load",l[14])),{c(){e=fo("div"),t&&oe(t.$$.fragment),Ja(e,"position","relative")},m(s,r){Ee(s,e,r),t&&ae(t,e,null),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(t){et();const a=t;Y(a.$$.fragment,1,0,()=>{se(a,1)}),xe()}i?(t=ie(i,o(s)),t.$on("load",s[14]),oe(t.$$.fragment),G(t.$$.fragment,1),ae(t,e,null)):t=null}else if(i){const a={};r&4&&(a.value=s[2]),r&64&&(a.i18n=s[6]),r&32&&(a.waveform_settings={autoplay:s[5].autoplay}),r&512&&(a.show_download_button=s[9]),r&1024&&(a.display_icon_button_wrapper_top_corner=s[10]),t.$set(a)}},i(s){n||(t&&G(t.$$.fragment,s),n=!0)},o(s){t&&Y(t.$$.fragment,s),n=!1},d(s){s&&Be(e),t&&se(t)}}}function xa(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],target:s[3],theme_mode:s[4],bokeh_version:s[5].bokeh_version,caption:"",show_actions_button:!0}}}return i&&(e=ie(i,o(l)),e.$on("load",l[13])),{c(){e&&oe(e.$$.fragment),t=pt()},m(s,r){e&&ae(e,s,r),Ee(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){et();const a=e;Y(a.$$.fragment,1,0,()=>{se(a,1)}),xe()}i?(e=ie(i,o(s)),e.$on("load",s[13]),oe(e.$$.fragment),G(e.$$.fragment,1),ae(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&8&&(a.target=s[3]),r&16&&(a.theme_mode=s[4]),r&32&&(a.bokeh_version=s[5].bokeh_version),e.$set(a)}},i(s){n||(e&&G(e.$$.fragment,s),n=!0)},o(s){e&&Y(e.$$.fragment,s),n=!1},d(s){s&&Be(t),e&&se(e,s)}}}function er(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],show_label:!1,i18n:s[6],label:"",interactive:!1,line_breaks:s[5].line_breaks,wrap:!0,root:"",gradio:{dispatch:ii,i18n:s[6]},datatype:s[5].datatype,latex_delimiters:s[5].latex_delimiters,col_count:s[5].col_count,row_count:s[5].row_count}}}return i&&(e=ie(i,o(l)),e.$on("load",l[12])),{c(){e&&oe(e.$$.fragment),t=pt()},m(s,r){e&&ae(e,s,r),Ee(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){et();const a=e;Y(a.$$.fragment,1,0,()=>{se(a,1)}),xe()}i?(e=ie(i,o(s)),e.$on("load",s[12]),oe(e.$$.fragment),G(e.$$.fragment,1),ae(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&64&&(a.i18n=s[6]),r&32&&(a.line_breaks=s[5].line_breaks),r&64&&(a.gradio={dispatch:ii,i18n:s[6]}),r&32&&(a.datatype=s[5].datatype),r&32&&(a.latex_delimiters=s[5].latex_delimiters),r&32&&(a.col_count=s[5].col_count),r&32&&(a.row_count=s[5].row_count),e.$set(a)}},i(s){n||(e&&G(e.$$.fragment,s),n=!0)},o(s){e&&Y(e.$$.fragment,s),n=!1},d(s){s&&Be(t),e&&se(e,s)}}}function tr(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],display_icon_button_wrapper_top_corner:s[10],show_label:!1,i18n:s[6],label:"",_fetch:s[8],allow_preview:!1,interactive:!1,mode:"minimal",fixed_height:1}}}return i&&(e=ie(i,o(l)),e.$on("load",l[11])),{c(){e&&oe(e.$$.fragment),t=pt()},m(s,r){e&&ae(e,s,r),Ee(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){et();const a=e;Y(a.$$.fragment,1,0,()=>{se(a,1)}),xe()}i?(e=ie(i,o(s)),e.$on("load",s[11]),oe(e.$$.fragment),G(e.$$.fragment,1),ae(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&1024&&(a.display_icon_button_wrapper_top_corner=s[10]),r&64&&(a.i18n=s[6]),r&256&&(a._fetch=s[8]),e.$set(a)}},i(s){n||(e&&G(e.$$.fragment,s),n=!0)},o(s){e&&Y(e.$$.fragment,s),n=!1},d(s){s&&Be(t),e&&se(e,s)}}}function nr(l){let e;return{c(){e=fo("track"),Wa(e,"kind","captions")},m(t,n){Ee(t,e,n)},p:Oa,d(t){t&&Be(e)}}}function lr(l){let e,t,n,i;const o=[tr,er,xa,Xa,Qa,Ka,Ya,Ga],s=[];function r(a,_){return a[0]==="gallery"?0:a[0]==="dataframe"?1:a[0]==="plot"?2:a[0]==="audio"?3:a[0]==="video"?4:a[0]==="image"?5:a[0]==="html"?6:a[0]==="model3d"?7:-1}return~(e=r(l))&&(t=s[e]=o[e](l)),{c(){t&&t.c(),n=pt()},m(a,_){~e&&s[e].m(a,_),Ee(a,n,_),i=!0},p(a,[_]){let f=e;e=r(a),e===f?~e&&s[e].p(a,_):(t&&(et(),Y(s[f],1,1,()=>{s[f]=null}),xe()),~e?(t=s[e],t?t.p(a,_):(t=s[e]=o[e](a),t.c()),G(t,1),t.m(n.parentNode,n)):t=null)},i(a){i||(G(t),i=!0)},o(a){Y(t),i=!1},d(a){a&&Be(n),~e&&s[e].d(a)}}}const ii=()=>{},ir=()=>{},On=()=>{};function or(l,e,t){let{type:n}=e,{components:i}=e,{value:o}=e,{target:s}=e,{theme_mode:r}=e,{props:a}=e,{i18n:_}=e,{upload:f}=e,{_fetch:u}=e,{allow_file_downloads:c}=e,{display_icon_button_wrapper_top_corner:w=!1}=e;function m(C){_t.call(this,l,C)}function h(C){_t.call(this,l,C)}function k(C){_t.call(this,l,C)}function d(C){_t.call(this,l,C)}function v(C){_t.call(this,l,C)}function S(C){_t.call(this,l,C)}function $(C){_t.call(this,l,C)}function F(C){_t.call(this,l,C)}return l.$$set=C=>{"type"in C&&t(0,n=C.type),"components"in C&&t(1,i=C.components),"value"in C&&t(2,o=C.value),"target"in C&&t(3,s=C.target),"theme_mode"in C&&t(4,r=C.theme_mode),"props"in C&&t(5,a=C.props),"i18n"in C&&t(6,_=C.i18n),"upload"in C&&t(7,f=C.upload),"_fetch"in C&&t(8,u=C._fetch),"allow_file_downloads"in C&&t(9,c=C.allow_file_downloads),"display_icon_button_wrapper_top_corner"in C&&t(10,w=C.display_icon_button_wrapper_top_corner)},[n,i,o,s,r,a,_,f,u,c,w,m,h,k,d,v,S,$,F]}class sr extends Ua{constructor(e){super(),Pa(this,e,or,lr,Ra,{type:0,components:1,value:2,target:3,theme_mode:4,props:5,i18n:6,upload:7,_fetch:8,allow_file_downloads:9,display_icon_button_wrapper_top_corner:10})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),Se()}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),Se()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),Se()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),Se()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),Se()}get props(){return this.$$.ctx[5]}set props(e){this.$$set({props:e}),Se()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),Se()}get upload(){return this.$$.ctx[7]}set upload(e){this.$$set({upload:e}),Se()}get _fetch(){return this.$$.ctx[8]}set _fetch(e){this.$$set({_fetch:e}),Se()}get allow_file_downloads(){return this.$$.ctx[9]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),Se()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[10]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),Se()}}const{SvelteComponent:ar,append:Oe,attr:we,check_outros:rr,create_component:cl,destroy_component:ml,detach:hl,element:Ht,empty:_r,flush:ne,group_outros:fr,init:ur,insert:dl,is_function:cr,mount_component:gl,safe_not_equal:mr,set_data:oi,space:si,text:ai,transition_in:Yt,transition_out:Kt}=window.__gradio__svelte__internal;function hr(l){let e,t,n,i,o,s,r,a=(l[15].content.value?.orig_name||l[15].content.value?.path.split("/").pop()||"file")+"",_,f,u,c,w,m=(l[15].content.value?.orig_name||l[15].content.value?.path||"").split(".").pop().toUpperCase()+"",h,k;return n=new ol({}),{c(){e=Ht("div"),t=Ht("div"),cl(n.$$.fragment),i=si(),o=Ht("div"),s=Ht("a"),r=Ht("span"),_=ai(a),c=si(),w=Ht("span"),h=ai(m),we(t,"class","file-icon svelte-ulpe0d"),we(r,"class","file-name svelte-ulpe0d"),we(s,"data-testid","chatbot-file"),we(s,"class","file-link svelte-ulpe0d"),we(s,"href",f=l[15].content.value.url),we(s,"target","_blank"),we(s,"download",u=window.__is_colab__?null:l[15].content.value?.orig_name||l[15].content.value?.path.split("/").pop()||"file"),we(w,"class","file-type svelte-ulpe0d"),we(o,"class","file-info svelte-ulpe0d"),we(e,"class","file-container svelte-ulpe0d")},m(d,v){dl(d,e,v),Oe(e,t),gl(n,t,null),Oe(e,i),Oe(e,o),Oe(o,s),Oe(s,r),Oe(r,_),Oe(o,c),Oe(o,w),Oe(w,h),k=!0},p(d,v){(!k||v&32768)&&a!==(a=(d[15].content.value?.orig_name||d[15].content.value?.path.split("/").pop()||"file")+"")&&oi(_,a),(!k||v&32768&&f!==(f=d[15].content.value.url))&&we(s,"href",f),(!k||v&32768&&u!==(u=window.__is_colab__?null:d[15].content.value?.orig_name||d[15].content.value?.path.split("/").pop()||"file"))&&we(s,"download",u),(!k||v&32768)&&m!==(m=(d[15].content.value?.orig_name||d[15].content.value?.path||"").split(".").pop().toUpperCase()+"")&&oi(h,m)},i(d){k||(Yt(n.$$.fragment,d),k=!0)},o(d){Kt(n.$$.fragment,d),k=!1},d(d){d&&hl(e),ml(n)}}}function dr(l){let e,t;return e=new sr({props:{target:l[6],theme_mode:l[7],props:l[15].content.props,type:l[15].content.component,components:l[8],value:l[15].content.value,display_icon_button_wrapper_top_corner:l[13]>0&&l[12],i18n:l[3],upload:l[5],_fetch:l[2],allow_file_downloads:l[11]}}),e.$on("load",l[16]),{c(){cl(e.$$.fragment)},m(n,i){gl(e,n,i),t=!0},p(n,i){const o={};i&64&&(o.target=n[6]),i&128&&(o.theme_mode=n[7]),i&32768&&(o.props=n[15].content.props),i&32768&&(o.type=n[15].content.component),i&256&&(o.components=n[8]),i&32768&&(o.value=n[15].content.value),i&12288&&(o.display_icon_button_wrapper_top_corner=n[13]>0&&n[12]),i&8&&(o.i18n=n[3]),i&32&&(o.upload=n[5]),i&4&&(o._fetch=n[2]),i&2048&&(o.allow_file_downloads=n[11]),e.$set(o)},i(n){t||(Yt(e.$$.fragment,n),t=!0)},o(n){Kt(e.$$.fragment,n),t=!1},d(n){ml(e,n)}}}function gr(l){let e,t,n;return t=new sl({props:{message:l[15].content,latex_delimiters:l[0],sanitize_html:l[1],render_markdown:l[9],line_breaks:l[4],allow_tags:l[14],theme_mode:l[7]}}),t.$on("load",function(){cr(l[10])&&l[10].apply(this,arguments)}),{c(){e=Ht("div"),cl(t.$$.fragment),we(e,"class","message-content")},m(i,o){dl(i,e,o),gl(t,e,null),n=!0},p(i,o){l=i;const s={};o&32768&&(s.message=l[15].content),o&1&&(s.latex_delimiters=l[0]),o&2&&(s.sanitize_html=l[1]),o&512&&(s.render_markdown=l[9]),o&16&&(s.line_breaks=l[4]),o&16384&&(s.allow_tags=l[14]),o&128&&(s.theme_mode=l[7]),t.$set(s)},i(i){n||(Yt(t.$$.fragment,i),n=!0)},o(i){Kt(t.$$.fragment,i),n=!1},d(i){i&&hl(e),ml(t)}}}function br(l){let e,t,n,i;const o=[gr,dr,hr],s=[];function r(a,_){return a[15].type==="text"?0:a[15].type==="component"&&a[15].content.component in a[8]?1:a[15].type==="component"&&a[15].content.component==="file"?2:-1}return~(e=r(l))&&(t=s[e]=o[e](l)),{c(){t&&t.c(),n=_r()},m(a,_){~e&&s[e].m(a,_),dl(a,n,_),i=!0},p(a,[_]){let f=e;e=r(a),e===f?~e&&s[e].p(a,_):(t&&(fr(),Kt(s[f],1,1,()=>{s[f]=null}),rr()),~e?(t=s[e],t?t.p(a,_):(t=s[e]=o[e](a),t.c()),Yt(t,1),t.m(n.parentNode,n)):t=null)},i(a){i||(Yt(t),i=!0)},o(a){Kt(t),i=!1},d(a){a&&hl(n),~e&&s[e].d(a)}}}function wr(l,e,t){let{latex_delimiters:n}=e,{sanitize_html:i}=e,{_fetch:o}=e,{i18n:s}=e,{line_breaks:r}=e,{upload:a}=e,{target:_}=e,{theme_mode:f}=e,{_components:u}=e,{render_markdown:c}=e,{scroll:w}=e,{allow_file_downloads:m}=e,{display_consecutive_in_same_bubble:h}=e,{thought_index:k}=e,{allow_tags:d=!1}=e,{message:v}=e;const S=()=>w();return l.$$set=$=>{"latex_delimiters"in $&&t(0,n=$.latex_delimiters),"sanitize_html"in $&&t(1,i=$.sanitize_html),"_fetch"in $&&t(2,o=$._fetch),"i18n"in $&&t(3,s=$.i18n),"line_breaks"in $&&t(4,r=$.line_breaks),"upload"in $&&t(5,a=$.upload),"target"in $&&t(6,_=$.target),"theme_mode"in $&&t(7,f=$.theme_mode),"_components"in $&&t(8,u=$._components),"render_markdown"in $&&t(9,c=$.render_markdown),"scroll"in $&&t(10,w=$.scroll),"allow_file_downloads"in $&&t(11,m=$.allow_file_downloads),"display_consecutive_in_same_bubble"in $&&t(12,h=$.display_consecutive_in_same_bubble),"thought_index"in $&&t(13,k=$.thought_index),"allow_tags"in $&&t(14,d=$.allow_tags),"message"in $&&t(15,v=$.message)},[n,i,o,s,r,a,_,f,u,c,w,m,h,k,d,v,S]}class uo extends ar{constructor(e){super(),ur(this,e,wr,br,mr,{latex_delimiters:0,sanitize_html:1,_fetch:2,i18n:3,line_breaks:4,upload:5,target:6,theme_mode:7,_components:8,render_markdown:9,scroll:10,allow_file_downloads:11,display_consecutive_in_same_bubble:12,thought_index:13,allow_tags:14,message:15})}get latex_delimiters(){return this.$$.ctx[0]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),ne()}get sanitize_html(){return this.$$.ctx[1]}set sanitize_html(e){this.$$set({sanitize_html:e}),ne()}get _fetch(){return this.$$.ctx[2]}set _fetch(e){this.$$set({_fetch:e}),ne()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),ne()}get line_breaks(){return this.$$.ctx[4]}set line_breaks(e){this.$$set({line_breaks:e}),ne()}get upload(){return this.$$.ctx[5]}set upload(e){this.$$set({upload:e}),ne()}get target(){return this.$$.ctx[6]}set target(e){this.$$set({target:e}),ne()}get theme_mode(){return this.$$.ctx[7]}set theme_mode(e){this.$$set({theme_mode:e}),ne()}get _components(){return this.$$.ctx[8]}set _components(e){this.$$set({_components:e}),ne()}get render_markdown(){return this.$$.ctx[9]}set render_markdown(e){this.$$set({render_markdown:e}),ne()}get scroll(){return this.$$.ctx[10]}set scroll(e){this.$$set({scroll:e}),ne()}get allow_file_downloads(){return this.$$.ctx[11]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),ne()}get display_consecutive_in_same_bubble(){return this.$$.ctx[12]}set display_consecutive_in_same_bubble(e){this.$$set({display_consecutive_in_same_bubble:e}),ne()}get thought_index(){return this.$$.ctx[13]}set thought_index(e){this.$$set({thought_index:e}),ne()}get allow_tags(){return this.$$.ctx[14]}set allow_tags(e){this.$$set({allow_tags:e}),ne()}get message(){return this.$$.ctx[15]}set message(e){this.$$set({message:e}),ne()}}const{SvelteComponent:kr,add_render_callback:pr,append:ut,attr:He,binding_callbacks:vr,check_outros:bl,create_bidirectional_transition:ri,create_component:mn,destroy_component:hn,destroy_each:$r,detach:ce,element:Nt,ensure_array_like:_i,flush:Q,group_outros:wl,init:Cr,insert:me,listen:el,mount_component:dn,run_all:yr,safe_not_equal:zr,set_data:pn,set_style:fi,space:Tt,stop_propagation:qr,text:Xe,toggle_class:At,transition_in:ke,transition_out:Ie}=window.__gradio__svelte__internal;function ui(l,e,t){const n=l.slice();return n[27]=e[t],n[29]=t,n}function ci(l){let e;return{c(){e=Nt("span"),He(e,"class","loading-spinner svelte-1qn6r4f")},m(t,n){me(t,e,n)},d(t){t&&ce(e)}}}function mi(l){let e,t,n=l[16].metadata.log&&hi(l),i=l[16].metadata.duration!==void 0&&di(l);return{c(){e=Nt("span"),n&&n.c(),t=Tt(),i&&i.c(),He(e,"class","duration svelte-1qn6r4f")},m(o,s){me(o,e,s),n&&n.m(e,null),ut(e,t),i&&i.m(e,null)},p(o,s){o[16].metadata.log?n?n.p(o,s):(n=hi(o),n.c(),n.m(e,t)):n&&(n.d(1),n=null),o[16].metadata.duration!==void 0?i?i.p(o,s):(i=di(o),i.c(),i.m(e,null)):i&&(i.d(1),i=null)},d(o){o&&ce(e),n&&n.d(),i&&i.d()}}}function hi(l){let e=l[16].metadata.log+"",t;return{c(){t=Xe(e)},m(n,i){me(n,t,i)},p(n,i){i&65536&&e!==(e=n[16].metadata.log+"")&&pn(t,e)},d(n){n&&ce(t)}}}function di(l){let e,t,n;function i(r,a){return a&65536&&(t=null),t==null&&(t=!!Number.isInteger(r[16].metadata.duration)),t?Lr:r[16].metadata.duration>=.1?Hr:Sr}let o=i(l,-1),s=o(l);return{c(){e=Xe("("),s.c(),n=Xe(")")},m(r,a){me(r,e,a),s.m(r,a),me(r,n,a)},p(r,a){o===(o=i(r,a))&&s?s.p(r,a):(s.d(1),s=o(r),s&&(s.c(),s.m(n.parentNode,n)))},d(r){r&&(ce(e),ce(n)),s.d(r)}}}function Sr(l){let e=(l[16].metadata.duration*1e3).toFixed(1)+"",t,n;return{c(){t=Xe(e),n=Xe("ms")},m(i,o){me(i,t,o),me(i,n,o)},p(i,o){o&65536&&e!==(e=(i[16].metadata.duration*1e3).toFixed(1)+"")&&pn(t,e)},d(i){i&&(ce(t),ce(n))}}}function Hr(l){let e=l[16].metadata.duration.toFixed(1)+"",t,n;return{c(){t=Xe(e),n=Xe("s")},m(i,o){me(i,t,o),me(i,n,o)},p(i,o){o&65536&&e!==(e=i[16].metadata.duration.toFixed(1)+"")&&pn(t,e)},d(i){i&&(ce(t),ce(n))}}}function Lr(l){let e=l[16].metadata.duration+"",t,n;return{c(){t=Xe(e),n=Xe("s")},m(i,o){me(i,t,o),me(i,n,o)},p(i,o){o&65536&&e!==(e=i[16].metadata.duration+"")&&pn(t,e)},d(i){i&&(ce(t),ce(n))}}}function gi(l){let e,t,n,i,o,s,r;t=new uo({props:{message:l[16],sanitize_html:l[1],allow_tags:l[15],latex_delimiters:l[2],render_markdown:l[3],_components:l[4],upload:l[5],thought_index:l[6],target:l[7],theme_mode:l[8],_fetch:l[9],scroll:l[10],allow_file_downloads:l[11],display_consecutive_in_same_bubble:l[12],i18n:l[13],line_breaks:l[14]}});let a=l[16].children?.length>0&&bi(l);return{c(){e=Nt("div"),mn(t.$$.fragment),n=Tt(),a&&a.c(),He(e,"class","svelte-1qn6r4f"),At(e,"content",l[18]),At(e,"content-preview",!l[18]&&l[16].metadata?.status!=="done")},m(_,f){me(_,e,f),dn(t,e,null),ut(e,n),a&&a.m(e,null),l[24](e),o=!0,s||(r=el(e,"scroll",l[20]),s=!0)},p(_,f){const u={};f&65536&&(u.message=_[16]),f&2&&(u.sanitize_html=_[1]),f&32768&&(u.allow_tags=_[15]),f&4&&(u.latex_delimiters=_[2]),f&8&&(u.render_markdown=_[3]),f&16&&(u._components=_[4]),f&32&&(u.upload=_[5]),f&64&&(u.thought_index=_[6]),f&128&&(u.target=_[7]),f&256&&(u.theme_mode=_[8]),f&512&&(u._fetch=_[9]),f&1024&&(u.scroll=_[10]),f&2048&&(u.allow_file_downloads=_[11]),f&4096&&(u.display_consecutive_in_same_bubble=_[12]),f&8192&&(u.i18n=_[13]),f&16384&&(u.line_breaks=_[14]),t.$set(u),_[16].children?.length>0?a?(a.p(_,f),f&65536&&ke(a,1)):(a=bi(_),a.c(),ke(a,1),a.m(e,null)):a&&(wl(),Ie(a,1,1,()=>{a=null}),bl()),(!o||f&262144)&&At(e,"content",_[18]),(!o||f&327680)&&At(e,"content-preview",!_[18]&&_[16].metadata?.status!=="done")},i(_){o||(ke(t.$$.fragment,_),ke(a),_&&pr(()=>{o&&(i||(i=ri(e,vl,{},!0)),i.run(1))}),o=!0)},o(_){Ie(t.$$.fragment,_),Ie(a),_&&(i||(i=ri(e,vl,{},!1)),i.run(0)),o=!1},d(_){_&&ce(e),hn(t),a&&a.d(),l[24](null),_&&i&&i.end(),s=!1,r()}}}function bi(l){let e,t,n=_i(l[16].children),i=[];for(let s=0;s<n.length;s+=1)i[s]=wi(ui(l,n,s));const o=s=>Ie(i[s],1,1,()=>{i[s]=null});return{c(){e=Nt("div");for(let s=0;s<i.length;s+=1)i[s].c();He(e,"class","children svelte-1qn6r4f")},m(s,r){me(s,e,r);for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(e,null);t=!0},p(s,r){if(r&98303){n=_i(s[16].children);let a;for(a=0;a<n.length;a+=1){const _=ui(s,n,a);i[a]?(i[a].p(_,r),ke(i[a],1)):(i[a]=wi(_),i[a].c(),ke(i[a],1),i[a].m(e,null))}for(wl(),a=n.length;a<i.length;a+=1)o(a);bl()}},i(s){if(!t){for(let r=0;r<n.length;r+=1)ke(i[r]);t=!0}},o(s){i=i.filter(Boolean);for(let r=0;r<i.length;r+=1)Ie(i[r]);t=!1},d(s){s&&ce(e),$r(i,s)}}}function wi(l){let e,t;return e=new co({props:{thought:l[27],rtl:l[0],sanitize_html:l[1],latex_delimiters:l[2],render_markdown:l[3],_components:l[4],upload:l[5],thought_index:l[6]+1,target:l[7],theme_mode:l[8],_fetch:l[9],scroll:l[10],allow_file_downloads:l[11],display_consecutive_in_same_bubble:l[12],i18n:l[13],line_breaks:l[14]}}),{c(){mn(e.$$.fragment)},m(n,i){dn(e,n,i),t=!0},p(n,i){const o={};i&65536&&(o.thought=n[27]),i&1&&(o.rtl=n[0]),i&2&&(o.sanitize_html=n[1]),i&4&&(o.latex_delimiters=n[2]),i&8&&(o.render_markdown=n[3]),i&16&&(o._components=n[4]),i&32&&(o.upload=n[5]),i&64&&(o.thought_index=n[6]+1),i&128&&(o.target=n[7]),i&256&&(o.theme_mode=n[8]),i&512&&(o._fetch=n[9]),i&1024&&(o.scroll=n[10]),i&2048&&(o.allow_file_downloads=n[11]),i&4096&&(o.display_consecutive_in_same_bubble=n[12]),i&8192&&(o.i18n=n[13]),i&16384&&(o.line_breaks=n[14]),e.$set(o)},i(n){t||(ke(e.$$.fragment,n),t=!0)},o(n){Ie(e.$$.fragment,n),t=!1},d(n){hn(e,n)}}}function Mr(l){let e,t,n,i,o,s,r,a,_,f,u,c,w;i=new ue({props:{Icon:Xo}}),s=new sl({props:{message:l[16].metadata?.title||"",render_markdown:l[3],latex_delimiters:l[2],sanitize_html:l[1],allow_tags:l[15]}});let m=l[16].metadata?.status==="pending"&&ci(),h=(l[16]?.metadata?.log||l[16]?.metadata?.duration)&&mi(l),k=l[18]&&gi(l);return{c(){e=Nt("div"),t=Nt("div"),n=Nt("span"),mn(i.$$.fragment),o=Tt(),mn(s.$$.fragment),r=Tt(),m&&m.c(),a=Tt(),h&&h.c(),f=Tt(),k&&k.c(),He(n,"class","arrow svelte-1qn6r4f"),fi(n,"transform",l[18]?"rotate(180deg)":"rotate(0deg)"),He(t,"class","title svelte-1qn6r4f"),He(t,"aria-busy",_=l[16].content===""||l[16].content===null),He(t,"role","button"),He(t,"tabindex","0"),At(t,"expanded",l[18]),He(e,"class","thought-group svelte-1qn6r4f")},m(d,v){me(d,e,v),ut(e,t),ut(t,n),dn(i,n,null),ut(t,o),dn(s,t,null),ut(t,r),m&&m.m(t,null),ut(t,a),h&&h.m(t,null),ut(e,f),k&&k.m(e,null),u=!0,c||(w=[el(t,"click",qr(l[19])),el(t,"keydown",l[23])],c=!0)},p(d,[v]){v&262144&&fi(n,"transform",d[18]?"rotate(180deg)":"rotate(0deg)");const S={};v&65536&&(S.message=d[16].metadata?.title||""),v&8&&(S.render_markdown=d[3]),v&4&&(S.latex_delimiters=d[2]),v&2&&(S.sanitize_html=d[1]),v&32768&&(S.allow_tags=d[15]),s.$set(S),d[16].metadata?.status==="pending"?m||(m=ci(),m.c(),m.m(t,a)):m&&(m.d(1),m=null),d[16]?.metadata?.log||d[16]?.metadata?.duration?h?h.p(d,v):(h=mi(d),h.c(),h.m(t,null)):h&&(h.d(1),h=null),(!u||v&65536&&_!==(_=d[16].content===""||d[16].content===null))&&He(t,"aria-busy",_),(!u||v&262144)&&At(t,"expanded",d[18]),d[18]?k?(k.p(d,v),v&262144&&ke(k,1)):(k=gi(d),k.c(),ke(k,1),k.m(e,null)):k&&(wl(),Ie(k,1,1,()=>{k=null}),bl())},i(d){u||(ke(i.$$.fragment,d),ke(s.$$.fragment,d),ke(k),u=!0)},o(d){Ie(i.$$.fragment,d),Ie(s.$$.fragment,d),Ie(k),u=!1},d(d){d&&ce(e),hn(i),hn(s),m&&m.d(),h&&h.d(),k&&k.d(),c=!1,yr(w)}}}function Nr(l){return"children"in l}function jr(l,e,t){let{thought:n}=e,{rtl:i=!1}=e,{sanitize_html:o}=e,{latex_delimiters:s}=e,{render_markdown:r}=e,{_components:a}=e,{upload:_}=e,{thought_index:f}=e,{target:u}=e,{theme_mode:c}=e,{_fetch:w}=e,{scroll:m}=e,{allow_file_downloads:h}=e,{display_consecutive_in_same_bubble:k}=e,{i18n:d}=e,{line_breaks:v}=e,{allow_tags:S=!1}=e,$,F=!1,C=!1,E,_e=!1;function fe(){t(18,F=!F),t(22,C=!0)}function he(){E&&!_e&&t(17,E.scrollTop=E.scrollHeight,E)}function de(){E&&(E.scrollHeight-E.scrollTop<=E.clientHeight+10||(_e=!0))}const q=y=>y.key==="Enter"&&fe();function Ce(y){vr[y?"unshift":"push"](()=>{E=y,t(17,E)})}return l.$$set=y=>{"thought"in y&&t(21,n=y.thought),"rtl"in y&&t(0,i=y.rtl),"sanitize_html"in y&&t(1,o=y.sanitize_html),"latex_delimiters"in y&&t(2,s=y.latex_delimiters),"render_markdown"in y&&t(3,r=y.render_markdown),"_components"in y&&t(4,a=y._components),"upload"in y&&t(5,_=y.upload),"thought_index"in y&&t(6,f=y.thought_index),"target"in y&&t(7,u=y.target),"theme_mode"in y&&t(8,c=y.theme_mode),"_fetch"in y&&t(9,w=y._fetch),"scroll"in y&&t(10,m=y.scroll),"allow_file_downloads"in y&&t(11,h=y.allow_file_downloads),"display_consecutive_in_same_bubble"in y&&t(12,k=y.display_consecutive_in_same_bubble),"i18n"in y&&t(13,d=y.i18n),"line_breaks"in y&&t(14,v=y.line_breaks),"allow_tags"in y&&t(15,S=y.allow_tags)},l.$$.update=()=>{l.$$.dirty&2097152&&t(16,$={...n,children:Nr(n)?n.children:[]}),l.$$.dirty&4259840&&(C||t(18,F=$?.metadata?.status!=="done")),l.$$.dirty&196608&&$.content&&E&&$.metadata?.status!=="done"&&setTimeout(he,0)},[i,o,s,r,a,_,f,u,c,w,m,h,k,d,v,S,$,E,F,fe,de,n,C,q,Ce]}class co extends kr{constructor(e){super(),Cr(this,e,jr,Mr,zr,{thought:21,rtl:0,sanitize_html:1,latex_delimiters:2,render_markdown:3,_components:4,upload:5,thought_index:6,target:7,theme_mode:8,_fetch:9,scroll:10,allow_file_downloads:11,display_consecutive_in_same_bubble:12,i18n:13,line_breaks:14,allow_tags:15})}get thought(){return this.$$.ctx[21]}set thought(e){this.$$set({thought:e}),Q()}get rtl(){return this.$$.ctx[0]}set rtl(e){this.$$set({rtl:e}),Q()}get sanitize_html(){return this.$$.ctx[1]}set sanitize_html(e){this.$$set({sanitize_html:e}),Q()}get latex_delimiters(){return this.$$.ctx[2]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),Q()}get render_markdown(){return this.$$.ctx[3]}set render_markdown(e){this.$$set({render_markdown:e}),Q()}get _components(){return this.$$.ctx[4]}set _components(e){this.$$set({_components:e}),Q()}get upload(){return this.$$.ctx[5]}set upload(e){this.$$set({upload:e}),Q()}get thought_index(){return this.$$.ctx[6]}set thought_index(e){this.$$set({thought_index:e}),Q()}get target(){return this.$$.ctx[7]}set target(e){this.$$set({target:e}),Q()}get theme_mode(){return this.$$.ctx[8]}set theme_mode(e){this.$$set({theme_mode:e}),Q()}get _fetch(){return this.$$.ctx[9]}set _fetch(e){this.$$set({_fetch:e}),Q()}get scroll(){return this.$$.ctx[10]}set scroll(e){this.$$set({scroll:e}),Q()}get allow_file_downloads(){return this.$$.ctx[11]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),Q()}get display_consecutive_in_same_bubble(){return this.$$.ctx[12]}set display_consecutive_in_same_bubble(e){this.$$set({display_consecutive_in_same_bubble:e}),Q()}get i18n(){return this.$$.ctx[13]}set i18n(e){this.$$set({i18n:e}),Q()}get line_breaks(){return this.$$.ctx[14]}set line_breaks(e){this.$$set({line_breaks:e}),Q()}get allow_tags(){return this.$$.ctx[15]}set allow_tags(e){this.$$set({allow_tags:e}),Q()}}const{SvelteComponent:Vr,append:Rn,assign:mo,attr:le,binding_callbacks:Dr,check_outros:Ft,create_component:Xt,destroy_component:xt,destroy_each:Ir,detach:Ke,element:jt,empty:ho,ensure_array_like:ki,flush:L,get_spread_object:go,get_spread_update:bo,group_outros:Zt,init:Br,insert:Qe,listen:tl,mount_component:en,noop:pi,null_to_empty:vi,run_all:Er,safe_not_equal:Tr,set_input_value:$i,set_style:Ge,space:nl,toggle_class:I,transition_in:W,transition_out:X}=window.__gradio__svelte__internal;function Ci(l,e,t){const n=l.slice();return n[47]=e[t],n[48]=e,n[49]=t,n}function yi(l){let e,t,n;return t=new Qt({props:{class:"avatar-image",src:l[2]?.url,alt:l[4]+" avatar"}}),{c(){e=jt("div"),Xt(t.$$.fragment),le(e,"class","avatar-container svelte-1csv61q")},m(i,o){Qe(i,e,o),en(t,e,null),n=!0},p(i,o){const s={};o[0]&4&&(s.src=i[2]?.url),o[0]&16&&(s.alt=i[4]+" avatar"),t.$set(s)},i(i){n||(W(t.$$.fragment,i),n=!0)},o(i){X(t.$$.fragment,i),n=!1},d(i){i&&Ke(e),xt(t)}}}function Ar(l){let e,t,n,i,o,s=l[49],r,a,_;const f=[Ur,Zr],u=[];function c(d,v){return d[47]?.metadata?.title?0:1}t=c(l),n=u[t]=f[t](l);const w=()=>l[43](e,s),m=()=>l[43](null,s);function h(){return l[44](l[47])}function k(...d){return l[45](l[47],...d)}return{c(){e=jt("div"),n.c(),le(e,"data-testid",l[4]),le(e,"dir",i=l[12]?"rtl":"ltr"),le(e,"aria-label",o=l[4]+"'s message: "+Hi(l[47])),le(e,"class","svelte-1csv61q"),I(e,"latest",l[20]===l[1].length-1),I(e,"message-markdown-disabled",!l[7]),I(e,"selectable",l[10]),Ge(e,"user-select","text"),Ge(e,"cursor",l[10]?"pointer":"auto"),Ge(e,"text-align",l[12]?"right":"left")},m(d,v){Qe(d,e,v),u[t].m(e,null),w(),r=!0,a||(_=[tl(e,"click",h),tl(e,"keydown",k)],a=!0)},p(d,v){l=d;let S=t;t=c(l),t===S?u[t].p(l,v):(Zt(),X(u[S],1,1,()=>{u[S]=null}),Ft(),n=u[t],n?n.p(l,v):(n=u[t]=f[t](l),n.c()),W(n,1),n.m(e,null)),(!r||v[0]&16)&&le(e,"data-testid",l[4]),(!r||v[0]&4096&&i!==(i=l[12]?"rtl":"ltr"))&&le(e,"dir",i),(!r||v[0]&48&&o!==(o=l[4]+"'s message: "+Hi(l[47])))&&le(e,"aria-label",o),s!==l[49]&&(m(),s=l[49],w()),(!r||v[0]&1048578)&&I(e,"latest",l[20]===l[1].length-1),(!r||v[0]&128)&&I(e,"message-markdown-disabled",!l[7]),(!r||v[0]&1024)&&I(e,"selectable",l[10]),v[0]&1024&&Ge(e,"cursor",l[10]?"pointer":"auto"),v[0]&4096&&Ge(e,"text-align",l[12]?"right":"left")},i(d){r||(W(n),r=!0)},o(d){X(n),r=!1},d(d){d&&Ke(e),u[t].d(),m(),a=!1,Er(_)}}}function Fr(l){let e,t,n;function i(){l[42].call(e,l[49])}return{c(){e=jt("textarea"),le(e,"class","edit-textarea svelte-1csv61q"),e.autofocus=!0,Ge(e,"width",`max(${l[29][l[49]]}px, 160px)`),Ge(e,"min-height",`${l[30][l[49]]}px`)},m(o,s){Qe(o,e,s),$i(e,l[0][l[49]]),e.focus(),t||(n=tl(e,"input",i),t=!0)},p(o,s){l=o,s[0]&1&&$i(e,l[0][l[49]]),s[0]&536870912&&Ge(e,"width",`max(${l[29][l[49]]}px, 160px)`),s[0]&1073741824&&Ge(e,"min-height",`${l[30][l[49]]}px`)},i:pi,o:pi,d(o){o&&Ke(e),t=!1,n()}}}function Zr(l){let e,t;return e=new uo({props:{message:l[47],sanitize_html:l[9],allow_tags:l[26],latex_delimiters:l[8],render_markdown:l[7],_components:l[19],upload:l[16],thought_index:l[49],target:l[17],theme_mode:l[18],_fetch:l[11],scroll:l[21],allow_file_downloads:l[22],display_consecutive_in_same_bubble:l[24],i18n:l[14],line_breaks:l[15]}}),{c(){Xt(e.$$.fragment)},m(n,i){en(e,n,i),t=!0},p(n,i){const o={};i[0]&32&&(o.message=n[47]),i[0]&512&&(o.sanitize_html=n[9]),i[0]&67108864&&(o.allow_tags=n[26]),i[0]&256&&(o.latex_delimiters=n[8]),i[0]&128&&(o.render_markdown=n[7]),i[0]&524288&&(o._components=n[19]),i[0]&65536&&(o.upload=n[16]),i[0]&131072&&(o.target=n[17]),i[0]&262144&&(o.theme_mode=n[18]),i[0]&2048&&(o._fetch=n[11]),i[0]&2097152&&(o.scroll=n[21]),i[0]&4194304&&(o.allow_file_downloads=n[22]),i[0]&16777216&&(o.display_consecutive_in_same_bubble=n[24]),i[0]&16384&&(o.i18n=n[14]),i[0]&32768&&(o.line_breaks=n[15]),e.$set(o)},i(n){t||(W(e.$$.fragment,n),t=!0)},o(n){X(e.$$.fragment,n),t=!1},d(n){xt(e,n)}}}function Ur(l){let e,t;return e=new co({props:{thought:l[47],rtl:l[12],sanitize_html:l[9],allow_tags:l[26],latex_delimiters:l[8],render_markdown:l[7],_components:l[19],upload:l[16],thought_index:l[49],target:l[17],theme_mode:l[18],_fetch:l[11],scroll:l[21],allow_file_downloads:l[22],display_consecutive_in_same_bubble:l[24],i18n:l[14],line_breaks:l[15]}}),{c(){Xt(e.$$.fragment)},m(n,i){en(e,n,i),t=!0},p(n,i){const o={};i[0]&32&&(o.thought=n[47]),i[0]&4096&&(o.rtl=n[12]),i[0]&512&&(o.sanitize_html=n[9]),i[0]&67108864&&(o.allow_tags=n[26]),i[0]&256&&(o.latex_delimiters=n[8]),i[0]&128&&(o.render_markdown=n[7]),i[0]&524288&&(o._components=n[19]),i[0]&65536&&(o.upload=n[16]),i[0]&131072&&(o.target=n[17]),i[0]&262144&&(o.theme_mode=n[18]),i[0]&2048&&(o._fetch=n[11]),i[0]&2097152&&(o.scroll=n[21]),i[0]&4194304&&(o.allow_file_downloads=n[22]),i[0]&16777216&&(o.display_consecutive_in_same_bubble=n[24]),i[0]&16384&&(o.i18n=n[14]),i[0]&32768&&(o.line_breaks=n[15]),e.$set(o)},i(n){t||(W(e.$$.fragment,n),t=!0)},o(n){X(e.$$.fragment,n),t=!1},d(n){xt(e,n)}}}function zi(l){let e,t;const n=[l[31],{current_feedback:l[25]},{watermark:l[27]},{i18n:l[14]}];let i={};for(let o=0;o<n.length;o+=1)i=mo(i,n[o]);return e=new _o({props:i}),e.$on("copy",l[46]),{c(){Xt(e.$$.fragment)},m(o,s){en(e,o,s),t=!0},p(o,s){const r=s[0]&167788544|s[1]&1?bo(n,[s[1]&1&&go(o[31]),s[0]&33554432&&{current_feedback:o[25]},s[0]&134217728&&{watermark:o[27]},s[0]&16384&&{i18n:o[14]}]):{};e.$set(r)},i(o){t||(W(e.$$.fragment,o),t=!0)},o(o){X(e.$$.fragment,o),t=!1},d(o){xt(e,o)}}}function qi(l){let e,t,n,i,o,s,r;const a=[Fr,Ar],_=[];function f(c,w){return c[23]&&c[47].type==="text"?0:1}t=f(l),n=_[t]=a[t](l);let u=l[6]==="panel"&&zi(l);return{c(){e=jt("div"),n.c(),o=nl(),u&&u.c(),s=ho(),le(e,"class",i="message "+(l[24]?"":l[4])+" svelte-1csv61q"),I(e,"panel-full-width",!0),I(e,"message-markdown-disabled",!l[7]),I(e,"component",l[47].type==="component"),I(e,"html",zl(l[47])&&l[47].content.component==="html"),I(e,"thought",l[49]>0)},m(c,w){Qe(c,e,w),_[t].m(e,null),Qe(c,o,w),u&&u.m(c,w),Qe(c,s,w),r=!0},p(c,w){let m=t;t=f(c),t===m?_[t].p(c,w):(Zt(),X(_[m],1,1,()=>{_[m]=null}),Ft(),n=_[t],n?n.p(c,w):(n=_[t]=a[t](c),n.c()),W(n,1),n.m(e,null)),(!r||w[0]&16777232&&i!==(i="message "+(c[24]?"":c[4])+" svelte-1csv61q"))&&le(e,"class",i),(!r||w[0]&16777232)&&I(e,"panel-full-width",!0),(!r||w[0]&16777360)&&I(e,"message-markdown-disabled",!c[7]),(!r||w[0]&16777264)&&I(e,"component",c[47].type==="component"),(!r||w[0]&16777264)&&I(e,"html",zl(c[47])&&c[47].content.component==="html"),(!r||w[0]&16777232)&&I(e,"thought",c[49]>0),c[6]==="panel"?u?(u.p(c,w),w[0]&64&&W(u,1)):(u=zi(c),u.c(),W(u,1),u.m(s.parentNode,s)):u&&(Zt(),X(u,1,1,()=>{u=null}),Ft())},i(c){r||(W(n),W(u),r=!0)},o(c){X(n),X(u),r=!1},d(c){c&&(Ke(e),Ke(o),Ke(s)),_[t].d(),u&&u.d(c)}}}function Si(l){let e,t;const n=[l[31],{i18n:l[14]}];let i={};for(let o=0;o<n.length;o+=1)i=mo(i,n[o]);return e=new _o({props:i}),{c(){Xt(e.$$.fragment)},m(o,s){en(e,o,s),t=!0},p(o,s){const r=s[0]&16384|s[1]&1?bo(n,[s[1]&1&&go(o[31]),s[0]&16384&&{i18n:o[14]}]):{};e.$set(r)},i(o){t||(W(e.$$.fragment,o),t=!0)},o(o){X(e.$$.fragment,o),t=!1},d(o){xt(e,o)}}}function Wr(l){let e,t,n,i,o,s,r,a,_,f=l[2]!==null&&yi(l),u=ki(l[5]),c=[];for(let h=0;h<u.length;h+=1)c[h]=qi(Ci(l,u,h));const w=h=>X(c[h],1,1,()=>{c[h]=null});let m=l[6]==="bubble"&&Si(l);return{c(){e=jt("div"),f&&f.c(),t=nl(),n=jt("div"),i=jt("div");for(let h=0;h<c.length;h+=1)c[h].c();r=nl(),m&&m.c(),a=ho(),le(i,"class",o=vi(l[24]?l[4]:"")+" svelte-1csv61q"),I(i,"message",l[24]),le(n,"class","flex-wrap svelte-1csv61q"),I(n,"role",l[4]),I(n,"component-wrap",l[5][0].type==="component"),le(e,"class",s="message-row "+l[6]+" "+l[4]+"-row svelte-1csv61q"),I(e,"with_avatar",l[2]!==null),I(e,"with_opposite_avatar",l[3]!==null)},m(h,k){Qe(h,e,k),f&&f.m(e,null),Rn(e,t),Rn(e,n),Rn(n,i);for(let d=0;d<c.length;d+=1)c[d]&&c[d].m(i,null);Qe(h,r,k),m&&m.m(h,k),Qe(h,a,k),_=!0},p(h,k){if(h[2]!==null?f?(f.p(h,k),k[0]&4&&W(f,1)):(f=yi(h),f.c(),W(f,1),f.m(e,t)):f&&(Zt(),X(f,1,1,()=>{f=null}),Ft()),k[0]&2147483635|k[1]&3){u=ki(h[5]);let d;for(d=0;d<u.length;d+=1){const v=Ci(h,u,d);c[d]?(c[d].p(v,k),W(c[d],1)):(c[d]=qi(v),c[d].c(),W(c[d],1),c[d].m(i,null))}for(Zt(),d=u.length;d<c.length;d+=1)w(d);Ft()}(!_||k[0]&16777232&&o!==(o=vi(h[24]?h[4]:"")+" svelte-1csv61q"))&&le(i,"class",o),(!_||k[0]&16777232)&&I(i,"message",h[24]),(!_||k[0]&16)&&I(n,"role",h[4]),(!_||k[0]&32)&&I(n,"component-wrap",h[5][0].type==="component"),(!_||k[0]&80&&s!==(s="message-row "+h[6]+" "+h[4]+"-row svelte-1csv61q"))&&le(e,"class",s),(!_||k[0]&84)&&I(e,"with_avatar",h[2]!==null),(!_||k[0]&88)&&I(e,"with_opposite_avatar",h[3]!==null),h[6]==="bubble"?m?(m.p(h,k),k[0]&64&&W(m,1)):(m=Si(h),m.c(),W(m,1),m.m(a.parentNode,a)):m&&(Zt(),X(m,1,1,()=>{m=null}),Ft())},i(h){if(!_){W(f);for(let k=0;k<u.length;k+=1)W(c[k]);W(m),_=!0}},o(h){X(f),c=c.filter(Boolean);for(let k=0;k<c.length;k+=1)X(c[k]);X(m),_=!1},d(h){h&&(Ke(e),Ke(r),Ke(a)),f&&f.d(),Ir(c,h),m&&m.d(h)}}}let Pr=!1;function Hi(l){return l.type==="text"?l.content:l.type==="component"&&l.content.component==="file"?Array.isArray(l.content.value)?`file of extension type: ${l.content.value[0].orig_name?.split(".").pop()}`:`file of extension type: ${l.content.value?.orig_name?.split(".").pop()}`+(l.content.value?.orig_name??""):`a component of type ${l.content.component??"unknown"}`}function Or(l,e,t){let{value:n}=e,{avatar_img:i}=e,{opposite_avatar_img:o=null}=e,{role:s="user"}=e,{messages:r=[]}=e,{layout:a}=e,{render_markdown:_}=e,{latex_delimiters:f}=e,{sanitize_html:u}=e,{selectable:c}=e,{_fetch:w}=e,{rtl:m}=e,{dispatch:h}=e,{i18n:k}=e,{line_breaks:d}=e,{upload:v}=e,{target:S}=e,{theme_mode:$}=e,{_components:F}=e,{i:C}=e,{show_copy_button:E}=e,{generating:_e}=e,{feedback_options:fe}=e,{show_like:he}=e,{show_edit:de}=e,{show_retry:q}=e,{show_undo:Ce}=e,{msg_format:y}=e,{handle_action:T}=e,{scroll:it}=e,{allow_file_downloads:ot}=e,{in_edit_mode:Me}=e,{edit_messages:ye}=e,{display_consecutive_in_same_bubble:Ne}=e,{current_feedback:Te=null}=e,{allow_tags:st=!1}=e,{watermark:Ae=null}=e,ge=[],Fe=Array(r.length).fill(160),Ze=Array(r.length).fill(0);function Ue(p,P){h("select",{index:P.index,value:P.content})}let at;function We(p){ye[p]=this.value,t(0,ye)}function ze(p,P){Dr[p?"unshift":"push"](()=>{ge[P]=p,t(28,ge)})}const x=p=>Ue(C,p),rt=(p,P)=>{P.key==="Enter"&&Ue(C,p)},ee=p=>h("copy",p.detail);return l.$$set=p=>{"value"in p&&t(1,n=p.value),"avatar_img"in p&&t(2,i=p.avatar_img),"opposite_avatar_img"in p&&t(3,o=p.opposite_avatar_img),"role"in p&&t(4,s=p.role),"messages"in p&&t(5,r=p.messages),"layout"in p&&t(6,a=p.layout),"render_markdown"in p&&t(7,_=p.render_markdown),"latex_delimiters"in p&&t(8,f=p.latex_delimiters),"sanitize_html"in p&&t(9,u=p.sanitize_html),"selectable"in p&&t(10,c=p.selectable),"_fetch"in p&&t(11,w=p._fetch),"rtl"in p&&t(12,m=p.rtl),"dispatch"in p&&t(13,h=p.dispatch),"i18n"in p&&t(14,k=p.i18n),"line_breaks"in p&&t(15,d=p.line_breaks),"upload"in p&&t(16,v=p.upload),"target"in p&&t(17,S=p.target),"theme_mode"in p&&t(18,$=p.theme_mode),"_components"in p&&t(19,F=p._components),"i"in p&&t(20,C=p.i),"show_copy_button"in p&&t(33,E=p.show_copy_button),"generating"in p&&t(34,_e=p.generating),"feedback_options"in p&&t(35,fe=p.feedback_options),"show_like"in p&&t(36,he=p.show_like),"show_edit"in p&&t(37,de=p.show_edit),"show_retry"in p&&t(38,q=p.show_retry),"show_undo"in p&&t(39,Ce=p.show_undo),"msg_format"in p&&t(40,y=p.msg_format),"handle_action"in p&&t(41,T=p.handle_action),"scroll"in p&&t(21,it=p.scroll),"allow_file_downloads"in p&&t(22,ot=p.allow_file_downloads),"in_edit_mode"in p&&t(23,Me=p.in_edit_mode),"edit_messages"in p&&t(0,ye=p.edit_messages),"display_consecutive_in_same_bubble"in p&&t(24,Ne=p.display_consecutive_in_same_bubble),"current_feedback"in p&&t(25,Te=p.current_feedback),"allow_tags"in p&&t(26,st=p.allow_tags),"watermark"in p&&t(27,Ae=p.watermark)},l.$$.update=()=>{if(l.$$.dirty[0]&276824096&&Me&&!Pr){const p=ge.length-r.length;for(let P=p;P<ge.length;P++)P>=0&&(t(29,Fe[P-p]=ge[P]?.clientWidth,Fe),t(30,Ze[P-p]=ge[P]?.clientHeight,Ze))}l.$$.dirty[0]&176169076|l.$$.dirty[1]&2044&&t(31,at={handle_action:T,likeable:he,feedback_options:fe,show_retry:q,show_undo:Ce,show_edit:de,in_edit_mode:Me,generating:_e,show_copy_button:E,message:y==="tuples"?r[0]:r,position:s==="user"?"right":"left",avatar:i,layout:a,dispatch:h,current_feedback:Te,watermark:Ae})},[ye,n,i,o,s,r,a,_,f,u,c,w,m,h,k,d,v,S,$,F,C,it,ot,Me,Ne,Te,st,Ae,ge,Fe,Ze,at,Ue,E,_e,fe,he,de,q,Ce,y,T,We,ze,x,rt,ee]}class Rr extends Vr{constructor(e){super(),Br(this,e,Or,Wr,Tr,{value:1,avatar_img:2,opposite_avatar_img:3,role:4,messages:5,layout:6,render_markdown:7,latex_delimiters:8,sanitize_html:9,selectable:10,_fetch:11,rtl:12,dispatch:13,i18n:14,line_breaks:15,upload:16,target:17,theme_mode:18,_components:19,i:20,show_copy_button:33,generating:34,feedback_options:35,show_like:36,show_edit:37,show_retry:38,show_undo:39,msg_format:40,handle_action:41,scroll:21,allow_file_downloads:22,in_edit_mode:23,edit_messages:0,display_consecutive_in_same_bubble:24,current_feedback:25,allow_tags:26,watermark:27},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),L()}get avatar_img(){return this.$$.ctx[2]}set avatar_img(e){this.$$set({avatar_img:e}),L()}get opposite_avatar_img(){return this.$$.ctx[3]}set opposite_avatar_img(e){this.$$set({opposite_avatar_img:e}),L()}get role(){return this.$$.ctx[4]}set role(e){this.$$set({role:e}),L()}get messages(){return this.$$.ctx[5]}set messages(e){this.$$set({messages:e}),L()}get layout(){return this.$$.ctx[6]}set layout(e){this.$$set({layout:e}),L()}get render_markdown(){return this.$$.ctx[7]}set render_markdown(e){this.$$set({render_markdown:e}),L()}get latex_delimiters(){return this.$$.ctx[8]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),L()}get sanitize_html(){return this.$$.ctx[9]}set sanitize_html(e){this.$$set({sanitize_html:e}),L()}get selectable(){return this.$$.ctx[10]}set selectable(e){this.$$set({selectable:e}),L()}get _fetch(){return this.$$.ctx[11]}set _fetch(e){this.$$set({_fetch:e}),L()}get rtl(){return this.$$.ctx[12]}set rtl(e){this.$$set({rtl:e}),L()}get dispatch(){return this.$$.ctx[13]}set dispatch(e){this.$$set({dispatch:e}),L()}get i18n(){return this.$$.ctx[14]}set i18n(e){this.$$set({i18n:e}),L()}get line_breaks(){return this.$$.ctx[15]}set line_breaks(e){this.$$set({line_breaks:e}),L()}get upload(){return this.$$.ctx[16]}set upload(e){this.$$set({upload:e}),L()}get target(){return this.$$.ctx[17]}set target(e){this.$$set({target:e}),L()}get theme_mode(){return this.$$.ctx[18]}set theme_mode(e){this.$$set({theme_mode:e}),L()}get _components(){return this.$$.ctx[19]}set _components(e){this.$$set({_components:e}),L()}get i(){return this.$$.ctx[20]}set i(e){this.$$set({i:e}),L()}get show_copy_button(){return this.$$.ctx[33]}set show_copy_button(e){this.$$set({show_copy_button:e}),L()}get generating(){return this.$$.ctx[34]}set generating(e){this.$$set({generating:e}),L()}get feedback_options(){return this.$$.ctx[35]}set feedback_options(e){this.$$set({feedback_options:e}),L()}get show_like(){return this.$$.ctx[36]}set show_like(e){this.$$set({show_like:e}),L()}get show_edit(){return this.$$.ctx[37]}set show_edit(e){this.$$set({show_edit:e}),L()}get show_retry(){return this.$$.ctx[38]}set show_retry(e){this.$$set({show_retry:e}),L()}get show_undo(){return this.$$.ctx[39]}set show_undo(e){this.$$set({show_undo:e}),L()}get msg_format(){return this.$$.ctx[40]}set msg_format(e){this.$$set({msg_format:e}),L()}get handle_action(){return this.$$.ctx[41]}set handle_action(e){this.$$set({handle_action:e}),L()}get scroll(){return this.$$.ctx[21]}set scroll(e){this.$$set({scroll:e}),L()}get allow_file_downloads(){return this.$$.ctx[22]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),L()}get in_edit_mode(){return this.$$.ctx[23]}set in_edit_mode(e){this.$$set({in_edit_mode:e}),L()}get edit_messages(){return this.$$.ctx[0]}set edit_messages(e){this.$$set({edit_messages:e}),L()}get display_consecutive_in_same_bubble(){return this.$$.ctx[24]}set display_consecutive_in_same_bubble(e){this.$$set({display_consecutive_in_same_bubble:e}),L()}get current_feedback(){return this.$$.ctx[25]}set current_feedback(e){this.$$set({current_feedback:e}),L()}get allow_tags(){return this.$$.ctx[26]}set allow_tags(e){this.$$set({allow_tags:e}),L()}get watermark(){return this.$$.ctx[27]}set watermark(e){this.$$set({watermark:e}),L()}}const{SvelteComponent:Jr,append:Jn,attr:ft,check_outros:Gr,create_component:Yr,destroy_component:Kr,detach:wo,element:rn,flush:Li,group_outros:Qr,init:Xr,insert:ko,mount_component:xr,safe_not_equal:e_,space:t_,toggle_class:an,transition_in:_n,transition_out:ll}=window.__gradio__svelte__internal;function Mi(l){let e,t,n;return t=new Qt({props:{class:"avatar-image",src:l[1][1].url,alt:"bot avatar"}}),{c(){e=rn("div"),Yr(t.$$.fragment),ft(e,"class","avatar-container svelte-134ihlx")},m(i,o){ko(i,e,o),xr(t,e,null),n=!0},p(i,o){const s={};o&2&&(s.src=i[1][1].url),t.$set(s)},i(i){n||(_n(t.$$.fragment,i),n=!0)},o(i){ll(t.$$.fragment,i),n=!1},d(i){i&&wo(e),Kr(t)}}}function n_(l){let e,t,n,i,o,s,r=l[1][1]!==null&&Mi(l);return{c(){e=rn("div"),r&&r.c(),t=t_(),n=rn("div"),i=rn("div"),i.innerHTML='<span class="sr-only">Loading content</span> <div class="dots svelte-134ihlx"><div class="dot svelte-134ihlx"></div> <div class="dot svelte-134ihlx"></div> <div class="dot svelte-134ihlx"></div></div>',ft(i,"class","message-content svelte-134ihlx"),ft(n,"class",o="message bot pending "+l[0]+" svelte-134ihlx"),ft(n,"role","status"),ft(n,"aria-label","Loading response"),ft(n,"aria-live","polite"),an(n,"with_avatar",l[1][1]!==null),an(n,"with_opposite_avatar",l[1][0]!==null),ft(e,"class","container svelte-134ihlx")},m(a,_){ko(a,e,_),r&&r.m(e,null),Jn(e,t),Jn(e,n),Jn(n,i),s=!0},p(a,[_]){a[1][1]!==null?r?(r.p(a,_),_&2&&_n(r,1)):(r=Mi(a),r.c(),_n(r,1),r.m(e,t)):r&&(Qr(),ll(r,1,1,()=>{r=null}),Gr()),(!s||_&1&&o!==(o="message bot pending "+a[0]+" svelte-134ihlx"))&&ft(n,"class",o),(!s||_&3)&&an(n,"with_avatar",a[1][1]!==null),(!s||_&3)&&an(n,"with_opposite_avatar",a[1][0]!==null)},i(a){s||(_n(r),s=!0)},o(a){ll(r),s=!1},d(a){a&&wo(e),r&&r.d()}}}function l_(l,e,t){let{layout:n="bubble"}=e,{avatar_images:i=[null,null]}=e;return l.$$set=o=>{"layout"in o&&t(0,n=o.layout),"avatar_images"in o&&t(1,i=o.avatar_images)},[n,i]}class po extends Jr{constructor(e){super(),Xr(this,e,l_,n_,e_,{layout:0,avatar_images:1})}get layout(){return this.$$.ctx[0]}set layout(e){this.$$set({layout:e}),Li()}get avatar_images(){return this.$$.ctx[1]}set avatar_images(e){this.$$set({avatar_images:e}),Li()}}const{SvelteComponent:i_,append:O,attr:z,check_outros:wt,create_component:vt,destroy_component:$t,destroy_each:vo,detach:R,element:Z,empty:$o,ensure_array_like:gn,flush:Gn,group_outros:kt,init:o_,insert:J,listen:s_,mount_component:Ct,noop:Vt,safe_not_equal:a_,set_data:vn,space:Pt,src_url_equal:bn,text:Dt,transition_in:V,transition_out:A}=window.__gradio__svelte__internal,{createEventDispatcher:r_}=window.__gradio__svelte__internal;function Ni(l,e,t){const n=l.slice();return n[6]=e[t],n[8]=t,n}function ji(l,e,t){const n=l.slice();return n[9]=e[t],n[8]=t,n}function Vi(l){let e,t,n;return t=new sl({props:{message:l[1],latex_delimiters:l[2]}}),{c(){e=Z("div"),vt(t.$$.fragment),z(e,"class","placeholder svelte-9pi8y1")},m(i,o){J(i,e,o),Ct(t,e,null),n=!0},p(i,o){const s={};o&2&&(s.message=i[1]),o&4&&(s.latex_delimiters=i[2]),t.$set(s)},i(i){n||(V(t.$$.fragment,i),n=!0)},o(i){A(t.$$.fragment,i),n=!1},d(i){i&&R(e),$t(t)}}}function Di(l){let e,t,n=gn(l[0]),i=[];for(let s=0;s<n.length;s+=1)i[s]=Ai(Ni(l,n,s));const o=s=>A(i[s],1,1,()=>{i[s]=null});return{c(){e=Z("div");for(let s=0;s<i.length;s+=1)i[s].c();z(e,"class","examples svelte-9pi8y1"),z(e,"role","list")},m(s,r){J(s,e,r);for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(e,null);t=!0},p(s,r){if(r&9){n=gn(s[0]);let a;for(a=0;a<n.length;a+=1){const _=Ni(s,n,a);i[a]?(i[a].p(_,r),V(i[a],1)):(i[a]=Ai(_),i[a].c(),V(i[a],1),i[a].m(e,null))}for(kt(),a=n.length;a<i.length;a+=1)o(a);wt()}},i(s){if(!t){for(let r=0;r<n.length;r+=1)V(i[r]);t=!0}},o(s){i=i.filter(Boolean);for(let r=0;r<i.length;r+=1)A(i[r]);t=!1},d(s){s&&R(e),vo(i,s)}}}function __(l){let e,t,n,i,o,s,r;const a=[g_,d_,h_,m_,c_],_=[];function f(u,c){return c&1&&(e=null),c&1&&(t=null),c&1&&(n=null),u[6].files.length>1?0:(e==null&&(e=!!u[6].files[0].mime_type?.includes("image")),e?1:(t==null&&(t=!!u[6].files[0].mime_type?.includes("video")),t?2:(n==null&&(n=!!u[6].files[0].mime_type?.includes("audio")),n?3:4)))}return i=f(l,-1),o=_[i]=a[i](l),{c(){o.c(),s=$o()},m(u,c){_[i].m(u,c),J(u,s,c),r=!0},p(u,c){let w=i;i=f(u,c),i===w?_[i].p(u,c):(kt(),A(_[w],1,1,()=>{_[w]=null}),wt(),o=_[i],o?o.p(u,c):(o=_[i]=a[i](u),o.c()),V(o,1),o.m(s.parentNode,s))},i(u){r||(V(o),r=!0)},o(u){A(o),r=!1},d(u){u&&R(s),_[i].d(u)}}}function f_(l){let e;return{c(){e=Z("div"),e.innerHTML='<span class="text-icon-aa svelte-9pi8y1">Aa</span>',z(e,"class","example-icon svelte-9pi8y1"),z(e,"aria-hidden","true")},m(t,n){J(t,e,n)},p:Vt,i:Vt,o:Vt,d(t){t&&R(e)}}}function u_(l){let e,t,n;return t=new Qt({props:{class:"example-image",src:l[6].icon.url,alt:"Example icon"}}),{c(){e=Z("div"),vt(t.$$.fragment),z(e,"class","example-image-container svelte-9pi8y1")},m(i,o){J(i,e,o),Ct(t,e,null),n=!0},p(i,o){const s={};o&1&&(s.src=i[6].icon.url),t.$set(s)},i(i){n||(V(t.$$.fragment,i),n=!0)},o(i){A(t.$$.fragment,i),n=!1},d(i){i&&R(e),$t(t)}}}function c_(l){let e,t,n,i;return t=new ol({}),{c(){e=Z("div"),vt(t.$$.fragment),z(e,"class","example-icon svelte-9pi8y1"),z(e,"aria-label",n=`File: ${l[6].files[0].orig_name}`)},m(o,s){J(o,e,s),Ct(t,e,null),i=!0},p(o,s){(!i||s&1&&n!==(n=`File: ${o[6].files[0].orig_name}`))&&z(e,"aria-label",n)},i(o){i||(V(t.$$.fragment,o),i=!0)},o(o){A(t.$$.fragment,o),i=!1},d(o){o&&R(e),$t(t)}}}function m_(l){let e,t,n,i;return t=new oo({}),{c(){e=Z("div"),vt(t.$$.fragment),z(e,"class","example-icon svelte-9pi8y1"),z(e,"aria-label",n=`File: ${l[6].files[0].orig_name}`)},m(o,s){J(o,e,s),Ct(t,e,null),i=!0},p(o,s){(!i||s&1&&n!==(n=`File: ${o[6].files[0].orig_name}`))&&z(e,"aria-label",n)},i(o){i||(V(t.$$.fragment,o),i=!0)},o(o){A(t.$$.fragment,o),i=!1},d(o){o&&R(e),$t(t)}}}function h_(l){let e,t,n;return{c(){e=Z("div"),t=Z("video"),z(t,"class","example-image"),bn(t.src,n=l[6].files[0].url)||z(t,"src",n),z(t,"aria-hidden","true"),z(e,"class","example-image-container svelte-9pi8y1")},m(i,o){J(i,e,o),O(e,t)},p(i,o){o&1&&!bn(t.src,n=i[6].files[0].url)&&z(t,"src",n)},i:Vt,o:Vt,d(i){i&&R(e)}}}function d_(l){let e,t,n;return t=new Qt({props:{class:"example-image",src:l[6].files[0].url,alt:l[6].files[0].orig_name||"Example image"}}),{c(){e=Z("div"),vt(t.$$.fragment),z(e,"class","example-image-container svelte-9pi8y1")},m(i,o){J(i,e,o),Ct(t,e,null),n=!0},p(i,o){const s={};o&1&&(s.src=i[6].files[0].url),o&1&&(s.alt=i[6].files[0].orig_name||"Example image"),t.$set(s)},i(i){n||(V(t.$$.fragment,i),n=!0)},o(i){A(t.$$.fragment,i),n=!1},d(i){i&&R(e),$t(t)}}}function g_(l){let e,t,n,i=gn(l[6].files.slice(0,4)),o=[];for(let a=0;a<i.length;a+=1)o[a]=Ei(ji(l,i,a));const s=a=>A(o[a],1,1,()=>{o[a]=null});let r=l[6].files.length>4&&Ti(l);return{c(){e=Z("div");for(let a=0;a<o.length;a+=1)o[a].c();t=Pt(),r&&r.c(),z(e,"class","example-icons-grid svelte-9pi8y1"),z(e,"role","group"),z(e,"aria-label","Example attachments")},m(a,_){J(a,e,_);for(let f=0;f<o.length;f+=1)o[f]&&o[f].m(e,null);O(e,t),r&&r.m(e,null),n=!0},p(a,_){if(_&1){i=gn(a[6].files.slice(0,4));let f;for(f=0;f<i.length;f+=1){const u=ji(a,i,f);o[f]?(o[f].p(u,_),V(o[f],1)):(o[f]=Ei(u),o[f].c(),V(o[f],1),o[f].m(e,t))}for(kt(),f=i.length;f<o.length;f+=1)s(f);wt()}a[6].files.length>4?r?r.p(a,_):(r=Ti(a),r.c(),r.m(e,null)):r&&(r.d(1),r=null)},i(a){if(!n){for(let _=0;_<i.length;_+=1)V(o[_]);n=!0}},o(a){o=o.filter(Boolean);for(let _=0;_<o.length;_+=1)A(o[_]);n=!1},d(a){a&&R(e),vo(o,a),r&&r.d()}}}function b_(l){let e,t,n,i,o,s;const r=[v_,p_],a=[];function _(f,u){return u&1&&(t=null),t==null&&(t=!!f[9].mime_type?.includes("audio")),t?0:1}return n=_(l,-1),i=a[n]=r[n](l),{c(){e=Z("div"),i.c(),z(e,"class","example-icon svelte-9pi8y1"),z(e,"aria-label",o=`File: ${l[9].orig_name}`)},m(f,u){J(f,e,u),a[n].m(e,null),s=!0},p(f,u){let c=n;n=_(f,u),n!==c&&(kt(),A(a[c],1,1,()=>{a[c]=null}),wt(),i=a[n],i||(i=a[n]=r[n](f),i.c()),V(i,1),i.m(e,null)),(!s||u&1&&o!==(o=`File: ${f[9].orig_name}`))&&z(e,"aria-label",o)},i(f){s||(V(i),s=!0)},o(f){A(i),s=!1},d(f){f&&R(e),a[n].d()}}}function w_(l){let e,t,n,i,o=l[8]===3&&l[6].files.length>4&&Ii(l);return{c(){e=Z("div"),t=Z("video"),i=Pt(),o&&o.c(),z(t,"class","example-image"),bn(t.src,n=l[9].url)||z(t,"src",n),z(t,"aria-hidden","true"),z(e,"class","example-image-container svelte-9pi8y1")},m(s,r){J(s,e,r),O(e,t),O(e,i),o&&o.m(e,null)},p(s,r){r&1&&!bn(t.src,n=s[9].url)&&z(t,"src",n),s[8]===3&&s[6].files.length>4?o?o.p(s,r):(o=Ii(s),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i:Vt,o:Vt,d(s){s&&R(e),o&&o.d()}}}function k_(l){let e,t,n,i;t=new Qt({props:{class:"example-image",src:l[9].url,alt:l[9].orig_name||`Example image ${l[8]+1}`}});let o=l[8]===3&&l[6].files.length>4&&Bi(l);return{c(){e=Z("div"),vt(t.$$.fragment),n=Pt(),o&&o.c(),z(e,"class","example-image-container svelte-9pi8y1")},m(s,r){J(s,e,r),Ct(t,e,null),O(e,n),o&&o.m(e,null),i=!0},p(s,r){const a={};r&1&&(a.src=s[9].url),r&1&&(a.alt=s[9].orig_name||`Example image ${s[8]+1}`),t.$set(a),s[8]===3&&s[6].files.length>4?o?o.p(s,r):(o=Bi(s),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i(s){i||(V(t.$$.fragment,s),i=!0)},o(s){A(t.$$.fragment,s),i=!1},d(s){s&&R(e),$t(t),o&&o.d()}}}function p_(l){let e,t;return e=new ol({}),{c(){vt(e.$$.fragment)},m(n,i){Ct(e,n,i),t=!0},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){$t(e,n)}}}function v_(l){let e,t;return e=new oo({}),{c(){vt(e.$$.fragment)},m(n,i){Ct(e,n,i),t=!0},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){$t(e,n)}}}function Ii(l){let e,t,n=l[6].files.length-4+"",i,o;return{c(){e=Z("div"),t=Dt("+"),i=Dt(n),z(e,"class","image-overlay svelte-9pi8y1"),z(e,"role","status"),z(e,"aria-label",o=`${l[6].files.length-4} more files`)},m(s,r){J(s,e,r),O(e,t),O(e,i)},p(s,r){r&1&&n!==(n=s[6].files.length-4+"")&&vn(i,n),r&1&&o!==(o=`${s[6].files.length-4} more files`)&&z(e,"aria-label",o)},d(s){s&&R(e)}}}function Bi(l){let e,t,n=l[6].files.length-4+"",i,o;return{c(){e=Z("div"),t=Dt("+"),i=Dt(n),z(e,"class","image-overlay svelte-9pi8y1"),z(e,"role","status"),z(e,"aria-label",o=`${l[6].files.length-4} more files`)},m(s,r){J(s,e,r),O(e,t),O(e,i)},p(s,r){r&1&&n!==(n=s[6].files.length-4+"")&&vn(i,n),r&1&&o!==(o=`${s[6].files.length-4} more files`)&&z(e,"aria-label",o)},d(s){s&&R(e)}}}function Ei(l){let e,t,n,i,o,s;const r=[k_,w_,b_],a=[];function _(f,u){return u&1&&(e=null),u&1&&(t=null),e==null&&(e=!!f[9].mime_type?.includes("image")),e?0:(t==null&&(t=!!f[9].mime_type?.includes("video")),t?1:2)}return n=_(l,-1),i=a[n]=r[n](l),{c(){i.c(),o=$o()},m(f,u){a[n].m(f,u),J(f,o,u),s=!0},p(f,u){let c=n;n=_(f,u),n===c?a[n].p(f,u):(kt(),A(a[c],1,1,()=>{a[c]=null}),wt(),i=a[n],i?i.p(f,u):(i=a[n]=r[n](f),i.c()),V(i,1),i.m(o.parentNode,o))},i(f){s||(V(i),s=!0)},o(f){A(i),s=!1},d(f){f&&R(o),a[n].d(f)}}}function Ti(l){let e,t,n,i=l[6].files.length-4+"",o,s;return{c(){e=Z("div"),t=Z("div"),n=Dt("+"),o=Dt(i),z(t,"class","file-overlay svelte-9pi8y1"),z(t,"role","status"),z(t,"aria-label",s=`${l[6].files.length-4} more files`),z(e,"class","example-icon svelte-9pi8y1")},m(r,a){J(r,e,a),O(e,t),O(t,n),O(t,o)},p(r,a){a&1&&i!==(i=r[6].files.length-4+"")&&vn(o,i),a&1&&s!==(s=`${r[6].files.length-4} more files`)&&z(t,"aria-label",s)},d(r){r&&R(e)}}}function Ai(l){let e,t,n,i,o,s,r,a=(l[6].display_text||l[6].text)+"",_,f,u,c,w,m;const h=[u_,f_,__],k=[];function d(S,$){return S[6]?.icon?.url?0:S[6]?.icon?.mime_type==="text"?1:S[6].files!==void 0&&S[6].files.length>0?2:-1}~(n=d(l))&&(i=k[n]=h[n](l));function v(){return l[4](l[8],l[6])}return{c(){e=Z("button"),t=Z("div"),i&&i.c(),o=Pt(),s=Z("div"),r=Z("span"),_=Dt(a),f=Pt(),z(r,"class","example-text svelte-9pi8y1"),z(s,"class","example-text-content svelte-9pi8y1"),z(t,"class","example-content svelte-9pi8y1"),z(e,"class","example svelte-9pi8y1"),z(e,"aria-label",u=`Select example ${l[8]+1}: ${l[6].display_text||l[6].text}`)},m(S,$){J(S,e,$),O(e,t),~n&&k[n].m(t,null),O(t,o),O(t,s),O(s,r),O(r,_),O(e,f),c=!0,w||(m=s_(e,"click",v),w=!0)},p(S,$){l=S;let F=n;n=d(l),n===F?~n&&k[n].p(l,$):(i&&(kt(),A(k[F],1,1,()=>{k[F]=null}),wt()),~n?(i=k[n],i?i.p(l,$):(i=k[n]=h[n](l),i.c()),V(i,1),i.m(t,o)):i=null),(!c||$&1)&&a!==(a=(l[6].display_text||l[6].text)+"")&&vn(_,a),(!c||$&1&&u!==(u=`Select example ${l[8]+1}: ${l[6].display_text||l[6].text}`))&&z(e,"aria-label",u)},i(S){c||(V(i),c=!0)},o(S){A(i),c=!1},d(S){S&&R(e),~n&&k[n].d(),w=!1,m()}}}function $_(l){let e,t,n,i=l[1]!==null&&Vi(l),o=l[0]!==null&&Di(l);return{c(){e=Z("div"),i&&i.c(),t=Pt(),o&&o.c(),z(e,"class","placeholder-content svelte-9pi8y1"),z(e,"role","complementary")},m(s,r){J(s,e,r),i&&i.m(e,null),O(e,t),o&&o.m(e,null),n=!0},p(s,[r]){s[1]!==null?i?(i.p(s,r),r&2&&V(i,1)):(i=Vi(s),i.c(),V(i,1),i.m(e,t)):i&&(kt(),A(i,1,1,()=>{i=null}),wt()),s[0]!==null?o?(o.p(s,r),r&1&&V(o,1)):(o=Di(s),o.c(),V(o,1),o.m(e,null)):o&&(kt(),A(o,1,1,()=>{o=null}),wt())},i(s){n||(V(i),V(o),n=!0)},o(s){A(i),A(o),n=!1},d(s){s&&R(e),i&&i.d(),o&&o.d()}}}function C_(l,e,t){let{examples:n=null}=e,{placeholder:i=null}=e,{latex_delimiters:o}=e;const s=r_();function r(_,f){const u=typeof f=="string"?{text:f}:f;s("example_select",{index:_,value:{text:u.text,files:u.files}})}const a=(_,f)=>r(_,typeof f=="string"?{text:f}:f);return l.$$set=_=>{"examples"in _&&t(0,n=_.examples),"placeholder"in _&&t(1,i=_.placeholder),"latex_delimiters"in _&&t(2,o=_.latex_delimiters)},[n,i,o,r,a]}class y_ extends i_{constructor(e){super(),o_(this,e,C_,$_,a_,{examples:0,placeholder:1,latex_delimiters:2})}get examples(){return this.$$.ctx[0]}set examples(e){this.$$set({examples:e}),Gn()}get placeholder(){return this.$$.ctx[1]}set placeholder(e){this.$$set({placeholder:e}),Gn()}get latex_delimiters(){return this.$$.ctx[2]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),Gn()}}const{SvelteComponent:z_,create_component:q_,destroy_component:S_,flush:Fi,init:H_,mount_component:L_,safe_not_equal:M_,transition_in:N_,transition_out:j_}=window.__gradio__svelte__internal,{onDestroy:V_}=window.__gradio__svelte__internal;function D_(l){let e,t;return e=new ue({props:{Icon:l[0]?Gt:fn,label:l[0]?"Copied conversation":"Copy conversation"}}),e.$on("click",l[1]),{c(){q_(e.$$.fragment)},m(n,i){L_(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.Icon=n[0]?Gt:fn),i&1&&(o.label=n[0]?"Copied conversation":"Copy conversation"),e.$set(o)},i(n){t||(N_(e.$$.fragment,n),t=!0)},o(n){j_(e.$$.fragment,n),t=!1},d(n){S_(e,n)}}}function I_(l,e,t){let n=!1,{value:i}=e,{watermark:o=null}=e,s;function r(){t(0,n=!0),s&&clearTimeout(s),s=setTimeout(()=>{t(0,n=!1)},1e3)}const a=()=>{if(i){const f=i.map(c=>c.type==="text"?`${c.role}: ${c.content}`:`${c.role}: ${c.content.value.url}`).join(`

`),u=o?`${f}

${o}`:f;navigator.clipboard.writeText(u).catch(c=>{console.error("Failed to copy conversation: ",c)})}};async function _(){"clipboard"in navigator&&(a(),r())}return V_(()=>{s&&clearTimeout(s)}),l.$$set=f=>{"value"in f&&t(2,i=f.value),"watermark"in f&&t(3,o=f.watermark)},[n,_,i,o]}class B_ extends z_{constructor(e){super(),H_(this,e,I_,D_,M_,{value:2,watermark:3})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),Fi()}get watermark(){return this.$$.ctx[3]}set watermark(e){this.$$set({watermark:e}),Fi()}}const{SvelteComponent:E_,action_destroyer:T_,add_flush_callback:A_,append:il,attr:Ye,bind:F_,binding_callbacks:Co,check_outros:mt,create_component:tt,destroy_component:nt,destroy_each:yo,detach:pe,element:tn,empty:kl,ensure_array_like:wn,flush:M,group_outros:ht,init:Z_,insert:ve,listen:U_,mount_component:lt,noop:kn,null_to_empty:Zi,safe_not_equal:W_,set_data:P_,space:It,text:O_,transition_in:N,transition_out:B}=window.__gradio__svelte__internal,{createEventDispatcher:R_,tick:J_,onMount:Yn}=window.__gradio__svelte__internal;function Ui(l,e,t){const n=l.slice();return n[61]=e[t],n[63]=t,n}function Wi(l,e,t){const n=l.slice();n[64]=e[t],n[71]=t;const i=n[64][0].role==="user"?"user":"bot";n[65]=i;const o=n[16][n[65]==="user"?0:1];n[66]=o;const s=n[16][n[65]==="user"?0:1];n[67]=s;const r=n[39].slice(0,n[71]).filter(_=>_[0].role==="assistant").length;n[68]=r;const a=n[65]==="bot"&&n[10]&&n[10][n[68]]?n[10][n[68]]:null;return n[69]=a,n}function Pi(l){let e,t;return e=new io({props:{$$slots:{default:[G_]},$$scope:{ctx:l}}}),{c(){tt(e.$$.fragment)},m(n,i){lt(e,n,i),t=!0},p(n,i){const o={};i[0]&2109441|i[1]&1|i[2]&1024&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(N(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){nt(e,n)}}}function Oi(l){let e,t;return e=new ue({props:{Icon:Do}}),e.$on("click",l[48]),{c(){tt(e.$$.fragment)},m(n,i){lt(e,n,i),t=!0},p:kn,i(n){t||(N(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){nt(e,n)}}}function Ri(l){let e,t;return e=new B_({props:{value:l[0],watermark:l[31]}}),{c(){tt(e.$$.fragment)},m(n,i){lt(e,n,i),t=!0},p(n,i){const o={};i[0]&1&&(o.value=n[0]),i[1]&1&&(o.watermark=n[31]),e.$set(o)},i(n){t||(N(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){nt(e,n)}}}function G_(l){let e,t,n,i,o,s=l[12]&&Oi(l);t=new ue({props:{Icon:Io,label:l[21]("chatbot.clear")}}),t.$on("click",l[49]);let r=l[13]&&Ri(l);return{c(){s&&s.c(),e=It(),tt(t.$$.fragment),n=It(),r&&r.c(),i=kl()},m(a,_){s&&s.m(a,_),ve(a,e,_),lt(t,a,_),ve(a,n,_),r&&r.m(a,_),ve(a,i,_),o=!0},p(a,_){a[12]?s?(s.p(a,_),_[0]&4096&&N(s,1)):(s=Oi(a),s.c(),N(s,1),s.m(e.parentNode,e)):s&&(ht(),B(s,1,1,()=>{s=null}),mt());const f={};_[0]&2097152&&(f.label=a[21]("chatbot.clear")),t.$set(f),a[13]?r?(r.p(a,_),_[0]&8192&&N(r,1)):(r=Ri(a),r.c(),N(r,1),r.m(i.parentNode,i)):r&&(ht(),B(r,1,1,()=>{r=null}),mt())},i(a){o||(N(s),N(t.$$.fragment,a),N(r),o=!0)},o(a){B(s),B(t.$$.fragment,a),B(r),o=!1},d(a){a&&(pe(e),pe(n),pe(i)),s&&s.d(a),nt(t,a),r&&r.d(a)}}}function Y_(l){let e,t;return e=new y_({props:{examples:l[26],placeholder:l[23],latex_delimiters:l[4]}}),e.$on("example_select",l[54]),{c(){tt(e.$$.fragment)},m(n,i){lt(e,n,i),t=!0},p(n,i){const o={};i[0]&67108864&&(o.examples=n[26]),i[0]&8388608&&(o.placeholder=n[23]),i[0]&16&&(o.latex_delimiters=n[4]),e.$set(o)},i(n){t||(N(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){nt(e,n)}}}function K_(l){let e,t,n,i,o,s,r,a=wn(l[39]),_=[];for(let m=0;m<a.length;m+=1)_[m]=Gi(Wi(l,a,m));const f=m=>B(_[m],1,1,()=>{_[m]=null}),u=[X_,Q_],c=[];function w(m,h){return m[32]!=="hidden"&&m[5]?0:m[40]?1:-1}return~(n=w(l))&&(i=c[n]=u[n](l)),{c(){e=tn("div");for(let m=0;m<_.length;m+=1)_[m].c();t=It(),i&&i.c(),Ye(e,"class","message-wrap svelte-gjtrl6")},m(m,h){ve(m,e,h);for(let k=0;k<_.length;k+=1)_[k]&&_[k].m(e,null);il(e,t),~n&&c[n].m(e,null),o=!0,s||(r=T_(Lo.call(null,e)),s=!0)},p(m,h){if(h[0]&2071973855|h[1]&11583){a=wn(m[39]);let d;for(d=0;d<a.length;d+=1){const v=Wi(m,a,d);_[d]?(_[d].p(v,h),N(_[d],1)):(_[d]=Gi(v),_[d].c(),N(_[d],1),_[d].m(e,t))}for(ht(),d=a.length;d<_.length;d+=1)f(d);mt()}let k=n;n=w(m),n===k?~n&&c[n].p(m,h):(i&&(ht(),B(c[k],1,1,()=>{c[k]=null}),mt()),~n?(i=c[n],i?i.p(m,h):(i=c[n]=u[n](m),i.c()),N(i,1),i.m(e,null)):i=null)},i(m){if(!o){for(let h=0;h<a.length;h+=1)N(_[h]);N(i),o=!0}},o(m){_=_.filter(Boolean);for(let h=0;h<_.length;h+=1)B(_[h]);B(i),o=!1},d(m){m&&pe(e),yo(_,m),~n&&c[n].d(),s=!1,r()}}}function Ji(l){let e,t;return e=new po({props:{layout:l[22],avatar_images:l[16]}}),{c(){tt(e.$$.fragment)},m(n,i){lt(e,n,i),t=!0},p(n,i){const o={};i[0]&4194304&&(o.layout=n[22]),i[0]&65536&&(o.avatar_images=n[16]),e.$set(o)},i(n){t||(N(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){nt(e,n)}}}function Gi(l){let e,t,n,i,o;function s(...f){return l[50](l[64],l[71],...f)}function r(f){l[51](f)}let a={messages:l[64],display_consecutive_in_same_bubble:l[3],opposite_avatar_img:l[67],avatar_img:l[66],role:l[65],layout:l[22],dispatch:l[42],i18n:l[21],_fetch:l[1],line_breaks:l[19],theme_mode:l[20],target:l[34],upload:l[24],selectable:l[7],sanitize_html:l[17],render_markdown:l[18],rtl:l[14],i:l[71],value:l[0],latex_delimiters:l[4],_components:l[33],generating:l[6],msg_format:l[25],feedback_options:l[9],current_feedback:l[69],allow_tags:l[30],watermark:l[31],show_like:l[65]==="user"?l[8]&&l[29]:l[8],show_retry:l[27]&&on(l[64],l[0]),show_undo:l[28]&&on(l[64],l[0]),show_edit:l[11]==="all"||l[11]=="user"&&l[65]==="user"&&l[64].length>0&&l[64][l[64].length-1].type=="text",in_edit_mode:l[35]===l[71],show_copy_button:l[15],handle_action:s,scroll:l[41]?scroll:ef,allow_file_downloads:l[2]};l[36]!==void 0&&(a.edit_messages=l[36]),e=new Rr({props:a}),Co.push(()=>F_(e,"edit_messages",r)),e.$on("copy",l[52]);let _=l[32]!=="hidden"&&l[6]&&l[64][l[64].length-1].role==="assistant"&&l[64][l[64].length-1].metadata?.status==="done"&&Ji(l);return{c(){tt(e.$$.fragment),n=It(),_&&_.c(),i=kl()},m(f,u){lt(e,f,u),ve(f,n,u),_&&_.m(f,u),ve(f,i,u),o=!0},p(f,u){l=f;const c={};u[1]&256&&(c.messages=l[64]),u[0]&8&&(c.display_consecutive_in_same_bubble=l[3]),u[0]&65536|u[1]&256&&(c.opposite_avatar_img=l[67]),u[0]&65536|u[1]&256&&(c.avatar_img=l[66]),u[1]&256&&(c.role=l[65]),u[0]&4194304&&(c.layout=l[22]),u[0]&2097152&&(c.i18n=l[21]),u[0]&2&&(c._fetch=l[1]),u[0]&524288&&(c.line_breaks=l[19]),u[0]&1048576&&(c.theme_mode=l[20]),u[1]&8&&(c.target=l[34]),u[0]&16777216&&(c.upload=l[24]),u[0]&128&&(c.selectable=l[7]),u[0]&131072&&(c.sanitize_html=l[17]),u[0]&262144&&(c.render_markdown=l[18]),u[0]&16384&&(c.rtl=l[14]),u[0]&1&&(c.value=l[0]),u[0]&16&&(c.latex_delimiters=l[4]),u[1]&4&&(c._components=l[33]),u[0]&64&&(c.generating=l[6]),u[0]&33554432&&(c.msg_format=l[25]),u[0]&512&&(c.feedback_options=l[9]),u[0]&1024|u[1]&256&&(c.current_feedback=l[69]),u[0]&1073741824&&(c.allow_tags=l[30]),u[1]&1&&(c.watermark=l[31]),u[0]&536871168|u[1]&256&&(c.show_like=l[65]==="user"?l[8]&&l[29]:l[8]),u[0]&134217729|u[1]&256&&(c.show_retry=l[27]&&on(l[64],l[0])),u[0]&268435457|u[1]&256&&(c.show_undo=l[28]&&on(l[64],l[0])),u[0]&2048|u[1]&256&&(c.show_edit=l[11]==="all"||l[11]=="user"&&l[65]==="user"&&l[64].length>0&&l[64][l[64].length-1].type=="text"),u[1]&16&&(c.in_edit_mode=l[35]===l[71]),u[0]&32768&&(c.show_copy_button=l[15]),u[1]&288&&(c.handle_action=s),u[0]&4&&(c.allow_file_downloads=l[2]),!t&&u[1]&32&&(t=!0,c.edit_messages=l[36],A_(()=>t=!1)),e.$set(c),l[32]!=="hidden"&&l[6]&&l[64][l[64].length-1].role==="assistant"&&l[64][l[64].length-1].metadata?.status==="done"?_?(_.p(l,u),u[0]&64|u[1]&258&&N(_,1)):(_=Ji(l),_.c(),N(_,1),_.m(i.parentNode,i)):_&&(ht(),B(_,1,1,()=>{_=null}),mt())},i(f){o||(N(e.$$.fragment,f),N(_),o=!0)},o(f){B(e.$$.fragment,f),B(_),o=!1},d(f){f&&(pe(n),pe(i)),nt(e,f),_&&_.d(f)}}}function Q_(l){let e,t=wn(l[40]),n=[];for(let i=0;i<t.length;i+=1)n[i]=Yi(Ui(l,t,i));return{c(){e=tn("div");for(let i=0;i<n.length;i+=1)n[i].c();Ye(e,"class","options svelte-gjtrl6")},m(i,o){ve(i,e,o);for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(e,null)},p(i,o){if(o[1]&2560){t=wn(i[40]);let s;for(s=0;s<t.length;s+=1){const r=Ui(i,t,s);n[s]?n[s].p(r,o):(n[s]=Yi(r),n[s].c(),n[s].m(e,null))}for(;s<n.length;s+=1)n[s].d(1);n.length=t.length}},i:kn,o:kn,d(i){i&&pe(e),yo(n,i)}}}function X_(l){let e,t;return e=new po({props:{layout:l[22],avatar_images:l[16]}}),{c(){tt(e.$$.fragment)},m(n,i){lt(e,n,i),t=!0},p(n,i){const o={};i[0]&4194304&&(o.layout=n[22]),i[0]&65536&&(o.avatar_images=n[16]),e.$set(o)},i(n){t||(N(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){nt(e,n)}}}function Yi(l){let e,t=(l[61].label||l[61].value)+"",n,i,o,s;function r(){return l[53](l[63],l[61])}return{c(){e=tn("button"),n=O_(t),i=It(),Ye(e,"class","option svelte-gjtrl6")},m(a,_){ve(a,e,_),il(e,n),il(e,i),o||(s=U_(e,"click",r),o=!0)},p(a,_){l=a,_[1]&512&&t!==(t=(l[61].label||l[61].value)+"")&&P_(n,t)},d(a){a&&pe(e),o=!1,s()}}}function Ki(l){let e,t,n;return t=new ue({props:{Icon:ms,label:"Scroll down",size:"large"}}),t.$on("click",l[43]),{c(){e=tn("div"),tt(t.$$.fragment),Ye(e,"class","scroll-down-button-container svelte-gjtrl6")},m(i,o){ve(i,e,o),lt(t,e,null),n=!0},p:kn,i(i){n||(N(t.$$.fragment,i),n=!0)},o(i){B(t.$$.fragment,i),n=!1},d(i){i&&pe(e),nt(t)}}}function x_(l){let e,t,n,i,o,s,r,a,_=l[0]!==null&&l[0].length>0&&Pi(l);const f=[K_,Y_],u=[];function c(m,h){return m[0]!==null&&m[0].length>0&&m[39]!==null?0:1}n=c(l),i=u[n]=f[n](l);let w=l[38]&&Ki(l);return{c(){_&&_.c(),e=It(),t=tn("div"),i.c(),s=It(),w&&w.c(),r=kl(),Ye(t,"class",o=Zi(l[22]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-gjtrl6"),Ye(t,"role","log"),Ye(t,"aria-label","chatbot conversation"),Ye(t,"aria-live","polite")},m(m,h){_&&_.m(m,h),ve(m,e,h),ve(m,t,h),u[n].m(t,null),l[55](t),ve(m,s,h),w&&w.m(m,h),ve(m,r,h),a=!0},p(m,h){m[0]!==null&&m[0].length>0?_?(_.p(m,h),h[0]&1&&N(_,1)):(_=Pi(m),_.c(),N(_,1),_.m(e.parentNode,e)):_&&(ht(),B(_,1,1,()=>{_=null}),mt());let k=n;n=c(m),n===k?u[n].p(m,h):(ht(),B(u[k],1,1,()=>{u[k]=null}),mt(),i=u[n],i?i.p(m,h):(i=u[n]=f[n](m),i.c()),N(i,1),i.m(t,null)),(!a||h[0]&4194304&&o!==(o=Zi(m[22]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-gjtrl6"))&&Ye(t,"class",o),m[38]?w?(w.p(m,h),h[1]&128&&N(w,1)):(w=Ki(m),w.c(),N(w,1),w.m(r.parentNode,r)):w&&(ht(),B(w,1,1,()=>{w=null}),mt())},i(m){a||(N(_),N(i),N(w),a=!0)},o(m){B(_),B(i),B(w),a=!1},d(m){m&&(pe(e),pe(t),pe(s),pe(r)),_&&_.d(m),u[n].d(),l[55](null),w&&w.d(m)}}}const ef=()=>{};function tf(l,e,t){let n,i,{value:o=[]}=e,s=null,{_fetch:r}=e,{load_component:a}=e,{allow_file_downloads:_}=e,{display_consecutive_in_same_bubble:f}=e,u={};const c=typeof window<"u";async function w(){t(33,u=await ks(ps(o),u,a))}let{latex_delimiters:m}=e,{pending_message:h=!1}=e,{generating:k=!1}=e,{selectable:d=!1}=e,{likeable:v=!1}=e,{feedback_options:S}=e,{feedback_value:$=null}=e,{editable:F=null}=e,{show_share_button:C=!1}=e,{show_copy_all_button:E=!1}=e,{rtl:_e=!1}=e,{show_copy_button:fe=!1}=e,{avatar_images:he=[null,null]}=e,{sanitize_html:de=!0}=e,{render_markdown:q=!0}=e,{line_breaks:Ce=!0}=e,{autoscroll:y=!0}=e,{theme_mode:T}=e,{i18n:it}=e,{layout:ot="bubble"}=e,{placeholder:Me=null}=e,{upload:ye}=e,{msg_format:Ne="tuples"}=e,{examples:Te=null}=e,{_retryable:st=!1}=e,{_undoable:Ae=!1}=e,{like_user_message:ge=!1}=e,{allow_tags:Fe=!1}=e,{watermark:Ze=null}=e,{show_progress:Ue="full"}=e,at=null,We=null,ze=[];Yn(()=>{t(34,at=document.querySelector("div.gradio-container"))});let x,rt=!1;const ee=R_();function p(){return x&&x.offsetHeight+x.scrollTop>x.scrollHeight-100}function P(){x&&(x.scrollTo(0,x.scrollHeight),t(38,rt=!1))}async function nn(){y&&p()&&(await J_(),await new Promise(b=>setTimeout(b,300)),P())}Yn(()=>{y&&P(),nn()}),Yn(()=>{function b(){p()?t(38,rt=!1):t(38,rt=!0)}return x?.addEventListener("scroll",b),()=>{x?.removeEventListener("scroll",b)}});function Ot(b,$e,te){if(te==="undo"||te==="retry"){const Pe=o;let qe=Pe.length-1;for(;Pe[qe].role==="assistant";)qe--;ee(te,{index:Pe[qe].index,value:Pe[qe].content})}else if(te=="edit")t(35,We=b),ze.push($e.content);else if(te=="edit_cancel")t(35,We=null);else if(te=="edit_submit")t(35,We=null),ee("edit",{index:$e.index,value:ze[b].slice(),previous_value:$e.content});else{let Pe=te==="Like"?!0:te==="Dislike"?!1:te||"";if(Ne==="tuples")ee("like",{index:$e.index,value:$e.content,liked:Pe});else{if(!n)return;const qe=n[b],[qo,bf]=[qe[0],qe[qe.length-1]];ee("like",{index:qo.index,value:qe.map(So=>So.content),liked:Pe})}}}function zn(){if(!o||!n||n.length===0)return;const b=n[n.length-1];if(b[0].role==="assistant")return b[b.length-1].options}const qn=async()=>{try{const b=await hs(o);ee("share",{description:b})}catch(b){console.error(b);let $e=b instanceof Ho?b.message:"Share failed.";ee("error",$e)}},Sn=()=>ee("clear"),Hn=(b,$e,te)=>{te=="edit"&&ze.splice(0,ze.length),te==="edit"||te==="edit_submit"?b.forEach((Pe,qe)=>{Ot(te==="edit"?$e:qe,Pe,te)}):Ot($e,b[0],te)};function Ln(b){ze=b,t(36,ze)}const Mn=b=>ee("copy",b.detail),Nn=(b,$e)=>ee("option_select",{index:b,value:$e.value}),g=b=>ee("example_select",b.detail);function zo(b){Co[b?"unshift":"push"](()=>{x=b,t(37,x)})}return l.$$set=b=>{"value"in b&&t(0,o=b.value),"_fetch"in b&&t(1,r=b._fetch),"load_component"in b&&t(45,a=b.load_component),"allow_file_downloads"in b&&t(2,_=b.allow_file_downloads),"display_consecutive_in_same_bubble"in b&&t(3,f=b.display_consecutive_in_same_bubble),"latex_delimiters"in b&&t(4,m=b.latex_delimiters),"pending_message"in b&&t(5,h=b.pending_message),"generating"in b&&t(6,k=b.generating),"selectable"in b&&t(7,d=b.selectable),"likeable"in b&&t(8,v=b.likeable),"feedback_options"in b&&t(9,S=b.feedback_options),"feedback_value"in b&&t(10,$=b.feedback_value),"editable"in b&&t(11,F=b.editable),"show_share_button"in b&&t(12,C=b.show_share_button),"show_copy_all_button"in b&&t(13,E=b.show_copy_all_button),"rtl"in b&&t(14,_e=b.rtl),"show_copy_button"in b&&t(15,fe=b.show_copy_button),"avatar_images"in b&&t(16,he=b.avatar_images),"sanitize_html"in b&&t(17,de=b.sanitize_html),"render_markdown"in b&&t(18,q=b.render_markdown),"line_breaks"in b&&t(19,Ce=b.line_breaks),"autoscroll"in b&&t(46,y=b.autoscroll),"theme_mode"in b&&t(20,T=b.theme_mode),"i18n"in b&&t(21,it=b.i18n),"layout"in b&&t(22,ot=b.layout),"placeholder"in b&&t(23,Me=b.placeholder),"upload"in b&&t(24,ye=b.upload),"msg_format"in b&&t(25,Ne=b.msg_format),"examples"in b&&t(26,Te=b.examples),"_retryable"in b&&t(27,st=b._retryable),"_undoable"in b&&t(28,Ae=b._undoable),"like_user_message"in b&&t(29,ge=b.like_user_message),"allow_tags"in b&&t(30,Fe=b.allow_tags),"watermark"in b&&t(31,Ze=b.watermark),"show_progress"in b&&t(32,Ue=b.show_progress)},l.$$.update=()=>{l.$$.dirty[0]&1&&w(),l.$$.dirty[0]&33|l.$$.dirty[1]&4&&(o||h||u)&&nn(),l.$$.dirty[0]&1|l.$$.dirty[1]&65536&&(Vo(o,s)||(t(47,s=o),ee("change"))),l.$$.dirty[0]&33554441&&t(39,n=o&&ws(o,Ne,f)),l.$$.dirty[0]&1&&t(40,i=o&&zn())},[o,r,_,f,m,h,k,d,v,S,$,F,C,E,_e,fe,he,de,q,Ce,T,it,ot,Me,ye,Ne,Te,st,Ae,ge,Fe,Ze,Ue,u,at,We,ze,x,rt,n,i,c,ee,P,Ot,a,y,s,qn,Sn,Hn,Ln,Mn,Nn,g,zo]}class nf extends E_{constructor(e){super(),Z_(this,e,tf,x_,W_,{value:0,_fetch:1,load_component:45,allow_file_downloads:2,display_consecutive_in_same_bubble:3,latex_delimiters:4,pending_message:5,generating:6,selectable:7,likeable:8,feedback_options:9,feedback_value:10,editable:11,show_share_button:12,show_copy_all_button:13,rtl:14,show_copy_button:15,avatar_images:16,sanitize_html:17,render_markdown:18,line_breaks:19,autoscroll:46,theme_mode:20,i18n:21,layout:22,placeholder:23,upload:24,msg_format:25,examples:26,_retryable:27,_undoable:28,like_user_message:29,allow_tags:30,watermark:31,show_progress:32},null,[-1,-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),M()}get _fetch(){return this.$$.ctx[1]}set _fetch(e){this.$$set({_fetch:e}),M()}get load_component(){return this.$$.ctx[45]}set load_component(e){this.$$set({load_component:e}),M()}get allow_file_downloads(){return this.$$.ctx[2]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),M()}get display_consecutive_in_same_bubble(){return this.$$.ctx[3]}set display_consecutive_in_same_bubble(e){this.$$set({display_consecutive_in_same_bubble:e}),M()}get latex_delimiters(){return this.$$.ctx[4]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),M()}get pending_message(){return this.$$.ctx[5]}set pending_message(e){this.$$set({pending_message:e}),M()}get generating(){return this.$$.ctx[6]}set generating(e){this.$$set({generating:e}),M()}get selectable(){return this.$$.ctx[7]}set selectable(e){this.$$set({selectable:e}),M()}get likeable(){return this.$$.ctx[8]}set likeable(e){this.$$set({likeable:e}),M()}get feedback_options(){return this.$$.ctx[9]}set feedback_options(e){this.$$set({feedback_options:e}),M()}get feedback_value(){return this.$$.ctx[10]}set feedback_value(e){this.$$set({feedback_value:e}),M()}get editable(){return this.$$.ctx[11]}set editable(e){this.$$set({editable:e}),M()}get show_share_button(){return this.$$.ctx[12]}set show_share_button(e){this.$$set({show_share_button:e}),M()}get show_copy_all_button(){return this.$$.ctx[13]}set show_copy_all_button(e){this.$$set({show_copy_all_button:e}),M()}get rtl(){return this.$$.ctx[14]}set rtl(e){this.$$set({rtl:e}),M()}get show_copy_button(){return this.$$.ctx[15]}set show_copy_button(e){this.$$set({show_copy_button:e}),M()}get avatar_images(){return this.$$.ctx[16]}set avatar_images(e){this.$$set({avatar_images:e}),M()}get sanitize_html(){return this.$$.ctx[17]}set sanitize_html(e){this.$$set({sanitize_html:e}),M()}get render_markdown(){return this.$$.ctx[18]}set render_markdown(e){this.$$set({render_markdown:e}),M()}get line_breaks(){return this.$$.ctx[19]}set line_breaks(e){this.$$set({line_breaks:e}),M()}get autoscroll(){return this.$$.ctx[46]}set autoscroll(e){this.$$set({autoscroll:e}),M()}get theme_mode(){return this.$$.ctx[20]}set theme_mode(e){this.$$set({theme_mode:e}),M()}get i18n(){return this.$$.ctx[21]}set i18n(e){this.$$set({i18n:e}),M()}get layout(){return this.$$.ctx[22]}set layout(e){this.$$set({layout:e}),M()}get placeholder(){return this.$$.ctx[23]}set placeholder(e){this.$$set({placeholder:e}),M()}get upload(){return this.$$.ctx[24]}set upload(e){this.$$set({upload:e}),M()}get msg_format(){return this.$$.ctx[25]}set msg_format(e){this.$$set({msg_format:e}),M()}get examples(){return this.$$.ctx[26]}set examples(e){this.$$set({examples:e}),M()}get _retryable(){return this.$$.ctx[27]}set _retryable(e){this.$$set({_retryable:e}),M()}get _undoable(){return this.$$.ctx[28]}set _undoable(e){this.$$set({_undoable:e}),M()}get like_user_message(){return this.$$.ctx[29]}set like_user_message(e){this.$$set({like_user_message:e}),M()}get allow_tags(){return this.$$.ctx[30]}set allow_tags(e){this.$$set({allow_tags:e}),M()}get watermark(){return this.$$.ctx[31]}set watermark(e){this.$$set({watermark:e}),M()}get show_progress(){return this.$$.ctx[32]}set show_progress(e){this.$$set({show_progress:e}),M()}}const lf=nf,{SvelteComponent:of,append:sf,assign:af,attr:rf,check_outros:Qi,create_component:$n,destroy_component:Cn,detach:Xi,element:_f,flush:H,get_spread_object:ff,get_spread_update:uf,group_outros:xi,init:cf,insert:eo,mount_component:yn,safe_not_equal:mf,space:to,transition_in:De,transition_out:ct}=window.__gradio__svelte__internal;function no(l){let e,t;const n=[{autoscroll:l[27].autoscroll},{i18n:l[27].i18n},l[30],{show_progress:l[30].show_progress==="hidden"?"hidden":"minimal"}];let i={};for(let o=0;o<n.length;o+=1)i=af(i,n[o]);return e=new To({props:i}),e.$on("clear_status",l[43]),{c(){$n(e.$$.fragment)},m(o,s){yn(e,o,s),t=!0},p(o,s){const r=s[0]&1207959552?uf(n,[s[0]&134217728&&{autoscroll:o[27].autoscroll},s[0]&134217728&&{i18n:o[27].i18n},s[0]&1073741824&&ff(o[30]),s[0]&1073741824&&{show_progress:o[30].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(r)},i(o){t||(De(e.$$.fragment,o),t=!0)},o(o){ct(e.$$.fragment,o),t=!1},d(o){Cn(e,o)}}}function lo(l){let e,t;return e=new Eo({props:{show_label:l[7],Icon:Oo,float:!0,label:l[6]||"Chatbot"}}),{c(){$n(e.$$.fragment)},m(n,i){yn(e,n,i),t=!0},p(n,i){const o={};i[0]&128&&(o.show_label=n[7]),i[0]&64&&(o.label=n[6]||"Chatbot"),e.$set(o)},i(n){t||(De(e.$$.fragment,n),t=!0)},o(n){ct(e.$$.fragment,n),t=!1},d(n){Cn(e,n)}}}function hf(l){let e,t,n,i,o,s=l[30]&&no(l),r=l[7]&&lo(l);return i=new lf({props:{i18n:l[27].i18n,selectable:l[8],likeable:l[9],feedback_options:l[10],feedback_value:l[11],show_share_button:l[12],show_copy_all_button:l[15],value:l[41],latex_delimiters:l[26],display_consecutive_in_same_bubble:l[24],render_markdown:l[19],theme_mode:l[38],editable:l[35],pending_message:l[30]?.status==="pending",generating:l[30]?.status==="generating",rtl:l[13],show_copy_button:l[14],like_user_message:l[29],show_progress:l[30]?.show_progress||"full",avatar_images:l[28],sanitize_html:l[16],line_breaks:l[20],autoscroll:l[21],layout:l[17],placeholder:l[36],examples:l[37],_retryable:l[22],_undoable:l[23],upload:l[44],_fetch:l[45],load_component:l[27].load_component,msg_format:l[18],allow_file_downloads:l[39],allow_tags:l[25],watermark:l[40]}}),i.$on("change",l[46]),i.$on("select",l[47]),i.$on("like",l[48]),i.$on("share",l[49]),i.$on("error",l[50]),i.$on("example_select",l[51]),i.$on("option_select",l[52]),i.$on("retry",l[53]),i.$on("undo",l[54]),i.$on("clear",l[55]),i.$on("copy",l[56]),i.$on("edit",l[57]),{c(){s&&s.c(),e=to(),t=_f("div"),r&&r.c(),n=to(),$n(i.$$.fragment),rf(t,"class","wrapper svelte-g3p8na")},m(a,_){s&&s.m(a,_),eo(a,e,_),eo(a,t,_),r&&r.m(t,null),sf(t,n),yn(i,t,null),o=!0},p(a,_){a[30]?s?(s.p(a,_),_[0]&1073741824&&De(s,1)):(s=no(a),s.c(),De(s,1),s.m(e.parentNode,e)):s&&(xi(),ct(s,1,1,()=>{s=null}),Qi()),a[7]?r?(r.p(a,_),_[0]&128&&De(r,1)):(r=lo(a),r.c(),De(r,1),r.m(t,n)):r&&(xi(),ct(r,1,1,()=>{r=null}),Qi());const f={};_[0]&134217728&&(f.i18n=a[27].i18n),_[0]&256&&(f.selectable=a[8]),_[0]&512&&(f.likeable=a[9]),_[0]&1024&&(f.feedback_options=a[10]),_[0]&2048&&(f.feedback_value=a[11]),_[0]&4096&&(f.show_share_button=a[12]),_[0]&32768&&(f.show_copy_all_button=a[15]),_[1]&1024&&(f.value=a[41]),_[0]&67108864&&(f.latex_delimiters=a[26]),_[0]&16777216&&(f.display_consecutive_in_same_bubble=a[24]),_[0]&524288&&(f.render_markdown=a[19]),_[1]&128&&(f.theme_mode=a[38]),_[1]&16&&(f.editable=a[35]),_[0]&1073741824&&(f.pending_message=a[30]?.status==="pending"),_[0]&1073741824&&(f.generating=a[30]?.status==="generating"),_[0]&8192&&(f.rtl=a[13]),_[0]&16384&&(f.show_copy_button=a[14]),_[0]&536870912&&(f.like_user_message=a[29]),_[0]&1073741824&&(f.show_progress=a[30]?.show_progress||"full"),_[0]&268435456&&(f.avatar_images=a[28]),_[0]&65536&&(f.sanitize_html=a[16]),_[0]&1048576&&(f.line_breaks=a[20]),_[0]&2097152&&(f.autoscroll=a[21]),_[0]&131072&&(f.layout=a[17]),_[1]&32&&(f.placeholder=a[36]),_[1]&64&&(f.examples=a[37]),_[0]&4194304&&(f._retryable=a[22]),_[0]&8388608&&(f._undoable=a[23]),_[0]&134217728&&(f.upload=a[44]),_[0]&134217728&&(f._fetch=a[45]),_[0]&134217728&&(f.load_component=a[27].load_component),_[0]&262144&&(f.msg_format=a[18]),_[1]&256&&(f.allow_file_downloads=a[39]),_[0]&33554432&&(f.allow_tags=a[25]),_[1]&512&&(f.watermark=a[40]),i.$set(f)},i(a){o||(De(s),De(r),De(i.$$.fragment,a),o=!0)},o(a){ct(s),ct(r),ct(i.$$.fragment,a),o=!1},d(a){a&&(Xi(e),Xi(t)),s&&s.d(a),r&&r.d(),Cn(i)}}}function df(l){let e,t;return e=new Bo({props:{elem_id:l[1],elem_classes:l[2],visible:l[3],padding:!1,scale:l[4],min_width:l[5],height:l[31],resizable:l[32],min_height:l[33],max_height:l[34],allow_overflow:!0,flex:!0,overflow_behavior:"auto",$$slots:{default:[hf]},$$scope:{ctx:l}}}),{c(){$n(e.$$.fragment)},m(n,i){yn(e,n,i),t=!0},p(n,i){const o={};i[0]&2&&(o.elem_id=n[1]),i[0]&4&&(o.elem_classes=n[2]),i[0]&8&&(o.visible=n[3]),i[0]&16&&(o.scale=n[4]),i[0]&32&&(o.min_width=n[5]),i[1]&1&&(o.height=n[31]),i[1]&2&&(o.resizable=n[32]),i[1]&4&&(o.min_height=n[33]),i[1]&8&&(o.max_height=n[34]),i[0]&2147483585|i[1]&134219760&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(De(e.$$.fragment,n),t=!0)},o(n){ct(e.$$.fragment,n),t=!1},d(n){Cn(e,n)}}}function gf(l,e,t){let{elem_id:n=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:s=[]}=e,{scale:r=null}=e,{min_width:a=void 0}=e,{label:_}=e,{show_label:f=!0}=e,{root:u}=e,{_selectable:c=!1}=e,{likeable:w=!1}=e,{feedback_options:m=["Like","Dislike"]}=e,{feedback_value:h=null}=e,{show_share_button:k=!1}=e,{rtl:d=!1}=e,{show_copy_button:v=!0}=e,{show_copy_all_button:S=!1}=e,{sanitize_html:$=!0}=e,{layout:F="bubble"}=e,{type:C="tuples"}=e,{render_markdown:E=!0}=e,{line_breaks:_e=!0}=e,{autoscroll:fe=!0}=e,{_retryable:he=!1}=e,{_undoable:de=!1}=e,{group_consecutive_messages:q=!0}=e,{allow_tags:Ce=!1}=e,{latex_delimiters:y}=e,{gradio:T}=e,it=[],{avatar_images:ot=[null,null]}=e,{like_user_message:Me=!1}=e,{loading_status:ye=void 0}=e,{height:Ne}=e,{resizable:Te}=e,{min_height:st}=e,{max_height:Ae}=e,{editable:ge=null}=e,{placeholder:Fe=null}=e,{examples:Ze=null}=e,{theme_mode:Ue}=e,{allow_file_downloads:at=!0}=e,{watermark:We=null}=e;const ze=()=>T.dispatch("clear_status",ye),x=(...g)=>T.client.upload(...g),rt=(...g)=>T.client.fetch(...g),ee=()=>T.dispatch("change",s),p=g=>T.dispatch("select",g.detail),P=g=>T.dispatch("like",g.detail),nn=g=>T.dispatch("share",g.detail),Ot=g=>T.dispatch("error",g.detail),zn=g=>T.dispatch("example_select",g.detail),qn=g=>T.dispatch("option_select",g.detail),Sn=g=>T.dispatch("retry",g.detail),Hn=g=>T.dispatch("undo",g.detail),Ln=()=>{t(0,s=[]),T.dispatch("clear")},Mn=g=>T.dispatch("copy",g.detail),Nn=g=>{s===null||s.length===0||(C==="messages"?t(0,s[g.detail.index].content=g.detail.value,s):t(0,s[g.detail.index[0]][g.detail.index[1]]=g.detail.value,s),t(0,s),T.dispatch("edit",g.detail))};return l.$$set=g=>{"elem_id"in g&&t(1,n=g.elem_id),"elem_classes"in g&&t(2,i=g.elem_classes),"visible"in g&&t(3,o=g.visible),"value"in g&&t(0,s=g.value),"scale"in g&&t(4,r=g.scale),"min_width"in g&&t(5,a=g.min_width),"label"in g&&t(6,_=g.label),"show_label"in g&&t(7,f=g.show_label),"root"in g&&t(42,u=g.root),"_selectable"in g&&t(8,c=g._selectable),"likeable"in g&&t(9,w=g.likeable),"feedback_options"in g&&t(10,m=g.feedback_options),"feedback_value"in g&&t(11,h=g.feedback_value),"show_share_button"in g&&t(12,k=g.show_share_button),"rtl"in g&&t(13,d=g.rtl),"show_copy_button"in g&&t(14,v=g.show_copy_button),"show_copy_all_button"in g&&t(15,S=g.show_copy_all_button),"sanitize_html"in g&&t(16,$=g.sanitize_html),"layout"in g&&t(17,F=g.layout),"type"in g&&t(18,C=g.type),"render_markdown"in g&&t(19,E=g.render_markdown),"line_breaks"in g&&t(20,_e=g.line_breaks),"autoscroll"in g&&t(21,fe=g.autoscroll),"_retryable"in g&&t(22,he=g._retryable),"_undoable"in g&&t(23,de=g._undoable),"group_consecutive_messages"in g&&t(24,q=g.group_consecutive_messages),"allow_tags"in g&&t(25,Ce=g.allow_tags),"latex_delimiters"in g&&t(26,y=g.latex_delimiters),"gradio"in g&&t(27,T=g.gradio),"avatar_images"in g&&t(28,ot=g.avatar_images),"like_user_message"in g&&t(29,Me=g.like_user_message),"loading_status"in g&&t(30,ye=g.loading_status),"height"in g&&t(31,Ne=g.height),"resizable"in g&&t(32,Te=g.resizable),"min_height"in g&&t(33,st=g.min_height),"max_height"in g&&t(34,Ae=g.max_height),"editable"in g&&t(35,ge=g.editable),"placeholder"in g&&t(36,Fe=g.placeholder),"examples"in g&&t(37,Ze=g.examples),"theme_mode"in g&&t(38,Ue=g.theme_mode),"allow_file_downloads"in g&&t(39,at=g.allow_file_downloads),"watermark"in g&&t(40,We=g.watermark)},l.$$.update=()=>{l.$$.dirty[0]&262145|l.$$.dirty[1]&2048&&t(41,it=C==="tuples"?bs(s,u):gs(s,u))},[s,n,i,o,r,a,_,f,c,w,m,h,k,d,v,S,$,F,C,E,_e,fe,he,de,q,Ce,y,T,ot,Me,ye,Ne,Te,st,Ae,ge,Fe,Ze,Ue,at,We,it,u,ze,x,rt,ee,p,P,nn,Ot,zn,qn,Sn,Hn,Ln,Mn,Nn]}class Rf extends of{constructor(e){super(),cf(this,e,gf,df,mf,{elem_id:1,elem_classes:2,visible:3,value:0,scale:4,min_width:5,label:6,show_label:7,root:42,_selectable:8,likeable:9,feedback_options:10,feedback_value:11,show_share_button:12,rtl:13,show_copy_button:14,show_copy_all_button:15,sanitize_html:16,layout:17,type:18,render_markdown:19,line_breaks:20,autoscroll:21,_retryable:22,_undoable:23,group_consecutive_messages:24,allow_tags:25,latex_delimiters:26,gradio:27,avatar_images:28,like_user_message:29,loading_status:30,height:31,resizable:32,min_height:33,max_height:34,editable:35,placeholder:36,examples:37,theme_mode:38,allow_file_downloads:39,watermark:40},null,[-1,-1])}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),H()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),H()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),H()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),H()}get scale(){return this.$$.ctx[4]}set scale(e){this.$$set({scale:e}),H()}get min_width(){return this.$$.ctx[5]}set min_width(e){this.$$set({min_width:e}),H()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),H()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),H()}get root(){return this.$$.ctx[42]}set root(e){this.$$set({root:e}),H()}get _selectable(){return this.$$.ctx[8]}set _selectable(e){this.$$set({_selectable:e}),H()}get likeable(){return this.$$.ctx[9]}set likeable(e){this.$$set({likeable:e}),H()}get feedback_options(){return this.$$.ctx[10]}set feedback_options(e){this.$$set({feedback_options:e}),H()}get feedback_value(){return this.$$.ctx[11]}set feedback_value(e){this.$$set({feedback_value:e}),H()}get show_share_button(){return this.$$.ctx[12]}set show_share_button(e){this.$$set({show_share_button:e}),H()}get rtl(){return this.$$.ctx[13]}set rtl(e){this.$$set({rtl:e}),H()}get show_copy_button(){return this.$$.ctx[14]}set show_copy_button(e){this.$$set({show_copy_button:e}),H()}get show_copy_all_button(){return this.$$.ctx[15]}set show_copy_all_button(e){this.$$set({show_copy_all_button:e}),H()}get sanitize_html(){return this.$$.ctx[16]}set sanitize_html(e){this.$$set({sanitize_html:e}),H()}get layout(){return this.$$.ctx[17]}set layout(e){this.$$set({layout:e}),H()}get type(){return this.$$.ctx[18]}set type(e){this.$$set({type:e}),H()}get render_markdown(){return this.$$.ctx[19]}set render_markdown(e){this.$$set({render_markdown:e}),H()}get line_breaks(){return this.$$.ctx[20]}set line_breaks(e){this.$$set({line_breaks:e}),H()}get autoscroll(){return this.$$.ctx[21]}set autoscroll(e){this.$$set({autoscroll:e}),H()}get _retryable(){return this.$$.ctx[22]}set _retryable(e){this.$$set({_retryable:e}),H()}get _undoable(){return this.$$.ctx[23]}set _undoable(e){this.$$set({_undoable:e}),H()}get group_consecutive_messages(){return this.$$.ctx[24]}set group_consecutive_messages(e){this.$$set({group_consecutive_messages:e}),H()}get allow_tags(){return this.$$.ctx[25]}set allow_tags(e){this.$$set({allow_tags:e}),H()}get latex_delimiters(){return this.$$.ctx[26]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),H()}get gradio(){return this.$$.ctx[27]}set gradio(e){this.$$set({gradio:e}),H()}get avatar_images(){return this.$$.ctx[28]}set avatar_images(e){this.$$set({avatar_images:e}),H()}get like_user_message(){return this.$$.ctx[29]}set like_user_message(e){this.$$set({like_user_message:e}),H()}get loading_status(){return this.$$.ctx[30]}set loading_status(e){this.$$set({loading_status:e}),H()}get height(){return this.$$.ctx[31]}set height(e){this.$$set({height:e}),H()}get resizable(){return this.$$.ctx[32]}set resizable(e){this.$$set({resizable:e}),H()}get min_height(){return this.$$.ctx[33]}set min_height(e){this.$$set({min_height:e}),H()}get max_height(){return this.$$.ctx[34]}set max_height(e){this.$$set({max_height:e}),H()}get editable(){return this.$$.ctx[35]}set editable(e){this.$$set({editable:e}),H()}get placeholder(){return this.$$.ctx[36]}set placeholder(e){this.$$set({placeholder:e}),H()}get examples(){return this.$$.ctx[37]}set examples(e){this.$$set({examples:e}),H()}get theme_mode(){return this.$$.ctx[38]}set theme_mode(e){this.$$set({theme_mode:e}),H()}get allow_file_downloads(){return this.$$.ctx[39]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),H()}get watermark(){return this.$$.ctx[40]}set watermark(e){this.$$set({watermark:e}),H()}}export{lf as BaseChatBot,Rf as default};
//# sourceMappingURL=Index-CB1YfUW4.js.map
