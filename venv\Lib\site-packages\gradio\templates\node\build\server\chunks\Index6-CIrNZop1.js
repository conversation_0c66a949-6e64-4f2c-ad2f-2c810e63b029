import { c as create_ssr_component, v as validate_component } from './ssr-C3HYbsxA.js';
import { m as mt } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const f=create_ssr_component((c,e,l,m)=>{let{elem_id:t}=e,{elem_classes:i}=e,{visible:a=!0}=e;return e.elem_id===void 0&&l.elem_id&&t!==void 0&&l.elem_id(t),e.elem_classes===void 0&&l.elem_classes&&i!==void 0&&l.elem_classes(i),e.visible===void 0&&l.visible&&a!==void 0&&l.visible(a),`${validate_component(mt,"Block").$$render(c,{elem_id:t,elem_classes:i,visible:a,explicit_call:!0},{},{default:()=>`${m.default?m.default({}):""}`})}`});

export { f as default };
//# sourceMappingURL=Index6-CIrNZop1.js.map
