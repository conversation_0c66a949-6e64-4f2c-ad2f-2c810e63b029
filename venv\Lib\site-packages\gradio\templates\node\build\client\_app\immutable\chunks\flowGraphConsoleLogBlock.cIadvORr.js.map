{"version": 3, "file": "flowGraphConsoleLogBlock.cIadvORr.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphConsoleLogBlock.js"], "sourcesContent": ["import { FlowGraphExecutionBlockWithOutSignal } from \"../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RichTypeAny } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { Logger } from \"../../../Misc/logger.js\";\n/**\n * Block that logs a message to the console.\n */\nexport class FlowGraphConsoleLogBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config) {\n        super(config);\n        this.message = this.registerDataInput(\"message\", RichTypeAny);\n        this.logType = this.registerDataInput(\"logType\", RichTypeAny, \"log\");\n        if (config?.messageTemplate) {\n            const matches = this._getTemplateMatches(config.messageTemplate);\n            for (const match of matches) {\n                this.registerDataInput(match, RichTypeAny);\n            }\n        }\n    }\n    /**\n     * @internal\n     */\n    _execute(context) {\n        const typeValue = this.logType.getValue(context);\n        const messageValue = this._getMessageValue(context);\n        if (typeValue === \"warn\") {\n            Logger.Warn(messageValue);\n        }\n        else if (typeValue === \"error\") {\n            Logger.Error(messageValue);\n        }\n        else {\n            Logger.Log(messageValue);\n        }\n        // activate the output flow block\n        this.out._activateSignal(context);\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphConsoleLogBlock\" /* FlowGraphBlockNames.ConsoleLog */;\n    }\n    _getMessageValue(context) {\n        if (this.config?.messageTemplate) {\n            let template = this.config.messageTemplate;\n            const matches = this._getTemplateMatches(template);\n            for (const match of matches) {\n                const value = this.getDataInput(match)?.getValue(context);\n                if (value !== undefined) {\n                    // replace all\n                    template = template.replace(new RegExp(`\\\\{${match}\\\\}`, \"g\"), value.toString());\n                }\n            }\n            return template;\n        }\n        else {\n            return this.message.getValue(context);\n        }\n    }\n    _getTemplateMatches(template) {\n        const regex = /\\{([^}]+)\\}/g;\n        const matches = [];\n        let match;\n        while ((match = regex.exec(template)) !== null) {\n            matches.push(match[1]);\n        }\n        return matches;\n    }\n}\nRegisterClass(\"FlowGraphConsoleLogBlock\" /* FlowGraphBlockNames.ConsoleLog */, FlowGraphConsoleLogBlock);\n//# sourceMappingURL=flowGraphConsoleLogBlock.js.map"], "names": ["FlowGraphConsoleLogBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeAny", "matches", "match", "context", "typeValue", "messageValue", "<PERSON><PERSON>", "_a", "template", "value", "_b", "regex", "RegisterClass"], "mappings": "uJAOO,MAAMA,UAAiCC,CAAqC,CAC/E,YAAYC,EAAQ,CAIhB,GAHA,MAAMA,CAAM,EACZ,KAAK,QAAU,KAAK,kBAAkB,UAAWC,CAAW,EAC5D,KAAK,QAAU,KAAK,kBAAkB,UAAWA,EAAa,KAAK,EAC/DD,GAAA,MAAAA,EAAQ,gBAAiB,CACzB,MAAME,EAAU,KAAK,oBAAoBF,EAAO,eAAe,EAC/D,UAAWG,KAASD,EAChB,KAAK,kBAAkBC,EAAOF,CAAW,CAEhD,CACJ,CAID,SAASG,EAAS,CACd,MAAMC,EAAY,KAAK,QAAQ,SAASD,CAAO,EACzCE,EAAe,KAAK,iBAAiBF,CAAO,EAC9CC,IAAc,OACdE,EAAO,KAAKD,CAAY,EAEnBD,IAAc,QACnBE,EAAO,MAAMD,CAAY,EAGzBC,EAAO,IAAID,CAAY,EAG3B,KAAK,IAAI,gBAAgBF,CAAO,CACnC,CAID,cAAe,CACX,MAAO,0BACV,CACD,iBAAiBA,EAAS,SACtB,IAAII,EAAA,KAAK,SAAL,MAAAA,EAAa,gBAAiB,CAC9B,IAAIC,EAAW,KAAK,OAAO,gBAC3B,MAAMP,EAAU,KAAK,oBAAoBO,CAAQ,EACjD,UAAWN,KAASD,EAAS,CACzB,MAAMQ,GAAQC,EAAA,KAAK,aAAaR,CAAK,IAAvB,YAAAQ,EAA0B,SAASP,GAC7CM,IAAU,SAEVD,EAAWA,EAAS,QAAQ,IAAI,OAAO,MAAMN,CAAK,MAAO,GAAG,EAAGO,EAAM,SAAU,CAAA,EAEtF,CACD,OAAOD,CACV,KAEG,QAAO,KAAK,QAAQ,SAASL,CAAO,CAE3C,CACD,oBAAoBK,EAAU,CAC1B,MAAMG,EAAQ,eACRV,EAAU,CAAA,EAChB,IAAIC,EACJ,MAAQA,EAAQS,EAAM,KAAKH,CAAQ,KAAO,MACtCP,EAAQ,KAAKC,EAAM,CAAC,CAAC,EAEzB,OAAOD,CACV,CACL,CACAW,EAAc,2BAAiEf,CAAwB", "x_google_ignoreList": [0]}