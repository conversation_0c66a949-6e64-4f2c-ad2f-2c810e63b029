import{SvelteComponent as c,init as d,safe_not_equal as p,svg_element as i,claim_svg_element as a,children as s,detach as l,attr as r,insert_hydration as u,append_hydration as m,noop as n}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function g(v){let t,e;return{c(){t=i("svg"),e=i("path"),this.h()},l(o){t=a(o,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var h=s(t);e=a(h,"path",{fill:!0,d:!0}),s(e).forEach(l),h.forEach(l),this.h()},h(){r(e,"fill","currentColor"),r(e,"d","M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z"),r(t,"xmlns","http://www.w3.org/2000/svg"),r(t,"width","100%"),r(t,"height","100%"),r(t,"viewBox","0 0 32 32")},m(o,h){u(o,t,h),m(t,e)},p:n,i:n,o:n,d(o){o&&l(t)}}}class _ extends c{constructor(t){super(),d(this,t,null,g,p,{})}}export{_ as D};
//# sourceMappingURL=Download.CpfEFmFf.js.map
