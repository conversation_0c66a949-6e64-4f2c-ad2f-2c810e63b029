import { c as create_ssr_component, v as validate_component, b as createEventDispatcher, e as escape, h as add_styles } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA, h } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const x={code:"span.svelte-1w6vloh{font-weight:var(--section-header-text-weight);font-size:var(--section-header-text-size)}.label-wrap.svelte-1w6vloh{display:flex;justify-content:space-between;cursor:pointer;width:var(--size-full);color:var(--accordion-text-color)}.label-wrap.open.svelte-1w6vloh{margin-bottom:var(--size-2)}.icon.svelte-1w6vloh{transition:150ms}",map:'{"version":3,"file":"Accordion.svelte","sources":["Accordion.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher } from \\"svelte\\";\\nconst dispatch = createEventDispatcher();\\nexport let open = true;\\nexport let label = \\"\\";\\n<\/script>\\n\\n<button\\n\\ton:click={() => {\\n\\t\\topen = !open;\\n\\t\\tif (open) {\\n\\t\\t\\tdispatch(\\"expand\\");\\n\\t\\t} else {\\n\\t\\t\\tdispatch(\\"collapse\\");\\n\\t\\t}\\n\\t}}\\n\\tclass=\\"label-wrap\\"\\n\\tclass:open\\n>\\n\\t<span>{label}</span>\\n\\t<span style:transform={open ? \\"rotate(0)\\" : \\"rotate(90deg)\\"} class=\\"icon\\">\\n\\t\\t▼\\n\\t</span>\\n</button>\\n<div style:display={open ? \\"block\\" : \\"none\\"}>\\n\\t<slot />\\n</div>\\n\\n<style>\\n\\tspan {\\n\\t\\tfont-weight: var(--section-header-text-weight);\\n\\t\\tfont-size: var(--section-header-text-size);\\n\\t}\\n\\t.label-wrap {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: space-between;\\n\\t\\tcursor: pointer;\\n\\t\\twidth: var(--size-full);\\n\\t\\tcolor: var(--accordion-text-color);\\n\\t}\\n\\t.label-wrap.open {\\n\\t\\tmargin-bottom: var(--size-2);\\n\\t}\\n\\n\\t.icon {\\n\\t\\ttransition: 150ms;\\n\\t}</style>\\n"],"names":[],"mappings":"AA4BC,mBAAK,CACJ,WAAW,CAAE,IAAI,4BAA4B,CAAC,CAC9C,SAAS,CAAE,IAAI,0BAA0B,CAC1C,CACA,0BAAY,CACX,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,KAAK,CAAE,IAAI,sBAAsB,CAClC,CACA,WAAW,oBAAM,CAChB,aAAa,CAAE,IAAI,QAAQ,CAC5B,CAEA,oBAAM,CACL,UAAU,CAAE,KACb"}'},I=create_ssr_component((a,e,t,n)=>{createEventDispatcher();let{open:o=!0}=e,{label:l=""}=e;return e.open===void 0&&t.open&&o!==void 0&&t.open(o),e.label===void 0&&t.label&&l!==void 0&&t.label(l),a.css.add(x),`<button class="${["label-wrap svelte-1w6vloh",o?"open":""].join(" ").trim()}"><span class="svelte-1w6vloh">${escape(l)}</span> <span class="icon svelte-1w6vloh"${add_styles({transform:o?"rotate(0)":"rotate(90deg)"})} data-svelte-h="svelte-1mqwc8d">▼</span></button> <div${add_styles({display:o?"block":"none"})}>${n.default?n.default({}):""} </div>`}),W=create_ssr_component((a,e,t,n)=>{let{label:o}=e,{elem_id:l}=e,{elem_classes:i}=e,{visible:c=!0}=e,{open:A=!0}=e,{loading_status:d}=e,{gradio:s}=e;e.label===void 0&&t.label&&o!==void 0&&t.label(o),e.elem_id===void 0&&t.elem_id&&l!==void 0&&t.elem_id(l),e.elem_classes===void 0&&t.elem_classes&&i!==void 0&&t.elem_classes(i),e.visible===void 0&&t.visible&&c!==void 0&&t.visible(c),e.open===void 0&&t.open&&A!==void 0&&t.open(A),e.loading_status===void 0&&t.loading_status&&d!==void 0&&t.loading_status(d),e.gradio===void 0&&t.gradio&&s!==void 0&&t.gradio(s);let v,C,p=a.head;do v=!0,a.head=p,C=`${validate_component(mt,"Block").$$render(a,{elem_id:l,elem_classes:i,visible:c},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(a,Object.assign({},{autoscroll:s.autoscroll},{i18n:s.i18n},d),{},{})} ${validate_component(I,"Accordion").$$render(a,{label:o,open:A},{open:u=>{A=u,v=!1;}},{default:()=>`${validate_component(h,"Column").$$render(a,{},{},{default:()=>`${n.default?n.default({}):""}`})}`})}`})}`;while(!v);return C});

export { W as default };
//# sourceMappingURL=Index39-CEhJ7tUd.js.map
