import*as i from"../../../svelte/svelte.js";import{SvelteComponent as r,init as _,safe_not_equal as a,create_slot as f,update_slot_base as u,get_all_dirty_from_scope as p,get_slot_changes as c,transition_in as m,transition_out as d}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";const w=typeof window<"u";if(w){const n={SvelteComponent:i.SvelteComponent};for(const t in i)t!=="SvelteComponent"&&(t==="SvelteComponentDev"?n[t]=n.SvelteComponent:n[t]=i[t]);window.__gradio__svelte__internal=n,window.__gradio__svelte__internal.globals={},window.globals=window}function v(n){let t;const s=n[1].default,o=f(s,n,n[0],null);return{c(){o&&o.c()},l(e){o&&o.l(e)},m(e,l){o&&o.m(e,l),t=!0},p(e,[l]){o&&o.p&&(!t||l&1)&&u(o,s,e,e[0],t?c(s,e[0],l,null):p(e[0]),null)},i(e){t||(m(o,e),t=!0)},o(e){d(o,e),t=!1},d(e){o&&o.d(e)}}}function g(n,t,s){let{$$slots:o={},$$scope:e}=t;return n.$$set=l=>{"$$scope"in l&&s(0,e=l.$$scope)},[e,o]}class S extends r{constructor(t){super(),_(this,t,g,v,a,{})}}export{S as component};
//# sourceMappingURL=0.Djr7cQdC.js.map
