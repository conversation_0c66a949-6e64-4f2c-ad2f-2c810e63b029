import{R as s,M as k,Q as S,az as v,V as D,ay as E}from"./index.BoI39RQH.js";import{g as u,n as d,p as x,b as p,R as l,c as y,F as G,j as m}from"./declarationMapper.UBCwU7BT.js";import{F as i}from"./flowGraphBinaryOperationBlock.CI0NxKdM.js";import{F as z}from"./flowGraphCachedOperationBlock.CtP7sxiu.js";import{F as h}from"./flowGraphUnaryOperationBlock.ZI1fHq8c.js";import{F as L}from"./flowGraphTernaryOperationBlock.DiGI0Nsv.js";import{e as F,j as _,k as M,l as T,i as N,g as I}from"./KHR_interactivity.DEAVS2UW.js";class C extends z{constructor(r,o,t,a){super(r,a),this._operation=o,this._className=t}_doOperation(r){return this._operation(r)}getClassName(){return this._className}}class q extends i{constructor(r){super(u(r==null?void 0:r.type),u(r==null?void 0:r.type),u(r==null?void 0:r.type),(o,t)=>this._polymorphicAdd(o,t),"FlowGraphAddBlock",r)}_polymorphicAdd(r,o){const t=F(r),a=F(o);return _(t,a)||M(t,a)||T(t,a)||t==="Quaternion"||a==="Quaternion"?r.add(o):r+o}}s("FlowGraphAddBlock",q);class V extends i{constructor(r){super(u(r==null?void 0:r.type),u(r==null?void 0:r.type),u(r==null?void 0:r.type),(o,t)=>this._polymorphicSubtract(o,t),"FlowGraphSubtractBlock",r)}_polymorphicSubtract(r,o){const t=F(r),a=F(o);return _(t,a)||T(t,a)||M(t,a)||t==="Quaternion"||a==="Quaternion"?r.subtract(o):r-o}}s("FlowGraphSubtractBlock",V);class P extends i{constructor(r){super(u(r==null?void 0:r.type),u(r==null?void 0:r.type),u(r==null?void 0:r.type),(o,t)=>this._polymorphicMultiply(o,t),"FlowGraphMultiplyBlock",r)}_polymorphicMultiply(r,o){var w;const t=F(r),a=F(o);if(_(t,a)||T(t,a))return r.multiply(o);if(t==="Quaternion"||a==="Quaternion"){const n=r.clone();return n.x*=o.x,n.y*=o.y,n.z*=o.z,n.w*=o.w,n}else if(M(t,a))if((w=this.config)!=null&&w.useMatrixPerComponent){const n=r.m;for(let B=0;B<n.length;B++)n[B]*=o.m[B];return t==="Matrix2D"?new d(n):t==="Matrix3D"?new x(n):k.FromArray(n)}else return r=r,o=o,o.multiply(r);else return r*o}}s("FlowGraphMultiplyBlock",P);class Q extends i{constructor(r){super(u(r==null?void 0:r.type),u(r==null?void 0:r.type),u(r==null?void 0:r.type),(o,t)=>this._polymorphicDivide(o,t),"FlowGraphDivideBlock",r)}_polymorphicDivide(r,o){var w;const t=F(r),a=F(o);if(_(t,a)||T(t,a))return r.divide(o);if(t==="Quaternion"||a==="Quaternion"){const n=r.clone();return n.x/=o.x,n.y/=o.y,n.z/=o.z,n.w/=o.w,n}else if(M(t,a))if((w=this.config)!=null&&w.useMatrixPerComponent){const n=r.m;for(let B=0;B<n.length;B++)n[B]/=o.m[B];return t==="Matrix2D"?new d(n):t==="Matrix3D"?new x(n):k.FromArray(n)}else return r=r,o=o,r.divide(o);else return r/o}}s("FlowGraphDivideBlock",Q);class $ extends C{constructor(r){super(p,o=>this._random(o),"FlowGraphRandomBlock",r),this.min=this.registerDataInput("min",p,(r==null?void 0:r.min)??0),this.max=this.registerDataInput("max",p,(r==null?void 0:r.max)??1),r!=null&&r.seed&&(this._seed=r.seed)}_isSeed(r=this._seed){return r!==void 0}_getRandomValue(){if(this._isSeed(this._seed)){const r=Math.sin(this._seed++)*1e4;return r-Math.floor(r)}return Math.random()}_random(r){const o=this.min.getValue(r),t=this.max.getValue(r);return this._getRandomValue()*(t-o)+o}}s("FlowGraphRandomBlock",$);class j extends C{constructor(r){super(p,()=>Math.E,"FlowGraphEBlock",r)}}s("FlowGraphEBlock",j);class Z extends C{constructor(r){super(p,()=>Math.PI,"FlowGraphPIBlock",r)}}s("FlowGraphPIBlock",Z);class X extends C{constructor(r){super(p,()=>Number.POSITIVE_INFINITY,"FlowGraphInfBlock",r)}}s("FlowGraphInfBlock",X);class W extends C{constructor(r){super(p,()=>Number.NaN,"FlowGraphNaNBlock",r)}}s("FlowGraphNaNBlock",W);function c(e,r){switch(F(e)){case"FlowGraphInteger":return e=e,new G(r(e.value));case"Vector2":return e=e,new E(r(e.x),r(e.y));case"Vector3":return e=e,new D(r(e.x),r(e.y),r(e.z));case"Vector4":return e=e,new v(r(e.x),r(e.y),r(e.z),r(e.w));case"Quaternion":return e=e,new S(r(e.x),r(e.y),r(e.z),r(e.w));case"Matrix":return e=e,k.FromArray(e.m.map(r));case"Matrix2D":return e=e,new d(e.m.map(r));case"Matrix3D":return e=e,new x(e.m.map(r));default:return e=e,r(e)}}class U extends h{constructor(r){super(p,p,o=>this._polymorphicAbs(o),"FlowGraphAbsBlock",r)}_polymorphicAbs(r){return c(r,Math.abs)}}s("FlowGraphAbsBlock",U);class H extends h{constructor(r){super(p,p,o=>this._polymorphicSign(o),"FlowGraphSignBlock",r)}_polymorphicSign(r){return c(r,Math.sign)}}s("FlowGraphSignBlock",H);class Y extends h{constructor(r){super(p,p,o=>this._polymorphicTrunc(o),"FlowGraphTruncBlock",r)}_polymorphicTrunc(r){return c(r,Math.trunc)}}s("FlowGraphTruncBlock",Y);class J extends h{constructor(r){super(p,p,o=>this._polymorphicFloor(o),"FlowGraphFloorBlock",r)}_polymorphicFloor(r){return c(r,Math.floor)}}s("FlowGraphFloorBlock",J);class K extends h{constructor(r){super(l,l,o=>this._polymorphicCeiling(o),"FlowGraphCeilBlock",r)}_polymorphicCeiling(r){return c(r,Math.ceil)}}s("FlowGraphCeilBlock",K);class f extends h{constructor(r){super(l,l,o=>this._polymorphicRound(o),"FlowGraphRoundBlock",r)}_polymorphicRound(r){return c(r,o=>{var t;return o<0&&((t=this.config)!=null&&t.roundHalfAwayFromZero)?-Math.round(-o):Math.round(o)})}}s("FlowGraphRoundBlock",f);class g extends h{constructor(r){super(l,l,o=>this._polymorphicFraction(o),"FlowGraphFractBlock",r)}_polymorphicFraction(r){return c(r,o=>o-Math.floor(o))}}s("FlowGraphFractBlock",g);class b extends h{constructor(r){super(l,l,o=>this._polymorphicNeg(o),"FlowGraphNegationBlock",r)}_polymorphicNeg(r){return c(r,o=>-o)}}s("FlowGraphNegationBlock",b);function A(e,r,o){switch(F(e)){case"FlowGraphInteger":return e=e,r=r,new G(o(e.value,r.value));case"Vector2":return e=e,r=r,new E(o(e.x,r.x),o(e.y,r.y));case"Vector3":return e=e,r=r,new D(o(e.x,r.x),o(e.y,r.y),o(e.z,r.z));case"Vector4":return e=e,r=r,new v(o(e.x,r.x),o(e.y,r.y),o(e.z,r.z),o(e.w,r.w));case"Quaternion":return e=e,r=r,new S(o(e.x,r.x),o(e.y,r.y),o(e.z,r.z),o(e.w,r.w));case"Matrix":return e=e,k.FromArray(e.m.map((a,w)=>o(a,r.m[w])));case"Matrix2D":return e=e,new d(e.m.map((a,w)=>o(a,r.m[w])));case"Matrix3D":return e=e,new x(e.m.map((a,w)=>o(a,r.m[w])));default:return o(e,r)}}class rr extends i{constructor(r){super(l,l,l,(o,t)=>this._polymorphicRemainder(o,t),"FlowGraphModuloBlock",r)}_polymorphicRemainder(r,o){return A(r,o,(t,a)=>t%a)}}s("FlowGraphModuloBlock",rr);class or extends i{constructor(r){super(l,l,l,(o,t)=>this._polymorphicMin(o,t),"FlowGraphMinBlock",r)}_polymorphicMin(r,o){return A(r,o,Math.min)}}s("FlowGraphMinBlock",or);class er extends i{constructor(r){super(l,l,l,(o,t)=>this._polymorphicMax(o,t),"FlowGraphMaxBlock",r)}_polymorphicMax(r,o){return A(r,o,Math.max)}}s("FlowGraphMaxBlock",er);function tr(e,r,o){return Math.min(Math.max(e,Math.min(r,o)),Math.max(r,o))}function O(e,r,o,t){switch(F(e)){case"FlowGraphInteger":return e=e,r=r,o=o,new G(t(e.value,r.value,o.value));case"Vector2":return e=e,r=r,o=o,new E(t(e.x,r.x,o.x),t(e.y,r.y,o.y));case"Vector3":return e=e,r=r,o=o,new D(t(e.x,r.x,o.x),t(e.y,r.y,o.y),t(e.z,r.z,o.z));case"Vector4":return e=e,r=r,o=o,new v(t(e.x,r.x,o.x),t(e.y,r.y,o.y),t(e.z,r.z,o.z),t(e.w,r.w,o.w));case"Quaternion":return e=e,r=r,o=o,new S(t(e.x,r.x,o.x),t(e.y,r.y,o.y),t(e.z,r.z,o.z),t(e.w,r.w,o.w));case"Matrix":return k.FromArray(e.m.map((w,n)=>t(w,r.m[n],o.m[n])));case"Matrix2D":return new d(e.m.map((w,n)=>t(w,r.m[n],o.m[n])));case"Matrix3D":return new x(e.m.map((w,n)=>t(w,r.m[n],o.m[n])));default:return t(e,r,o)}}class lr extends L{constructor(r){super(l,l,l,l,(o,t,a)=>this._polymorphicClamp(o,t,a),"FlowGraphClampBlock",r)}_polymorphicClamp(r,o,t){return O(r,o,t,tr)}}s("FlowGraphClampBlock",lr);function sr(e){return Math.min(Math.max(e,0),1)}class ar extends h{constructor(r){super(l,l,o=>this._polymorphicSaturate(o),"FlowGraphSaturateBlock",r)}_polymorphicSaturate(r){return c(r,sr)}}s("FlowGraphSaturateBlock",ar);function pr(e,r,o){return(1-o)*e+o*r}class hr extends L{constructor(r){super(l,l,l,l,(o,t,a)=>this._polymorphicInterpolate(o,t,a),"FlowGraphMathInterpolationBlock",r)}_polymorphicInterpolate(r,o,t){return O(r,o,t,pr)}}s("FlowGraphMathInterpolationBlock",hr);class nr extends i{constructor(r){super(l,l,y,(o,t)=>this._polymorphicEq(o,t),"FlowGraphEqualityBlock",r)}_polymorphicEq(r,o){const t=F(r),a=F(o);return _(t,a)||M(t,a)||T(t,a)?r.equals(o):r===o}}s("FlowGraphEqualityBlock",nr);function R(e,r,o){if(N(e)&&N(r))return o(I(e),I(r));throw new Error(`Cannot compare ${e} and ${r}`)}class cr extends i{constructor(r){super(l,l,y,(o,t)=>this._polymorphicLessThan(o,t),"FlowGraphLessThanBlock",r)}_polymorphicLessThan(r,o){return R(r,o,(t,a)=>t<a)}}s("FlowGraphLessThanBlock",cr);class ur extends i{constructor(r){super(l,l,y,(o,t)=>this._polymorphicLessThanOrEqual(o,t),"FlowGraphLessThanOrEqualBlock",r)}_polymorphicLessThanOrEqual(r,o){return R(r,o,(t,a)=>t<=a)}}s("FlowGraphLessThanOrEqualBlock",ur);class ir extends i{constructor(r){super(l,l,y,(o,t)=>this._polymorphicGreaterThan(o,t),"FlowGraphGreaterThanBlock",r)}_polymorphicGreaterThan(r,o){return R(r,o,(t,a)=>t>a)}}s("FlowGraphGreaterThanBlock",ir);class wr extends i{constructor(r){super(l,l,y,(o,t)=>this._polymorphicGreaterThanOrEqual(o,t),"FlowGraphGreaterThanOrEqualBlock",r)}_polymorphicGreaterThanOrEqual(r,o){return R(r,o,(t,a)=>t>=a)}}s("FlowGraphGreaterThanOrEqualBlock",wr);class Fr extends h{constructor(r){super(l,y,o=>this._polymorphicIsNan(o),"FlowGraphIsNaNBlock",r)}_polymorphicIsNan(r){if(N(r))return isNaN(I(r));throw new Error(`Cannot get NaN of ${r}`)}}s("FlowGraphIsNaNBlock",Fr);class mr extends h{constructor(r){super(l,y,o=>this._polymorphicIsInf(o),"FlowGraphIsInfBlock",r)}_polymorphicIsInf(r){if(N(r))return!isFinite(I(r));throw new Error(`Cannot get isInf of ${r}`)}}s("FlowGraphIsInfBlock",mr);class Gr extends h{constructor(r){super(l,l,o=>this._polymorphicDegToRad(o),"FlowGraphDegToRadBlock",r)}_degToRad(r){return r*Math.PI/180}_polymorphicDegToRad(r){return c(r,this._degToRad)}}s("FlowGraphDegToRadBlock",Gr);class Br extends h{constructor(r){super(l,l,o=>this._polymorphicRadToDeg(o),"FlowGraphRadToDegBlock",r)}_radToDeg(r){return r*180/Math.PI}_polymorphicRadToDeg(r){return c(r,this._radToDeg)}}s("FlowGraphRadToDegBlock",Br);class gr extends h{constructor(r){super(p,p,o=>this._polymorphicSin(o),"FlowGraphSinBlock",r)}_polymorphicSin(r){return c(r,Math.sin)}}class br extends h{constructor(r){super(p,p,o=>this._polymorphicCos(o),"FlowGraphCosBlock",r)}_polymorphicCos(r){return c(r,Math.cos)}}class ro extends h{constructor(r){super(p,p,o=>this._polymorphicTan(o),"FlowGraphTanBlock",r)}_polymorphicTan(r){return c(r,Math.tan)}}class yr extends h{constructor(r){super(p,p,o=>this._polymorphicAsin(o),"FlowGraphASinBlock",r)}_polymorphicAsin(r){return c(r,Math.asin)}}s("FlowGraphASinBlock",yr);class kr extends h{constructor(r){super(p,p,o=>this._polymorphicAcos(o),"FlowGraphACosBlock",r)}_polymorphicAcos(r){return c(r,Math.acos)}}s("FlowGraphACosBlock",kr);class dr extends h{constructor(r){super(p,p,o=>this._polymorphicAtan(o),"FlowGraphATanBlock",r)}_polymorphicAtan(r){return c(r,Math.atan)}}s("FlowGraphATanBlock",dr);class xr extends i{constructor(r){super(l,l,l,(o,t)=>this._polymorphicAtan2(o,t),"FlowGraphATan2Block",r)}_polymorphicAtan2(r,o){return A(r,o,Math.atan2)}}s("FlowGraphATan2Block",xr);class _r extends h{constructor(r){super(l,l,o=>this._polymorphicSinh(o),"FlowGraphSinhBlock",r)}_polymorphicSinh(r){return c(r,Math.sinh)}}s("FlowGraphSinhBlock",_r);class Mr extends h{constructor(r){super(l,l,o=>this._polymorphicCosh(o),"FlowGraphCoshBlock",r)}_polymorphicCosh(r){return c(r,Math.cosh)}}s("FlowGraphCoshBlock",Mr);class Tr extends h{constructor(r){super(l,l,o=>this._polymorphicTanh(o),"FlowGraphTanhBlock",r)}_polymorphicTanh(r){return c(r,Math.tanh)}}s("FlowGraphTanhBlock",Tr);class Cr extends h{constructor(r){super(l,p,o=>this._polymorphicAsinh(o),"FlowGraphASinhBlock",r)}_polymorphicAsinh(r){return c(r,Math.asinh)}}s("FlowGraphASinhBlock",Cr);class Ar extends h{constructor(r){super(l,p,o=>this._polymorphicAcosh(o),"FlowGraphACoshBlock",r)}_polymorphicAcosh(r){return c(r,Math.acosh)}}s("FlowGraphACoshBlock",Ar);class Nr extends h{constructor(r){super(l,p,o=>this._polymorphicAtanh(o),"FlowGraphATanhBlock",r)}_polymorphicAtanh(r){return c(r,Math.atanh)}}s("FlowGraphATanhBlock",Nr);class Ir extends h{constructor(r){super(l,p,o=>this._polymorphicExp(o),"FlowGraphExponentialBlock",r)}_polymorphicExp(r){return c(r,Math.exp)}}s("FlowGraphExponentialBlock",Ir);class Rr extends h{constructor(r){super(l,p,o=>this._polymorphicLog(o),"FlowGraphLogBlock",r)}_polymorphicLog(r){return c(r,Math.log)}}s("FlowGraphLogBlock",Rr);class Sr extends h{constructor(r){super(l,p,o=>this._polymorphicLog2(o),"FlowGraphLog2Block",r)}_polymorphicLog2(r){return c(r,Math.log2)}}s("FlowGraphLog2Block",Sr);class vr extends h{constructor(r){super(l,p,o=>this._polymorphicLog10(o),"FlowGraphLog10Block",r)}_polymorphicLog10(r){return c(r,Math.log10)}}s("FlowGraphLog10Block",vr);class Dr extends h{constructor(r){super(l,p,o=>this._polymorphicSqrt(o),"FlowGraphSquareRootBlock",r)}_polymorphicSqrt(r){return c(r,Math.sqrt)}}s("FlowGraphSquareRootBlock",Dr);class Er extends h{constructor(r){super(l,p,o=>this._polymorphicCubeRoot(o),"FlowGraphCubeRootBlock",r)}_polymorphicCubeRoot(r){return c(r,Math.cbrt)}}s("FlowGraphCubeRootBlock",Er);class Lr extends i{constructor(r){super(l,p,p,(o,t)=>this._polymorphicPow(o,t),"FlowGraphPowerBlock",r)}_polymorphicPow(r,o){return A(r,o,Math.pow)}}s("FlowGraphPowerBlock",Lr);class Or extends h{constructor(r){super(u((r==null?void 0:r.valueType)||"FlowGraphInteger"),u((r==null?void 0:r.valueType)||"FlowGraphInteger"),o=>typeof o=="boolean"?!o:typeof o=="number"?~o:new G(~o.value),"FlowGraphBitwiseNotBlock",r)}}s("FlowGraphBitwiseNotBlock",Or);class zr extends i{constructor(r){super(u((r==null?void 0:r.valueType)||"FlowGraphInteger"),u((r==null?void 0:r.valueType)||"FlowGraphInteger"),u((r==null?void 0:r.valueType)||"FlowGraphInteger"),(o,t)=>{if(typeof o=="boolean"&&typeof t=="boolean")return o&&t;if(typeof o=="number"&&typeof t=="number")return o&t;if(typeof o=="object"&&typeof t=="object")return new G(o.value&t.value);throw new Error(`Cannot perform bitwise AND on ${o} and ${t}`)},"FlowGraphBitwiseAndBlock",r)}}s("FlowGraphBitwiseAndBlock",zr);class qr extends i{constructor(r){super(u((r==null?void 0:r.valueType)||"FlowGraphInteger"),u((r==null?void 0:r.valueType)||"FlowGraphInteger"),u((r==null?void 0:r.valueType)||"FlowGraphInteger"),(o,t)=>{if(typeof o=="boolean"&&typeof t=="boolean")return o||t;if(typeof o=="number"&&typeof t=="number")return o|t;if(typeof o=="object"&&typeof t=="object")return new G(o.value|t.value);throw new Error(`Cannot perform bitwise OR on ${o} and ${t}`)},"FlowGraphBitwiseOrBlock",r)}}s("FlowGraphBitwiseOrBlock",qr);class Vr extends i{constructor(r){super(u((r==null?void 0:r.valueType)||"FlowGraphInteger"),u((r==null?void 0:r.valueType)||"FlowGraphInteger"),u((r==null?void 0:r.valueType)||"FlowGraphInteger"),(o,t)=>{if(typeof o=="boolean"&&typeof t=="boolean")return o!==t;if(typeof o=="number"&&typeof t=="number")return o^t;if(typeof o=="object"&&typeof t=="object")return new G(o.value^t.value);throw new Error(`Cannot perform bitwise XOR on ${o} and ${t}`)},"FlowGraphBitwiseXorBlock",r)}}s("FlowGraphBitwiseXorBlock",Vr);class Pr extends i{constructor(r){super(m,m,m,(o,t)=>new G(o.value<<t.value),"FlowGraphBitwiseLeftShiftBlock",r)}}s("FlowGraphBitwiseLeftShiftBlock",Pr);class Qr extends i{constructor(r){super(m,m,m,(o,t)=>new G(o.value>>t.value),"FlowGraphBitwiseRightShiftBlock",r)}}s("FlowGraphBitwiseRightShiftBlock",Qr);class $r extends h{constructor(r){super(m,m,o=>new G(Math.clz32(o.value)),"FlowGraphLeadingZerosBlock",r)}}s("FlowGraphLeadingZerosBlock",$r);class jr extends h{constructor(r){super(m,m,o=>new G(o.value?31-Math.clz32(o.value&-o.value):32),"FlowGraphTrailingZerosBlock",r)}}s("FlowGraphTrailingZerosBlock",jr);function Zr(e){let r=0;for(;e;)r+=e&1,e>>=1;return r}class Xr extends h{constructor(r){super(m,m,o=>new G(Zr(o.value)),"FlowGraphOneBitsCounterBlock",r)}}s("FlowGraphOneBitsCounterBlock",Xr);export{U as FlowGraphAbsBlock,kr as FlowGraphAcosBlock,Ar as FlowGraphAcoshBlock,q as FlowGraphAddBlock,yr as FlowGraphAsinBlock,Cr as FlowGraphAsinhBlock,xr as FlowGraphAtan2Block,dr as FlowGraphAtanBlock,Nr as FlowGraphAtanhBlock,zr as FlowGraphBitwiseAndBlock,Pr as FlowGraphBitwiseLeftShiftBlock,Or as FlowGraphBitwiseNotBlock,qr as FlowGraphBitwiseOrBlock,Qr as FlowGraphBitwiseRightShiftBlock,Vr as FlowGraphBitwiseXorBlock,K as FlowGraphCeilBlock,lr as FlowGraphClampBlock,br as FlowGraphCosBlock,Mr as FlowGraphCoshBlock,Er as FlowGraphCubeRootBlock,Gr as FlowGraphDegToRadBlock,Q as FlowGraphDivideBlock,j as FlowGraphEBlock,nr as FlowGraphEqualityBlock,Ir as FlowGraphExpBlock,J as FlowGraphFloorBlock,g as FlowGraphFractionBlock,ir as FlowGraphGreaterThanBlock,wr as FlowGraphGreaterThanOrEqualBlock,X as FlowGraphInfBlock,mr as FlowGraphIsInfinityBlock,Fr as FlowGraphIsNanBlock,$r as FlowGraphLeadingZerosBlock,cr as FlowGraphLessThanBlock,ur as FlowGraphLessThanOrEqualBlock,vr as FlowGraphLog10Block,Sr as FlowGraphLog2Block,Rr as FlowGraphLogBlock,hr as FlowGraphMathInterpolationBlock,er as FlowGraphMaxBlock,or as FlowGraphMinBlock,rr as FlowGraphModuloBlock,P as FlowGraphMultiplyBlock,W as FlowGraphNaNBlock,b as FlowGraphNegationBlock,Xr as FlowGraphOneBitsCounterBlock,Z as FlowGraphPiBlock,Lr as FlowGraphPowerBlock,Br as FlowGraphRadToDegBlock,$ as FlowGraphRandomBlock,f as FlowGraphRoundBlock,ar as FlowGraphSaturateBlock,H as FlowGraphSignBlock,gr as FlowGraphSinBlock,_r as FlowGraphSinhBlock,Dr as FlowGraphSquareRootBlock,V as FlowGraphSubtractBlock,ro as FlowGraphTanBlock,Tr as FlowGraphTanhBlock,jr as FlowGraphTrailingZerosBlock,Y as FlowGraphTruncBlock};
//# sourceMappingURL=flowGraphMathBlocks.BgQ6jpID.js.map
