{"version": 3, "file": "glTFLoaderAnimation.CKms7EUf.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/glTFLoaderAnimation.js"], "sourcesContent": ["import { Animation } from \"@babylonjs/core/Animations/animation.js\";\nimport { Quaternion, Vector3 } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { SetInterpolationForKey } from \"./Extensions/objectModelMapping.js\";\n/** @internal */\nexport function getVector3(_target, source, offset, scale) {\n    return Vector3.FromArray(source, offset).scaleInPlace(scale);\n}\n/** @internal */\nexport function getQuaternion(_target, source, offset, scale) {\n    return Quaternion.FromArray(source, offset).scaleInPlace(scale);\n}\n/** @internal */\nexport function getWeights(target, source, offset, scale) {\n    const value = new Array(target._numMorphTargets);\n    for (let i = 0; i < value.length; i++) {\n        value[i] = source[offset++] * scale;\n    }\n    return value;\n}\n/** @internal */\nexport class AnimationPropertyInfo {\n    /** @internal */\n    constructor(type, name, getValue, getStride) {\n        this.type = type;\n        this.name = name;\n        this.getValue = getValue;\n        this.getStride = getStride;\n    }\n    _buildAnimation(name, fps, keys) {\n        const babylonAnimation = new Animation(name, this.name, fps, this.type);\n        babylonAnimation.setKeys(keys);\n        return babylonAnimation;\n    }\n}\n/** @internal */\nexport class TransformNodeAnimationPropertyInfo extends AnimationPropertyInfo {\n    /** @internal */\n    buildAnimations(target, name, fps, keys) {\n        const babylonAnimations = [];\n        babylonAnimations.push({ babylonAnimatable: target._babylonTransformNode, babylonAnimation: this._buildAnimation(name, fps, keys) });\n        return babylonAnimations;\n    }\n}\n/** @internal */\nexport class WeightAnimationPropertyInfo extends AnimationPropertyInfo {\n    buildAnimations(target, name, fps, keys) {\n        const babylonAnimations = [];\n        if (target._numMorphTargets) {\n            for (let targetIndex = 0; targetIndex < target._numMorphTargets; targetIndex++) {\n                const babylonAnimation = new Animation(`${name}_${targetIndex}`, this.name, fps, this.type);\n                babylonAnimation.setKeys(keys.map((key) => ({\n                    frame: key.frame,\n                    inTangent: key.inTangent ? key.inTangent[targetIndex] : undefined,\n                    value: key.value[targetIndex],\n                    outTangent: key.outTangent ? key.outTangent[targetIndex] : undefined,\n                    interpolation: key.interpolation,\n                })));\n                if (target._primitiveBabylonMeshes) {\n                    for (const babylonMesh of target._primitiveBabylonMeshes) {\n                        if (babylonMesh.morphTargetManager) {\n                            const morphTarget = babylonMesh.morphTargetManager.getTarget(targetIndex);\n                            const babylonAnimationClone = babylonAnimation.clone();\n                            morphTarget.animations.push(babylonAnimationClone);\n                            babylonAnimations.push({ babylonAnimatable: morphTarget, babylonAnimation: babylonAnimationClone });\n                        }\n                    }\n                }\n            }\n        }\n        return babylonAnimations;\n    }\n}\nSetInterpolationForKey(\"/nodes/{}/translation\", [new TransformNodeAnimationPropertyInfo(Animation.ANIMATIONTYPE_VECTOR3, \"position\", getVector3, () => 3)]);\nSetInterpolationForKey(\"/nodes/{}/rotation\", [new TransformNodeAnimationPropertyInfo(Animation.ANIMATIONTYPE_QUATERNION, \"rotationQuaternion\", getQuaternion, () => 4)]);\nSetInterpolationForKey(\"/nodes/{}/scale\", [new TransformNodeAnimationPropertyInfo(Animation.ANIMATIONTYPE_VECTOR3, \"scaling\", getVector3, () => 3)]);\nSetInterpolationForKey(\"/nodes/{}/weights\", [new WeightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"influence\", getWeights, (target) => target._numMorphTargets)]);\n//# sourceMappingURL=glTFLoaderAnimation.js.map"], "names": ["getVector3", "_target", "source", "offset", "scale", "Vector3", "getQuaternion", "Quaternion", "getWeights", "target", "value", "AnimationPropertyInfo", "type", "name", "getValue", "<PERSON><PERSON><PERSON><PERSON>", "fps", "keys", "babylonAnimation", "Animation", "TransformNodeAnimationPropertyInfo", "babylonAnimations", "WeightAnimationPropertyInfo", "targetIndex", "key", "<PERSON><PERSON><PERSON><PERSON>", "morph<PERSON>arget", "babylonAnimationClone", "SetInterpolationForKey"], "mappings": "4GAIO,SAASA,EAAWC,EAASC,EAAQC,EAAQC,EAAO,CACvD,OAAOC,EAAQ,UAAUH,EAAQC,CAAM,EAAE,aAAaC,CAAK,CAC/D,CAEO,SAASE,EAAcL,EAASC,EAAQC,EAAQC,EAAO,CAC1D,OAAOG,EAAW,UAAUL,EAAQC,CAAM,EAAE,aAAaC,CAAK,CAClE,CAEO,SAASI,EAAWC,EAAQP,EAAQC,EAAQC,EAAO,CACtD,MAAMM,EAAQ,IAAI,MAAMD,EAAO,gBAAgB,EAC/C,QAAS,EAAI,EAAG,EAAIC,EAAM,OAAQ,IAC9BA,EAAM,CAAC,EAAIR,EAAOC,GAAQ,EAAIC,EAElC,OAAOM,CACX,CAEO,MAAMC,CAAsB,CAE/B,YAAYC,EAAMC,EAAMC,EAAUC,EAAW,CACzC,KAAK,KAAOH,EACZ,KAAK,KAAOC,EACZ,KAAK,SAAWC,EAChB,KAAK,UAAYC,CACpB,CACD,gBAAgBF,EAAMG,EAAKC,EAAM,CAC7B,MAAMC,EAAmB,IAAIC,EAAUN,EAAM,KAAK,KAAMG,EAAK,KAAK,IAAI,EACtE,OAAAE,EAAiB,QAAQD,CAAI,EACtBC,CACV,CACL,CAEO,MAAME,UAA2CT,CAAsB,CAE1E,gBAAgBF,EAAQI,EAAMG,EAAKC,EAAM,CACrC,MAAMI,EAAoB,CAAA,EAC1B,OAAAA,EAAkB,KAAK,CAAE,kBAAmBZ,EAAO,sBAAuB,iBAAkB,KAAK,gBAAgBI,EAAMG,EAAKC,CAAI,CAAG,CAAA,EAC5HI,CACV,CACL,CAEO,MAAMC,UAAoCX,CAAsB,CACnE,gBAAgBF,EAAQI,EAAMG,EAAKC,EAAM,CACrC,MAAMI,EAAoB,CAAA,EAC1B,GAAIZ,EAAO,iBACP,QAASc,EAAc,EAAGA,EAAcd,EAAO,iBAAkBc,IAAe,CAC5E,MAAML,EAAmB,IAAIC,EAAU,GAAGN,CAAI,IAAIU,CAAW,GAAI,KAAK,KAAMP,EAAK,KAAK,IAAI,EAQ1F,GAPAE,EAAiB,QAAQD,EAAK,IAAKO,IAAS,CACxC,MAAOA,EAAI,MACX,UAAWA,EAAI,UAAYA,EAAI,UAAUD,CAAW,EAAI,OACxD,MAAOC,EAAI,MAAMD,CAAW,EAC5B,WAAYC,EAAI,WAAaA,EAAI,WAAWD,CAAW,EAAI,OAC3D,cAAeC,EAAI,aACtB,EAAC,CAAC,EACCf,EAAO,yBACP,UAAWgB,KAAehB,EAAO,wBAC7B,GAAIgB,EAAY,mBAAoB,CAChC,MAAMC,EAAcD,EAAY,mBAAmB,UAAUF,CAAW,EAClEI,EAAwBT,EAAiB,QAC/CQ,EAAY,WAAW,KAAKC,CAAqB,EACjDN,EAAkB,KAAK,CAAE,kBAAmBK,EAAa,iBAAkBC,CAAqB,CAAE,CACrG,EAGZ,CAEL,OAAON,CACV,CACL,CACAO,EAAuB,wBAAyB,CAAC,IAAIR,EAAmCD,EAAU,sBAAuB,WAAYnB,EAAY,IAAM,CAAC,CAAC,CAAC,EAC1J4B,EAAuB,qBAAsB,CAAC,IAAIR,EAAmCD,EAAU,yBAA0B,qBAAsBb,EAAe,IAAM,CAAC,CAAC,CAAC,EACvKsB,EAAuB,kBAAmB,CAAC,IAAIR,EAAmCD,EAAU,sBAAuB,UAAWnB,EAAY,IAAM,CAAC,CAAC,CAAC,EACnJ4B,EAAuB,oBAAqB,CAAC,IAAIN,EAA4BH,EAAU,oBAAqB,YAAaX,EAAaC,GAAWA,EAAO,gBAAgB,CAAC,CAAC", "x_google_ignoreList": [0]}