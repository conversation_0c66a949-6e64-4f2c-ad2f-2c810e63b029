import { c as create_ssr_component, e as escape } from './ssr-C3HYbsxA.js';

const c={code:".gallery.svelte-1ayixqk{padding:var(--size-1) var(--size-2)}",map:'{"version":3,"file":"Example.svelte","sources":["Example.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let value;\\nexport let type;\\nexport let selected = false;\\n<\/script>\\n\\n<div\\n\\tclass:table={type === \\"table\\"}\\n\\tclass:gallery={type === \\"gallery\\"}\\n\\tclass:selected\\n>\\n\\t<pre>{JSON.stringify(value, null, 2)}</pre>\\n</div>\\n\\n<style>\\n\\t.gallery {\\n\\t\\tpadding: var(--size-1) var(--size-2);\\n\\t}</style>\\n"],"names":[],"mappings":"AAcC,uBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CACpC"}'},d=create_ssr_component((r,e,l,i)=>{let{value:a}=e,{type:t}=e,{selected:s=!1}=e;return e.value===void 0&&l.value&&a!==void 0&&l.value(a),e.type===void 0&&l.type&&t!==void 0&&l.type(t),e.selected===void 0&&l.selected&&s!==void 0&&l.selected(s),r.css.add(c),`<div class="${["svelte-1ayixqk",(t==="table"?"table":"")+" "+(t==="gallery"?"gallery":"")+" "+(s?"selected":"")].join(" ").trim()}"><pre>${escape(JSON.stringify(a,null,2))}</pre> </div>`});

export { d as default };
//# sourceMappingURL=Example22-DUXZ9Y7A.js.map
