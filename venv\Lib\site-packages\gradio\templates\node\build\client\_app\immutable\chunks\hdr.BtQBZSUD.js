import{V as l}from"./index.BoI39RQH.js";class b{static ConvertPanoramaToCubemap(e,r,t,n,a=!1){if(!e)throw"ConvertPanoramaToCubemap: input cannot be null";if(e.length!=r*t*3)throw"ConvertPanoramaToCubemap: input size is wrong";const h=this.CreateCubemapTexture(n,this.FACE_FRONT,e,r,t,a),u=this.CreateCubemapTexture(n,this.FACE_BACK,e,r,t,a),s=this.CreateCubemapTexture(n,this.FACE_LEFT,e,r,t,a),c=this.CreateCubemapTexture(n,this.FACE_RIGHT,e,r,t,a),w=this.CreateCubemapTexture(n,this.FACE_UP,e,r,t,a),i=this.CreateCubemapTexture(n,this.FACE_DOWN,e,r,t,a);return{front:h,back:u,left:s,right:c,up:w,down:i,size:n,type:1,format:4,gammaSpace:!1}}static CreateCubemapTexture(e,r,t,n,a,h=!1){const u=new ArrayBuffer(e*e*4*3),s=new Float32Array(u),c=h?Math.max(1,Math.round(n/4/e)):1,w=1/c,i=w*w,f=r[1].subtract(r[0]).scale(w/e),m=r[3].subtract(r[2]).scale(w/e),C=1/e;let M=0;for(let d=0;d<e;d++)for(let F=0;F<c;F++){let g=r[0],T=r[2];for(let R=0;R<e;R++)for(let P=0;P<c;P++){const E=T.subtract(g).scale(M).add(g);E.normalize();const A=this.CalcProjectionSpherical(E,t,n,a);s[d*e*3+R*3+0]+=A.r*i,s[d*e*3+R*3+1]+=A.g*i,s[d*e*3+R*3+2]+=A.b*i,g=g.add(f),T=T.add(m)}M+=C*w}return s}static CalcProjectionSpherical(e,r,t,n){let a=Math.atan2(e.z,e.x);const h=Math.acos(e.y);for(;a<-Math.PI;)a+=2*Math.PI;for(;a>Math.PI;)a-=2*Math.PI;let u=a/Math.PI;const s=h/Math.PI;u=u*.5+.5;let c=Math.round(u*t);c<0?c=0:c>=t&&(c=t-1);let w=Math.round(s*n);w<0?w=0:w>=n&&(w=n-1);const i=n-w-1,f=r[i*t*3+c*3+0],m=r[i*t*3+c*3+1],C=r[i*t*3+c*3+2];return{r:f,g:m,b:C}}}b.FACE_LEFT=[new l(-1,-1,-1),new l(1,-1,-1),new l(-1,1,-1),new l(1,1,-1)];b.FACE_RIGHT=[new l(1,-1,1),new l(-1,-1,1),new l(1,1,1),new l(-1,1,1)];b.FACE_FRONT=[new l(1,-1,-1),new l(1,-1,1),new l(1,1,-1),new l(1,1,1)];b.FACE_BACK=[new l(-1,-1,1),new l(-1,-1,-1),new l(-1,1,1),new l(-1,1,-1)];b.FACE_DOWN=[new l(1,1,-1),new l(1,1,1),new l(-1,1,-1),new l(-1,1,1)];b.FACE_UP=[new l(-1,-1,-1),new l(-1,-1,1),new l(1,-1,-1),new l(1,-1,1)];function x(o,e){return e>1023?o*Math.pow(2,1023)*Math.pow(2,e-1023):e<-1074?o*Math.pow(2,-1074)*Math.pow(2,e+1074):o*Math.pow(2,e)}function _(o,e,r,t,n,a){n>0?(n=x(1,n-136),o[a+0]=e*n,o[a+1]=r*n,o[a+2]=t*n):(o[a+0]=0,o[a+1]=0,o[a+2]=0)}function B(o,e){let r="",t="";for(let n=e;n<o.length-e&&(t=String.fromCharCode(o[n]),t!=`
`);n++)r+=t;return r}function D(o){let e=0,r=0,t=B(o,0);if(t[0]!="#"||t[1]!="?")throw"Bad HDR Format.";let n=!1,a=!1,h=0;do h+=t.length+1,t=B(o,h),t=="FORMAT=32-bit_rle_rgbe"?a=!0:t.length==0&&(n=!0);while(!n);if(!a)throw"HDR Bad header format, unsupported FORMAT";h+=t.length+1,t=B(o,h);const s=/^-Y (.*) \+X (.*)$/g.exec(t);if(!s||s.length<3)throw"HDR Bad header format, no size";if(r=parseInt(s[2]),e=parseInt(s[1]),r<8||r>32767)throw"HDR Bad header format, unsupported size";return h+=t.length+1,{height:e,width:r,dataPosition:h}}function v(o,e,r=!1){const t=new Uint8Array(o),n=D(t),a=G(t,n);return b.ConvertPanoramaToCubemap(a,n.width,n.height,e,r)}function G(o,e){return L(o,e)}function L(o,e){let r=e.height;const t=e.width;let n,a,h,u,s,c=e.dataPosition,w=0,i=0,f=0;const m=new ArrayBuffer(t*4),C=new Uint8Array(m),M=new ArrayBuffer(e.width*e.height*4*3),d=new Float32Array(M);for(;r>0;){if(n=o[c++],a=o[c++],h=o[c++],u=o[c++],n!=2||a!=2||h&128||e.width<8||e.width>32767)return O(o,e);if((h<<8|u)!=t)throw"HDR Bad header format, wrong scan line width";for(w=0,f=0;f<4;f++)for(i=(f+1)*t;w<i;)if(n=o[c++],a=o[c++],n>128){if(s=n-128,s==0||s>i-w)throw"HDR Bad Format, bad scanline data (run)";for(;s-- >0;)C[w++]=a}else{if(s=n,s==0||s>i-w)throw"HDR Bad Format, bad scanline data (non-run)";if(C[w++]=a,--s>0)for(let F=0;F<s;F++)C[w++]=o[c++]}for(f=0;f<t;f++)n=C[f],a=C[f+t],h=C[f+2*t],u=C[f+3*t],_(d,n,a,h,u,(e.height-r)*t*3+f*3);r--}return d}function O(o,e){let r=e.height;const t=e.width;let n,a,h,u,s,c=e.dataPosition;const w=new ArrayBuffer(e.width*e.height*4*3),i=new Float32Array(w);for(;r>0;){for(s=0;s<e.width;s++)n=o[c++],a=o[c++],h=o[c++],u=o[c++],_(i,n,a,h,u,(e.height-r)*t*3+s*3);r--}return i}export{v as G,D as R,G as a};
//# sourceMappingURL=hdr.BtQBZSUD.js.map
