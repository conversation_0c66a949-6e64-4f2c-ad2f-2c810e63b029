{"version": 3, "mappings": ";gpCAOyB,oEAorBV,YAAAA,KAAO,UAAU,EACvB,MAAAA,KAAO,IAAI,EACbA,EAAc,0LAFNC,EAAA,kBAAAD,KAAO,UAAU,EACvBC,EAAA,YAAAD,KAAO,IAAI,gBACbA,EAAc,gsBAcuBA,EAAK,2CAALA,EAAK,gFAS1B,sQAJhBA,EAAO,IAAAE,GAAAF,CAAA,uFAFZG,EAAoCC,EAAAC,EAAAC,CAAA,iDAE/BN,EAAO,yVACSA,EAAO,mDAA3BG,EAA+BC,EAAAG,EAAAD,CAAA,+BAAXN,EAAO,yDAxBxBA,EAAc,KAAAQ,GAAAR,CAAA,IAQdA,EAAsB,IAAAS,GAAAT,CAAA,wCAUI,sFAE1B,OAAAA,MAASA,EAAU,qNApBnBA,EAAc,mHAQdA,EAAsB,8oBAbX,WACP,4mBAjqBE,OAAAU,CAAA,EAAAC,EACA,GAAAC,CAAA,EAAAD,EACA,GAAAE,CAAA,EAAAF,GACA,MAAAG,EAAuB,MAAAH,GAMvB,MAAAI,EAAuB,MAAAJ,GACvB,QAAAK,GAAyB,MAAAL,GACzB,QAAAM,GAAyB,MAAAN,GACzB,YAAAO,GAA6B,MAAAP,GAC7B,MAAAQ,EAAgC,MAAAR,GAChC,YAAAS,EAMI,QAAAT,GACJ,UAAAU,EAA2C,MAAAV,GAC3C,MAAAW,EAAiC,MAAAX,GACjC,MAAAY,EAA+C,MAAAZ,GAK/C,cAAAa,GAA+B,MAAAb,GAC/B,cAAAc,GAA+B,MAAAd,GAC/B,sBAAAe,GAAwB,IAAAf,GACxB,QAAAgB,GAAyB,MAAAhB,GACzB,KAAAiB,GAAkD,MAAAjB,GAClD,QAAAkB,EAA8C,QAAAlB,GAC9C,uBAAAmB,GAAyB,IAAAnB,EAChCoB,EAAa,YAERC,GACRC,MASIA,IAAU,IACN,qBACGA,IAAU,KACb,sBACGA,IAAU,IACX,aAAOpB,EAAG,MAAO,gBAChBoB,IAAU,KACX,aAAOpB,EAAG,MAAO,iBAChBoB,IAAU,KACb,YACG,SAAM,QAAQA,CAAK,EACtBA,aAIE,YAAAC,GAAc,IAAAvB,EACrBwB,GAGO,QAAAC,EAAA,EAAAzB,EAQP0B,EACAC,GAAsB,SACpBC,GACL,GAAG,EACH,EAAG,GACH,EAAG,GAAK,GACR,EAAG,GAAK,GAAK,IASV,IAAAC,GACAC,EAaK,SAAAC,GACRC,EACAC,EACAC,EACAC,EACAC,EACAC,MAGCL,EAAK,OAAS,KACdxB,IAAU,MACVT,GAAO,OAAS,QAChBA,GAAO,UAAUE,CAAC,IAAM,UAEjB,OAAA+B,QAEFM,EAAY,IACd,IAAAC,EAAA,GAeAH,IAXAA,YAAyBC,IAAU,SACtCL,EAAK,QAASQ,GAAA,CACT,IAAAC,EAAUD,EAAIP,CAAO,GACrBG,YAAyBK,EAAUL,KACtCA,EAAUK,IAEPJ,YAAuBI,EAAUJ,KACpCA,EAAQI,KAIPL,YAAyBC,IAAU,OAC/B,OAAAL,EAGF,MAAAU,GADUL,EAAQD,GACGE,EAC3BN,EAAK,SAASQ,EAAKG,IAAA,CACZ,MAAAF,EAAUD,EAAIP,CAAO,EACrBW,EAAUJ,EAAIN,CAAO,EACrBW,EACLV,IAAgB,KAAQK,EAAIL,CAAW,EAAe,MACjDW,EAAY,KAAK,OAAOL,EAAWL,GAAsBM,CAAQ,EACnEH,EAAuBM,CAAW,IAAM,SAC3CN,EAAuBM,CAAW,MAEnCN,EAAuBM,CAAW,EAAEC,CAAS,EAAIP,EAChDM,CACD,EAAEC,CAAS,GACV,MACA,OAAO,kBACP,KACA,OAAO,mBAEJF,EAAUL,EAAuBM,CAAW,EAAEC,CAAS,EAAE,CAAC,IAC7DP,EAAuBM,CAAW,EAAEC,CAAS,EAAE,CAAC,EAAIH,EACpDJ,EAAuBM,CAAW,EAAEC,CAAS,EAAE,CAAC,EAAIF,GAEjDA,EAAUL,EAAuBM,CAAW,EAAEC,CAAS,EAAE,CAAC,IAC7DP,EAAuBM,CAAW,EAAEC,CAAS,EAAE,CAAC,EAAIH,EACpDJ,EAAuBM,CAAW,EAAEC,CAAS,EAAE,CAAC,EAAIF,KAGhD,MAAAG,EAAA,GACN,cAAO,OAAOR,CAAsB,EAAE,QAASS,GAAA,CAC9CA,EAAK,QAAU,EAAAC,EAAWC,EAAGC,EAAWC,CAAE,KACrC,IAAAC,EAAA,GACAJ,IAAc,MAAQE,IAAc,KACvCE,EACC,MAAK,IAAIJ,EAAWE,CAAS,EAC7B,KAAK,IAAIF,EAAWE,CAAS,GAEpBF,IAAc,KACxBI,EAAA,CAAWJ,CAAS,EACVE,IAAc,OACxBE,EAAA,CAAWF,CAAS,GAErBE,EAAQ,QAASC,IAAA,CAChBP,EAAiB,KAAKf,EAAKsB,EAAK,SAI5BP,EAEC,SAAAQ,GACRvB,EACAI,EACAC,OAIIJ,EAAUD,EAAK,QAAQ,QAAQ/B,CAAC,EAChCiC,EAAUF,EAAK,QAAQ,QAAQ9B,CAAC,EAChCiC,EAAchC,EAAQ6B,EAAK,QAAQ,QAAQ7B,CAAK,EAAI,KACpDqD,EAAYxB,EAAK,KAEjBI,eAAyBC,IAAU,QAChC,MAAAoB,EAAczB,EAAK,UAAU/B,CAAC,IAAM,WAAa,IAAO,EACxDyD,EAAWtB,EAAUqB,EACrBE,EAAStB,EAAQoB,EACnB,IAAAG,EAAA,GACAC,EAAA,SACEC,EAAaN,EAAU,QAAQhB,EAAKG,IAAA,CACnC,MAAAF,EAAUD,EAAIP,CAAO,EACrBY,EACLV,IAAgB,KAAQK,EAAIL,CAAW,EAAe,aAEtDM,EAAUiB,IACTE,EAAqBf,CAAW,YAChCJ,EAAUmB,EAAqBf,CAAW,EAAE,CAAC,KAE9Ce,EAAqBf,CAAW,EAAK,CAAAF,EAAGF,CAAO,GAG/CA,EAAUkB,IACTE,EAAmBhB,CAAW,YAC9BJ,EAAUoB,EAAmBhB,CAAW,EAAE,CAAC,KAE5CgB,EAAmBhB,CAAW,EAAK,CAAAF,EAAGF,CAAO,GAEvCA,GAAWiB,GAAYjB,GAAWkB,IAE1CH,EAAA,IACI,OAAO,OAAOI,CAAoB,EAAE,MAAMjB,EAAGO,CAAC,IAAMM,EAAUb,CAAC,GAC/D,GAAAZ,GACF+B,EACA7B,EACAC,EACAC,EACAuB,EACAC,CAAA,KAEE,OAAO,OAAOE,CAAkB,EAAE,MAAMlB,EAAGO,CAAC,IAAMM,EAAUb,CAAC,SAGjEa,EAAYzB,GACXyB,EACAvB,EACAC,EACAC,SAEA,eAIEjB,GAAW,OAAS,MAAM,QAAQA,CAAO,EACrCsC,EAAU,IAAKhB,GAAA,CACf,MAAAuB,EAAA,GACN,OAAA/B,EAAK,QAAQ,SAASgC,EAAKrB,IAAA,CAC1BoB,EAAIC,CAAG,EAAIxB,EAAIG,CAAC,IAEVoB,IAGFP,EAAU,IAAKhB,GAAA,OACfuB,EACJ,EAAA9D,CAAC,EAAGuC,EAAIP,CAAO,GACf/B,CAAC,EAAGsC,EAAIN,CAAO,GAEb,OAAA/B,GAASgC,IAAgB,OAC5B4B,EAAI5D,CAAK,EAAIqC,EAAIL,CAAW,GAEtB4B,QAILE,GAAYlE,EAMV,MAAAmE,GAAA,OAAoB,OAAW,IACjC,IAAAC,EAIAC,EACAC,GAAU,GACVC,GACAC,GACAC,GAEAC,GACW,eAAAC,IAAA,CACV,GAAA/C,GAAA,CACHgD,GAAkB,aAGfP,GACHA,EAAK,YAEDrE,GAAU,CAAAoE,EAAA,OACfG,GAAYH,EAAc,YAC1BI,GAAaJ,EAAc,mBACrBS,EAAOC,KACRD,IACLJ,GAAA,IAAqB,eAAgBM,GAAA,CAC/B,CAAAA,EAAG,CAAC,EAAE,UAAYA,EAAG,CAAC,EAAE,kBAAkB,eAE9CR,KAAc,GACdH,EAAc,cAAgB,GAC9BpE,EAAM,UAAUE,CAAC,IAAM,UAGvByE,KAEAN,EAAK,OAAO,QAASU,EAAG,CAAC,EAAE,OAAO,WAAW,EAAE,MAE5CP,KAAeO,EAAG,CAAC,EAAE,OAAO,cAAgB1D,IAC/CgD,EAAK,OAAO,SAAUU,EAAG,CAAC,EAAE,OAAO,YAAY,EAAE,MACjDP,GAAaO,EAAG,CAAC,EAAE,OAAO,iBAIvBL,KACJA,IAAA,MAAAM,GAAA,WAA0B,iCAAY,oEAAG,SAE1CN,GAAUN,EAAeS,EAAA,CAAQ,QAAS,KAAS,KAAe,SAAAI,EAAA,CACjEC,EAAA,GAAAb,EAAOY,EAAO,MAEdR,GAAe,QAAQL,CAAa,EAChC,IAAAe,EACAC,EAAiB,EACrBf,EAAK,iBAAiB,gBACrB3C,GAAO,SAAS,cAAc,IAG/B0C,EAAc,iBACb,YACU,SAAAiB,EAAA,CACLA,EAAE,OAAS,GACdA,EAAE,kBAGJ,IAEG7D,IACH6C,EAAK,kBAAkB,iBAAmBlB,EAAGnD,MACxC,KAAK,MAAQoF,EAAiB,MAClCxD,GAAsB,GAClB,OAAO,KAAK5B,CAAK,EAAE,SAAW,UAClC,aAAamF,CAAe,EACxB,IAAAG,EAA0BtF,EAAM,OAAO,KAAKA,CAAK,EAAE,CAAC,GACpDuF,IACHD,EAAA,CAASA,EAAM,CAAC,EAAI,IAAMA,EAAM,CAAC,EAAI,GAAI,GAE1CH,EAAkB,sBACjBvD,GAAsB,GACtBwD,EAAiB,KAAK,MACtB1D,GAAO,SAAS,UACf,MAAO4D,EACP,MAAOA,EACP,SAAU,KAEPV,KACHA,GAAkB,GAClBD,OAEC,cAMHC,GAAkB,GACtBY,GAAA,UACClB,GAAU,cAETA,GAAU,IACND,GACHA,EAAK,WAEFI,IACHA,GAAe,gBA2BT,SAAAK,IAAA,CACH,IAAA9E,GAAA,CAAUyF,EAAuB,gBAClCC,EAAeD,EAAe,iBAAiB,gBAAgB,EAC/DE,EAAkBF,EAAe,iBAAiB,mBAAmB,EACrEG,EAAqBH,EAAe,iBACvC,0BAEGI,EAAcJ,EAAe,WAC7BK,EAAeL,EAAe,iBACjC,mCAaKM,EAAkBC,GAChBA,EAAK,SAAS,IAAI,EAAI,WAAWA,EAAK,MAAM,IAAK,CAAK,SAE1DC,EAAeF,EAClBN,EAAe,iBAAiB,WAAW,GAExCS,EAAeH,EAClBN,EAAe,iBAAiB,WAAW,UAK3C,QAAS,uDACT,WAAY,cACZ,QACC,SAAY,MAAM,MAAO,SAAU,WACnC,MACC,UAAWI,EACX,WAAYF,EACZ,UAAWE,EACX,WAAYF,EACZ,aAAc,EACd,UAAWC,EACX,cAAeM,EACf,UAAWN,EACX,gBAAiB,SACjB,cAAeM,EACf,gBAAiB,SACjB,OAAQ,GACR,WAAY,GAEb,QACC,WAAYP,EACZ,UAAWE,EACX,WAAYF,EACZ,UAAWE,EACX,gBAAiB,SACjB,cAAeK,EACf,gBAAiB,SACjB,OAAQ,GAET,OACC,MAAOP,EACP,KAAME,EACN,SAAUI,EACV,WAAYH,EACZ,OAAQ,UAET,MAAQ,OAAQF,CAAA,EAChB,MACC,OAAQ5F,EAAM,OAAS,MAAQ0F,EAAe,OAC9C,KAAM1F,EAAM,OAAS,MAAQ0F,EAAe,OAC5C,OAAQ,cAGV,MAAQ,KAAM,QACd,UACC,KAAMjE,EAAA,EAEP,OAAQ,UAAYzB,EAAM,OAAS,OAAU,QAAO,MAAS,IAC3DmG,KAEC,UACC,KACCnG,EAAM,OAAS,OACZmG,GAAQ,QAEP,WACC,MAAO,GACP,MAAO,YACP,MAAO,GAER,MAAO,IAGP,WAAa,MAAO,GAAO,MAAO,QAAS,MAAO,KAClD,MAAO,GAER,OACJ,QACCA,IAAS,OACN,QAEA,WAAa,MAAO,GAAO,MAAO,QAAS,MAAO,GAClD,MAAO,GAEX,GACC,MACK,GAAArF,KAAkB,OAAU,WAAYA,EAAA,EAC5C,OAAQE,GACR,MAAOA,IAER,MAAOd,EACP,MAAOI,IAAWJ,EAClB,KAAMF,EAAM,UAAUE,CAAC,EACvB,MAAOkG,EAAA,CAAW,OAAQA,CAAW,SACrC,IAAKzE,EAAA,CAAW,KAAMA,CAAW,SACjC,KAAMJ,GAEP,GACC,KAAMR,IAAkB,WAAYA,EAAA,KACpC,MAAOZ,EACP,MAAOI,IAAWJ,EAClB,KAAMH,EAAM,UAAUG,CAAC,EACvB,OACC,KAAM,GACN,UAAWkG,GAAW,OACtB,UAAWC,GAAS,QAErB,UAAWvE,EAAcD,GAAe,QAEzC,MAAO1B,GAEJ,MAAOA,EACP,OAAU,QAAQ,SAAU,MAAOI,EAAA,EACnC,MACCR,EAAM,UAAUI,CAAK,IAAM,WAExB,OAAQmG,EACR,MAAO5F,EACJ4F,EAAc,IAAKC,GAAM7F,EAAU6F,CAAC,GACpC,SAGH,OACC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvC,IAAKC,GACNhB,EAAe,iBAAiB,aAAegB,CAAC,GAEjD,YAAa,OAEjB,KAAMzG,EAAM,UAAUI,CAAK,GAE3B,OACH,QACCe,GAAW,OACR,SAGC,MAAOhB,EACP,KAAMH,EAAM,UAAUG,CAAC,EACvB,UAAW4B,EAAcD,GAAe,OACxC,MAAOvB,IAAWJ,IAGlB,MAAOD,EACP,KAAMF,EAAM,UAAUE,CAAC,EACvB,MAAOI,IAAWJ,EAClB,OAAQqF,EAAa,oBAAsB,OAC3C,IAAK5D,EAAA,CAAW,KAAMA,CAAW,UAE9B,GAAAvB,IAGA,MAAOA,EACP,KAAMJ,EAAM,UAAUI,CAAK,UAI3Be,IAAY,UAEbnB,GAAO,QACN,OACCiE,GACAA,IAAQ/D,GACR+D,IAAQ9D,GACR8D,IAAQ7D,IACPe,IAAY,OAASA,EAAQ,SAAS8C,CAAG,IAE3C,IAAKyC,IAAA,CACL,MAAOA,EACP,KAAM1G,EAAM,UAAU0G,CAAM,OAIrC,cACA,MAAQ,KAAM,GAAM,KAAMP,IAAS,QAAU,QAAUnG,EAAM,MAC7D,KAAMmG,KAKT,QACK,GAAAnG,EAAM,OAAS,SAGf,KAAM,YACN,QACC,MAAO,WACP,OAAQI,GAASA,CAAK,KACtB,QAAS,GACT,GAAI,YACJ,KAAM,SAEP,OAAQ,OAAO,IAGf,KAAM,QACN,QACC,MAAO,WACP,QAAS,GACT,GAAI,YACJ,KAAM,SAEP,OAAQ,OAAO,OAIf,GAAAoB,KAGA,KAAM,QACN,QACC,WAAY,GAAG,EACf,MAAQ,KAAM,OAAQ,YAAa,GAAK,OAAQ,QAChD,KAAM,YAEP,OAAQ,MAAM,QAKnB,MAAO4C,EAAc,YACrB,OAAQuC,IAAUtF,EAAa,YAAc,OAC7C,MAAOhB,GAAS,YAKP,MAAAuG,GAAQ,WAAA3G,GACR,QAAA4G,GAAU,IAAA5G,EACV,cAAA6G,GAAA,IAAA7G,GACA,QAAA8G,GAAU,IAAA9G,EACV,YAAA+G,EAAA,EAAA/G,GACA,MAAAgH,GAAuB,MAAAhH,GACvB,UAAAiH,GAAgC,QAAAjH,GAChC,eAAAkH,GAA4C,QAAAlH,GAC5C,OAAA0G,GAA6B,QAAA1G,EAmBf,MAAAmH,GAAA,IAAA1F,GAAO,SAAS,eAAgByF,EAAc,OAOlD,OAAAE,KAAM,CACvBnC,EAAA,GAAA7D,EAAagG,CAAM,8CAQNjD,EAAakD,wrCAlqB9BpC,EAAA,GAAGtE,EAAQA,GAAS,2BAEpBsE,EAAA,IAAI7C,EAASC,CAAK,EAAI1B,IAAU,KAAQ,cAAoB,EAAIA,EAAAyB,GAAA6C,EAAA,GAAA5C,CAAA,EAAA4C,EAAA,GAAAtE,CAAA,yCAkPhEsE,EAAA,GAAGzD,GAAQzB,EAAQwD,GAAcxD,EAAOqC,EAASC,CAAK,8CAxQnDiE,EACFnG,GAASJ,GAASA,EAAM,UAAUI,CAAK,IAAM,UAC1C,MAAM,SAAS,IAAIqB,GAAM,IAAK8F,GAAMA,EAAEnH,CAAK,+BAmB/C8E,EAAA,GAAGrE,EAAQA,GAAS,4BAEpBqE,EAAA,IAAImB,EAASC,CAAK,EAAIzF,GAAA,cAA8B,EAAAwF,GAAAnB,EAAA,GAAAoB,CAAA,EAAApB,EAAA,GAAArE,CAAA,sBAkCjDU,EAAQD,GAAcJ,EAAI,yBAW7BgE,EAAA,GAAGK,EAAavF,GAASA,EAAM,UAAUE,CAAC,IAAM,qDAC7CkG,EAASxF,GAAS2E,EAAc,CAAA3E,EAAM,CAAC,EAAI,IAAMA,EAAM,CAAC,EAAI,GAAI,EAAIA,2BASvEsE,EAAA,GAAGvD,EAASlB,SACFA,GAAU,SAChB,IACD,SAASA,EAAM,UAAU,EAAGA,EAAM,OAAS,CAAC,GAC5CoB,GAAgBpB,EAAMA,EAAM,OAAS,CAAC,GACrCA,EACD,iDAIET,IACCA,EAAM,OAAS,SAClBkF,EAAA,GAAAnD,EAAcJ,IAAW,aACzBG,GAAepB,GAAeqB,EAAc,MAAQ,UAEpDmD,EAAA,GAAAnD,EAAcJ,IAAW,QAAa3B,EAAM,UAAUE,CAAC,IAAM,WAC7DgF,EAAA,GAAApD,GAAepB,GAA4B,4CA4KvCwD,KAAclE,GAASqE,SAC7BH,GAAYlE,CAAA,EACZqE,EAAK,KAAK,OAAQ5C,EAAK,EAAE,iCAK1ByD,EAAA,GAAGO,EAAiBrB,EACjB,OAAO,iBAAiBA,CAAa,EACrC,+BAsGAc,EAAA,GAAAsC,EAAa,KAAK,UAAU7G,CAAS,gDAqBvC8E,GAAkB,sBAAsBd,EAAU", "names": ["ctx", "dirty", "create_if_block_1", "insert", "target", "div", "anchor", "p", "create_if_block_3", "create_if_block_2", "value", "$$props", "x", "y", "color", "title", "x_title", "y_title", "color_title", "x_bin", "y_aggregate", "color_map", "x_lim", "y_lim", "x_label_angle", "y_label_angle", "x_axis_labels_visible", "caption", "sort", "tooltip", "show_fullscreen_button", "fullscreen", "reformat_sort", "_sort", "_selectable", "_data", "gradio", "_x_bin", "mouse_down_on_chart", "SUFFIX_DURATION", "_y_aggregate", "aggregating", "downsample", "data", "x_index", "y_index", "color_index", "x_start", "x_end", "bin_count", "min_max_bins_per_color", "row", "x_value", "bin_size", "i", "y_value", "color_value", "bin_index", "downsampled_data", "bins", "min_index", "_", "max_index", "__", "indices", "index", "reformat_data", "datatable", "time_factor", "_x_start", "_x_end", "largest_before_start", "smallest_after_end", "_datatable", "obj", "col", "old_value", "is_browser", "chart_element", "view", "mounted", "old_width", "old_height", "resizeObserver", "vegaEmbed", "load_chart", "refresh_pending", "spec", "create_vega_lite_spec", "el", "__vitePreload", "result", "$$invalidate", "debounceTimeout", "lastSelectTime", "e", "range", "x_temporal", "onMount", "computed_style", "accent_color", "body_text_color", "borderColorPrimary", "font_family", "title_weight", "font_to_px_val", "font", "text_size_md", "text_size_sm", "mode", "_x_lim", "y_start", "y_end", "unique_colors", "c", "n", "column", "height", "label", "elem_id", "elem_classes", "visible", "show_label", "scale", "min_width", "loading_status", "clear_status_handler", "detail", "$$value", "d", "_color_map"], "ignoreList": [], "sources": ["../../../../js/nativeplot/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { FullscreenButton, IconButtonWrapper } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { onMount } from \"svelte\";\n\n\timport type { TopLevelSpec as Spec } from \"vega-lite\";\n\timport type { View } from \"vega\";\n\timport { LineChart as LabelIcon } from \"@gradio/icons\";\n\timport { Empty } from \"@gradio/atoms\";\n\n\tinterface PlotData {\n\t\tcolumns: string[];\n\t\tdata: [string | number][];\n\t\tdatatypes: Record<string, \"quantitative\" | \"temporal\" | \"nominal\">;\n\t\tmark: \"line\" | \"point\" | \"bar\";\n\t}\n\texport let value: PlotData | null;\n\texport let x: string;\n\texport let y: string;\n\texport let color: string | null = null;\n\t$: unique_colors =\n\t\tcolor && value && value.datatypes[color] === \"nominal\"\n\t\t\t? Array.from(new Set(_data.map((d) => d[color])))\n\t\t\t: [];\n\n\texport let title: string | null = null;\n\texport let x_title: string | null = null;\n\texport let y_title: string | null = null;\n\texport let color_title: string | null = null;\n\texport let x_bin: string | number | null = null;\n\texport let y_aggregate:\n\t\t| \"sum\"\n\t\t| \"mean\"\n\t\t| \"median\"\n\t\t| \"min\"\n\t\t| \"max\"\n\t\t| undefined = undefined;\n\texport let color_map: Record<string, string> | null = null;\n\texport let x_lim: [number, number] | null = null;\n\texport let y_lim: [number | null, number | null] | null = null;\n\t$: x_lim = x_lim || null; // for some unknown reason, x_lim was getting set to undefined when used in re-render, so this line is needed\n\t$: y_lim = y_lim || null;\n\t$: [x_start, x_end] = x_lim === null ? [undefined, undefined] : x_lim;\n\t$: [y_start, y_end] = y_lim || [undefined, undefined];\n\texport let x_label_angle: number | null = null;\n\texport let y_label_angle: number | null = null;\n\texport let x_axis_labels_visible = true;\n\texport let caption: string | null = null;\n\texport let sort: \"x\" | \"y\" | \"-x\" | \"-y\" | string[] | null = null;\n\texport let tooltip: \"axis\" | \"none\" | \"all\" | string[] = \"axis\";\n\texport let show_fullscreen_button = false;\n\tlet fullscreen = false;\n\n\tfunction reformat_sort(\n\t\t_sort: typeof sort\n\t):\n\t\t| string\n\t\t| \"ascending\"\n\t\t| \"descending\"\n\t\t| { field: string; order: \"ascending\" | \"descending\" }\n\t\t| string[]\n\t\t| null\n\t\t| undefined {\n\t\tif (_sort === \"x\") {\n\t\t\treturn \"ascending\";\n\t\t} else if (_sort === \"-x\") {\n\t\t\treturn \"descending\";\n\t\t} else if (_sort === \"y\") {\n\t\t\treturn { field: y, order: \"ascending\" };\n\t\t} else if (_sort === \"-y\") {\n\t\t\treturn { field: y, order: \"descending\" };\n\t\t} else if (_sort === null) {\n\t\t\treturn null;\n\t\t} else if (Array.isArray(_sort)) {\n\t\t\treturn _sort;\n\t\t}\n\t}\n\t$: _sort = reformat_sort(sort);\n\texport let _selectable = false;\n\tlet _data: {\n\t\t[x: string]: string | number;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t\tdouble_click: undefined;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\n\t$: x_temporal = value && value.datatypes[x] === \"temporal\";\n\t$: _x_lim = x_lim && x_temporal ? [x_lim[0] * 1000, x_lim[1] * 1000] : x_lim;\n\tlet _x_bin: number | undefined;\n\tlet mouse_down_on_chart = false;\n\tconst SUFFIX_DURATION: Record<string, number> = {\n\t\ts: 1,\n\t\tm: 60,\n\t\th: 60 * 60,\n\t\td: 24 * 60 * 60\n\t};\n\t$: _x_bin = x_bin\n\t\t? typeof x_bin === \"string\"\n\t\t\t? 1000 *\n\t\t\t\tparseInt(x_bin.substring(0, x_bin.length - 1)) *\n\t\t\t\tSUFFIX_DURATION[x_bin[x_bin.length - 1]]\n\t\t\t: x_bin\n\t\t: undefined;\n\tlet _y_aggregate: typeof y_aggregate;\n\tlet aggregating: boolean;\n\t$: {\n\t\tif (value) {\n\t\t\tif (value.mark === \"point\") {\n\t\t\t\taggregating = _x_bin !== undefined;\n\t\t\t\t_y_aggregate = y_aggregate || aggregating ? \"sum\" : undefined;\n\t\t\t} else {\n\t\t\t\taggregating = _x_bin !== undefined || value.datatypes[x] === \"nominal\";\n\t\t\t\t_y_aggregate = y_aggregate ? y_aggregate : \"sum\";\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction downsample(\n\t\tdata: PlotData[\"data\"],\n\t\tx_index: number,\n\t\ty_index: number,\n\t\tcolor_index: number | null,\n\t\tx_start: number | undefined,\n\t\tx_end: number | undefined\n\t): PlotData[\"data\"] {\n\t\tif (\n\t\t\tdata.length < 1000 ||\n\t\t\tx_bin !== null ||\n\t\t\tvalue?.mark !== \"line\" ||\n\t\t\tvalue?.datatypes[x] === \"nominal\"\n\t\t) {\n\t\t\treturn data;\n\t\t}\n\t\tconst bin_count = 250;\n\t\tlet min_max_bins_per_color: Record<\n\t\t\tstring,\n\t\t\t[number | null, number, number | null, number][]\n\t\t> = {};\n\t\tif (x_start === undefined || x_end === undefined) {\n\t\t\tdata.forEach((row) => {\n\t\t\t\tlet x_value = row[x_index] as number;\n\t\t\t\tif (x_start === undefined || x_value < x_start) {\n\t\t\t\t\tx_start = x_value;\n\t\t\t\t}\n\t\t\t\tif (x_end === undefined || x_value > x_end) {\n\t\t\t\t\tx_end = x_value;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\tif (x_start === undefined || x_end === undefined) {\n\t\t\treturn data;\n\t\t}\n\t\tconst x_range = x_end - x_start;\n\t\tconst bin_size = x_range / bin_count;\n\t\tdata.forEach((row, i) => {\n\t\t\tconst x_value = row[x_index] as number;\n\t\t\tconst y_value = row[y_index] as number;\n\t\t\tconst color_value =\n\t\t\t\tcolor_index !== null ? (row[color_index] as string) : \"any\";\n\t\t\tconst bin_index = Math.floor((x_value - (x_start as number)) / bin_size);\n\t\t\tif (min_max_bins_per_color[color_value] === undefined) {\n\t\t\t\tmin_max_bins_per_color[color_value] = [];\n\t\t\t}\n\t\t\tmin_max_bins_per_color[color_value][bin_index] = min_max_bins_per_color[\n\t\t\t\tcolor_value\n\t\t\t][bin_index] || [\n\t\t\t\tnull,\n\t\t\t\tNumber.POSITIVE_INFINITY,\n\t\t\t\tnull,\n\t\t\t\tNumber.NEGATIVE_INFINITY\n\t\t\t];\n\t\t\tif (y_value < min_max_bins_per_color[color_value][bin_index][1]) {\n\t\t\t\tmin_max_bins_per_color[color_value][bin_index][0] = i;\n\t\t\t\tmin_max_bins_per_color[color_value][bin_index][1] = y_value;\n\t\t\t}\n\t\t\tif (y_value > min_max_bins_per_color[color_value][bin_index][3]) {\n\t\t\t\tmin_max_bins_per_color[color_value][bin_index][2] = i;\n\t\t\t\tmin_max_bins_per_color[color_value][bin_index][3] = y_value;\n\t\t\t}\n\t\t});\n\t\tconst downsampled_data: PlotData[\"data\"] = [];\n\t\tObject.values(min_max_bins_per_color).forEach((bins) => {\n\t\t\tbins.forEach(([min_index, _, max_index, __]) => {\n\t\t\t\tlet indices: number[] = [];\n\t\t\t\tif (min_index !== null && max_index !== null) {\n\t\t\t\t\tindices = [\n\t\t\t\t\t\tMath.min(min_index, max_index),\n\t\t\t\t\t\tMath.max(min_index, max_index)\n\t\t\t\t\t];\n\t\t\t\t} else if (min_index !== null) {\n\t\t\t\t\tindices = [min_index];\n\t\t\t\t} else if (max_index !== null) {\n\t\t\t\t\tindices = [max_index];\n\t\t\t\t}\n\t\t\t\tindices.forEach((index) => {\n\t\t\t\t\tdownsampled_data.push(data[index]);\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\t\treturn downsampled_data;\n\t}\n\tfunction reformat_data(\n\t\tdata: PlotData,\n\t\tx_start: number | undefined,\n\t\tx_end: number | undefined\n\t): {\n\t\t[x: string]: string | number;\n\t}[] {\n\t\tlet x_index = data.columns.indexOf(x);\n\t\tlet y_index = data.columns.indexOf(y);\n\t\tlet color_index = color ? data.columns.indexOf(color) : null;\n\t\tlet datatable = data.data;\n\n\t\tif (x_start !== undefined && x_end !== undefined) {\n\t\t\tconst time_factor = data.datatypes[x] === \"temporal\" ? 1000 : 1;\n\t\t\tconst _x_start = x_start * time_factor;\n\t\t\tconst _x_end = x_end * time_factor;\n\t\t\tlet largest_before_start: Record<string, [number, number]> = {};\n\t\t\tlet smallest_after_end: Record<string, [number, number]> = {};\n\t\t\tconst _datatable = datatable.filter((row, i) => {\n\t\t\t\tconst x_value = row[x_index] as number;\n\t\t\t\tconst color_value =\n\t\t\t\t\tcolor_index !== null ? (row[color_index] as string) : \"any\";\n\t\t\t\tif (\n\t\t\t\t\tx_value < _x_start &&\n\t\t\t\t\t(largest_before_start[color_value] === undefined ||\n\t\t\t\t\t\tx_value > largest_before_start[color_value][1])\n\t\t\t\t) {\n\t\t\t\t\tlargest_before_start[color_value] = [i, x_value];\n\t\t\t\t}\n\t\t\t\tif (\n\t\t\t\t\tx_value > _x_end &&\n\t\t\t\t\t(smallest_after_end[color_value] === undefined ||\n\t\t\t\t\t\tx_value < smallest_after_end[color_value][1])\n\t\t\t\t) {\n\t\t\t\t\tsmallest_after_end[color_value] = [i, x_value];\n\t\t\t\t}\n\t\t\t\treturn x_value >= _x_start && x_value <= _x_end;\n\t\t\t});\n\t\t\tdatatable = [\n\t\t\t\t...Object.values(largest_before_start).map(([i, _]) => datatable[i]),\n\t\t\t\t...downsample(\n\t\t\t\t\t_datatable,\n\t\t\t\t\tx_index,\n\t\t\t\t\ty_index,\n\t\t\t\t\tcolor_index,\n\t\t\t\t\t_x_start,\n\t\t\t\t\t_x_end\n\t\t\t\t),\n\t\t\t\t...Object.values(smallest_after_end).map(([i, _]) => datatable[i])\n\t\t\t];\n\t\t} else {\n\t\t\tdatatable = downsample(\n\t\t\t\tdatatable,\n\t\t\t\tx_index,\n\t\t\t\ty_index,\n\t\t\t\tcolor_index,\n\t\t\t\tundefined,\n\t\t\t\tundefined\n\t\t\t);\n\t\t}\n\n\t\tif (tooltip == \"all\" || Array.isArray(tooltip)) {\n\t\t\treturn datatable.map((row) => {\n\t\t\t\tconst obj: { [x: string]: string | number } = {};\n\t\t\t\tdata.columns.forEach((col, i) => {\n\t\t\t\t\tobj[col] = row[i];\n\t\t\t\t});\n\t\t\t\treturn obj;\n\t\t\t});\n\t\t}\n\t\treturn datatable.map((row) => {\n\t\t\tconst obj = {\n\t\t\t\t[x]: row[x_index],\n\t\t\t\t[y]: row[y_index]\n\t\t\t};\n\t\t\tif (color && color_index !== null) {\n\t\t\t\tobj[color] = row[color_index];\n\t\t\t}\n\t\t\treturn obj;\n\t\t});\n\t}\n\t$: _data = value ? reformat_data(value, x_start, x_end) : [];\n\tlet old_value = value;\n\t$: if (old_value !== value && view) {\n\t\told_value = value;\n\t\tview.data(\"data\", _data).runAsync();\n\t}\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tlet chart_element: HTMLDivElement;\n\t$: computed_style = chart_element\n\t\t? window.getComputedStyle(chart_element)\n\t\t: null;\n\tlet view: View;\n\tlet mounted = false;\n\tlet old_width: number;\n\tlet old_height: number;\n\tlet resizeObserver: ResizeObserver;\n\n\tlet vegaEmbed: typeof import(\"vega-embed\").default;\n\tasync function load_chart(): Promise<void> {\n\t\tif (mouse_down_on_chart) {\n\t\t\trefresh_pending = true;\n\t\t\treturn;\n\t\t}\n\t\tif (view) {\n\t\t\tview.finalize();\n\t\t}\n\t\tif (!value || !chart_element) return;\n\t\told_width = chart_element.offsetWidth;\n\t\told_height = chart_element.offsetHeight;\n\t\tconst spec = create_vega_lite_spec();\n\t\tif (!spec) return;\n\t\tresizeObserver = new ResizeObserver((el) => {\n\t\t\tif (!el[0].target || !(el[0].target instanceof HTMLElement)) return;\n\t\t\tif (\n\t\t\t\told_width === 0 &&\n\t\t\t\tchart_element.offsetWidth !== 0 &&\n\t\t\t\tvalue.datatypes[x] === \"nominal\"\n\t\t\t) {\n\t\t\t\t// a bug where when a nominal chart is first loaded, the width is 0, it doesn't resize\n\t\t\t\tload_chart();\n\t\t\t} else {\n\t\t\t\tview.signal(\"width\", el[0].target.offsetWidth).run();\n\t\t\t}\n\t\t\tif (old_height !== el[0].target.offsetHeight && fullscreen) {\n\t\t\t\tview.signal(\"height\", el[0].target.offsetHeight).run();\n\t\t\t\told_height = el[0].target.offsetHeight;\n\t\t\t}\n\t\t});\n\n\t\tif (!vegaEmbed) {\n\t\t\tvegaEmbed = (await import(\"vega-embed\")).default;\n\t\t}\n\t\tvegaEmbed(chart_element, spec, { actions: false }).then(function (result) {\n\t\t\tview = result.view;\n\n\t\t\tresizeObserver.observe(chart_element);\n\t\t\tvar debounceTimeout: NodeJS.Timeout;\n\t\t\tvar lastSelectTime = 0;\n\t\t\tview.addEventListener(\"dblclick\", () => {\n\t\t\t\tgradio.dispatch(\"double_click\");\n\t\t\t});\n\t\t\t// prevent double-clicks from highlighting text\n\t\t\tchart_element.addEventListener(\n\t\t\t\t\"mousedown\",\n\t\t\t\tfunction (e) {\n\t\t\t\t\tif (e.detail > 1) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfalse\n\t\t\t);\n\t\t\tif (_selectable) {\n\t\t\t\tview.addSignalListener(\"brush\", function (_, value) {\n\t\t\t\t\tif (Date.now() - lastSelectTime < 1000) return;\n\t\t\t\t\tmouse_down_on_chart = true;\n\t\t\t\t\tif (Object.keys(value).length === 0) return;\n\t\t\t\t\tclearTimeout(debounceTimeout);\n\t\t\t\t\tlet range: [number, number] = value[Object.keys(value)[0]];\n\t\t\t\t\tif (x_temporal) {\n\t\t\t\t\t\trange = [range[0] / 1000, range[1] / 1000];\n\t\t\t\t\t}\n\t\t\t\t\tdebounceTimeout = setTimeout(function () {\n\t\t\t\t\t\tmouse_down_on_chart = false;\n\t\t\t\t\t\tlastSelectTime = Date.now();\n\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\tvalue: range,\n\t\t\t\t\t\t\tindex: range,\n\t\t\t\t\t\t\tselected: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (refresh_pending) {\n\t\t\t\t\t\t\trefresh_pending = false;\n\t\t\t\t\t\t\tload_chart();\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 250);\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t}\n\n\tlet refresh_pending = false;\n\tonMount(() => {\n\t\tmounted = true;\n\t\treturn () => {\n\t\t\tmounted = false;\n\t\t\tif (view) {\n\t\t\t\tview.finalize();\n\t\t\t}\n\t\t\tif (resizeObserver) {\n\t\t\t\tresizeObserver.disconnect();\n\t\t\t}\n\t\t};\n\t});\n\t$: _color_map = JSON.stringify(color_map);\n\n\t$: title,\n\t\tx_title,\n\t\ty_title,\n\t\tcolor_title,\n\t\tx,\n\t\ty,\n\t\tcolor,\n\t\tx_bin,\n\t\t_y_aggregate,\n\t\t_color_map,\n\t\tx_start,\n\t\tx_end,\n\t\ty_start,\n\t\ty_end,\n\t\tcaption,\n\t\tsort,\n\t\tmounted,\n\t\tchart_element,\n\t\tfullscreen,\n\t\tcomputed_style && requestAnimationFrame(load_chart);\n\n\tfunction create_vega_lite_spec(): Spec | null {\n\t\tif (!value || !computed_style) return null;\n\t\tlet accent_color = computed_style.getPropertyValue(\"--color-accent\");\n\t\tlet body_text_color = computed_style.getPropertyValue(\"--body-text-color\");\n\t\tlet borderColorPrimary = computed_style.getPropertyValue(\n\t\t\t\"--border-color-primary\"\n\t\t);\n\t\tlet font_family = computed_style.fontFamily;\n\t\tlet title_weight = computed_style.getPropertyValue(\n\t\t\t\"--block-title-text-weight\"\n\t\t) as\n\t\t\t| \"bold\"\n\t\t\t| \"normal\"\n\t\t\t| 100\n\t\t\t| 200\n\t\t\t| 300\n\t\t\t| 400\n\t\t\t| 500\n\t\t\t| 600\n\t\t\t| 700\n\t\t\t| 800\n\t\t\t| 900;\n\t\tconst font_to_px_val = (font: string): number => {\n\t\t\treturn font.endsWith(\"px\") ? parseFloat(font.slice(0, -2)) : 12;\n\t\t};\n\t\tlet text_size_md = font_to_px_val(\n\t\t\tcomputed_style.getPropertyValue(\"--text-md\")\n\t\t);\n\t\tlet text_size_sm = font_to_px_val(\n\t\t\tcomputed_style.getPropertyValue(\"--text-sm\")\n\t\t);\n\n\t\t/* eslint-disable complexity */\n\t\treturn {\n\t\t\t$schema: \"https://vega.github.io/schema/vega-lite/v5.17.0.json\",\n\t\t\tbackground: \"transparent\",\n\t\t\tconfig: {\n\t\t\t\tautosize: { type: \"fit\", contains: \"padding\" },\n\t\t\t\taxis: {\n\t\t\t\t\tlabelFont: font_family,\n\t\t\t\t\tlabelColor: body_text_color,\n\t\t\t\t\ttitleFont: font_family,\n\t\t\t\t\ttitleColor: body_text_color,\n\t\t\t\t\ttitlePadding: 8,\n\t\t\t\t\ttickColor: borderColorPrimary,\n\t\t\t\t\tlabelFontSize: text_size_sm,\n\t\t\t\t\tgridColor: borderColorPrimary,\n\t\t\t\t\ttitleFontWeight: \"normal\",\n\t\t\t\t\ttitleFontSize: text_size_sm,\n\t\t\t\t\tlabelFontWeight: \"normal\",\n\t\t\t\t\tdomain: false,\n\t\t\t\t\tlabelAngle: 0\n\t\t\t\t},\n\t\t\t\tlegend: {\n\t\t\t\t\tlabelColor: body_text_color,\n\t\t\t\t\tlabelFont: font_family,\n\t\t\t\t\ttitleColor: body_text_color,\n\t\t\t\t\ttitleFont: font_family,\n\t\t\t\t\ttitleFontWeight: \"normal\",\n\t\t\t\t\ttitleFontSize: text_size_sm,\n\t\t\t\t\tlabelFontWeight: \"normal\",\n\t\t\t\t\toffset: 2\n\t\t\t\t},\n\t\t\t\ttitle: {\n\t\t\t\t\tcolor: body_text_color,\n\t\t\t\t\tfont: font_family,\n\t\t\t\t\tfontSize: text_size_md,\n\t\t\t\t\tfontWeight: title_weight,\n\t\t\t\t\tanchor: \"middle\"\n\t\t\t\t},\n\t\t\t\tview: { stroke: borderColorPrimary },\n\t\t\t\tmark: {\n\t\t\t\t\tstroke: value.mark !== \"bar\" ? accent_color : undefined,\n\t\t\t\t\tfill: value.mark === \"bar\" ? accent_color : undefined,\n\t\t\t\t\tcursor: \"crosshair\"\n\t\t\t\t}\n\t\t\t},\n\t\t\tdata: { name: \"data\" },\n\t\t\tdatasets: {\n\t\t\t\tdata: _data\n\t\t\t},\n\t\t\tlayer: [\"plot\", ...(value.mark === \"line\" ? [\"hover\"] : [])].map(\n\t\t\t\t(mode) => {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tencoding: {\n\t\t\t\t\t\t\tsize:\n\t\t\t\t\t\t\t\tvalue.mark === \"line\"\n\t\t\t\t\t\t\t\t\t? mode == \"plot\"\n\t\t\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\t\t\tcondition: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tempty: false,\n\t\t\t\t\t\t\t\t\t\t\t\t\tparam: \"hoverPlot\",\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: 3\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tvalue: 2\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\t\tcondition: { empty: false, param: \"hover\", value: 100 },\n\t\t\t\t\t\t\t\t\t\t\t\tvalue: 0\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t: undefined,\n\t\t\t\t\t\t\topacity:\n\t\t\t\t\t\t\t\tmode === \"plot\"\n\t\t\t\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\tcondition: { empty: false, param: \"hover\", value: 1 },\n\t\t\t\t\t\t\t\t\t\t\tvalue: 0\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tx: {\n\t\t\t\t\t\t\t\taxis: {\n\t\t\t\t\t\t\t\t\t...(x_label_angle !== null && { labelAngle: x_label_angle }),\n\t\t\t\t\t\t\t\t\tlabels: x_axis_labels_visible,\n\t\t\t\t\t\t\t\t\tticks: x_axis_labels_visible\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfield: x,\n\t\t\t\t\t\t\t\ttitle: x_title || x,\n\t\t\t\t\t\t\t\ttype: value.datatypes[x],\n\t\t\t\t\t\t\t\tscale: _x_lim ? { domain: _x_lim } : undefined,\n\t\t\t\t\t\t\t\tbin: _x_bin ? { step: _x_bin } : undefined,\n\t\t\t\t\t\t\t\tsort: _sort\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\ty: {\n\t\t\t\t\t\t\t\taxis: y_label_angle ? { labelAngle: y_label_angle } : {},\n\t\t\t\t\t\t\t\tfield: y,\n\t\t\t\t\t\t\t\ttitle: y_title || y,\n\t\t\t\t\t\t\t\ttype: value.datatypes[y],\n\t\t\t\t\t\t\t\tscale: {\n\t\t\t\t\t\t\t\t\tzero: false,\n\t\t\t\t\t\t\t\t\tdomainMin: y_start ?? undefined,\n\t\t\t\t\t\t\t\t\tdomainMax: y_end ?? undefined\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\taggregate: aggregating ? _y_aggregate : undefined\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tcolor: color\n\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\tfield: color,\n\t\t\t\t\t\t\t\t\t\tlegend: { orient: \"bottom\", title: color_title },\n\t\t\t\t\t\t\t\t\t\tscale:\n\t\t\t\t\t\t\t\t\t\t\tvalue.datatypes[color] === \"nominal\"\n\t\t\t\t\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdomain: unique_colors,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trange: color_map\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? unique_colors.map((c) => color_map[c])\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trange: [\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t100, 200, 300, 400, 500, 600, 700, 800, 900\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t].map((n) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcomputed_style.getPropertyValue(\"--primary-\" + n)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tinterpolate: \"hsl\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[color]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t: undefined,\n\t\t\t\t\t\t\ttooltip:\n\t\t\t\t\t\t\t\ttooltip == \"none\"\n\t\t\t\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t\t\t\t: [\n\t\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\tfield: y,\n\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[y],\n\t\t\t\t\t\t\t\t\t\t\t\taggregate: aggregating ? _y_aggregate : undefined,\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: y_title || y\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\tfield: x,\n\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[x],\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: x_title || x,\n\t\t\t\t\t\t\t\t\t\t\t\tformat: x_temporal ? \"%Y-%m-%d %H:%M:%S\" : undefined,\n\t\t\t\t\t\t\t\t\t\t\t\tbin: _x_bin ? { step: _x_bin } : undefined\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t...(color\n\t\t\t\t\t\t\t\t\t\t\t\t? [\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield: color,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[color]\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t\t\t\t\t: []),\n\t\t\t\t\t\t\t\t\t\t\t...(tooltip === \"axis\"\n\t\t\t\t\t\t\t\t\t\t\t\t? []\n\t\t\t\t\t\t\t\t\t\t\t\t: value?.columns\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(col) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcol !== x &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcol !== y &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcol !== color &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(tooltip === \"all\" || tooltip.includes(col))\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((column) => ({\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield: column,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[column]\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})))\n\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t},\n\t\t\t\t\t\tstrokeDash: {},\n\t\t\t\t\t\tmark: { clip: true, type: mode === \"hover\" ? \"point\" : value.mark },\n\t\t\t\t\t\tname: mode\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t),\n\t\t\t// @ts-ignore\n\t\t\tparams: [\n\t\t\t\t...(value.mark === \"line\"\n\t\t\t\t\t? [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"hoverPlot\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tclear: \"mouseout\",\n\t\t\t\t\t\t\t\t\tfields: color ? [color] : [],\n\t\t\t\t\t\t\t\t\tnearest: true,\n\t\t\t\t\t\t\t\t\ton: \"mouseover\",\n\t\t\t\t\t\t\t\t\ttype: \"point\" as \"point\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"hover\"]\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"hover\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tclear: \"mouseout\",\n\t\t\t\t\t\t\t\t\tnearest: true,\n\t\t\t\t\t\t\t\t\ton: \"mouseover\",\n\t\t\t\t\t\t\t\t\ttype: \"point\" as \"point\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"hover\"]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t: []),\n\t\t\t\t...(_selectable\n\t\t\t\t\t? [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"brush\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tencodings: [\"x\"],\n\t\t\t\t\t\t\t\t\tmark: { fill: \"gray\", fillOpacity: 0.3, stroke: \"none\" },\n\t\t\t\t\t\t\t\t\ttype: \"interval\" as \"interval\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"plot\"]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t: [])\n\t\t\t],\n\t\t\twidth: chart_element.offsetWidth,\n\t\t\theight: height || fullscreen ? \"container\" : undefined,\n\t\t\ttitle: title || undefined\n\t\t};\n\t\t/* eslint-enable complexity */\n\t}\n\n\texport let label = \"Textbox\";\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let show_label: boolean;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height: number | undefined = undefined;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tpadding={true}\n\t{height}\n\tbind:fullscreen\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t{#if show_fullscreen_button}\n\t\t<IconButtonWrapper>\n\t\t\t<FullscreenButton\n\t\t\t\t{fullscreen}\n\t\t\t\ton:fullscreen={({ detail }) => {\n\t\t\t\t\tfullscreen = detail;\n\t\t\t\t}}\n\t\t\t/>\n\t\t</IconButtonWrapper>\n\t{/if}\n\t<BlockTitle {show_label} info={undefined}>{label}</BlockTitle>\n\n\t{#if value && is_browser}\n\t\t<div bind:this={chart_element}></div>\n\n\t\t{#if caption}\n\t\t\t<p class=\"caption\">{caption}</p>\n\t\t{/if}\n\t{:else}\n\t\t<Empty unpadded_box={true}><LabelIcon /></Empty>\n\t{/if}\n</Block>\n\n<style>\n\tdiv {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\t:global(#vg-tooltip-element) {\n\t\tfont-family: var(--font) !important;\n\t\tfont-size: var(--text-xs) !important;\n\t\tbox-shadow: none !important;\n\t\tbackground-color: var(--block-background-fill) !important;\n\t\tborder: 1px solid var(--border-color-primary) !important;\n\t\tcolor: var(--body-text-color) !important;\n\t}\n\t:global(#vg-tooltip-element .key) {\n\t\tcolor: var(--body-text-color-subdued) !important;\n\t}\n\t.caption {\n\t\tpadding: 0 4px;\n\t\tmargin: 0;\n\t\ttext-align: center;\n\t}\n</style>\n"], "file": "assets/Index-DRkwtSQ6.js"}