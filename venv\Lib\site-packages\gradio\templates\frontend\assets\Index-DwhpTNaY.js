import{a as H}from"./Tabs-DS4O-Nv1.js";import J from"./Index-C7inCcrM.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./index-B1FJGuzG.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./prism-python-MMh3z1bK.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:K,attr:w,check_outros:L,component_subscribe:N,create_component:O,create_slot:P,destroy_component:Q,detach:M,element:R,empty:U,flush:u,get_all_dirty_from_scope:V,get_slot_changes:W,group_outros:X,init:Y,insert:z,mount_component:Z,safe_not_equal:y,set_style:S,toggle_class:A,transition_in:k,transition_out:T,update_slot_base:$}=window.__gradio__svelte__internal,{getContext:x,onMount:p,createEventDispatcher:ee,tick:te}=window.__gradio__svelte__internal;function D(n){let e,s,t,i;return s=new J({props:{scale:n[4]>=1?n[4]:null,$$slots:{default:[ie]},$$scope:{ctx:n}}}),{c(){e=R("div"),O(s.$$.fragment),w(e,"id",n[0]),w(e,"class",t="tabitem "+n[1].join(" ")+" svelte-wv8on1"),w(e,"role","tabpanel"),A(e,"grow-children",n[4]>=1),S(e,"display",n[5]===n[2]&&n[3]?"flex":"none"),S(e,"flex-grow",n[4])},m(l,a){z(l,e,a),Z(s,e,null),i=!0},p(l,a){const _={};a&16&&(_.scale=l[4]>=1?l[4]:null),a&16384&&(_.$$scope={dirty:a,ctx:l}),s.$set(_),(!i||a&1)&&w(e,"id",l[0]),(!i||a&2&&t!==(t="tabitem "+l[1].join(" ")+" svelte-wv8on1"))&&w(e,"class",t),(!i||a&18)&&A(e,"grow-children",l[4]>=1),a&44&&S(e,"display",l[5]===l[2]&&l[3]?"flex":"none"),a&16&&S(e,"flex-grow",l[4])},i(l){i||(k(s.$$.fragment,l),i=!0)},o(l){T(s.$$.fragment,l),i=!1},d(l){l&&M(e),Q(s)}}}function ie(n){let e;const s=n[13].default,t=P(s,n,n[14],null);return{c(){t&&t.c()},m(i,l){t&&t.m(i,l),e=!0},p(i,l){t&&t.p&&(!e||l&16384)&&$(t,s,i,i[14],e?W(s,i[14],l,null):V(i[14]),null)},i(i){e||(k(t,i),e=!0)},o(i){T(t,i),e=!1},d(i){t&&t.d(i)}}}function le(n){let e,s,t=n[5]===n[2]&&n[3]&&D(n);return{c(){t&&t.c(),e=U()},m(i,l){t&&t.m(i,l),z(i,e,l),s=!0},p(i,[l]){i[5]===i[2]&&i[3]?t?(t.p(i,l),l&44&&k(t,1)):(t=D(i),t.c(),k(t,1),t.m(e.parentNode,e)):t&&(X(),T(t,1,1,()=>{t=null}),L())},i(i){s||(k(t),s=!0)},o(i){T(t),s=!1},d(i){i&&M(e),t&&t.d(i)}}}function se(n,e,s){let t,i,{$$slots:l={},$$scope:a}=e,{elem_id:_=""}=e,{elem_classes:g=[]}=e,{label:c}=e,{id:m={}}=e,{visible:h}=e,{interactive:b}=e,{order:d}=e,{scale:v}=e;const r=ee(),{register_tab:F,unregister_tab:G,selected_tab:j,selected_tab_index:B}=x(H);N(n,j,o=>s(5,i=o)),N(n,B,o=>s(12,t=o));let C;return p(()=>()=>G({label:c,id:m,elem_id:_},d)),n.$$set=o=>{"elem_id"in o&&s(0,_=o.elem_id),"elem_classes"in o&&s(1,g=o.elem_classes),"label"in o&&s(8,c=o.label),"id"in o&&s(2,m=o.id),"visible"in o&&s(3,h=o.visible),"interactive"in o&&s(9,b=o.interactive),"order"in o&&s(10,d=o.order),"scale"in o&&s(4,v=o.scale),"$$scope"in o&&s(14,a=o.$$scope)},n.$$.update=()=>{n.$$.dirty&1821&&s(11,C=F({label:c,id:m,elem_id:_,visible:h,interactive:b,scale:v},d)),n.$$.dirty&6400&&t===C&&te().then(()=>r("select",{value:c,index:C}))},[_,g,m,h,v,i,j,B,c,b,d,C,t,l,a]}class ne extends K{constructor(e){super(),Y(this,e,se,le,y,{elem_id:0,elem_classes:1,label:8,id:2,visible:3,interactive:9,order:10,scale:4})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),u()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),u()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),u()}get id(){return this.$$.ctx[2]}set id(e){this.$$set({id:e}),u()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),u()}get interactive(){return this.$$.ctx[9]}set interactive(e){this.$$set({interactive:e}),u()}get order(){return this.$$.ctx[10]}set order(e){this.$$set({order:e}),u()}get scale(){return this.$$.ctx[4]}set scale(e){this.$$set({scale:e}),u()}}const re=ne,{SvelteComponent:oe,check_outros:ae,create_component:_e,create_slot:ce,destroy_component:fe,detach:ue,empty:me,flush:f,get_all_dirty_from_scope:de,get_slot_changes:he,group_outros:be,init:ge,insert:ve,mount_component:we,safe_not_equal:ke,transition_in:I,transition_out:q,update_slot_base:Ie}=window.__gradio__svelte__internal;function E(n){let e;const s=n[9].default,t=ce(s,n,n[11],null);return{c(){t&&t.c()},m(i,l){t&&t.m(i,l),e=!0},p(i,l){t&&t.p&&(!e||l&2048)&&Ie(t,s,i,i[11],e?he(s,i[11],l,null):de(i[11]),null)},i(i){e||(I(t,i),e=!0)},o(i){q(t,i),e=!1},d(i){t&&t.d(i)}}}function Ce(n){let e,s,t=n[5]&&E(n);return{c(){t&&t.c(),e=me()},m(i,l){t&&t.m(i,l),ve(i,e,l),s=!0},p(i,l){i[5]?t?(t.p(i,l),l&32&&I(t,1)):(t=E(i),t.c(),I(t,1),t.m(e.parentNode,e)):t&&(be(),q(t,1,1,()=>{t=null}),ae())},i(i){s||(I(t),s=!0)},o(i){q(t),s=!1},d(i){i&&ue(e),t&&t.d(i)}}}function Se(n){let e,s;return e=new re({props:{elem_id:n[0],elem_classes:n[1],label:n[2],visible:n[5],interactive:n[6],id:n[3],order:n[7],scale:n[8],$$slots:{default:[Ce]},$$scope:{ctx:n}}}),e.$on("select",n[10]),{c(){_e(e.$$.fragment)},m(t,i){we(e,t,i),s=!0},p(t,[i]){const l={};i&1&&(l.elem_id=t[0]),i&2&&(l.elem_classes=t[1]),i&4&&(l.label=t[2]),i&32&&(l.visible=t[5]),i&64&&(l.interactive=t[6]),i&8&&(l.id=t[3]),i&128&&(l.order=t[7]),i&256&&(l.scale=t[8]),i&2080&&(l.$$scope={dirty:i,ctx:t}),e.$set(l)},i(t){s||(I(e.$$.fragment,t),s=!0)},o(t){q(e.$$.fragment,t),s=!1},d(t){fe(e,t)}}}function Te(n,e,s){let{$$slots:t={},$$scope:i}=e,{elem_id:l=""}=e,{elem_classes:a=[]}=e,{label:_}=e,{id:g}=e,{gradio:c}=e,{visible:m=!0}=e,{interactive:h=!0}=e,{order:b}=e,{scale:d}=e;const v=({detail:r})=>c?.dispatch("select",r);return n.$$set=r=>{"elem_id"in r&&s(0,l=r.elem_id),"elem_classes"in r&&s(1,a=r.elem_classes),"label"in r&&s(2,_=r.label),"id"in r&&s(3,g=r.id),"gradio"in r&&s(4,c=r.gradio),"visible"in r&&s(5,m=r.visible),"interactive"in r&&s(6,h=r.interactive),"order"in r&&s(7,b=r.order),"scale"in r&&s(8,d=r.scale),"$$scope"in r&&s(11,i=r.$$scope)},[l,a,_,g,c,m,h,b,d,t,v,i]}class He extends oe{constructor(e){super(),ge(this,e,Te,Se,ke,{elem_id:0,elem_classes:1,label:2,id:3,gradio:4,visible:5,interactive:6,order:7,scale:8})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),f()}get id(){return this.$$.ctx[3]}set id(e){this.$$set({id:e}),f()}get gradio(){return this.$$.ctx[4]}set gradio(e){this.$$set({gradio:e}),f()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),f()}get interactive(){return this.$$.ctx[6]}set interactive(e){this.$$set({interactive:e}),f()}get order(){return this.$$.ctx[7]}set order(e){this.$$set({order:e}),f()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),f()}}export{re as BaseTabItem,He as default};
//# sourceMappingURL=Index-DwhpTNaY.js.map
