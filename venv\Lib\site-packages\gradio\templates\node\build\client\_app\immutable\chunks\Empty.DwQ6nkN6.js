import{SvelteComponent as p,init as b,safe_not_equal as v,create_slot as E,element as h,claim_element as m,children as c,detach as d,attr as f,toggle_class as r,insert_hydration as z,append_hydration as C,update_slot_base as y,get_all_dirty_from_scope as B,get_slot_changes as D,transition_in as I,transition_out as R,binding_callbacks as V}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.B2AoQPnG.js";function k(l){let e,a,n;const _=l[5].default,i=E(_,l,l[4],null);return{c(){e=h("div"),a=h("div"),i&&i.c(),this.h()},l(t){e=m(t,"DIV",{class:!0,"aria-label":!0});var s=c(e);a=m(s,"DIV",{class:!0});var u=c(a);i&&i.l(u),u.forEach(d),s.forEach(d),this.h()},h(){f(a,"class","icon svelte-1oiin9d"),f(e,"class","empty svelte-1oiin9d"),f(e,"aria-label","Empty value"),r(e,"small",l[0]==="small"),r(e,"large",l[0]==="large"),r(e,"unpadded_box",l[1]),r(e,"small_parent",l[3])},m(t,s){z(t,e,s),C(e,a),i&&i.m(a,null),l[6](e),n=!0},p(t,[s]){i&&i.p&&(!n||s&16)&&y(i,_,t,t[4],n?D(_,t[4],s,null):B(t[4]),null),(!n||s&1)&&r(e,"small",t[0]==="small"),(!n||s&1)&&r(e,"large",t[0]==="large"),(!n||s&2)&&r(e,"unpadded_box",t[1]),(!n||s&8)&&r(e,"small_parent",t[3])},i(t){n||(I(i,t),n=!0)},o(t){R(i,t),n=!1},d(t){t&&d(e),i&&i.d(t),l[6](null)}}}function q(l){var n;if(!l)return!1;const{height:e}=l.getBoundingClientRect(),{height:a}=((n=l.parentElement)==null?void 0:n.getBoundingClientRect())||{height:e};return e>a+2}function S(l,e,a){let n,{$$slots:_={},$$scope:i}=e,{size:t="small"}=e,{unpadded_box:s=!1}=e,u;function g(o){V[o?"unshift":"push"](()=>{u=o,a(2,u)})}return l.$$set=o=>{"size"in o&&a(0,t=o.size),"unpadded_box"in o&&a(1,s=o.unpadded_box),"$$scope"in o&&a(4,i=o.$$scope)},l.$$.update=()=>{l.$$.dirty&4&&a(3,n=q(u))},[t,s,u,n,i,_,g]}class F extends p{constructor(e){super(),b(this,e,S,k,v,{size:0,unpadded_box:1})}}export{F as E};
//# sourceMappingURL=Empty.DwQ6nkN6.js.map
