import{SvelteComponent as v,init as w,safe_not_equal as $,svg_element as m,claim_svg_element as p,children as _,detach as u,attr as n,insert_hydration as x,append_hydration as y,noop as c,empty as b,group_outros as I,transition_out as d,check_outros as C,transition_in as k,createEventDispatcher as F,create_component as B,claim_component as E,mount_component as M,destroy_component as j}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{I as z}from"./2.B2AoQPnG.js";function H(i){let e,r;return{c(){e=m("svg"),r=m("path"),this.h()},l(t){e=p(t,"svg",{xmlns:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0,width:!0,height:!0});var o=_(e);r=p(o,"path",{d:!0});var a=_(r);a.forEach(u),o.forEach(u),this.h()},h(){n(r,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","2"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round"),n(e,"class","feather feather-maximize"),n(e,"width","100%"),n(e,"height","100%")},m(t,o){x(t,e,o),y(e,r)},p:c,i:c,o:c,d(t){t&&u(e)}}}class V extends v{constructor(e){super(),w(this,e,null,H,$,{})}}function q(i){let e,r;return{c(){e=m("svg"),r=m("path"),this.h()},l(t){e=p(t,"svg",{xmlns:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0,width:!0,height:!0});var o=_(e);r=p(o,"path",{d:!0}),_(r).forEach(u),o.forEach(u),this.h()},h(){n(r,"d","M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","2"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round"),n(e,"class","feather feather-minimize"),n(e,"width","100%"),n(e,"height","100%")},m(t,o){x(t,e,o),y(e,r)},p:c,i:c,o:c,d(t){t&&u(e)}}}class D extends v{constructor(e){super(),w(this,e,null,q,$,{})}}function N(i){let e,r;return e=new z({props:{Icon:V,label:"Fullscreen"}}),e.$on("click",i[3]),{c(){B(e.$$.fragment)},l(t){E(e.$$.fragment,t)},m(t,o){M(e,t,o),r=!0},p:c,i(t){r||(k(e.$$.fragment,t),r=!0)},o(t){d(e.$$.fragment,t),r=!1},d(t){j(e,t)}}}function S(i){let e,r;return e=new z({props:{Icon:D,label:"Exit fullscreen mode"}}),e.$on("click",i[2]),{c(){B(e.$$.fragment)},l(t){E(e.$$.fragment,t)},m(t,o){M(e,t,o),r=!0},p:c,i(t){r||(k(e.$$.fragment,t),r=!0)},o(t){d(e.$$.fragment,t),r=!1},d(t){j(e,t)}}}function A(i){let e,r,t,o;const a=[S,N],s=[];function f(l,h){return l[0]?0:1}return e=f(i),r=s[e]=a[e](i),{c(){r.c(),t=b()},l(l){r.l(l),t=b()},m(l,h){s[e].m(l,h),x(l,t,h),o=!0},p(l,[h]){let g=e;e=f(l),e===g?s[e].p(l,h):(I(),d(s[g],1,1,()=>{s[g]=null}),C(),r=s[e],r?r.p(l,h):(r=s[e]=a[e](l),r.c()),k(r,1),r.m(t.parentNode,t))},i(l){o||(k(r),o=!0)},o(l){d(r),o=!1},d(l){l&&u(t),s[e].d(l)}}}function G(i,e,r){const t=F();let{fullscreen:o}=e;const a=()=>t("fullscreen",!1),s=()=>t("fullscreen",!0);return i.$$set=f=>{"fullscreen"in f&&r(0,o=f.fullscreen)},[o,t,a,s]}class O extends v{constructor(e){super(),w(this,e,G,A,$,{fullscreen:0})}}export{O as F};
//# sourceMappingURL=FullscreenButton.g_8wwg6y.js.map
