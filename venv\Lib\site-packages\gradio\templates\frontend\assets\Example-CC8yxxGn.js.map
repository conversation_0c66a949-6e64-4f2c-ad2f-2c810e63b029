{"version": 3, "file": "Example-CC8yxxGn.js", "sources": ["../../../../js/image/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport Image from \"./shared/Image.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let value: null | FileData;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass=\"container\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass:border={value}\n>\n\t{#if value}\n\t\t<Image src={value.url} alt=\"\" />\n\t{/if}\n</div>\n\n<style>\n\t.container :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.container.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\t.border.table {\n\t\tborder: 2px solid var(--border-color-primary);\n\t}\n\n\t.container.table {\n\t\tmargin: 0 auto;\n\t\tborder-radius: var(--radius-lg);\n\t\toverflow: hidden;\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.container.gallery {\n\t\twidth: var(--size-20);\n\t\tmax-width: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n</style>\n"], "names": ["ctx", "dirty", "image_changes", "create_if_block", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected"], "mappings": "+eAiBc,MAAA,CAAA,IAAAA,KAAM,IAAG,IAAA,EAAA,mEAATC,EAAA,IAAAC,EAAA,IAAAF,KAAM,uHADdA,EAAK,CAAA,GAAAG,EAAAH,CAAA,wEALGI,EAAAC,EAAA,QAAAL,OAAS,OAAO,EACdI,EAAAC,EAAA,UAAAL,OAAS,SAAS,oCAEnBA,EAAK,CAAA,CAAA,UALpBM,EAUKC,EAAAF,EAAAG,CAAA,gCAHCR,EAAK,CAAA,2GALGI,EAAAC,EAAA,QAAAL,OAAS,OAAO,aACdI,EAAAC,EAAA,UAAAL,OAAS,SAAS,0DAEnBA,EAAK,CAAA,CAAA,kFAVR,GAAA,CAAA,MAAAS,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF"}