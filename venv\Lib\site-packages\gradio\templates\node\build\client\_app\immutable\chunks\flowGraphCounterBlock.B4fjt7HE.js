import{b as r}from"./declarationMapper.UBCwU7BT.js";import{b as i}from"./KHR_interactivity.DEAVS2UW.js";import{R as a}from"./index.BoI39RQH.js";class o extends i{constructor(t){super(t),this.count=this.registerDataOutput("count",r),this.reset=this._registerSignalInput("reset")}_execute(t,s){if(s===this.reset){t._setExecutionVariable(this,"count",0),this.count.setValue(0,t);return}const e=t._getExecutionVariable(this,"count",0)+1;t._setExecutionVariable(this,"count",e),this.count.setValue(e,t),this.out._activateSignal(t)}getClassName(){return"FlowGraphCallCounterBlock"}}a("FlowGraphCallCounterBlock",o);export{o as FlowGraphCallCounterBlock};
//# sourceMappingURL=flowGraphCounterBlock.B4fjt7HE.js.map
