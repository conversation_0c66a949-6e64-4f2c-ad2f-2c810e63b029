"""
DuckDuckGo搜索引擎
提供无需API Key的网络搜索功能
"""

import re
import time
import requests
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from urllib.parse import quote_plus
from bs4 import BeautifulSoup

try:
    from duckduckgo_search import DDGS
except ImportError:
    DDGS = None

from core.utils.logger import get_logger
from core.utils.config import config_manager

logger = get_logger(__name__)

@dataclass
class SearchResult:
    """搜索结果"""
    title: str
    url: str
    snippet: str
    source: str
    timestamp: Optional[str] = None

class DuckDuckGoSearch:
    """DuckDuckGo搜索引擎"""
    
    def __init__(self):
        self.max_results = config_manager.get('search.max_results', 5)
        self.timeout = config_manager.get('search.search_timeout', 30)
        self.user_agent = config_manager.get('network.user_agent', 'Reverie-Agents/1.0.0')
        
        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
    
    def search(self, query: str, max_results: Optional[int] = None) -> List[SearchResult]:
        """执行搜索"""
        max_results = max_results or self.max_results
        
        logger.info(f"搜索查询: {query}")
        
        # 优先使用duckduckgo-search库
        if DDGS:
            return self._search_with_ddgs(query, max_results)
        else:
            return self._search_with_requests(query, max_results)
    
    def _search_with_ddgs(self, query: str, max_results: int) -> List[SearchResult]:
        """使用duckduckgo-search库搜索"""
        try:
            with DDGS() as ddgs:
                results = []
                
                # 执行搜索
                search_results = ddgs.text(query, max_results=max_results)
                
                for result in search_results:
                    search_result = SearchResult(
                        title=result.get('title', ''),
                        url=result.get('href', ''),
                        snippet=result.get('body', ''),
                        source='DuckDuckGo'
                    )
                    results.append(search_result)
                
                logger.info(f"搜索完成，找到 {len(results)} 个结果")
                return results
                
        except Exception as e:
            logger.error(f"DDGS搜索失败: {e}")
            return self._search_with_requests(query, max_results)
    
    def _search_with_requests(self, query: str, max_results: int) -> List[SearchResult]:
        """使用requests直接搜索DuckDuckGo"""
        try:
            # DuckDuckGo搜索URL
            search_url = f"https://duckduckgo.com/html/?q={quote_plus(query)}"
            
            response = self.session.get(search_url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # 解析搜索结果
            result_divs = soup.find_all('div', class_='result')
            
            for div in result_divs[:max_results]:
                try:
                    # 提取标题和链接
                    title_link = div.find('a', class_='result__a')
                    if not title_link:
                        continue
                    
                    title = title_link.get_text(strip=True)
                    url = title_link.get('href', '')
                    
                    # 提取摘要
                    snippet_div = div.find('div', class_='result__snippet')
                    snippet = snippet_div.get_text(strip=True) if snippet_div else ''
                    
                    if title and url:
                        search_result = SearchResult(
                            title=title,
                            url=url,
                            snippet=snippet,
                            source='DuckDuckGo'
                        )
                        results.append(search_result)
                
                except Exception as e:
                    logger.warning(f"解析搜索结果失败: {e}")
                    continue
            
            logger.info(f"搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def get_page_content(self, url: str, max_length: int = 5000) -> str:
        """获取网页内容"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 移除脚本和样式
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取文本内容
            text = soup.get_text()
            
            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # 限制长度
            if len(text) > max_length:
                text = text[:max_length] + "..."
            
            return text
            
        except Exception as e:
            logger.error(f"获取网页内容失败: {e}")
            return ""
    
    def search_and_extract(self, query: str, max_results: Optional[int] = None, 
                          extract_content: bool = True) -> List[Dict[str, Any]]:
        """搜索并提取内容"""
        results = self.search(query, max_results)
        
        enhanced_results = []
        for result in results:
            result_dict = {
                'title': result.title,
                'url': result.url,
                'snippet': result.snippet,
                'source': result.source,
                'content': ''
            }
            
            # 提取完整内容
            if extract_content and result.url:
                content = self.get_page_content(result.url)
                result_dict['content'] = content
                
                # 添加延迟避免被限制
                time.sleep(1)
            
            enhanced_results.append(result_dict)
        
        return enhanced_results

# 全局搜索引擎实例
duckduckgo_search = DuckDuckGoSearch()
