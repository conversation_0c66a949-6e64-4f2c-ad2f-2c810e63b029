{"version": 3, "file": "Embed-HnJWX_lT.js", "sources": ["../../../../js/core/src/images/spaces.svg", "../../../../js/core/src/Embed.svelte"], "sourcesContent": ["export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='10'%20height='10'%20fill='none'%3e%3cpath%20fill='%23FF3270'%20d='M1.93%206.03v2.04h2.04V6.03H1.93Z'/%3e%3cpath%20fill='%23861FFF'%20d='M6.03%206.03v2.04h2.04V6.03H6.03Z'/%3e%3cpath%20fill='%23097EFF'%20d='M1.93%201.93v2.04h2.04V1.93H1.93Z'/%3e%3cpath%20fill='%23000'%20fill-rule='evenodd'%20d='M.5%201.4c0-.5.4-.9.9-.9h3.1a.9.9%200%200%201%20.87.67A2.44%202.44%200%200%201%209.5%202.95c0%20.65-.25%201.24-.67%***********.67.46.67.88v3.08c0%20.5-.4.91-.9.91H1.4a.9.9%200%200%201-.9-.9V1.4Zm1.43.53v2.04h2.04V1.93H1.93Zm0%206.14V6.03h2.04v2.04H1.93Zm4.1%200V6.03h2.04v2.04H6.03Zm0-5.12a1.02%201.02%200%201%201%202.04%200%201.02%201.02%200%200%201-2.04%200Z'%20clip-rule='evenodd'/%3e%3cpath%20fill='%23FFD702'%20d='M7.05%201.93a1.02%201.02%200%201%200%200%202.04%201.02%201.02%200%200%200%200-2.04Z'/%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\timport { getContext } from \"svelte\";\n\timport space_logo from \"./images/spaces.svg\";\n\timport { _ } from \"svelte-i18n\";\n\texport let wrapper: HTMLDivElement;\n\texport let version: string;\n\texport let initial_height: string;\n\texport let fill_width: boolean;\n\texport let is_embed: boolean;\n\texport let is_lite: boolean;\n\n\texport let space: string | null;\n\texport let display: boolean;\n\texport let info: boolean;\n\texport let loaded: boolean;\n\texport let pages: [string, string][] = [];\n\texport let current_page = \"\";\n\texport let root: string;\n\n\tconst set_page: ((page: string) => void) | undefined =\n\t\tgetContext(\"set_lite_page\");\n</script>\n\n<div\n\tbind:this={wrapper}\n\tclass:fill_width\n\tclass:embed-container={display}\n\tclass:with-info={info}\n\tclass=\"gradio-container gradio-container-{version}\"\n\tstyle:min-height={loaded ? \"initial\" : initial_height}\n\tstyle:flex-grow={!display ? \"1\" : \"auto\"}\n\tdata-iframe-height\n>\n\t{#if pages.length > 1}\n\t\t<div class=\"nav-holder\">\n\t\t\t<nav class=\"fillable\" class:fill_width>\n\t\t\t\t{#each pages as [route, label], i}\n\t\t\t\t\t{#if is_lite}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass:active={route === current_page}\n\t\t\t\t\t\t\ton:click={(e) => {\n\t\t\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\t\t\tset_page?.(route);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t>{label}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref={`${root}/${route}`}\n\t\t\t\t\t\t\tclass:active={route === current_page}\n\t\t\t\t\t\t\tdata-sveltekit-reload\n\t\t\t\t\t\t\t>{label}\n\t\t\t\t\t\t</a>\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t</nav>\n\t\t</div>\n\t{/if}\n\t<main class=\"fillable\" class:fill_width class:app={!display && !is_embed}>\n\t\t<slot />\n\t\t<div>\n\t\t\t{#if display && space && info}\n\t\t\t\t<div class=\"info\">\n\t\t\t\t\t<span>\n\t\t\t\t\t\t<a href=\"https://huggingface.co/spaces/{space}\" class=\"title\"\n\t\t\t\t\t\t\t>{space}</a\n\t\t\t\t\t\t>\n\t\t\t\t\t</span>\n\t\t\t\t\t<span>\n\t\t\t\t\t\t{$_(\"common.built_with\")}\n\t\t\t\t\t\t<a class=\"gradio\" href=\"https://gradio.app\">Gradio</a>.\n\t\t\t\t\t</span>\n\t\t\t\t\t<span>\n\t\t\t\t\t\t{$_(\"common.hosted_on\")}\n\t\t\t\t\t\t<a class=\"hf\" href=\"https://huggingface.co/spaces\"\n\t\t\t\t\t\t\t><span class=\"space-logo\">\n\t\t\t\t\t\t\t\t<img src={space_logo} alt=\"Hugging Face Space\" />\n\t\t\t\t\t\t\t</span> Spaces</a\n\t\t\t\t\t\t>\n\t\t\t\t\t</span>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</div>\n\t</main>\n</div>\n\n<style>\n\t.nav-holder {\n\t\tpadding: var(--size-2) 0;\n\t\tborder-bottom: solid 1px var(--border-color-primary);\n\t}\n\tnav {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tmargin: 0 auto;\n\t\tpadding: 0 var(--size-8);\n\t}\n\tnav a,\n\tbutton {\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tborder-radius: var(--block-radius);\n\t\tborder-width: var(--block-border-width);\n\t\tborder-color: transparent;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\tnav a.active,\n\tbutton.active {\n\t\tcolor: var(--body-text-color);\n\t\tborder-color: var(--block-border-color);\n\t\tbackground-color: var(--block-background-fill);\n\t}\n\t.gradio-container {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\tpadding: 0;\n\t\tmin-height: 1px;\n\t\toverflow: hidden;\n\t\tcolor: var(--button-secondary-text-color);\n\t}\n\n\t.embed-container {\n\t\tmargin: var(--size-4) 0px;\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--embed-radius);\n\t}\n\n\t.with-info {\n\t\tpadding-bottom: var(--size-7);\n\t}\n\n\t.embed-container > main {\n\t\tpadding: var(--size-4);\n\t}\n\n\tmain {\n\t\tdisplay: flex;\n\t\tflex-grow: 1;\n\t\tflex-direction: column;\n\t}\n\n\t.app {\n\t\tposition: relative;\n\t\tmargin: auto;\n\t\tpadding: var(--size-4) var(--size-8);\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t@media (--screen-sm) {\n\t\t.fillable:not(.fill_width) {\n\t\t\tmax-width: 640px;\n\t\t}\n\t}\n\t@media (--screen-md) {\n\t\t.fillable:not(.fill_width) {\n\t\t\tmax-width: 768px;\n\t\t}\n\t}\n\t@media (--screen-lg) {\n\t\t.fillable:not(.fill_width) {\n\t\t\tmax-width: 1024px;\n\t\t}\n\t}\n\t@media (--screen-xl) {\n\t\t.fillable:not(.fill_width) {\n\t\t\tmax-width: 1280px;\n\t\t}\n\t}\n\t@media (--screen-xxl) {\n\t\t.fillable:not(.fill_width) {\n\t\t\tmax-width: 1536px;\n\t\t}\n\t}\n\t@media (--screen-xxxl) {\n\t\t.fillable:not(.fill_width) {\n\t\t\tmax-width: 1920px;\n\t\t}\n\t}\n\n\t.info {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: flex-start;\n\t\tborder-top: 1px solid var(--button-secondary-border-color);\n\t\tpadding: var(--size-1) var(--size-5);\n\t\twidth: 100%;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-md);\n\t\twhite-space: nowrap;\n\t}\n\n\t.info > span {\n\t\tword-wrap: break-word;\n\t\t-break: keep-all;\n\t\tdisplay: block;\n\t\tword-break: keep-all;\n\t}\n\n\t.info > span:nth-child(1) {\n\t\tmargin-right: 4px;\n\t\tmin-width: 0px;\n\t\tmax-width: max-content;\n\t\toverflow: hidden;\n\t\tcolor: var(--body-text-color);\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.info > span:nth-child(2) {\n\t\tmargin-right: 3px;\n\t}\n\n\t.info > span:nth-child(2),\n\t.info > span:nth-child(3) {\n\t\twidth: max-content;\n\t}\n\n\t.info > span:nth-child(3) {\n\t\talign-self: flex-end;\n\t\tjustify-self: flex-end;\n\t\tmargin-left: auto;\n\t\ttext-align: right;\n\t}\n\n\t.info > span:nth-child(1) {\n\t\tflex-shrink: 9;\n\t}\n\n\t.hidden-title {\n\t\tposition: absolute;\n\t\tleft: var(--size-5);\n\t\topacity: 0;\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tpadding-right: 4px;\n\t}\n\n\t.info a {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.title {\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.hf {\n\t\tmargin-left: 5px;\n\t}\n\n\t.space-logo img {\n\t\tdisplay: inline-block;\n\t\tmargin-bottom: 4px;\n\t\theight: 12px;\n\t}\n\n\tmain a:hover {\n\t\ttext-decoration: underline;\n\t}\n</style>\n"], "names": ["space_logo", "ctx", "i", "insert", "target", "div", "anchor", "append", "nav", "attr", "a", "a_href_value", "toggle_class", "set_data", "t0", "t0_value", "dirty", "button", "create_if_block_2", "t2_value", "t7_value", "span0", "a0", "span1", "a1", "span3", "a2", "t2", "t7", "create_if_block_1", "create_if_block", "main", "div1", "div0", "wrapper", "$$props", "version", "initial_height", "fill_width", "is_embed", "is_lite", "space", "display", "info", "loaded", "pages", "current_page", "root", "set_page", "getContext", "e", "route", "$$value"], "mappings": "wCAAA,MAAeA,EAAA,4wCCCa,EAAA,OAAA,sIAmCjBC,EAAK,EAAA,CAAA,uBAAV,OAAIC,GAAA,wMAFRC,EAsBKC,EAAAC,EAAAC,CAAA,EArBJC,EAoBKF,EAAAG,CAAA,2EAnBGP,EAAK,EAAA,CAAA,oBAAV,OAAIC,GAAA,EAAA,iHAAJ,iFAeGD,EAAK,EAAA,EAAA,0CAHEQ,EAAAC,EAAA,OAAAC,EAAA,GAAAV,SAAQA,EAAK,EAAA,CAAA,EAAA,gEACRW,EAAAF,EAAA,SAAAT,QAAUA,EAAY,EAAA,CAAA,UAFrCE,EAKGC,EAAAM,EAAAJ,CAAA,uCADAL,EAAK,EAAA,EAAA,KAAAY,EAAAC,EAAAC,CAAA,EAHEC,EAAA,MAAAL,KAAAA,EAAA,GAAAV,SAAQA,EAAK,EAAA,CAAA,2BACRW,EAAAF,EAAA,SAAAT,QAAUA,EAAY,EAAA,CAAA,yCALlCA,EAAK,EAAA,EAAA,yHALOW,EAAAK,EAAA,SAAAhB,QAAUA,EAAY,EAAA,CAAA,UADrCE,EAOQC,EAAAa,EAAAX,CAAA,sEADLL,EAAK,EAAA,EAAA,KAAAY,EAAAC,EAAAC,CAAA,UALOH,EAAAK,EAAA,SAAAhB,QAAUA,EAAY,EAAA,CAAA,sEAFjCA,EAAO,CAAA,EAAAiB,gNAgCVC,EAAAlB,MAAG,mBAAmB,EAAA,eAItBmB,EAAAnB,MAAG,kBAAkB,EAAA,wDARnBA,EAAK,CAAA,CAAA,qEAK8C,GACvD,2GAKaD,CAAU,kHAZkBC,EAAK,CAAA,CAAA,8TAF/CE,EAkBKC,EAAAC,EAAAC,CAAA,EAjBJC,EAIMF,EAAAgB,CAAA,EAHLd,EAEAc,EAAAC,CAAA,gBAEDf,EAGMF,EAAAkB,CAAA,gBADLhB,EAAsDgB,EAAAC,CAAA,gBAEvDjB,EAOMF,EAAAoB,CAAA,gBALLlB,EAIAkB,EAAAC,CAAA,oBAbGzB,EAAK,CAAA,CAAA,gDADgCA,EAAK,CAAA,kBAK5Ce,EAAA,MAAAG,KAAAA,EAAAlB,MAAG,mBAAmB,EAAA,KAAAY,EAAAc,EAAAR,CAAA,EAItBH,EAAA,MAAAI,KAAAA,EAAAnB,MAAG,kBAAkB,EAAA,KAAAY,EAAAe,EAAAR,CAAA,qDAxCtBnB,EAAK,EAAA,EAAC,OAAS,GAAC4B,EAAA5B,CAAA,kDA4BdA,EAAO,CAAA,GAAIA,EAAK,CAAA,GAAIA,EAAI,CAAA,GAAA6B,EAAA7B,CAAA,oJAHqBW,EAAAmB,EAAA,MAAA,CAAA9B,OAAYA,EAAQ,CAAA,CAAA,qDA9B9BA,EAAO,CAAA,EAAA,iBAAA,4EAF1BA,EAAO,CAAA,CAAA,kBACbA,EAAI,CAAA,CAAA,mBAEHA,EAAM,CAAA,EAAG,UAAYA,EAAc,CAAA,CAAA,kBACnCA,EAAO,CAAA,EAAS,OAAN,GAAY,UAPzCE,EA6DKC,EAAA4B,EAAA1B,CAAA,wBA1BJC,EAyBMyB,EAAAD,CAAA,wBAvBLxB,EAsBKwB,EAAAE,CAAA,yCAjDDhC,EAAK,EAAA,EAAC,OAAS,iIA4BbA,EAAO,CAAA,GAAIA,EAAK,CAAA,GAAIA,EAAI,CAAA,0GAHqBW,EAAAmB,EAAA,MAAA,CAAA9B,OAAYA,EAAQ,CAAA,CAAA,wDA9B9BA,EAAO,CAAA,EAAA,0GAF1BA,EAAO,CAAA,CAAA,+BACbA,EAAI,CAAA,CAAA,0BAEHA,EAAM,CAAA,EAAG,UAAYA,EAAc,CAAA,CAAA,yBACnCA,EAAO,CAAA,EAAS,OAAN,GAAY,+KA1B7B,CAAA,QAAAiC,CAAA,EAAAC,EACA,CAAA,QAAAC,CAAA,EAAAD,EACA,CAAA,eAAAE,CAAA,EAAAF,EACA,CAAA,WAAAG,CAAA,EAAAH,EACA,CAAA,SAAAI,CAAA,EAAAJ,EACA,CAAA,QAAAK,CAAA,EAAAL,EAEA,CAAA,MAAAM,CAAA,EAAAN,EACA,CAAA,QAAAO,CAAA,EAAAP,EACA,CAAA,KAAAQ,CAAA,EAAAR,EACA,CAAA,OAAAS,CAAA,EAAAT,EACA,CAAA,MAAAU,EAAA,EAAA,EAAAV,GACA,aAAAW,EAAe,EAAA,EAAAX,EACf,CAAA,KAAAY,CAAA,EAAAZ,EAEL,MAAAa,EACLC,GAAW,eAAe,OAoBVC,IAAC,CACXA,EAAE,eAAc,EAChBF,IAAWG,CAAK,4CAlBZjB,EAAOkB"}