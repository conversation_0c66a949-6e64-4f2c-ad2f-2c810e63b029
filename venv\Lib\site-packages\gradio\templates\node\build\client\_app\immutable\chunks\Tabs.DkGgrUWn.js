import{SvelteComponent as Ee,init as Te,safe_not_equal as Be,svg_element as W,claim_svg_element as X,children as D,detach as k,attr as u,insert_hydration as A,append_hydration as B,noop as x,create_slot as Ue,element as U,space as P,claim_element as S,claim_space as F,toggle_class as V,set_style as ae,listen as L,transition_in as K,group_outros as Se,transition_out as Y,check_outros as je,update_slot_base as Ae,get_all_dirty_from_scope as Re,get_slot_changes as qe,run_all as Me,component_subscribe as ce,createEventDispatcher as Pe,onMount as Fe,setContext as Ge,set_store_value as J,ensure_array_like as G,create_component as He,claim_component as Je,mount_component as Ke,stop_propagation as Le,destroy_each as $,destroy_component as Qe,empty as H,binding_callbacks as ee,text as le,claim_text as te,set_data as ie,tick as We}from"../../../svelte/svelte.js";import{writable as fe}from"../../../svelte/svelte-submodules.js";function Xe(t){let e,l,s,i;return{c(){e=W("svg"),l=W("circle"),s=W("circle"),i=W("circle"),this.h()},l(f){e=X(f,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var c=D(e);l=X(c,"circle",{cx:!0,cy:!0,r:!0,fill:!0}),D(l).forEach(k),s=X(c,"circle",{cx:!0,cy:!0,r:!0,fill:!0}),D(s).forEach(k),i=X(c,"circle",{cx:!0,cy:!0,r:!0,fill:!0}),D(i).forEach(k),c.forEach(k),this.h()},h(){u(l,"cx","2.5"),u(l,"cy","8"),u(l,"r","1.5"),u(l,"fill","currentColor"),u(s,"cx","8"),u(s,"cy","8"),u(s,"r","1.5"),u(s,"fill","currentColor"),u(i,"cx","13.5"),u(i,"cy","8"),u(i,"r","1.5"),u(i,"fill","currentColor"),u(e,"width","16"),u(e,"height","16"),u(e,"viewBox","0 0 16 16"),u(e,"fill","none"),u(e,"xmlns","http://www.w3.org/2000/svg")},m(f,c){A(f,e,c),B(e,l),B(e,s),B(e,i)},p:x,i:x,o:x,d(f){f&&k(e)}}}class Ye extends Ee{constructor(e){super(),Te(this,e,null,Xe,Be,{})}}function _e(t,e,l){const s=t.slice();return s[33]=e[l],s}function ue(t,e,l){const s=t.slice();return s[33]=e[l],s[37]=l,s}function de(t,e,l){const s=t.slice();return s[33]=e[l],s[38]=e,s[37]=l,s}function he(t){let e,l,s,i,f,c,d,g,_,r,b,I,j,m=G(t[3]),p=[];for(let a=0;a<m.length;a+=1)p[a]=me(de(t,m,a));let T=G(t[7]),h=[];for(let a=0;a<T.length;a+=1)h[a]=ge(ue(t,T,a));g=new Ye({});let N=G(t[8]),v=[];for(let a=0;a<N.length;a+=1)v[a]=pe(_e(t,N,a));return{c(){e=U("div"),l=U("div");for(let a=0;a<p.length;a+=1)p[a].c();s=P(),i=U("div");for(let a=0;a<h.length;a+=1)h[a].c();f=P(),c=U("span"),d=U("button"),He(g.$$.fragment),_=P(),r=U("div");for(let a=0;a<v.length;a+=1)v[a].c();this.h()},l(a){e=S(a,"DIV",{class:!0});var w=D(e);l=S(w,"DIV",{class:!0,"aria-hidden":!0});var o=D(l);for(let O=0;O<p.length;O+=1)p[O].l(o);o.forEach(k),s=F(w),i=S(w,"DIV",{class:!0,role:!0});var C=D(i);for(let O=0;O<h.length;O+=1)h[O].l(C);C.forEach(k),f=F(w),c=S(w,"SPAN",{class:!0});var R=D(c);d=S(R,"BUTTON",{class:!0});var q=D(d);Je(g.$$.fragment,q),q.forEach(k),_=F(R),r=S(R,"DIV",{class:!0});var M=D(r);for(let O=0;O<v.length;O+=1)v[O].l(M);M.forEach(k),R.forEach(k),w.forEach(k),this.h()},h(){u(l,"class","tab-container visually-hidden svelte-1tcem6n"),u(l,"aria-hidden","true"),u(i,"class","tab-container svelte-1tcem6n"),u(i,"role","tablist"),u(d,"class","svelte-1tcem6n"),V(d,"overflow-item-selected",t[12]),u(r,"class","overflow-dropdown svelte-1tcem6n"),V(r,"hide",!t[9]),u(c,"class","overflow-menu svelte-1tcem6n"),V(c,"hide",!t[11]||!t[8].some(ke)),u(e,"class","tab-wrapper svelte-1tcem6n")},m(a,w){A(a,e,w),B(e,l);for(let o=0;o<p.length;o+=1)p[o]&&p[o].m(l,null);B(e,s),B(e,i);for(let o=0;o<h.length;o+=1)h[o]&&h[o].m(i,null);t[28](i),B(e,f),B(e,c),B(c,d),Ke(g,d,null),B(c,_),B(c,r);for(let o=0;o<v.length;o+=1)v[o]&&v[o].m(r,null);t[31](c),b=!0,I||(j=L(d,"click",Le(t[29])),I=!0)},p(a,w){if(w[0]&40){m=G(a[3]);let o;for(o=0;o<m.length;o+=1){const C=de(a,m,o);p[o]?p[o].p(C,w):(p[o]=me(C),p[o].c(),p[o].m(l,null))}for(;o<p.length;o+=1)p[o].d(1);p.length=m.length}if(w[0]&393408){T=G(a[7]);let o;for(o=0;o<T.length;o+=1){const C=ue(a,T,o);h[o]?h[o].p(C,w):(h[o]=ge(C),h[o].c(),h[o].m(i,null))}for(;o<h.length;o+=1)h[o].d(1);h.length=T.length}if((!b||w[0]&4096)&&V(d,"overflow-item-selected",a[12]),w[0]&262464){N=G(a[8]);let o;for(o=0;o<N.length;o+=1){const C=_e(a,N,o);v[o]?v[o].p(C,w):(v[o]=pe(C),v[o].c(),v[o].m(r,null))}for(;o<v.length;o+=1)v[o].d(1);v.length=N.length}(!b||w[0]&512)&&V(r,"hide",!a[9]),(!b||w[0]&2304)&&V(c,"hide",!a[11]||!a[8].some(ke))},i(a){b||(K(g.$$.fragment,a),b=!0)},o(a){Y(g.$$.fragment,a),b=!1},d(a){a&&k(e),$(p,a),$(h,a),t[28](null),Qe(g),$(v,a),t[31](null),I=!1,j()}}}function be(t){var g;let e,l=((g=t[33])==null?void 0:g.label)+"",s,i,f=t[33];const c=()=>t[26](e,f),d=()=>t[26](null,f);return{c(){e=U("button"),s=le(l),i=P(),this.h()},l(_){e=S(_,"BUTTON",{class:!0});var r=D(e);s=te(r,l),i=F(r),r.forEach(k),this.h()},h(){u(e,"class","svelte-1tcem6n")},m(_,r){A(_,e,r),B(e,s),B(e,i),c()},p(_,r){var b;t=_,r[0]&8&&l!==(l=((b=t[33])==null?void 0:b.label)+"")&&ie(s,l),f!==t[33]&&(d(),f=t[33],c())},d(_){_&&k(e),d()}}}function me(t){var s;let e,l=((s=t[33])==null?void 0:s.visible)&&be(t);return{c(){l&&l.c(),e=H()},l(i){l&&l.l(i),e=H()},m(i,f){l&&l.m(i,f),A(i,e,f)},p(i,f){var c;(c=i[33])!=null&&c.visible?l?l.p(i,f):(l=be(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&k(e),l&&l.d(i)}}}function ve(t){var m,p;let e,l=(((m=t[33])==null?void 0:m.label)!==void 0?(p=t[33])==null?void 0:p.label:"Tab "+(t[37]+1))+"",s,i,f,c,d,g,_,r,b,I;function j(){return t[27](t[33],t[37])}return{c(){e=U("button"),s=le(l),i=P(),this.h()},l(T){e=S(T,"BUTTON",{role:!0,"aria-selected":!0,"aria-controls":!0,"aria-disabled":!0,id:!0,"data-tab-id":!0,class:!0});var h=D(e);s=te(h,l),i=F(h),h.forEach(k),this.h()},h(){u(e,"role","tab"),u(e,"aria-selected",f=t[33].id===t[6]),u(e,"aria-controls",c=t[33].elem_id),e.disabled=d=!t[33].interactive,u(e,"aria-disabled",g=!t[33].interactive),u(e,"id",_=t[33].elem_id?t[33].elem_id+"-button":null),u(e,"data-tab-id",r=t[33].id),u(e,"class","svelte-1tcem6n"),V(e,"selected",t[33].id===t[6])},m(T,h){A(T,e,h),B(e,s),B(e,i),b||(I=L(e,"click",j),b=!0)},p(T,h){var N,v;t=T,h[0]&128&&l!==(l=(((N=t[33])==null?void 0:N.label)!==void 0?(v=t[33])==null?void 0:v.label:"Tab "+(t[37]+1))+"")&&ie(s,l),h[0]&192&&f!==(f=t[33].id===t[6])&&u(e,"aria-selected",f),h[0]&128&&c!==(c=t[33].elem_id)&&u(e,"aria-controls",c),h[0]&128&&d!==(d=!t[33].interactive)&&(e.disabled=d),h[0]&128&&g!==(g=!t[33].interactive)&&u(e,"aria-disabled",g),h[0]&128&&_!==(_=t[33].elem_id?t[33].elem_id+"-button":null)&&u(e,"id",_),h[0]&128&&r!==(r=t[33].id)&&u(e,"data-tab-id",r),h[0]&192&&V(e,"selected",t[33].id===t[6])},d(T){T&&k(e),b=!1,I()}}}function ge(t){var s;let e,l=((s=t[33])==null?void 0:s.visible)&&ve(t);return{c(){l&&l.c(),e=H()},l(i){l&&l.l(i),e=H()},m(i,f){l&&l.m(i,f),A(i,e,f)},p(i,f){var c;(c=i[33])!=null&&c.visible?l?l.p(i,f):(l=ve(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&k(e),l&&l.d(i)}}}function we(t){var g;let e,l=((g=t[33])==null?void 0:g.label)+"",s,i,f,c;function d(){return t[30](t[33])}return{c(){e=U("button"),s=le(l),i=P(),this.h()},l(_){e=S(_,"BUTTON",{class:!0});var r=D(e);s=te(r,l),i=F(r),r.forEach(k),this.h()},h(){var _;u(e,"class","svelte-1tcem6n"),V(e,"selected",((_=t[33])==null?void 0:_.id)===t[6])},m(_,r){A(_,e,r),B(e,s),B(e,i),f||(c=L(e,"click",d),f=!0)},p(_,r){var b,I;t=_,r[0]&256&&l!==(l=((b=t[33])==null?void 0:b.label)+"")&&ie(s,l),r[0]&320&&V(e,"selected",((I=t[33])==null?void 0:I.id)===t[6])},d(_){_&&k(e),f=!1,c()}}}function pe(t){var s;let e,l=((s=t[33])==null?void 0:s.visible)&&we(t);return{c(){l&&l.c(),e=H()},l(i){l&&l.l(i),e=H()},m(i,f){l&&l.m(i,f),A(i,e,f)},p(i,f){var c;(c=i[33])!=null&&c.visible?l?l.p(i,f):(l=we(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&k(e),l&&l.d(i)}}}function Ze(t){let e,l,s,i,f,c,d=t[14]&&he(t);const g=t[25].default,_=Ue(g,t,t[24],null);return{c(){e=U("div"),d&&d.c(),l=P(),_&&_.c(),this.h()},l(r){e=S(r,"DIV",{class:!0,id:!0});var b=D(e);d&&d.l(b),l=F(b),_&&_.l(b),b.forEach(k),this.h()},h(){u(e,"class",s="tabs "+t[2].join(" ")+" svelte-1tcem6n"),u(e,"id",t[1]),V(e,"hide",!t[0]),ae(e,"flex-grow",t[13])},m(r,b){A(r,e,b),d&&d.m(e,null),B(e,l),_&&_.m(e,null),i=!0,f||(c=[L(window,"resize",t[20]),L(window,"click",t[19])],f=!0)},p(r,b){r[14]?d?(d.p(r,b),b[0]&16384&&K(d,1)):(d=he(r),d.c(),K(d,1),d.m(e,l)):d&&(Se(),Y(d,1,1,()=>{d=null}),je()),_&&_.p&&(!i||b[0]&16777216)&&Ae(_,g,r,r[24],i?qe(g,r[24],b,null):Re(r[24]),null),(!i||b[0]&4&&s!==(s="tabs "+r[2].join(" ")+" svelte-1tcem6n"))&&u(e,"class",s),(!i||b[0]&2)&&u(e,"id",r[1]),(!i||b[0]&5)&&V(e,"hide",!r[0]),b[0]&8192&&ae(e,"flex-grow",r[13])},i(r){i||(K(d),K(_,r),i=!0)},o(r){Y(d),Y(_,r),i=!1},d(r){r&&k(e),d&&d.d(),_&&_.d(r),f=!1,Me(c)}}}const ye={};function xe(t,e){const l={};return t.forEach(s=>{var i;s&&(l[s.id]=(i=e[s.id])==null?void 0:i.getBoundingClientRect())}),l}const ke=t=>t==null?void 0:t.visible;function $e(t,e,l){var se;let s,i,f,c,{$$slots:d={},$$scope:g}=e,{visible:_=!0}=e,{elem_id:r=""}=e,{elem_classes:b=[]}=e,{selected:I}=e,{initial_tabs:j}=e,m=[...j],p=[...j],T=[],h=!1,N,v;const a=fe(I||((se=m[0])==null?void 0:se.id)||!1);ce(t,a,n=>l(6,c=n));const w=fe(m.findIndex(n=>(n==null?void 0:n.id)===I)||0);ce(t,w,n=>l(23,f=n));const o=Pe();let C=!1,R=!1,q={};Fe(()=>{new IntersectionObserver(E=>{Z()}).observe(v)}),Ge(ye,{register_tab:(n,E)=>(l(3,m[E]=n,m),c===!1&&n.visible&&n.interactive&&(J(a,c=n.id,c),J(w,f=E,f)),E),unregister_tab:(n,E)=>{var z;c===n.id&&J(a,c=((z=m[0])==null?void 0:z.id)||!1,c),l(3,m[E]=null,m)},selected_tab:a,selected_tab_index:w});function M(n){const E=m.find(z=>(z==null?void 0:z.id)===n);n!==void 0&&E&&E.interactive&&E.visible&&c!==E.id&&(l(21,I=n),J(a,c=n,c),J(w,f=m.findIndex(z=>(z==null?void 0:z.id)===n),f),o("change"),l(9,h=!1))}function O(n){h&&N&&!N.contains(n.target)&&l(9,h=!1)}async function Z(){if(!v)return;await We();const n=v.getBoundingClientRect();let E=n.width;const z=xe(m,q);let y=0;const Ve=n.left;for(let Q=m.length-1;Q>=0;Q--){const oe=m[Q];if(!oe)continue;const re=z[oe.id];if(re&&re.right-Ve<E){y=Q;break}}l(8,T=m.slice(y+1)),l(7,p=m.slice(0,y+1)),l(12,R=ne(c)),l(11,C=T.length>0)}function ne(n){return n===!1?!1:T.some(E=>(E==null?void 0:E.id)===n)}function Ie(n,E){ee[n?"unshift":"push"](()=>{q[E.id]=n,l(5,q)})}const Ne=(n,E)=>{n.id!==c&&(M(n.id),o("select",{value:n.label,index:E}))};function Ce(n){ee[n?"unshift":"push"](()=>{v=n,l(4,v)})}const De=()=>l(9,h=!h),Oe=n=>M(n==null?void 0:n.id);function ze(n){ee[n?"unshift":"push"](()=>{N=n,l(10,N)})}return t.$$set=n=>{"visible"in n&&l(0,_=n.visible),"elem_id"in n&&l(1,r=n.elem_id),"elem_classes"in n&&l(2,b=n.elem_classes),"selected"in n&&l(21,I=n.selected),"initial_tabs"in n&&l(22,j=n.initial_tabs),"$$scope"in n&&l(24,g=n.$$scope)},t.$$.update=()=>{var n;t.$$.dirty[0]&8&&l(14,s=m.length>0),t.$$.dirty[0]&2097160&&I!==null&&M(I),t.$$.dirty[0]&56&&Z(),t.$$.dirty[0]&64&&l(12,R=ne(c)),t.$$.dirty[0]&8388616&&l(13,i=(n=m[f>=0?f:0])==null?void 0:n.scale)},[_,r,b,m,v,q,c,p,T,h,N,C,R,i,s,a,w,o,M,O,Z,I,j,f,g,d,Ie,Ne,Ce,De,Oe,ze]}class el extends Ee{constructor(e){super(),Te(this,e,$e,Ze,Be,{visible:0,elem_id:1,elem_classes:2,selected:21,initial_tabs:22},null,[-1,-1])}}const il=el;export{il as T,ye as a};
//# sourceMappingURL=Tabs.DkGgrUWn.js.map
