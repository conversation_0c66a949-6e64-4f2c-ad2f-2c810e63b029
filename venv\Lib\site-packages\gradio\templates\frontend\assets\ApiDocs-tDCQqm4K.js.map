{"version": 3, "file": "ApiDocs-tDCQqm4K.js", "sources": ["../../../../js/core/src/api_docs/NoApi.svelte", "../../../../js/core/src/api_docs/ApiBanner.svelte", "../../../../js/core/src/api_docs/utils.ts", "../../../../js/core/src/api_docs/ParametersSnippet.svelte", "../../../../js/core/src/api_docs/CopyButton.svelte", "../../../../js/core/src/api_docs/InstallSnippet.svelte", "../../../../js/core/src/api_docs/EndpointDetail.svelte", "../../../../js/core/src/api_docs/CodeSnippet.svelte", "../../../../js/core/src/api_docs/RecordingSnippet.svelte", "../../../../js/core/src/api_docs/img/python.svg", "../../../../js/core/src/api_docs/img/javascript.svg", "../../../../js/core/src/api_docs/img/bash.svg", "../../../../js/core/src/api_docs/ResponseSnippet.svelte", "../../../../js/core/src/api_docs/img/mcp.svg", "../../../../js/core/src/api_docs/MCPSnippet.svelte", "../../../../js/core/src/api_docs/ApiDocs.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport Clear from \"./img/clear.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let root: string;\n</script>\n\n<div class=\"wrap prose\">\n\t<h1>API Docs</h1>\n\t<p class=\"attention\">\n\t\tNo API Routes found for\n\t\t<code>\n\t\t\t{root}\n\t\t</code>\n\t</p>\n\t<p>\n\t\tTo expose an API endpoint of your app in this page, set the <code>\n\t\t\tapi_name\n\t\t</code>\n\t\tparameter of the event listener.\n\t\t<br />\n\t\tFor more information, visit the\n\t\t<a href=\"https://gradio.app/sharing_your_app/#api-page\" target=\"_blank\">\n\t\t\tAPI Page guide\n\t\t</a>\n\t\t. To hide the API documentation button and this page, set\n\t\t<code>show_api=False</code>\n\t\tin the\n\t\t<code>Blocks.launch()</code>\n\t\tmethod.\n\t</p>\n</div>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\t.wrap {\n\t\tpadding: var(--size-6);\n\t}\n\n\t.attention {\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.attention code {\n\t\tborder: none;\n\t\tbackground: none;\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport api_logo from \"./img/api-logo.svg\";\n\timport Clear from \"./img/clear.svelte\";\n\timport { BaseButton } from \"@gradio/button\";\n\n\texport let root: string;\n\texport let api_count: number;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\" | \"mcp\" =\n\t\t\"python\";\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<h2>\n\t<img src={api_logo} alt=\"\" />\n\t<div class=\"title\">\n\t\t{#if current_language === \"mcp\"}MCP{:else}API{/if} documentation\n\t\t<div class=\"url\">\n\t\t\t{root}\n\t\t</div>\n\t</div>\n\t<span class=\"counts\">\n\t\t{#if current_language !== \"mcp\"}\n\t\t\t<BaseButton\n\t\t\t\tsize=\"sm\"\n\t\t\t\tvariant=\"secondary\"\n\t\t\t\telem_id=\"start-api-recorder\"\n\t\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t>\n\t\t\t\t<div class=\"loading-dot self-baseline\"></div>\n\t\t\t\t<p class=\"self-baseline btn-text\">API Recorder</p>\n\t\t\t</BaseButton>\n\t\t{/if}\n\t\t<p>\n\t\t\t<span class=\"url\">{api_count}</span>\n\t\t\t{#if current_language !== \"mcp\"}API endpoint{:else}MCP Tool{/if}{#if api_count > 1}s{/if}<br\n\t\t\t/>\n\t\t</p>\n\t</span>\n</h2>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\th2 {\n\t\tdisplay: flex;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tgap: var(--size-4);\n\t}\n\n\th2 img {\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-4);\n\t\tdisplay: inline-block;\n\t}\n\n\t.url {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: normal;\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\n\t\th2 img {\n\t\t\twidth: var(--size-5);\n\t\t}\n\t}\n\n\t.counts {\n\t\tmargin-top: auto;\n\t\tmargin-right: var(--size-8);\n\t\tmargin-bottom: auto;\n\t\tmargin-left: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-light);\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 12px;\n\t\theight: 12px;\n\t\tborder-radius: 6px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tmargin-right: 0.3rem;\n\t}\n\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t.title {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n\t.btn-text {\n\t\tfont-size: var(--text-lg);\n\t}\n</style>\n", "// eslint-disable-next-line complexity\nexport function represent_value(\n\tvalue: string,\n\ttype: string | undefined,\n\tlang: \"js\" | \"py\" | \"bash\" | null = null\n): string | null | number | boolean | Record<string, unknown> {\n\tif (type === undefined) {\n\t\treturn lang === \"py\" ? \"None\" : null;\n\t}\n\tif (value === null && lang === \"py\") {\n\t\treturn \"None\";\n\t}\n\tif (type === \"string\" || type === \"str\") {\n\t\treturn lang === null ? value : '\"' + value + '\"';\n\t} else if (type === \"number\") {\n\t\treturn lang === null ? parseFloat(value) : value;\n\t} else if (type === \"boolean\" || type == \"bool\") {\n\t\tif (lang === \"py\") {\n\t\t\tvalue = String(value);\n\t\t\treturn value === \"true\" ? \"True\" : \"False\";\n\t\t} else if (lang === \"js\" || lang === \"bash\") {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === \"true\";\n\t} else if (type === \"List[str]\") {\n\t\tvalue = JSON.stringify(value);\n\t\treturn value;\n\t} else if (type.startsWith(\"Literal['\")) {\n\t\t// a literal of strings\n\t\treturn '\"' + value + '\"';\n\t}\n\t// assume object type\n\tif (lang === null) {\n\t\treturn value === \"\" ? null : JSON.parse(value);\n\t} else if (typeof value === \"string\") {\n\t\tif (value === \"\") {\n\t\t\treturn lang === \"py\" ? \"None\" : \"null\";\n\t\t}\n\t\treturn value;\n\t}\n\tif (lang === \"bash\") {\n\t\tvalue = simplify_file_data(value);\n\t}\n\tif (lang === \"py\") {\n\t\tvalue = replace_file_data_with_file_function(value);\n\t}\n\treturn stringify_except_file_function(value);\n}\n\nexport function is_potentially_nested_file_data(obj: any): boolean {\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tif (obj.hasOwnProperty(\"url\") && obj.hasOwnProperty(\"meta\")) {\n\t\t\tif (\n\t\t\t\ttypeof obj.meta === \"object\" &&\n\t\t\t\tobj.meta !== null &&\n\t\t\t\tobj.meta._type === \"gradio.FileData\"\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t}\n\tif (typeof obj === \"object\" && obj !== null) {\n\t\tfor (let key in obj) {\n\t\t\tif (typeof obj[key] === \"object\") {\n\t\t\t\tlet result = is_potentially_nested_file_data(obj[key]);\n\t\t\t\tif (result) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\treturn false;\n}\n\nfunction simplify_file_data(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\tobj.url &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn { path: obj.url, meta: { _type: \"gradio.FileData\" } };\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = simplify_file_data(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = simplify_file_data(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction replace_file_data_with_file_function(obj: any): any {\n\tif (typeof obj === \"object\" && obj !== null && !Array.isArray(obj)) {\n\t\tif (\n\t\t\t\"url\" in obj &&\n\t\t\tobj.url &&\n\t\t\t\"meta\" in obj &&\n\t\t\tobj.meta?._type === \"gradio.FileData\"\n\t\t) {\n\t\t\treturn `handle_file('${obj.url}')`;\n\t\t}\n\t}\n\tif (Array.isArray(obj)) {\n\t\tobj.forEach((item, index) => {\n\t\t\tif (typeof item === \"object\" && item !== null) {\n\t\t\t\tobj[index] = replace_file_data_with_file_function(item); // Recurse and update array elements\n\t\t\t}\n\t\t});\n\t} else if (typeof obj === \"object\" && obj !== null) {\n\t\tObject.keys(obj).forEach((key) => {\n\t\t\tobj[key] = replace_file_data_with_file_function(obj[key]); // Recurse and update object properties\n\t\t});\n\t}\n\treturn obj;\n}\n\nfunction stringify_except_file_function(obj: any): string {\n\tlet jsonString = JSON.stringify(obj, (key, value) => {\n\t\tif (value === null) {\n\t\t\treturn \"UNQUOTEDNone\";\n\t\t}\n\t\tif (\n\t\t\ttypeof value === \"string\" &&\n\t\t\tvalue.startsWith(\"handle_file(\") &&\n\t\t\tvalue.endsWith(\")\")\n\t\t) {\n\t\t\treturn `UNQUOTED${value}`; // Flag the special strings\n\t\t}\n\t\treturn value;\n\t});\n\tconst regex = /\"UNQUOTEDhandle_file\\(([^)]*)\\)\"/g;\n\tjsonString = jsonString.replace(regex, (match, p1) => `handle_file(${p1})`);\n\tconst regexNone = /\"UNQUOTEDNone\"/g;\n\treturn jsonString.replace(regexNone, \"None\");\n}\n", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\timport { represent_value } from \"./utils\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot\" />\n\t</div>\n\tAccepts {endpoint_returns.length} parameter{#if endpoint_returns.length != 1}s{/if}:\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, python_type, component, parameter_name, parameter_has_default, parameter_default }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p style=\"white-space: nowrap; overflow-x: auto;\">\n\t\t\t\t<span class=\"code\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{current_language !== \"bash\" && parameter_name\n\t\t\t\t\t\t? parameter_name\n\t\t\t\t\t\t: \"[\" + i + \"]\"}</span\n\t\t\t\t>\n\t\t\t\t<span class=\"code highlight\" style=\"margin-right: 10px;\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{#if parameter_has_default && parameter_default === null}&nbsp;|\n\t\t\t\t\t\t\tNone{/if}{:else}{js_returns[i].type || \"any\"}{/if}</span\n\t\t\t\t>\n\t\t\t\t{#if !parameter_has_default || current_language == \"bash\"}<span\n\t\t\t\t\t\tstyle=\"font-weight:bold\">Required</span\n\t\t\t\t\t>{:else}<span> Default: </span><span\n\t\t\t\t\t\tclass=\"code\"\n\t\t\t\t\t\tstyle=\"font-size: var(--text-sm);\"\n\t\t\t\t\t\t>{represent_value(parameter_default, python_type.type, \"py\")}</span\n\t\t\t\t\t>{/if}\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe input value that is provided in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->. {python_type.description}\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t\tmargin-bottom: 12px;\n\t}\n\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tdisplay: inline;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t\tmargin-top: var(--size-1);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-right: auto;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { BaseButton } from \"@gradio/button\";\n\texport let code: string;\n\tlet copy_text = \"copy\";\n\n\tfunction copy(): void {\n\t\tnavigator.clipboard.writeText(code);\n\t\tcopy_text = \"copied!\";\n\t\tsetTimeout(() => {\n\t\t\tcopy_text = \"copy\";\n\t\t}, 1500);\n\t}\n</script>\n\n<BaseButton size=\"sm\" on:click={copy}>\n\t{copy_text}\n</BaseButton>\n", "<script lang=\"ts\">\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let current_language: \"python\" | \"javascript\" | \"bash\" | \"mcp\";\n\n\tlet py_install = \"pip install gradio_client\";\n\tlet js_install = \"npm i -D @gradio/client\";\n\tlet bash_install = \"curl --version\";\n</script>\n\n<Block>\n\t<code>\n\t\t{#if current_language === \"python\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={py_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {py_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={js_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {js_install}</pre>\n\t\t\t</div>\n\t\t{:else if current_language === \"bash\"}\n\t\t\t<div class=\"copy\">\n\t\t\t\t<CopyButton code={bash_install} />\n\t\t\t</div>\n\t\t\t<div>\n\t\t\t\t<pre>$ {bash_install}</pre>\n\t\t\t</div>\n\t\t{/if}\n\t</code>\n</Block>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let api_name: string | null = null;\n\texport let description: string | null = null;\n</script>\n\n<h3>\n\tAPI name:\n\t<span class=\"post\">{\"/\" + api_name}</span>\n\t<span class=\"desc\">{description}</span>\n</h3>\n\n<style>\n\th3 {\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.post {\n\t\tmargin-right: var(--size-2);\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: var(--size-1);\n\t\tpadding-left: var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t\tmargin-top: var(--size-1);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport Copy<PERSON>utton from \"./CopyButton.svelte\";\n\timport { represent_value, is_potentially_nested_file_data } from \"./utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport EndpointDetail from \"./EndpointDetail.svelte\";\n\n\tinterface EndpointParameter {\n\t\tlabel: string;\n\t\ttype: string;\n\t\tpython_type: { type: string };\n\t\tcomponent: string;\n\t\texample_input: string;\n\t\tserializer: string;\n\t}\n\n\texport let dependency: Dependency;\n\texport let root: string;\n\texport let api_prefix: string;\n\texport let space_id: string | null;\n\texport let endpoint_parameters: any;\n\texport let username: string | null;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n\texport let api_description: string | null = null;\n\n\tlet python_code: HTMLElement;\n\tlet js_code: HTMLElement;\n\tlet bash_post_code: HTMLElement;\n\tlet bash_get_code: HTMLElement;\n\n\tlet has_file_path = endpoint_parameters.some((param: EndpointParameter) =>\n\t\tis_potentially_nested_file_data(param.example_input)\n\t);\n\tlet blob_components = [\"Audio\", \"File\", \"Image\", \"Video\"];\n\tlet blob_examples: any[] = endpoint_parameters.filter(\n\t\t(param: EndpointParameter) => blob_components.includes(param.component)\n\t);\n\n\t$: normalised_api_prefix = api_prefix ? api_prefix : \"/\";\n\t$: normalised_root = root.replace(/\\/$/, \"\");\n</script>\n\n<div class=\"container\">\n\t<EndpointDetail\n\t\tapi_name={dependency.api_name}\n\t\tdescription={api_description}\n\t/>\n\t{#if current_language === \"python\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client{#if has_file_path}, handle_file{/if}\n\nclient = Client(<span class=\"token string\">\"{space_id || root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, auth=(\"{username}\", **password**){/if})\nresult = client.<span class=\"highlight\">predict</span\n\t\t\t\t\t\t>(<!--\n-->{#each endpoint_parameters as { python_type, example_input, parameter_name, parameter_has_default, parameter_default }, i}<!--\n        -->\n\t\t{parameter_name\n\t\t\t\t\t\t\t\t? parameter_name + \"=\"\n\t\t\t\t\t\t\t\t: \"\"}<span\n\t\t\t\t\t\t\t\t>{represent_value(\n\t\t\t\t\t\t\t\t\tparameter_has_default ? parameter_default : example_input,\n\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t>,{/each}<!--\n\n\t\t-->\n\t\tapi_name=<span class=\"api-name\">\"/{dependency.api_name}\"</span><!--\n\t\t-->\n)\n<span class=\"highlight\">print</span>(result)</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_language === \"javascript\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; Client &rbrace; from \"@gradio/client\";\n{#each blob_examples as { component, example_input }, i}<!--\n-->\nconst response_{i} = await fetch(\"{example_input.url}\");\nconst example{component} = await response_{i}.blob();\n\t\t\t\t\t\t{/each}<!--\n-->\nconst client = await Client.connect(<span class=\"token string\"\n\t\t\t\t\t\t\t>\"{space_id || root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, &lbrace;auth: [\"{username}\", **password**]&rbrace;{/if});\nconst result = await client.predict(<span class=\"api-name\"\n\t\t\t\t\t\t\t>\"/{dependency.api_name}\"</span\n\t\t\t\t\t\t>, &lbrace; <!--\n-->{#each endpoint_parameters as { label, parameter_name, type, python_type, component, example_input, serializer }, i}<!--\n\t\t-->{#if blob_components.includes(component)}<!--\n\t-->\n\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{parameter_name}: example{component}</span\n\t\t\t\t\t\t\t\t>, <!--\n\t\t--><span class=\"desc\"><!--\n\t\t--></span\n\t\t\t\t\t\t\t\t><!--\n\t\t-->{:else}<!--\n\t-->\t\t\n\t\t<span class=\"example-inputs\"\n\t\t\t\t\t\t\t\t\t>{parameter_name}: {represent_value(\n\t\t\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t\t\t\t)}</span\n\t\t\t\t\t\t\t\t>, <!--\n--><!--\n-->{/if}\n\t\t\t\t\t\t{/each}\n&rbrace;);\n\nconsole.log(result.data);\n</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_language === \"bash\"}\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={bash_post_code?.innerText}></CopyButton>\n\t\t\t\t</div>\n\n\t\t\t\t<div bind:this={bash_post_code}>\n\t\t\t\t\t<pre>curl -X POST {normalised_root}{normalised_api_prefix}/call/{dependency.api_name} -s -H \"Content-Type: application/json\" -d '{\"{\"}\n  \"data\": [{#each endpoint_parameters as { label, parameter_name, type, python_type, component, example_input, serializer }, i}\n\t\t\t\t\t\t\t<!-- \n-->{represent_value(\n\t\t\t\t\t\t\t\texample_input,\n\t\t\t\t\t\t\t\tpython_type.type,\n\t\t\t\t\t\t\t\t\"bash\"\n\t\t\t\t\t\t\t)}{#if i < endpoint_parameters.length - 1},\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/each}\n]{\"}\"}' \\\n  | awk -F'\"' '{\"{\"} print $4{\"}\"}'  \\\n  | read EVENT_ID; curl -N {normalised_root}{normalised_api_prefix}/call/{dependency.api_name}/$EVENT_ID</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{/if}\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Dependency, Payload } from \"../types\";\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { represent_value } from \"./utils\";\n\timport { onMount, tick } from \"svelte\";\n\n\texport let dependencies: Dependency[];\n\texport let short_root: string;\n\texport let root: string;\n\texport let api_prefix = \"\";\n\texport let current_language: \"python\" | \"javascript\" | \"bash\" | \"mcp\";\n\texport let username: string | null;\n\n\tlet python_code: HTMLElement;\n\tlet python_code_text: string;\n\tlet js_code: HTMLElement;\n\tlet bash_code: HTMLElement;\n\n\texport let api_calls: Payload[] = [];\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(\n\t\t\troot.replace(/\\/$/, \"\") + api_prefix + \"/info/?all_endpoints=true\"\n\t\t);\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\n\tlet endpoints_info: any;\n\tlet py_zipped: { call: string; api_name: string }[] = [];\n\tlet js_zipped: { call: string; api_name: string }[] = [];\n\tlet bash_zipped: { call: string; api_name: string }[] = [];\n\n\tfunction format_api_call(call: Payload, lang: \"py\" | \"js\" | \"bash\"): string {\n\t\tconst api_name = `/${dependencies[call.fn_index].api_name}`;\n\t\t// If an input is undefined (distinct from null) then it corresponds to a State component.\n\t\tlet call_data_excluding_state = call.data.filter(\n\t\t\t(d) => typeof d !== \"undefined\"\n\t\t);\n\n\t\tconst params = call_data_excluding_state\n\t\t\t.map((param, index) => {\n\t\t\t\tif (endpoints_info[api_name]) {\n\t\t\t\t\tconst param_info = endpoints_info[api_name].parameters[index];\n\t\t\t\t\tif (!param_info) {\n\t\t\t\t\t\treturn undefined;\n\t\t\t\t\t}\n\t\t\t\t\tconst param_name = param_info.parameter_name;\n\t\t\t\t\tconst python_type = param_info.python_type.type;\n\t\t\t\t\tif (lang === \"py\") {\n\t\t\t\t\t\treturn `  ${param_name}=${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"py\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t} else if (lang === \"js\") {\n\t\t\t\t\t\treturn `    ${param_name}: ${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"js\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t} else if (lang === \"bash\") {\n\t\t\t\t\t\treturn `    ${represent_value(\n\t\t\t\t\t\t\tparam as string,\n\t\t\t\t\t\t\tpython_type,\n\t\t\t\t\t\t\t\"bash\"\n\t\t\t\t\t\t)}`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn `  ${represent_value(param as string, undefined, lang)}`;\n\t\t\t})\n\t\t\t.filter((d) => typeof d !== \"undefined\")\n\t\t\t.join(\",\\n\");\n\t\tif (params) {\n\t\t\tif (lang === \"py\") {\n\t\t\t\treturn `${params},\\n`;\n\t\t\t} else if (lang === \"js\") {\n\t\t\t\treturn `{\\n${params},\\n}`;\n\t\t\t} else if (lang === \"bash\") {\n\t\t\t\treturn `\\n${params}\\n`;\n\t\t\t}\n\t\t}\n\t\tif (lang === \"py\") {\n\t\t\treturn \"\";\n\t\t}\n\t\treturn \"\\n\";\n\t}\n\n\tonMount(async () => {\n\t\tconst data = await get_info();\n\t\tendpoints_info = data[\"named_endpoints\"];\n\t\tlet py_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"py\")\n\t\t);\n\t\tlet js_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"js\")\n\t\t);\n\t\tlet bash_api_calls: string[] = api_calls.map((call) =>\n\t\t\tformat_api_call(call, \"bash\")\n\t\t);\n\t\tlet api_names: string[] = api_calls.map(\n\t\t\t(call) => dependencies[call.fn_index].api_name || \"\"\n\t\t);\n\t\tpy_zipped = py_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\t\tjs_zipped = js_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\t\tbash_zipped = bash_api_calls.map((call, index) => ({\n\t\t\tcall,\n\t\t\tapi_name: api_names[index]\n\t\t}));\n\n\t\tawait tick();\n\n\t\tpython_code_text = python_code.innerText;\n\t});\n</script>\n\n<div class=\"container\">\n\t<!-- <EndpointDetail {named} api_name={dependency.api_name} /> -->\n\t<Block border_mode={\"focus\"}>\n\t\t{#if current_language === \"python\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={python_code_text} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={python_code}>\n\t\t\t\t\t<pre><span class=\"highlight\">from</span> gradio_client <span\n\t\t\t\t\t\t\tclass=\"highlight\">import</span\n\t\t\t\t\t\t> Client, file\n\nclient = Client(<span class=\"token string\">\"{short_root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, auth=(\"{username}\", **password**){/if})\n{#each py_zipped as { call, api_name }}<!--\n-->\nclient.<span class=\"highlight\"\n\t\t\t\t\t\t\t\t>predict(\n{call}  api_name=<span class=\"api-name\">\"/{api_name}\"</span>\n)\n</span>{/each}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{:else if current_language === \"javascript\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={js_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={js_code}>\n\t\t\t\t\t<pre>import &lbrace; Client &rbrace; from \"@gradio/client\";\n\nconst app = await Client.connect(<span class=\"token string\">\"{short_root}\"</span\n\t\t\t\t\t\t>{#if username !== null}, &lbrace;auth: [\"{username}\", **password**]&rbrace;{/if});\n\t\t\t\t\t{#each js_zipped as { call, api_name }}<!--\n\t\t\t\t\t-->\nawait client.predict(<span\n\t\t\t\t\t\t\t\tclass=\"api-name\">\n  \"/{api_name}\"</span\n\t\t\t\t\t\t\t>{#if call}, {call}{/if});\n\t\t\t\t\t\t{/each}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{:else if current_language === \"bash\"}\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={bash_code?.innerText} />\n\t\t\t\t</div>\n\t\t\t\t<div bind:this={bash_code}>\n\t\t\t\t\t{#each bash_zipped as { call, api_name }}\n\t\t\t\t\t\t<pre>curl -X POST {short_root}call/{api_name} -s -H \"Content-Type: application/json\" -d '{\"{\"} \n\t\"data\": [{call}]{\"}\"}' \\\n  | awk -F'\"' '{\"{\"} print $4{\"}\"}' \\\n  | read EVENT_ID; curl -N {short_root}call/{api_name}/$EVENT_ID</pre>\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t{/if}\n\t</Block>\n</div>\n\n<style>\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\tcode {\n\t\tposition: relative;\n\t\tdisplay: block;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: -5px;\n\t\tmargin-right: -5px;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\timport { Loader } from \"@gradio/statustracker\";\n\n\texport let is_running: boolean;\n\texport let endpoint_returns: any;\n\texport let js_returns: any;\n\texport let current_language: \"python\" | \"javascript\" | \"bash\";\n</script>\n\n<h4>\n\t<div class=\"toggle-icon\">\n\t\t<div class=\"toggle-dot toggle-right\" />\n\t</div>\n\tReturns {#if endpoint_returns.length > 1}\n\t\t{current_language == \"python\" ? \"tuple\" : \"list\"} of {endpoint_returns.length}\n\t\telements{:else}\n\t\t1 element{/if}\n</h4>\n\n<div class:hide={is_running}>\n\t{#each endpoint_returns as { label, type, python_type, component, serializer }, i}\n\t\t<hr class=\"hr\" />\n\t\t<div style=\"margin:10px;\">\n\t\t\t<p>\n\t\t\t\t{#if endpoint_returns.length > 1}\n\t\t\t\t\t<span class=\"code\">[{i}]</span>\n\t\t\t\t{/if}\n\t\t\t\t<span class=\"code highlight\"\n\t\t\t\t\t>{#if current_language === \"python\"}{python_type.type}{:else}{js_returns[\n\t\t\t\t\t\t\ti\n\t\t\t\t\t\t].type}{/if}</span\n\t\t\t\t>\n\t\t\t</p>\n\t\t\t<p class=\"desc\">\n\t\t\t\tThe output value that appears in the \"{label}\" <!--\n\t-->{component}\n\t\t\t\tcomponent<!--\n\t-->.\n\t\t\t</p>\n\t\t</div>\n\t{/each}\n</div>\n{#if is_running}\n\t<div class=\"load-wrap\">\n\t\t<Loader margin={false} />\n\t</div>\n{/if}\n\n<style>\n\t.hr {\n\t\tborder: 0;\n\t\theight: 1px;\n\t\tbackground: var(--color-accent-soft);\n\t}\n\t.code {\n\t\tfont-family: var(--font-mono);\n\t\tmargin-right: 10px;\n\t}\n\n\t.highlight {\n\t\tbackground: var(--color-accent-soft);\n\t\tcolor: var(--color-accent);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-lg);\n\t}\n\n\th4 {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-6);\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.toggle-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-right: var(--size-2);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-300);\n\t\twidth: 12px;\n\t\theight: 4px;\n\t}\n\n\t.toggle-dot {\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--color-grey-700);\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\tmargin-left: auto;\n\t}\n</style>\n", "export default \"__VITE_ASSET__CKRBDa1e__\"", "<script lang=\"ts\">\n\timport { Block } from \"@gradio/atoms\";\n\timport Copy<PERSON>utton from \"./CopyButton.svelte\";\n\n\texport let mcp_server_active: boolean;\n\texport let mcp_server_url: string;\n\texport let mcp_server_url_streamable: string;\n\texport let tools: Tool[];\n\texport let all_tools: Tool[] = [];\n\texport let selected_tools: Set<string> = new Set();\n\texport let mcp_json_sse: any;\n\texport let mcp_json_stdio: any;\n\texport let file_data_present: boolean;\n\texport let mcp_docs: string;\n\n\tinterface ToolParameter {\n\t\ttitle?: string;\n\t\ttype: string;\n\t\tdescription: string;\n\t\tformat?: string;\n\t\tdefault?: any;\n\t}\n\n\tinterface Tool {\n\t\tname: string;\n\t\tdescription: string;\n\t\tparameters: Record<string, ToolParameter>;\n\t\texpanded?: boolean;\n\t}\n\n\ttype Transport = \"streamable_http\" | \"sse\" | \"stdio\";\n\tlet current_transport: Transport = \"streamable_http\";\n\tlet include_file_upload = true;\n\n\tconst transports = [\n\t\t[\"streamable_http\", \"Streamable HTTP\"],\n\t\t[\"sse\", \"SSE\"],\n\t\t[\"stdio\", \"STDIO\"]\n\t] as const;\n\n\t$: display_url =\n\t\tcurrent_transport === \"sse\" ? mcp_server_url : mcp_server_url_streamable;\n\n\t// Helper function to add/remove file upload tool from config\n\tfunction update_config_with_file_upload(\n\t\tbase_config: any,\n\t\tinclude_upload: boolean\n\t): any {\n\t\tif (!base_config) return null;\n\n\t\tconst config = JSON.parse(JSON.stringify(base_config));\n\n\t\tif (include_upload && file_data_present) {\n\t\t\tconst upload_file_mcp_server = {\n\t\t\t\tcommand: \"uvx\",\n\t\t\t\targs: [\n\t\t\t\t\t\"--from\",\n\t\t\t\t\t\"gradio[mcp]\",\n\t\t\t\t\t\"gradio\",\n\t\t\t\t\t\"upload-mcp\",\n\t\t\t\t\tcurrent_transport === \"sse\"\n\t\t\t\t\t\t? mcp_server_url\n\t\t\t\t\t\t: mcp_server_url_streamable,\n\t\t\t\t\t\"<UPLOAD_DIRECTORY>\"\n\t\t\t\t]\n\t\t\t};\n\t\t\tconfig.mcpServers.upload_files_to_gradio = upload_file_mcp_server;\n\t\t} else {\n\t\t\tdelete config.mcpServers?.upload_files_to_gradio;\n\t\t}\n\n\t\treturn config;\n\t}\n\n\t$: mcp_json_streamable_http = update_config_with_file_upload(\n\t\tmcp_json_sse\n\t\t\t? {\n\t\t\t\t\t...mcp_json_sse,\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\t...mcp_json_sse.mcpServers,\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\t...mcp_json_sse.mcpServers.gradio,\n\t\t\t\t\t\t\turl: mcp_server_url_streamable\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t: null,\n\t\tinclude_file_upload\n\t);\n\n\t$: mcp_json_sse_updated = update_config_with_file_upload(\n\t\tmcp_json_sse,\n\t\tinclude_file_upload\n\t);\n\t$: mcp_json_stdio_updated = update_config_with_file_upload(\n\t\tmcp_json_stdio,\n\t\tinclude_file_upload\n\t);\n</script>\n\n{#if mcp_server_active}\n\t<div class=\"transport-selection\">\n\t\t<div class=\"snippets\">\n\t\t\t<span class=\"transport-label\">Transport:</span>\n\t\t\t{#each transports as [transport, display_name]}\n\t\t\t\t<button\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\tclass=\"snippet {current_transport === transport\n\t\t\t\t\t\t? 'current-lang'\n\t\t\t\t\t\t: 'inactive-lang'}\"\n\t\t\t\t\ton:click={() => (current_transport = transport)}\n\t\t\t\t>\n\t\t\t\t\t{display_name}\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t</div>\n\n\t{#if current_transport !== \"stdio\"}\n\t\t<Block>\n\t\t\t<div class=\"mcp-url\">\n\t\t\t\t<label for=\"mcp-server-url\"\n\t\t\t\t\t><span class=\"status-indicator active\">●</span>MCP Server URL ({current_transport ===\n\t\t\t\t\t\"sse\"\n\t\t\t\t\t\t? \"SSE\"\n\t\t\t\t\t\t: \"Streamable HTTP\"})</label\n\t\t\t\t>\n\t\t\t\t<div class=\"textbox\">\n\t\t\t\t\t<input id=\"mcp-server-url\" type=\"text\" readonly value={display_url} />\n\t\t\t\t\t<CopyButton code={display_url} />\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</Block>\n\t\t<p>&nbsp;</p>\n\t{/if}\n\n\t<div class=\"tool-selection\">\n\t\t<strong\n\t\t\t>{all_tools.length > 0 ? all_tools.length : tools.length} Available MCP Tools</strong\n\t\t>\n\t\t{#if all_tools.length > 0}\n\t\t\t<div class=\"tool-selection-controls\">\n\t\t\t\t<button\n\t\t\t\t\tclass=\"select-all-btn\"\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tselected_tools = new Set(all_tools.map((t) => t.name));\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\tSelect All\n\t\t\t\t</button>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"select-none-btn\"\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tselected_tools = new Set();\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\tSelect None\n\t\t\t\t</button>\n\t\t\t</div>\n\t\t{/if}\n\t</div>\n\t<div class=\"mcp-tools\">\n\t\t{#each all_tools.length > 0 ? all_tools : tools as tool}\n\t\t\t<div class=\"tool-item\">\n\t\t\t\t<div class=\"tool-header-wrapper\">\n\t\t\t\t\t{#if all_tools.length > 0}\n\t\t\t\t\t\t<input\n\t\t\t\t\t\t\ttype=\"checkbox\"\n\t\t\t\t\t\t\tclass=\"tool-checkbox\"\n\t\t\t\t\t\t\tchecked={selected_tools.has(tool.name) ||\n\t\t\t\t\t\t\t\tcurrent_transport !== \"streamable_http\"}\n\t\t\t\t\t\t\tdisabled={current_transport !== \"streamable_http\"}\n\t\t\t\t\t\t\tstyle={current_transport !== \"streamable_http\"\n\t\t\t\t\t\t\t\t? \"opacity: 0.5; cursor: not-allowed;\"\n\t\t\t\t\t\t\t\t: \"\"}\n\t\t\t\t\t\t\ton:change={(e) => {\n\t\t\t\t\t\t\t\tif (e.currentTarget.checked) {\n\t\t\t\t\t\t\t\t\tselected_tools.add(tool.name);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tselected_tools.delete(tool.name);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tselected_tools = selected_tools;\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"tool-header\"\n\t\t\t\t\t\ton:click={() => (tool.expanded = !tool.expanded)}\n\t\t\t\t\t>\n\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t><span class=\"tool-name\">{tool.name}</span> &nbsp;\n\t\t\t\t\t\t\t<span class=\"tool-description\"\n\t\t\t\t\t\t\t\t>{tool.description\n\t\t\t\t\t\t\t\t\t? tool.description\n\t\t\t\t\t\t\t\t\t: \"⚠︎ No description provided in function docstring\"}</span\n\t\t\t\t\t\t\t></span\n\t\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"tool-arrow\">{tool.expanded ? \"▼\" : \"▶\"}</span>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t\t{#if tool.expanded}\n\t\t\t\t\t<div class=\"tool-content\">\n\t\t\t\t\t\t{#if Object.keys(tool.parameters).length > 0}\n\t\t\t\t\t\t\t<div class=\"tool-parameters\">\n\t\t\t\t\t\t\t\t{#each Object.entries(tool.parameters) as [name, param]}\n\t\t\t\t\t\t\t\t\t<div class=\"parameter\">\n\t\t\t\t\t\t\t\t\t\t<code>{name}</code>\n\t\t\t\t\t\t\t\t\t\t<span class=\"parameter-type\">\n\t\t\t\t\t\t\t\t\t\t\t({param.type}{param.default !== undefined\n\t\t\t\t\t\t\t\t\t\t\t\t? `, default: ${JSON.stringify(param.default)}`\n\t\t\t\t\t\t\t\t\t\t\t\t: \"\"})\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t<p class=\"parameter-description\">\n\t\t\t\t\t\t\t\t\t\t\t{param.description\n\t\t\t\t\t\t\t\t\t\t\t\t? param.description\n\t\t\t\t\t\t\t\t\t\t\t\t: \"⚠︎ No description for this parameter in function docstring\"}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t<p>Takes no input parameters</p>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t{/each}\n\t</div>\n\t<p>&nbsp;</p>\n\n\t{#if current_transport === \"streamable_http\"}\n\t\t<strong>Streamable HTTP Transport</strong>: To add this MCP to clients that\n\t\tsupport Streamable HTTP, simply add the following configuration to your MCP\n\t\tconfig.\n\t\t<p>&nbsp;</p>\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton\n\t\t\t\t\t\tcode={JSON.stringify(mcp_json_streamable_http, null, 2)}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t\t<div>\n\t\t\t\t\t<pre>{JSON.stringify(mcp_json_streamable_http, null, 2)}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_transport === \"sse\"}\n\t\t<strong>SSE Transport</strong>: The SSE transport has been deprecated by the\n\t\tMCP spec. We recommend using the Streamable HTTP transport instead. But to\n\t\tadd this MCP to clients that only support server-sent events (SSE), simply\n\t\tadd the following configuration to your MCP config.\n\t\t<p>&nbsp;</p>\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={JSON.stringify(mcp_json_sse_updated, null, 2)} />\n\t\t\t\t</div>\n\t\t\t\t<div>\n\t\t\t\t\t<pre>{JSON.stringify(mcp_json_sse_updated, null, 2)}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{:else if current_transport === \"stdio\"}\n\t\t<strong>STDIO Transport</strong>: For clients that only support stdio (e.g.\n\t\tClaude Desktop), first\n\t\t<a href=\"https://nodejs.org/en/download/\" target=\"_blank\">install Node.js</a\n\t\t>. Then, you can use the following command:\n\t\t<p>&nbsp;</p>\n\t\t<Block>\n\t\t\t<code>\n\t\t\t\t<div class=\"copy\">\n\t\t\t\t\t<CopyButton code={JSON.stringify(mcp_json_stdio_updated, null, 2)} />\n\t\t\t\t</div>\n\t\t\t\t<div>\n\t\t\t\t\t<pre>{JSON.stringify(mcp_json_stdio_updated, null, 2)}</pre>\n\t\t\t\t</div>\n\t\t\t</code>\n\t\t</Block>\n\t{/if}\n\t{#if file_data_present}\n\t\t<div class=\"file-upload-section\">\n\t\t\t<label class=\"checkbox-label\">\n\t\t\t\t<input\n\t\t\t\t\ttype=\"checkbox\"\n\t\t\t\t\tbind:checked={include_file_upload}\n\t\t\t\t\tclass=\"checkbox\"\n\t\t\t\t/>\n\t\t\t\tInclude Gradio file upload tool\n\t\t\t</label>\n\t\t\t<p class=\"file-upload-explanation\">\n\t\t\t\tThe <code>upload_files_to_gradio</code> tool uploads files from your\n\t\t\t\tlocal <code>UPLOAD_DIRECTORY</code> (or any of its subdirectories) to\n\t\t\t\tthe Gradio app. This is needed because MCP servers require files to be\n\t\t\t\tprovided as URLs. You can omit this tool if you prefer to upload files\n\t\t\t\tmanually. This tool requires\n\t\t\t\t<a\n\t\t\t\t\thref=\"https://docs.astral.sh/uv/getting-started/installation/\"\n\t\t\t\t\ttarget=\"_blank\">uv</a\n\t\t\t\t> to be installed.\n\t\t\t</p>\n\t\t</div>\n\t{/if}\n\n\t<p>&nbsp;</p>\n\t<p>\n\t\t<a href={mcp_docs} target=\"_blank\">\n\t\t\tRead more about MCP in the Gradio docs\n\t\t</a>\n\t</p>\n{:else}\n\tThis Gradio app can also serve as an MCP server, with an MCP tool\n\tcorresponding to each API endpoint. To enable this, launch this Gradio app\n\twith <code>.launch(mcp_server=True)</code> or set the\n\t<code>GRADIO_MCP_SERVER</code>\n\tenv variable to\n\t<code>\"True\"</code>.\n{/if}\n\n<style>\n\t.transport-selection {\n\t\tmargin-bottom: var(--size-4);\n\t}\n\n\t.snippets {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-4);\n\t}\n\n\t.snippets > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.transport-label {\n\t\tfont-weight: 600;\n\t\tcolor: var(--body-text-color);\n\t\tmargin-right: var(--size-2);\n\t}\n\n\t.file-upload-section {\n\t\tmargin-top: var(--size-4);\n\t\tpadding: var(--size-3);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.checkbox-label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-weight: 600;\n\t\tcolor: var(--body-text-color);\n\t\tcursor: pointer;\n\t\tmargin-bottom: var(--size-2);\n\t}\n\n\t.checkbox {\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tcursor: pointer;\n\t\taccent-color: var(--color-accent);\n\t}\n\n\t.checkbox:checked {\n\t\tbackground-color: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.file-upload-explanation {\n\t\tmargin: 0;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.snippet {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-1) var(--size-1-5);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: 1;\n\t\tuser-select: none;\n\t}\n\n\t.current-lang {\n\t\tborder: 1px solid var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.inactive-lang {\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.inactive-lang:hover,\n\t.inactive-lang:focus {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: 5px;\n\t\tmargin-right: 5px;\n\t\tz-index: 10;\n\t}\n\n\t.mcp-url {\n\t\tpadding: var(--size-2);\n\t\tposition: relative;\n\t}\n\n\t.mcp-url label {\n\t\tdisplay: block;\n\t\tmargin-bottom: var(--size-2);\n\t\tfont-weight: 600;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.mcp-url .textbox {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-2);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.mcp-url input {\n\t\tflex: 1;\n\t\tborder: none;\n\t\tbackground: none;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-md);\n\t\twidth: 100%;\n\t}\n\n\t.mcp-url input:focus {\n\t\toutline: none;\n\t}\n\n\t.status-indicator {\n\t\tdisplay: inline-block;\n\t\tmargin-right: var(--size-1-5);\n\t\tposition: relative;\n\t\ttop: -1px;\n\t\tfont-size: 0.8em;\n\t}\n\n\t.status-indicator.active {\n\t\tcolor: #4caf50;\n\t\tanimation: pulse 1s infinite;\n\t}\n\n\t@keyframes pulse {\n\t\t0% {\n\t\t\topacity: 1;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.6;\n\t\t}\n\t\t100% {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.mcp-tools {\n\t\tmargin-top: var(--size-4);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\toverflow: hidden;\n\t}\n\n\t.tool-selection {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-2);\n\t}\n\n\t.tool-selection-controls {\n\t\tdisplay: flex;\n\t\tgap: var(--size-2);\n\t}\n\n\t.select-all-btn,\n\t.select-none-btn {\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--background-fill-primary);\n\t\tcolor: var(--body-text-color);\n\t\tcursor: pointer;\n\t\tfont-size: var(--text-sm);\n\t}\n\n\t.select-all-btn:hover,\n\t.select-none-btn:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.tool-item {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t}\n\n\t.tool-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.tool-header-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.tool-checkbox {\n\t\tmargin-left: var(--size-3);\n\t\tmargin-right: var(--size-2);\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tcursor: pointer;\n\t\taccent-color: var(--color-accent);\n\t}\n\n\t.tool-checkbox:checked {\n\t\tbackground-color: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.tool-header {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: var(--size-3);\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\ttext-align: left;\n\t}\n\n\t.tool-header:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.tool-name {\n\t\tfont-family: var(--font-mono);\n\t\tfont-weight: 600;\n\t}\n\n\t.tool-arrow {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.tool-content {\n\t\tpadding: var(--size-3);\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.tool-description {\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t}\n\t.parameter {\n\t\tmargin-bottom: var(--size-2);\n\t\tpadding: var(--size-2);\n\t\tbackground: var(--background-fill-primary);\n\t\tborder-radius: var(--radius-sm);\n\t}\n\n\t.parameter code {\n\t\tfont-weight: 600;\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.parameter-type {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.parameter-description {\n\t\tmargin-top: var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\ta {\n\t\ttext-decoration: underline;\n\t}\n</style>\n", "<script lang=\"ts\">\n\t/* eslint-disable */\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { ComponentMeta, Dependency } from \"../types\";\n\timport NoApi from \"./NoApi.svelte\";\n\timport type { Client } from \"@gradio/client\";\n\timport type { Payload } from \"../types\";\n\n\timport ApiBanner from \"./ApiBanner.svelte\";\n\timport { BaseButton as Button } from \"@gradio/button\";\n\timport ParametersSnippet from \"./ParametersSnippet.svelte\";\n\timport InstallSnippet from \"./InstallSnippet.svelte\";\n\timport CodeSnippet from \"./CodeSnippet.svelte\";\n\timport RecordingSnippet from \"./RecordingSnippet.svelte\";\n\timport CopyButton from \"./CopyButton.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\n\timport python from \"./img/python.svg\";\n\timport javascript from \"./img/javascript.svg\";\n\timport bash from \"./img/bash.svg\";\n\timport ResponseSnippet from \"./ResponseSnippet.svelte\";\n\timport mcp from \"./img/mcp.svg\";\n\timport MCPSnippet from \"./MCPSnippet.svelte\";\n\n\texport let dependencies: Dependency[];\n\texport let root: string;\n\texport let app: Awaited<ReturnType<typeof Client.connect>>;\n\texport let space_id: string | null;\n\texport let root_node: ComponentMeta;\n\texport let username: string | null;\n\n\tconst js_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-js-client\";\n\tconst py_docs =\n\t\t\"https://www.gradio.app/guides/getting-started-with-the-python-client\";\n\tconst bash_docs =\n\t\t\"https://www.gradio.app/guides/querying-gradio-apps-with-curl\";\n\tconst spaces_docs_suffix = \"#connecting-to-a-hugging-face-space\";\n\tconst mcp_docs =\n\t\t\"https://www.gradio.app/guides/building-mcp-server-with-gradio\";\n\n\tlet api_count = dependencies.filter(\n\t\t(dependency) => dependency.show_api\n\t).length;\n\n\tif (root === \"\") {\n\t\troot = location.protocol + \"//\" + location.host + location.pathname;\n\t}\n\tif (!root.endsWith(\"/\")) {\n\t\troot += \"/\";\n\t}\n\n\texport let api_calls: Payload[] = [];\n\tlet current_language: \"python\" | \"javascript\" | \"bash\" | \"mcp\" = \"python\";\n\n\tfunction set_query_param(key: string, value: string) {\n\t\tconst url = new URL(window.location.href);\n\t\turl.searchParams.set(key, value);\n\t\thistory.replaceState(null, \"\", url.toString());\n\t}\n\n\tfunction get_query_param(key: string): string | null {\n\t\tconst url = new URL(window.location.href);\n\t\treturn url.searchParams.get(key);\n\t}\n\n\tfunction is_valid_language(lang: string | null): boolean {\n\t\treturn [\"python\", \"javascript\", \"bash\", \"mcp\"].includes(lang ?? \"\");\n\t}\n\n\tconst langs = [\n\t\t[\"python\", \"Python\", python],\n\t\t[\"javascript\", \"JavaScript\", javascript],\n\t\t[\"bash\", \"cURL\", bash],\n\t\t[\"mcp\", \"MCP\", mcp]\n\t] as const;\n\n\tlet is_running = false;\n\tlet mcp_server_active = false;\n\n\tasync function get_info(): Promise<{\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t}> {\n\t\tlet response = await fetch(\n\t\t\troot.replace(/\\/$/, \"\") + app.api_prefix + \"/info\"\n\t\t);\n\t\tlet data = await response.json();\n\t\treturn data;\n\t}\n\tasync function get_js_info(): Promise<Record<string, any>> {\n\t\tlet js_api_info = await app.view_api();\n\t\treturn js_api_info;\n\t}\n\n\tlet info: {\n\t\tnamed_endpoints: any;\n\t\tunnamed_endpoints: any;\n\t};\n\n\tlet js_info: Record<string, any>;\n\n\tget_info().then((data) => {\n\t\tinfo = data;\n\t});\n\n\tget_js_info().then((js_api_info) => {\n\t\tjs_info = js_api_info;\n\t});\n\n\tconst dispatch = createEventDispatcher();\n\n\t$: selected_tools_array = Array.from(selected_tools);\n\t$: selected_tools_without_prefix =\n\t\tselected_tools_array.map(remove_tool_prefix);\n\t$: mcp_server_url = `${root}gradio_api/mcp/sse`;\n\t$: mcp_server_url_streamable =\n\t\tselected_tools_array.length > 0 &&\n\t\tselected_tools_array.length < tools.length\n\t\t\t? `${root}gradio_api/mcp/?tools=${selected_tools_without_prefix.join(\",\")}`\n\t\t\t: `${root}gradio_api/mcp/`;\n\n\t$: if (mcp_json_sse && selected_tools.size > 0) {\n\t\tconst baseUrl =\n\t\t\tselected_tools_array.length > 0 &&\n\t\t\tselected_tools_array.length < tools.length\n\t\t\t\t? `${root}gradio_api/mcp/sse?tools=${selected_tools_without_prefix.join(\",\")}`\n\t\t\t\t: `${root}gradio_api/mcp/sse`;\n\t\tmcp_json_sse.mcpServers.gradio.url = baseUrl;\n\t\tif (mcp_json_stdio) {\n\t\t\tmcp_json_stdio.mcpServers.gradio.args[1] = baseUrl;\n\t\t}\n\t}\n\n\tinterface ToolParameter {\n\t\ttitle?: string;\n\t\ttype: string;\n\t\tdescription: string;\n\t\tformat?: string;\n\t\tdefault?: any;\n\t}\n\n\tinterface Tool {\n\t\tname: string;\n\t\tdescription: string;\n\t\tparameters: Record<string, ToolParameter>;\n\t\texpanded?: boolean;\n\t}\n\n\tlet tools: Tool[] = [];\n\tlet headers: string[] = [];\n\tlet mcp_json_sse: any;\n\tlet mcp_json_stdio: any;\n\tlet file_data_present = false;\n\tlet selected_tools: Set<string> = new Set();\n\tlet tool_prefix = space_id ? space_id.split(\"/\").pop() + \"_\" : \"\";\n\n\tfunction remove_tool_prefix(toolName: string): string {\n\t\tif (tool_prefix && toolName.startsWith(tool_prefix)) {\n\t\t\treturn toolName.slice(tool_prefix.length);\n\t\t}\n\t\treturn toolName;\n\t}\n\n\tconst upload_file_mcp_server = {\n\t\tcommand: \"uvx\",\n\t\targs: [\n\t\t\t\"--from\",\n\t\t\t\"gradio[mcp]\",\n\t\t\t\"gradio\",\n\t\t\t\"upload-mcp\",\n\t\t\troot,\n\t\t\t\"<UPLOAD_DIRECTORY>\"\n\t\t]\n\t};\n\n\tasync function fetch_mcp_tools() {\n\t\ttry {\n\t\t\tlet schema_url = `${root}gradio_api/mcp/schema`;\n\t\t\tconst response = await fetch(schema_url);\n\t\t\tconst schema = await response.json();\n\t\t\tfile_data_present = schema\n\t\t\t\t.map((tool: any) => tool.meta?.file_data_present)\n\t\t\t\t.some((present: boolean) => present);\n\n\t\t\ttools = schema.map((tool: any) => ({\n\t\t\t\tname: tool.name,\n\t\t\t\tdescription: tool.description || \"\",\n\t\t\t\tparameters: tool.inputSchema?.properties || {},\n\t\t\t\texpanded: false\n\t\t\t}));\n\t\t\tselected_tools = new Set(tools.map((tool) => tool.name));\n\t\t\theaders = schema.map((tool: any) => tool.meta?.headers || []).flat();\n\t\t\tif (headers.length > 0) {\n\t\t\t\tmcp_json_sse = {\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\turl: mcp_server_url,\n\t\t\t\t\t\t\theaders: headers.reduce(\n\t\t\t\t\t\t\t\t(accumulator: Record<string, string>, current_key: string) => {\n\t\t\t\t\t\t\t\t\taccumulator[current_key] = \"<YOUR_HEADER_VALUE>\";\n\t\t\t\t\t\t\t\t\treturn accumulator;\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t{}\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tmcp_json_stdio = {\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\tcommand: \"npx\",\n\t\t\t\t\t\t\targs: [\n\t\t\t\t\t\t\t\t\"mcp-remote\",\n\t\t\t\t\t\t\t\tmcp_server_url,\n\t\t\t\t\t\t\t\t\"--transport\",\n\t\t\t\t\t\t\t\t\"sse-only\",\n\t\t\t\t\t\t\t\t...headers\n\t\t\t\t\t\t\t\t\t.map((header) => [\n\t\t\t\t\t\t\t\t\t\t\"--header\",\n\t\t\t\t\t\t\t\t\t\t`${header}: <YOUR_HEADER_VALUE>`\n\t\t\t\t\t\t\t\t\t])\n\t\t\t\t\t\t\t\t\t.flat()\n\t\t\t\t\t\t\t]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tmcp_json_sse = {\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\turl: mcp_server_url\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tmcp_json_stdio = {\n\t\t\t\t\tmcpServers: {\n\t\t\t\t\t\tgradio: {\n\t\t\t\t\t\t\tcommand: \"npx\",\n\t\t\t\t\t\t\targs: [\"mcp-remote\", mcp_server_url, \"--transport\", \"sse-only\"]\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tif (file_data_present) {\n\t\t\t\t\tmcp_json_sse.mcpServers.upload_files_to_gradio =\n\t\t\t\t\t\tupload_file_mcp_server;\n\t\t\t\t\tmcp_json_stdio.mcpServers.upload_files_to_gradio =\n\t\t\t\t\t\tupload_file_mcp_server;\n\t\t\t\t}\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Failed to fetch MCP tools:\", error);\n\t\t\ttools = [];\n\t\t}\n\t}\n\n\tonMount(() => {\n\t\tdocument.body.style.overflow = \"hidden\";\n\t\tif (\"parentIFrame\" in window) {\n\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t}\n\n\t\tconst lang_param = get_query_param(\"lang\");\n\t\tif (is_valid_language(lang_param)) {\n\t\t\tcurrent_language = lang_param as \"python\" | \"javascript\" | \"bash\" | \"mcp\";\n\t\t}\n\n\t\t// Check MCP server status and fetch tools if active\n\t\tfetch(mcp_server_url)\n\t\t\t.then((response) => {\n\t\t\t\tmcp_server_active = response.ok;\n\t\t\t\tif (mcp_server_active) {\n\t\t\t\t\tfetch_mcp_tools();\n\t\t\t\t\tif (!is_valid_language(lang_param)) {\n\t\t\t\t\t\tcurrent_language = \"mcp\";\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (!is_valid_language(lang_param)) {\n\t\t\t\t\t\tcurrent_language = \"python\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(() => {\n\t\t\t\tmcp_server_active = false;\n\t\t\t});\n\n\t\treturn () => {\n\t\t\tdocument.body.style.overflow = \"auto\";\n\t\t};\n\t});\n</script>\n\n{#if info}\n\t{#if api_count}\n\t\t<div class=\"banner-wrap\">\n\t\t\t<ApiBanner\n\t\t\t\ton:close\n\t\t\t\troot={space_id || root}\n\t\t\t\t{api_count}\n\t\t\t\t{current_language}\n\t\t\t/>\n\t\t</div>\n\n\t\t<div class=\"docs-wrap\">\n\t\t\t<div class=\"client-doc\">\n\t\t\t\t<p style=\"font-size: var(--text-lg);\">\n\t\t\t\t\tChoose one of the following ways to interact with the API.\n\t\t\t\t</p>\n\t\t\t</div>\n\t\t\t<div class=\"endpoint\">\n\t\t\t\t<div class=\"snippets\">\n\t\t\t\t\t{#each langs as [language, display_name, img]}\n\t\t\t\t\t\t<li\n\t\t\t\t\t\t\tclass=\"snippet\n\t\t\t\t\t\t{current_language === language ? 'current-lang' : 'inactive-lang'}\"\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tcurrent_language = language;\n\t\t\t\t\t\t\t\tset_query_param(\"lang\", language);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<img src={img} alt=\"\" />\n\t\t\t\t\t\t\t{display_name}\n\t\t\t\t\t\t</li>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t\t{#if api_calls.length}\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<p\n\t\t\t\t\t\t\tid=\"num-recorded-api-calls\"\n\t\t\t\t\t\t\tstyle=\"font-size: var(--text-lg); font-weight:bold; margin: 10px 0px;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t🪄 Recorded API Calls <span class=\"api-count\"\n\t\t\t\t\t\t\t\t>[{api_calls.length}]</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</p>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\tHere is the code snippet to replay the most recently recorded API\n\t\t\t\t\t\t\tcalls using the {current_language}\n\t\t\t\t\t\t\tclient.\n\t\t\t\t\t\t</p>\n\n\t\t\t\t\t\t<RecordingSnippet\n\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t{api_calls}\n\t\t\t\t\t\t\t{dependencies}\n\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\tapi_prefix={app.api_prefix}\n\t\t\t\t\t\t\tshort_root={space_id || root}\n\t\t\t\t\t\t\t{username}\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\tNote: Some API calls only affect the UI, so when using the\n\t\t\t\t\t\t\tclients, the desired result may be achieved with only a subset of\n\t\t\t\t\t\t\tthe recorded calls.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<p\n\t\t\t\t\t\tstyle=\"font-size: var(--text-lg); font-weight:bold; margin: 30px 0px 10px;\"\n\t\t\t\t\t>\n\t\t\t\t\t\tAPI Documentation\n\t\t\t\t\t</p>\n\t\t\t\t{:else}\n\t\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t\t{#if current_language == \"python\" || current_language == \"javascript\"}\n\t\t\t\t\t\t\t1. Install the\n\t\t\t\t\t\t\t<span style=\"text-transform:capitalize\">{current_language}</span>\n\t\t\t\t\t\t\tclient (<a\n\t\t\t\t\t\t\t\thref={current_language == \"python\" ? py_docs : js_docs}\n\t\t\t\t\t\t\t\ttarget=\"_blank\">docs</a\n\t\t\t\t\t\t\t>) if you don't already have it installed.\n\t\t\t\t\t\t{:else if current_language == \"mcp\"}\n\t\t\t\t\t\t\t<MCPSnippet\n\t\t\t\t\t\t\t\t{mcp_server_active}\n\t\t\t\t\t\t\t\t{mcp_server_url}\n\t\t\t\t\t\t\t\t{mcp_server_url_streamable}\n\t\t\t\t\t\t\t\ttools={tools.filter((tool) => selected_tools.has(tool.name))}\n\t\t\t\t\t\t\t\tall_tools={tools}\n\t\t\t\t\t\t\t\tbind:selected_tools\n\t\t\t\t\t\t\t\t{mcp_json_sse}\n\t\t\t\t\t\t\t\t{mcp_json_stdio}\n\t\t\t\t\t\t\t\t{file_data_present}\n\t\t\t\t\t\t\t\t{mcp_docs}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t1. Confirm that you have cURL installed on your system.\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</p>\n\n\t\t\t\t\t{#if current_language !== \"mcp\"}\n\t\t\t\t\t\t<InstallSnippet {current_language} />\n\n\t\t\t\t\t\t<p class=\"padded\">\n\t\t\t\t\t\t\t2. Find the API endpoint below corresponding to your desired\n\t\t\t\t\t\t\tfunction in the app. Copy the code snippet, replacing the\n\t\t\t\t\t\t\tplaceholder values with your own input data.\n\t\t\t\t\t\t\t{#if space_id}If this is a private Space, you may need to pass\n\t\t\t\t\t\t\t\tyour Hugging Face token as well (<a\n\t\t\t\t\t\t\t\t\thref={current_language == \"python\"\n\t\t\t\t\t\t\t\t\t\t? py_docs + spaces_docs_suffix\n\t\t\t\t\t\t\t\t\t\t: current_language == \"javascript\"\n\t\t\t\t\t\t\t\t\t\t\t? js_docs + spaces_docs_suffix\n\t\t\t\t\t\t\t\t\t\t\t: bash_docs}\n\t\t\t\t\t\t\t\t\tclass=\"underline\"\n\t\t\t\t\t\t\t\t\ttarget=\"_blank\">read more</a\n\t\t\t\t\t\t\t\t>).{/if}\n\n\t\t\t\t\t\t\tOr use the\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\t\t\t\tdispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div class=\"loading-dot\"></div>\n\t\t\t\t\t\t\t\t<p class=\"self-baseline\">API Recorder</p>\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\tto automatically generate your API requests.\n\t\t\t\t\t\t\t{#if current_language == \"bash\"}<br />&nbsp;<br />Making a\n\t\t\t\t\t\t\t\tprediction and getting a result requires\n\t\t\t\t\t\t\t\t<strong>2 requests</strong>: a\n\t\t\t\t\t\t\t\t<code>POST</code>\n\t\t\t\t\t\t\t\tand a <code>GET</code> request. The <code>POST</code> request\n\t\t\t\t\t\t\t\treturns an <code>EVENT_ID</code>, which is used in the second\n\t\t\t\t\t\t\t\t<code>GET</code> request to fetch the results. In these\n\t\t\t\t\t\t\t\tsnippets, we've used <code>awk</code> and <code>read</code> to\n\t\t\t\t\t\t\t\tparse the results, combining these two requests into one command\n\t\t\t\t\t\t\t\tfor ease of use. {#if username !== null}\n\t\t\t\t\t\t\t\t\tNote: connecting to an authenticated app requires an\n\t\t\t\t\t\t\t\t\tadditional request.{/if} See\n\t\t\t\t\t\t\t\t<a href={bash_docs} target=\"_blank\">curl docs</a>.\n\t\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t\t<!-- <span\n\t\t\t\t\t\t\tid=\"api-recorder\"\n\t\t\t\t\t\t\ton:click={() => dispatch(\"close\", { api_recorder_visible: true })}\n\t\t\t\t\t\t\t>🪄 API Recorder</span\n\t\t\t\t\t\t> to automatically generate your API requests! -->\n\t\t\t\t\t\t</p>\n\t\t\t\t\t{/if}\n\t\t\t\t{/if}\n\n\t\t\t\t{#if current_language !== \"mcp\"}\n\t\t\t\t\t{#each dependencies as dependency}\n\t\t\t\t\t\t{#if dependency.show_api && info.named_endpoints[\"/\" + dependency.api_name]}\n\t\t\t\t\t\t\t<div class=\"endpoint-container\">\n\t\t\t\t\t\t\t\t<CodeSnippet\n\t\t\t\t\t\t\t\t\tendpoint_parameters={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\t\t{dependency}\n\t\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t{space_id}\n\t\t\t\t\t\t\t\t\t{username}\n\t\t\t\t\t\t\t\t\tapi_prefix={app.api_prefix}\n\t\t\t\t\t\t\t\t\tapi_description={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t\t].description}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<ParametersSnippet\n\t\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t\t].parameters}\n\t\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t\t.parameters}\n\t\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<ResponseSnippet\n\t\t\t\t\t\t\t\t\tendpoint_returns={info.named_endpoints[\n\t\t\t\t\t\t\t\t\t\t\"/\" + dependency.api_name\n\t\t\t\t\t\t\t\t\t].returns}\n\t\t\t\t\t\t\t\t\tjs_returns={js_info.named_endpoints[\"/\" + dependency.api_name]\n\t\t\t\t\t\t\t\t\t\t.returns}\n\t\t\t\t\t\t\t\t\t{is_running}\n\t\t\t\t\t\t\t\t\t{current_language}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t{:else}\n\t\t<NoApi {root} on:close />\n\t{/if}\n{/if}\n\n<style>\n\t.banner-wrap {\n\t\tposition: relative;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tpadding: var(--size-4) var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t@media (--screen-md) {\n\t\t.banner-wrap {\n\t\t\tfont-size: var(--text-xl);\n\t\t}\n\t}\n\n\t.docs-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t}\n\n\t.endpoint {\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-primary);\n\t\tpadding: var(--size-6);\n\t\tpadding-top: var(--size-1);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.client-doc {\n\t\tpadding-top: var(--size-6);\n\t\tpadding-right: var(--size-6);\n\t\tpadding-left: var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.library {\n\t\tborder: 1px solid var(--border-color-accent);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--color-accent-soft);\n\t\tpadding: 0px var(--size-1);\n\t\tcolor: var(--color-accent);\n\t\tfont-size: var(--text-md);\n\t\ttext-decoration: none;\n\t}\n\n\t.snippets {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-4);\n\t}\n\n\t.snippets > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.snippet {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-1) var(--size-1-5);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t\tline-height: 1;\n\t\tuser-select: none;\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t.current-lang {\n\t\tborder: 1px solid var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.inactive-lang {\n\t\tcursor: pointer;\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.inactive-lang:hover,\n\t.inactive-lang:focus {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.snippet img {\n\t\tmargin-right: var(--size-1-5);\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n\n\t.header {\n\t\tmargin-top: var(--size-6);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.endpoint-container {\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-3);\n\t\tpadding-top: 0;\n\t}\n\n\ta {\n\t\ttext-decoration: underline;\n\t}\n\n\tp.padded {\n\t\tpadding: 15px 0px;\n\t\tfont-size: var(--text-lg);\n\t}\n\n\t#api-recorder {\n\t\tborder: 1px solid var(--color-accent);\n\t\tbackground-color: var(--color-accent-soft);\n\t\tpadding: 0px var(--size-2);\n\t\tborder-radius: var(--size-1);\n\t\tcursor: pointer;\n\t}\n\n\tcode {\n\t\tfont-size: var(--text-md);\n\t}\n\t.loading-dot {\n\t\tposition: relative;\n\t\tleft: -9999px;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tborder-radius: 5px;\n\t\tbackground-color: #fd7b00;\n\t\tcolor: #fd7b00;\n\t\tbox-shadow: 9999px 0 0 -1px;\n\t\tmargin-right: 0.25rem;\n\t}\n\t:global(.docs-wrap .sm.secondary) {\n\t\tpadding-top: 1px;\n\t\tpadding-bottom: 1px;\n\t}\n\t.self-baseline {\n\t\talign-self: baseline;\n\t}\n\t.api-count {\n\t\tfont-weight: bold;\n\t\tcolor: #fd7b00;\n\t\talign-self: baseline;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-md);\n\t}\n\n\tcode pre {\n\t\toverflow-x: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-family: var(--font-mono);\n\t\ttab-size: 2;\n\t}\n\n\t.token.string {\n\t\tdisplay: contents;\n\t\tcolor: var(--color-accent-base);\n\t}\n\n\t.copy {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tmargin-top: 5px;\n\t\tmargin-right: 5px;\n\t\tz-index: 10;\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xxl);\n\t\tmargin-top: var(--size-3);\n\t\tmargin-bottom: var(--size-3);\n\t}\n\n\t.desc {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.api-name {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "append", "h1", "p0", "code0", "p1", "button", "dispatch", "createEventDispatcher", "root", "$$props", "click_handler", "p", "create_if_block_3", "if_block1", "create_if_block_2", "create_if_block_1", "if_block3", "create_if_block", "api_logo", "attr", "img", "img_src_value", "h2", "div1", "div0", "span1", "span0", "br", "api_count", "current_language", "click_handler_1", "represent_value", "value", "type", "lang", "simplify_file_data", "replace_file_data_with_file_function", "stringify_except_file_function", "is_potentially_nested_file_data", "obj", "key", "item", "index", "jsonString", "regex", "match", "regexNone", "t_value", "dirty", "set_data", "t1_value", "t1", "span", "t10_value", "hr", "t6", "t6_value", "t8", "t8_value", "t10", "create_if_block_4", "i", "h4", "div2", "current", "is_running", "endpoint_returns", "js_returns", "code", "copy_text", "copy", "bash_install", "pre", "js_install", "py_install", "h3", "api_name", "description", "if_block", "create_if_block_7", "t5_value", "t19_value", "copybutton_changes", "t5", "t19", "t3_value", "t3", "t4_value", "create_if_block_5", "t4", "t7_value", "t14_value", "span2", "span3", "span4", "span5", "t7", "t14", "endpointdetail_changes", "dependency", "api_prefix", "space_id", "endpoint_parameters", "username", "api_description", "python_code", "js_code", "bash_post_code", "has_file_path", "param", "blob_components", "blob_examples", "$$value", "$$invalidate", "normalised_api_prefix", "normalised_root", "onMount", "tick", "t17", "t17_value", "t2", "t2_value", "dependencies", "short_root", "python_code_text", "bash_code", "api_calls", "get_info", "endpoints_info", "py_zipped", "js_zipped", "bash_zipped", "format_api_call", "call", "params", "d", "param_info", "param_name", "python_type", "py_api_calls", "js_api_calls", "bash_api_calls", "api_names", "python", "javascript", "bash", "t0_value", "t0", "mcp", "code1", "code2", "if_block0", "create_if_block_9", "create_if_block_8", "strong", "div3", "p2", "a", "button_class_value", "label", "input", "button0", "button1", "input_disabled_value", "input_style_value", "each_value_1", "ensure_array_like", "mcp_server_active", "mcp_server_url", "mcp_server_url_streamable", "tools", "all_tools", "selected_tools", "mcp_json_sse", "mcp_json_stdio", "file_data_present", "mcp_docs", "current_transport", "include_file_upload", "transports", "update_config_with_file_upload", "base_config", "include_upload", "config", "upload_file_mcp_server", "transport", "t", "e", "tool", "display_url", "mcp_json_streamable_http", "mcp_json_sse_updated", "mcp_json_stdio_updated", "div4", "apibanner_changes", "img_1", "img_1_src_value", "li", "li_class_value", "p3", "recordingsnippet_changes", "mcpsnippet_changes", "a_href_value", "py_docs", "js_docs", "create_if_block_6", "spaces_docs_suffix", "bash_docs", "br0", "br1", "code3", "code4", "code5", "code6", "each_blocks", "codesnippet_changes", "set_query_param", "url", "get_query_param", "is_valid_language", "app", "root_node", "langs", "get_js_info", "info", "js_info", "data", "js_api_info", "headers", "tool_prefix", "remove_tool_prefix", "toolName", "fetch_mcp_tools", "schema_url", "schema", "present", "accumulator", "current_key", "header", "error", "lang_param", "response", "language", "func", "selected_tools_array", "selected_tools_without_prefix", "baseUrl"], "mappings": "8mCACuC,EAAA,OAAA,2KAUlB;AAAA,GAEnB,oBACEA,EAAI,CAAA,CAAA;;;;;;;;;wMALRC,GAwBKC,EAAAC,EAAAC,CAAA,EAvBJC,GAAgBF,EAAAG,CAAA,UAChBD,GAKGF,EAAAI,CAAA,UAHFF,GAEME,EAAAC,CAAA,kBAEPH,GAeGF,EAAAM,CAAA,YAGJR,GAEQC,EAAAQ,EAAAN,CAAA,6EAvBJJ,EAAI,CAAA,CAAA,4IAVDW,EAAWC,KAEN,GAAA,CAAA,KAAAC,CAAA,EAAAC,EA6BY,MAAAC,EAAA,IAAAJ,EAAS,OAAO,yjBClCD,EAAA,OAAA,gEAgBK,KAAG,0EAAb,KAAG,kmBAajCV,GAA4CC,EAAAC,EAAAC,CAAA,YAC5CH,GAAiDC,EAAAc,EAAAZ,CAAA,2EAKC,UAAQ,0EAA3B,cAAY,0EAAuC,GAAC,sHAnBhF,OAAAJ,OAAqB,MAAKiB,wBAM1BC,EAAAlB,OAAqB,OAAKmB,GAAAnB,CAAA,kBAazB,OAAAA,OAAqB,MAAKoB,wBAAsCC,EAAArB,KAAY,GAACsB,GAAA,gFAnBjC;AAAA,GAClD,mBACEtB,EAAI,CAAA,CAAA,kEAgBcA,EAAS,CAAA,CAAA,6FApBpBuB,EAAQ,GAAAC,GAAAC,EAAA,MAAAC,CAAA,uQADnBzB,GA0BIC,EAAAyB,EAAAvB,CAAA,EAzBHC,GAA4BsB,EAAAF,CAAA,UAC5BpB,GAKKsB,EAAAC,CAAA,sBAHJvB,GAEKuB,EAAAC,CAAA,kBAENxB,GAiBMsB,EAAAG,CAAA,yBALLzB,GAIGyB,EAAAd,CAAA,EAHFX,GAAmCW,EAAAe,CAAA,qDACsD1B,GACxFW,EAAAgB,CAAA,YAKJ/B,GAEQC,EAAAQ,EAAAN,CAAA,+HAzBJJ,EAAI,CAAA,CAAA,EAIDA,OAAqB,uHAYNA,EAAS,CAAA,CAAA,oDACyCA,KAAY,8NA9BxE,GAAA,CAAA,KAAAa,CAAA,EAAAC,EACA,CAAA,UAAAmB,CAAA,EAAAnB,GACA,iBAAAoB,EACV,QAAA,EAAApB,QAEKH,EAAWC,KAiBEG,EAAA,IAAAJ,EAAS,QAAW,CAAA,qBAAsB,EAAI,CAAA,EAc1CwB,EAAA,IAAAxB,EAAS,OAAO,ogBCzCjC,SAASyB,GACfC,EACAC,EACAC,EAAoC,KACyB,CAC7D,OAAID,IAAS,OACLC,IAAS,KAAO,OAAS,KAE7BF,IAAU,MAAQE,IAAS,KACvB,OAEJD,IAAS,UAAYA,IAAS,MAC1BC,IAAS,KAAOF,EAAQ,IAAMA,EAAQ,IACnCC,IAAS,SACZC,IAAS,KAAO,WAAWF,CAAK,EAAIA,EACjCC,IAAS,WAAaA,GAAQ,OACpCC,IAAS,MACZF,EAAQ,OAAOA,CAAK,EACbA,IAAU,OAAS,OAAS,SACzBE,IAAS,MAAQA,IAAS,OAC7BF,EAEDA,IAAU,OACPC,IAAS,aACXD,EAAA,KAAK,UAAUA,CAAK,EACrBA,GACGC,EAAK,WAAW,WAAW,EAE9B,IAAMD,EAAQ,IAGlBE,IAAS,KACLF,IAAU,GAAK,KAAO,KAAK,MAAMA,CAAK,EACnC,OAAOA,GAAU,SACvBA,IAAU,GACNE,IAAS,KAAO,OAAS,OAE1BF,GAEJE,IAAS,SACZF,EAAQG,GAAmBH,CAAK,GAE7BE,IAAS,OACZF,EAAQI,GAAqCJ,CAAK,GAE5CK,GAA+BL,CAAK,EAC5C,CAEO,SAASM,GAAgCC,EAAmB,CAClE,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MAClCA,EAAI,eAAe,KAAK,GAAKA,EAAI,eAAe,MAAM,GAExD,OAAOA,EAAI,MAAS,UACpBA,EAAI,OAAS,MACbA,EAAI,KAAK,QAAU,kBAEZ,MAAA,GAIV,GAAI,OAAOA,GAAQ,UAAYA,IAAQ,MACtC,QAASC,KAAOD,EACf,GAAI,OAAOA,EAAIC,CAAG,GAAM,UACVF,GAAgCC,EAAIC,CAAG,CAAC,EAE7C,MAAA,GAKJ,MAAA,EACR,CAEA,SAASL,GAAmBI,EAAe,CACtC,OAAA,OAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACTA,EAAI,KACJ,SAAUA,GACVA,EAAI,MAAM,QAAU,kBAEb,CAAE,KAAMA,EAAI,IAAK,KAAM,CAAE,MAAO,qBAGrC,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACE,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCF,EAAAG,CAAK,EAAIP,GAAmBM,CAAI,EACrC,CACA,EACS,OAAOF,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIL,GAAmBI,EAAIC,CAAG,CAAC,CAAA,CACtC,EAEKD,EACR,CAEA,SAASH,GAAqCG,EAAe,CACxD,OAAA,OAAOA,GAAQ,UAAYA,IAAQ,MAAQ,CAAC,MAAM,QAAQA,CAAG,GAE/D,QAASA,GACTA,EAAI,KACJ,SAAUA,GACVA,EAAI,MAAM,QAAU,kBAEb,gBAAgBA,EAAI,GAAG,MAG5B,MAAM,QAAQA,CAAG,EAChBA,EAAA,QAAQ,CAACE,EAAMC,IAAU,CACxB,OAAOD,GAAS,UAAYA,IAAS,OACpCF,EAAAG,CAAK,EAAIN,GAAqCK,CAAI,EACvD,CACA,EACS,OAAOF,GAAQ,UAAYA,IAAQ,MAC7C,OAAO,KAAKA,CAAG,EAAE,QAASC,GAAQ,CACjCD,EAAIC,CAAG,EAAIJ,GAAqCG,EAAIC,CAAG,CAAC,CAAA,CACxD,EAEKD,EACR,CAEA,SAASF,GAA+BE,EAAkB,CACzD,IAAII,EAAa,KAAK,UAAUJ,EAAK,CAACC,EAAKR,IACtCA,IAAU,KACN,eAGP,OAAOA,GAAU,UACjBA,EAAM,WAAW,cAAc,GAC/BA,EAAM,SAAS,GAAG,EAEX,WAAWA,CAAK,GAEjBA,CACP,EACD,MAAMY,EAAQ,oCACDD,EAAAA,EAAW,QAAQC,EAAO,CAACC,EAAOzC,IAAO,eAAeA,CAAE,GAAG,EAC1E,MAAM0C,EAAY,kBACX,OAAAH,EAAW,QAAQG,EAAW,MAAM,CAC5C,0mBChI8E,GAAC,oDAevD,IAAAC,GAAApD,EAAW,CAAA,EAAAA,EAAG,EAAA,CAAA,EAAA,MAAQ,OAAK,kDAA3BqD,EAAA,GAAAD,KAAAA,GAAApD,EAAW,CAAA,EAAAA,EAAG,EAAA,CAAA,EAAA,MAAQ,OAAK,KAAAsD,GAAA,EAAAF,CAAA,kCADT,IAAAA,EAAApD,KAAY,KAAI,SAAMA,EAAqB,CAAA,GAAIA,EAAiB,CAAA,IAAK,MAAIiB,GAAA,qFAAzEoC,EAAA,GAAAD,KAAAA,EAAApD,KAAY,KAAI,KAAAsD,GAAA,EAAAF,CAAA,EAAMpD,EAAqB,CAAA,GAAIA,EAAiB,CAAA,IAAK,sIAAK;AAAA,YACzG,4DAOHuD,EAAAnB,GAAgBpC,EAAiB,CAAA,EAAEA,EAAY,CAAA,EAAA,KAAM,IAAI,EAAA,2JAHpDC,GAAuBC,EAAA6B,EAAA3B,CAAA,EAAAH,GAI9BC,EAAA4B,EAAA1B,CAAA,kBADEiD,EAAA,GAAAE,KAAAA,EAAAnB,GAAgBpC,EAAiB,CAAA,EAAEA,EAAY,CAAA,EAAA,KAAM,IAAI,EAAA,KAAAsD,GAAAE,EAAAD,CAAA,oIALHtD,GAExDC,EAAAuD,EAAArD,CAAA,wDAVCJ,EAAgB,CAAA,IAAK,QAAUA,EAAA,CAAA,EAC9BA,EAAA,CAAA,EACA,IAAMA,EAAC,EAAA,EAAG,KAAG,mBAeyBA,EAAK,CAAA,EAAA,SAC9CA,EAAS,CAAA,EAAA,OAEP0D,EAAA1D,KAAY,YAAW,uBAfnB,OAAAA,OAAqB,SAAQmB,+CAG9BnB,EAAqB,CAAA,GAAIA,EAAgB,CAAA,GAAI,OAAMoB,6JAQ3C,2CAC2B,eAAO,IAAE,eAGlD;AAAA,gBAAE,sTAxBJnB,GAAgBC,EAAAyD,EAAAvD,CAAA,YAChBH,GAyBKC,EAAAC,EAAAC,CAAA,EAxBJC,GAiBGF,EAAAI,CAAA,EAhBFF,GAIAE,EAAAwB,CAAA,kBACA1B,GAGAE,EAAAuB,CAAA,0CASDzB,GAKGF,EAAAM,CAAA,gFArBCT,EAAgB,CAAA,IAAK,QAAUA,EAAA,CAAA,EAC9BA,EAAA,CAAA,EACA,IAAMA,EAAC,EAAA,EAAG,KAAG,KAAAsD,GAAAE,EAAAD,CAAA,8IAeyBvD,EAAK,CAAA,EAAA,KAAAsD,GAAAM,EAAAC,CAAA,cAC9C7D,EAAS,CAAA,EAAA,KAAAsD,GAAAQ,EAAAC,CAAA,EAEPV,EAAA,GAAAK,KAAAA,EAAA1D,KAAY,YAAW,KAAAsD,GAAAU,EAAAN,CAAA,qGAOZ,EAAK,CAAA,CAAA,uEADtBzD,GAEKC,EAAAC,EAAAC,CAAA,uIArCImD,EAAAvD,KAAiB,OAAM,qBAAgBA,EAAgB,CAAA,EAAC,QAAU,GAACiE,GAAA,OAIrEjE,EAAgB,CAAA,CAAA,uBAArB,OAAIkE,GAAA,2BA8BFlE,EAAU,CAAA,GAAAsB,GAAA,qGAnCT;AAAA,UACG,eAAyB,YAAU,gBAAwC,GACpF,uKAEiBtB,EAAU,CAAA,CAAA,UAP3BC,GAKIC,EAAAiE,EAAA/D,CAAA,EAJHC,GAEK8D,EAAAvC,CAAA,2DAIN3B,GA8BKC,EAAAkE,EAAAhE,CAAA,sGAjCK,CAAAiE,GAAAhB,EAAA,IAAAE,KAAAA,EAAAvD,KAAiB,OAAM,KAAAsD,GAAAE,EAAAD,CAAA,EAAgBvD,EAAgB,CAAA,EAAC,QAAU,2DAIpEA,EAAgB,CAAA,CAAA,oBAArB,OAAIkE,GAAA,EAAA,mHAAJ,8BADclE,EAAU,CAAA,CAAA,EA+BtBA,EAAU,CAAA,kOA5CH,GAAA,CAAA,WAAAsE,CAAA,EAAAxD,EACA,CAAA,iBAAAyD,CAAA,EAAAzD,EACA,CAAA,WAAA0D,CAAA,EAAA1D,EACA,CAAA,iBAAAoB,CAAA,EAAApB,q+BCQVd,EAAS,CAAA,CAAA,sCAATA,EAAS,CAAA,CAAA,oIADqBA,EAAI,CAAA,CAAA,sNAZxB,GAAA,CAAA,KAAAyE,CAAA,EAAA3D,EACP4D,EAAY,OAEP,SAAAC,GAAA,CACR,UAAU,UAAU,UAAUF,CAAI,MAClCC,EAAY,SAAA,EACZ,oBACCA,EAAY,MAAA,GACV,wiBCmBiBE,EAAY,CAAA,CAAA,uFAGtBA,EAAY,4EAJrB3E,GAEKC,EAAA2B,EAAAzB,CAAA,yBACLH,GAEKC,EAAA0B,EAAAxB,CAAA,EADJC,GAA0BuB,EAAAiD,CAAA,gLAVRC,EAAU,CAAA,CAAA,uFAGpBA,EAAU,4EAJnB7E,GAEKC,EAAA2B,EAAAzB,CAAA,yBACLH,GAEKC,EAAA0B,EAAAxB,CAAA,EADJC,GAAwBuB,EAAAiD,CAAA,gLAVNE,EAAU,CAAA,CAAA,uFAGpBA,EAAU,4EAJnB9E,GAEKC,EAAA2B,EAAAzB,CAAA,yBACLH,GAEKC,EAAA0B,EAAAxB,CAAA,EADJC,GAAwBuB,EAAAiD,CAAA,uLALrB,OAAA7E,OAAqB,SAAQ,EAOxBA,OAAqB,aAAY,EAOjCA,OAAqB,OAAM,wGAftCC,GAuBMC,EAAAuE,EAAArE,CAAA,iiBA7BF2E,GAAa,4BACbD,GAAa,0BACbF,GAAe,oCAJR,GAAA,CAAA,iBAAA1C,CAAA,EAAApB,0dCGSyC,EAAA,IAAMvD,EAAQ,CAAA,qCAFhC;AAAA,EAEF,gDACoBA,EAAW,CAAA,CAAA,iHAHhCC,GAIIC,EAAA8E,EAAA5E,CAAA,UAFHC,GAAyC2E,EAAAjD,CAAA,kBACzC1B,GAAsC2E,EAAAlD,CAAA,oBADlBuB,EAAA,GAAAE,KAAAA,EAAA,IAAMvD,EAAQ,CAAA,IAAAsD,GAAAE,EAAAD,CAAA,YACdvD,EAAW,CAAA,CAAA,oDAPpB,SAAAiF,EAA0B,IAAA,EAAAnE,GAC1B,YAAAoE,EAA6B,IAAA,EAAApE,8yECgJQ,GAC1C,wDALHyC,EAAAnB,GACIpC,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,MAAK,EAAA,OACCmF,EAAAnF,EAAI,EAAA,EAAAA,EAAoB,CAAA,EAAA,OAAS,GAACoF,GAAA,iBAN8E;AAAA,QACvH,gFACH/B,EAAA,CAAA,EAAA,GAAAE,KAAAA,EAAAnB,GACIpC,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,MAAK,EAAA,KAAAsD,GAAAE,EAAAD,CAAA,EACCvD,EAAI,EAAA,EAAAA,EAAoB,CAAA,EAAA,OAAS,wIAPuBqF,EAAArF,KAAW,SAAQ,SAA8C,YAUrI,UACc,UAAc,cAC4CsF,EAAAtF,KAAW,SAAQ,0BAhBtE,KAAAA,MAAgB,sBAKrBA,EAAmB,CAAA,CAAA,uBAAxB,OAAIkE,GAAA,8GADP,eAAa,MAAClE,EAAe,EAAA,CAAA,MAAEA,EAAqB,EAAA,CAAA,MAAC,QAAM,aAAqB,8CAA4C,aAAI;AAAA,YAC/H,2CAQC;AAAA,EACX,aAAK;AAAA,gBACS,aAAK,WAAS,aAAK;AAAA,4BACP,MAACA,EAAe,EAAA,CAAA,MAAEA,EAAqB,EAAA,CAAA,MAAC,QAAM,aAAqB,YAAU,4GAlBrGC,EAoBMC,EAAAuE,EAAArE,CAAA,EAnBLC,EAEKoE,EAAA5C,CAAA,sBAELxB,EAcKoE,EAAA7C,CAAA,EAbJvB,EAYwGuB,EAAAiD,CAAA,mOAhBtFxB,EAAA,CAAA,EAAA,MAAAkC,EAAA,KAAAvF,MAAgB,2CAIfA,EAAe,EAAA,CAAA,wBAAEA,EAAqB,EAAA,CAAA,GAAQ,CAAAqE,GAAAhB,EAAA,CAAA,EAAA,IAAAgC,KAAAA,EAAArF,KAAW,SAAQ,KAAAsD,GAAAkC,EAAAH,CAAA,eACvErF,EAAmB,CAAA,CAAA,oBAAxB,OAAIkE,GAAA,EAAA,mHAAJ,6BAWelE,EAAe,EAAA,CAAA,wBAAEA,EAAqB,EAAA,CAAA,GAAQ,CAAAqE,GAAAhB,EAAA,CAAA,EAAA,IAAAiC,KAAAA,EAAAtF,KAAW,SAAQ,KAAAsD,GAAAmC,EAAAH,CAAA,wIA3D1DI,EAAA1F,MAAc,IAAG,SACtCA,EAAS,EAAA,EAAA,0BAFrB;AAAA,gBACa,MAACA,EAAC,EAAA,CAAA,MAAC,kBAAgB,aAAmB;AAAA,cACxC,aAAW,oBAAkB,MAACA,EAAC,EAAA,CAAA,MAAC;AAAA,OACvC,kMAIwB,aAAkB,MAACA,EAAQ,CAAA,CAAA,MAAC,mBAAwB,2DAAjCA,EAAQ,CAAA,CAAA,uDAiB9CA,EAAc,EAAA,EAAA,OAAI0F,EAAAtD,GACnBpC,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,IAAG,EAAA,sBALT;AAAA,GACF,0BACwB,IAAE,aAKnB,IAAE,kDANTC,EAMOC,EAAAuD,EAAArD,CAAA,uDALEJ,EAAc,EAAA,EAAA,KAAAsD,GAAAE,EAAAD,CAAA,EAAIF,EAAA,CAAA,EAAA,GAAAqC,KAAAA,EAAAtD,GACnBpC,EAAa,EAAA,EACbA,EAAY,EAAA,EAAA,KACZ,IAAG,EAAA,KAAAsD,GAAAqC,EAAAD,CAAA,uDAXF1F,EAAc,EAAA,EAAA,SAAWA,EAAS,EAAA,EAAA,wBAH1C;AAAA,KACC,0BAEsB,WAAS,aAC1B,IAAE,kHAHPC,EAGKC,EAAA6B,EAAA3B,CAAA,gCACJH,EAEIC,EAAA4B,EAAA1B,CAAA,yBAJEJ,EAAc,EAAA,EAAA,KAAAsD,GAAAE,EAAAD,CAAA,iBAAWvD,EAAS,EAAA,EAAA,KAAAsD,GAAAqC,EAAAD,CAAA,gHAJnC1F,EAAe,EAAA,EAAC,SAASA,EAAS,EAAA,CAAA,uOANlC4F,GAAA5F,MAAYA,EAAI,CAAA,GAAA,aAGf+D,EAAA/D,KAAW,SAAQ,uBAdP,MAAA,CAAA,KAAAA,MAAS,SAAS,aAIlCA,EAAa,EAAA,CAAA,uBAAlB,OAAIkE,GAAA,qBAQM,IAAAiB,EAAAnF,OAAa,MAAI6F,GAAA7F,CAAA,OAInBA,EAAmB,CAAA,CAAA,uBAAxB,OAAIkE,GAAA,8GAbC;AAAA,CACV,2CAKE;AAAA,qCACkC,mBAC5B,GAAC,aAAkB,GAAC,eAC2D;AAAA,qCACnD,mBAC5B,IAAE,aAAqB,GAAC,MACzB,MAAW,2CAsBN;AAAA;AAAA;AAAA;AAAA,CAIZ,gMA3CGjE,EA6CMC,EAAAuE,EAAArE,CAAA,EA5CLC,EAEKoE,EAAA5C,CAAA,sBACLxB,EAwCKoE,EAAA7C,CAAA,EAvCJvB,EAsCAuB,EAAAiD,CAAA,iEA/B+BxE,EAE7BwE,EAAA9C,CAAA,6CAC6B1B,EAE7BwE,EAAA/C,CAAA,0HAfgBuB,EAAA,CAAA,EAAA,MAAAkC,EAAA,KAAAvF,MAAS,sCAIzBA,EAAa,EAAA,CAAA,oBAAlB,OAAIkE,GAAA,EAAA,gHAAJ,WAOQ,CAAAG,GAAAhB,EAAA,CAAA,EAAA,IAAAuC,KAAAA,GAAA5F,MAAYA,EAAI,CAAA,GAAA,KAAAsD,GAAAwC,EAAAF,CAAA,EACd5F,OAAa,6DAEd,CAAAqE,GAAAhB,EAAA,CAAA,EAAA,IAAAU,KAAAA,EAAA/D,KAAW,SAAQ,KAAAsD,GAAAQ,EAAAC,CAAA,kBAEpB/D,EAAmB,CAAA,CAAA,oBAAxB,OAAIkE,GAAA,EAAA,gHAAJ,0KA9C4B,eAAa,2EAGhB,WAAS,MAAClE,EAAQ,CAAA,CAAA,MAAC,kBAAgB,2DAAzBA,EAAQ,CAAA,CAAA,mDAK7CuD,EAAAvD,EAAA,EAAA,EACOA,MAAiB,IACjB,SACAoC,GACDpC,EAAqB,EAAA,EAAGA,EAAiB,EAAA,EAAGA,EAAa,EAAA,EACzDA,MAAY,KACZ,4BAPC;AAAA,GACR,iCAQM,GAAC,4BANIC,EAMLC,EAAAuD,EAAArD,CAAA,0BARLiD,EAAA,CAAA,EAAA,GAAAE,KAAAA,EAAAvD,EAAA,EAAA,EACOA,MAAiB,IACjB,KAAEsD,GAAAE,EAAAD,CAAA,iBACFnB,GACDpC,EAAqB,EAAA,EAAGA,EAAiB,EAAA,EAAGA,EAAa,EAAA,EACzDA,MAAY,KACZ,iGAZoC+F,GAAA/F,MAAYA,EAAI,CAAA,GAAA,mBAiBxBgG,EAAAhG,KAAW,SAAQ,yBAxBjC,MAAA,CAAA,KAAAA,MAAa,SAAS,UAK1BA,EAAa,EAAA,GAAAmB,GAAA,EAGpBD,EAAAlB,OAAa,MAAIoB,GAAApB,CAAA,OAGnBA,EAAmB,CAAA,CAAA,uBAAxB,OAAIkE,GAAA,gJARoC,iBAAe,0CAErD,SAAO,eAAoC;AAAA;AAAA,iBAElC,mBAA2B,GAAC,aAAkB,GAAC,eACO;AAAA,iBACtD,2CACT,GAAC,2CAaJ;AAAA,YACO,mBAAuB,IAAE,aAAqB,GAAC,MACtD;AAAA;AAAA,CAEJ,yCAAoC,UAAQ,wSA7BzCjE,EA+BMC,EAAAuE,EAAArE,CAAA,EA9BLC,EAEKoE,EAAA5C,CAAA,sBACLxB,EA0BKoE,EAAA7C,CAAA,EAzBJvB,EAwB4CuB,EAAAiD,CAAA,EAxBvCxE,EAAmCwE,EAAA9C,CAAA,SAAe1B,EAErDwE,EAAA/C,CAAA,+BAESzB,EACTwE,EAAAoB,CAAA,6CACS5F,EACTwE,EAAAqB,CAAA,iEAcI7F,EAAsDwE,EAAAsB,CAAA,8BAGjE9F,EAAoCwE,EAAAuB,CAAA,6CA3Bb/C,EAAA,CAAA,EAAA,MAAAkC,EAAA,KAAAvF,MAAa,sBAOS,CAAAqE,GAAAhB,EAAA,CAAA,EAAA,IAAA0C,KAAAA,GAAA/F,MAAYA,EAAI,CAAA,GAAA,KAAAsD,GAAA+C,EAAAN,CAAA,EACjD/F,OAAa,yEAGfA,EAAmB,CAAA,CAAA,oBAAxB,OAAIkE,GAAA,EAAA,gHAAJ,QAagC,CAAAG,GAAAhB,EAAA,CAAA,EAAA,IAAA2C,KAAAA,EAAAhG,KAAW,SAAQ,KAAAsD,GAAAgD,EAAAN,CAAA,iLA/B5C,SAAAhG,KAAW,qBACRA,EAAe,CAAA,6CAExB,OAAAA,OAAqB,SAAQ,EAmCxBA,OAAqB,aAAY,EAiDjCA,OAAqB,OAAM,2IAzFtCC,EAkHKC,EAAAC,EAAAC,CAAA,iEAhHOiD,EAAA,CAAA,EAAA,IAAAkD,EAAA,SAAAvG,KAAW,kCACRA,EAAe,CAAA,sTA7BlB,CAAA,WAAAwG,CAAA,EAAA1F,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,WAAA2F,CAAA,EAAA3F,EACA,CAAA,SAAA4F,CAAA,EAAA5F,EACA,CAAA,oBAAA6F,CAAA,EAAA7F,EACA,CAAA,SAAA8F,CAAA,EAAA9F,EACA,CAAA,iBAAAoB,CAAA,EAAApB,GACA,gBAAA+F,EAAiC,IAAA,EAAA/F,EAExCgG,EACAC,EACAC,EAGAC,EAAgBN,EAAoB,KAAMO,GAC7CvE,GAAgCuE,EAAM,aAAa,CAAA,EAEhDC,EAAmB,CAAA,QAAS,OAAQ,QAAS,OAAO,EACpDC,EAAuBT,EAAoB,OAC7CO,GAA6BC,EAAgB,SAASD,EAAM,SAAS,CAAA,4CAkBpDJ,EAAWO,qDAmCXN,EAAOM,qDAkDPL,EAAcK,0YApG9BC,EAAA,GAAAC,EAAwBd,GAA0B,GAAA,mBACrDa,EAAA,GAAGE,EAAkB3G,EAAK,QAAQ,MAAO,EAAE,CAAA,i0CClClC,CAAA,QAAA4G,GAAA,KAAAC,EAAqB,EAAA,OAAA,0TAuKR,MAAA,CAAA,KAAA1H,MAAW,SAAS,aAG/BA,EAAW,CAAA,CAAA,uBAAhB,OAAIkE,GAAA,4MALRjE,GAaMC,EAAAuE,EAAArE,CAAA,EAZLC,EAEKoE,EAAA5C,CAAA,sBACLxB,EAQKoE,EAAA7C,CAAA,uFAVcyB,EAAA,KAAAkC,EAAA,KAAAvF,MAAW,iCAGtBA,EAAW,CAAA,CAAA,oBAAhB,OAAIkE,GAAA,EAAA,mHAAJ,6KAtBgB,MAAA,CAAA,KAAAlE,MAAS,SAAS,IAM7B,IAAAmF,EAAAnF,OAAa,MAAIiE,GAAAjE,CAAA,OACjBA,EAAS,CAAA,CAAA,uBAAd,OAAIkE,GAAA,8GAJD;AAAA;AAAA,kCAEuB,mBAA2B,GAAC,MAAClE,EAAU,CAAA,CAAA,MAAC,GAAC,eACa;AAAA,MAClF,yLATFC,GAiBMC,EAAAuE,EAAArE,CAAA,EAhBLC,EAEKoE,EAAA5C,CAAA,sBACLxB,EAYKoE,EAAA7C,CAAA,EAXJvB,EAUauB,EAAAiD,CAAA,SARexE,EAC1BwE,EAAApB,CAAA,kIANgBJ,EAAA,KAAAkC,EAAA,KAAAvF,MAAS,qCAK8BA,EAAU,CAAA,CAAA,EAC5DA,OAAa,wEACbA,EAAS,CAAA,CAAA,oBAAd,OAAIkE,GAAA,EAAA,mHAAJ,wMA5BgBlE,EAAgB,CAAA,CAAA,IAQ3B,IAAAmF,EAAAnF,OAAa,MAAIoB,GAAApB,CAAA,OACtBA,EAAS,CAAA,CAAA,uBAAd,OAAIkE,GAAA,gJANuC,iBAAe,0CAErD;AAAA;AAAA,iBAES,mBAA2B,GAAC,MAAClE,EAAU,CAAA,CAAA,MAAC,GAAC,eACa;AAAA,CACtE,6OAXGC,GAmBMC,EAAAuE,EAAArE,CAAA,EAlBLC,EAEKoE,EAAA5C,CAAA,sBACLxB,EAcKoE,EAAA7C,CAAA,EAbJvB,EAYcuB,EAAAiD,CAAA,EAZTxE,EAAmCwE,EAAA9C,CAAA,SAAe1B,EAErDwE,EAAA/C,CAAA,SAESzB,EACTwE,EAAAoB,CAAA,gJARgBjG,EAAgB,CAAA,6BAOMA,EAAU,CAAA,CAAA,EAC3CA,OAAa,wEAClBA,EAAS,CAAA,CAAA,oBAAd,OAAIkE,GAAA,EAAA,mHAAJ,4JAmCwClE,EAAQ,EAAA,EAAA,SAA8C,UACrFA,EAAI,EAAA,EAAA,SAAG,UACF,UAAc,cACeA,EAAQ,EAAA,EAAA,sCAH1C,eAAa,MAACA,EAAU,CAAA,CAAA,MAAC,OAAK,aAAU,8CAA4C,aAAK;AAAA,WAC1F,aAAM,GAAC,aAAK;AAAA,gBACP,aAAK,WAAS,aAAK;AAAA,4BACP,MAACA,EAAU,CAAA,CAAA,MAAC,OAAK,aAAU,YAAU,0DAH1DC,GAG+DC,EAAA2E,EAAAzE,CAAA,iJAC/DH,GAAKC,EAAA8B,EAAA5B,CAAA,oBAJcJ,EAAU,CAAA,CAAA,gBAAOA,EAAQ,EAAA,EAAA,KAAAsD,GAAAqC,EAAAD,CAAA,gBACvC1F,EAAI,EAAA,EAAA,KAAAsD,GAAA+C,EAAAN,CAAA,YAEa/F,EAAU,CAAA,CAAA,gBAAOA,EAAQ,EAAA,EAAA,KAAAsD,GAAAqE,EAAAC,CAAA,yEApBvB,aAAkB,MAAC5H,EAAQ,CAAA,CAAA,MAAC,mBAAwB,0DAAjCA,EAAQ,CAAA,CAAA,wDAMpCA,EAAI,EAAA,EAAA,oBAAP,IAAE,2DAACA,EAAI,EAAA,EAAA,KAAAsD,GAAAE,EAAAD,CAAA,sDADpBvD,EAAQ,EAAA,EAAA,WACAA,EAAI,EAAA,GAAAiB,GAAAjB,CAAA,iBAJV;AAAA,sBACc,mBACG;AAAA,KACpB,aAAU,GAAC,eACgB;AAAA,OACzB,2DAJeC,GAGbC,EAAAuD,EAAArD,CAAA,mEADHJ,EAAQ,EAAA,EAAA,KAAAsD,GAAAuE,EAAAC,CAAA,EACA9H,EAAI,EAAA,qJAzBa,WAAS,MAACA,EAAQ,CAAA,CAAA,MAAC,kBAAgB,0DAAzBA,EAAQ,CAAA,CAAA,4DAK/CA,EAAI,EAAA,EAAA,aAAsCA,EAAQ,EAAA,EAAA,wBAHjD;AAAA,QACK,mBACE;AAAA,CACT,aAAM,aAAW,mBAAuB,IAAE,aAAU,GAAC,MAAM;AAAA;AAAA,CAE3D,qFAJOC,GAIAC,EAAA4B,EAAA1B,CAAA,uBAFUC,EAA0CyB,EAAAC,CAAA,oDAA1D/B,EAAI,EAAA,EAAA,KAAAsD,GAAAuE,EAAAC,CAAA,gBAAsC9H,EAAQ,EAAA,EAAA,KAAAsD,GAAAkC,EAAAH,CAAA,8FAhB5C,OAAArF,OAAqB,SAAQ,EAqBxBA,OAAqB,aAAY,EAmBjCA,OAAqB,OAAM,kZAzClB,qIAFrBC,GA4DKC,EAAAC,EAAAC,CAAA,yMAnLO,GAAA,CAAA,aAAA2H,CAAA,EAAAjH,EACA,CAAA,WAAAkH,CAAA,EAAAlH,EACA,CAAA,KAAAD,CAAA,EAAAC,GACA,WAAA2F,EAAa,EAAA,EAAA3F,EACb,CAAA,iBAAAoB,CAAA,EAAApB,EACA,CAAA,SAAA8F,CAAA,EAAA9F,EAEPgG,EACAmB,EACAlB,EACAmB,EAEO,CAAA,UAAAC,EAAA,EAAA,EAAArH,EAEI,eAAAsH,GAAA,CAQP,OADH,MAHA,MAAiB,MACpBvH,EAAK,QAAQ,MAAO,EAAE,EAAI4F,EAAa,2BAAA,GAEd,OAIvB,IAAA4B,EACAC,EAAA,CAAA,EACAC,EAAA,CAAA,EACAC,EAAA,CAAA,EAEK,SAAAC,EAAgBC,EAAenG,EAAA,CACjC,MAAA0C,EAAA,IAAe8C,EAAaW,EAAK,QAAQ,EAAE,QAAQ,GAMnDC,EAJ0BD,EAAK,KAAK,OACxCE,GAAA,OAAaA,EAAM,GAAA,EAInB,KAAK1B,EAAOnE,IAAA,CACR,GAAAsF,EAAepD,CAAQ,EAAA,CACpB,MAAA4D,EAAaR,EAAepD,CAAQ,EAAE,WAAWlC,CAAK,EACvD,GAAA,CAAA8F,EACG,OAEF,MAAAC,EAAaD,EAAW,eACxBE,EAAcF,EAAW,YAAY,QACvCtG,IAAS,gBACAuG,CAAU,IAAI1G,GACzB8E,EACA6B,EACA,IAAA,CAAA,MAESxG,IAAS,kBACLuG,CAAU,KAAK1G,GAC5B8E,EACA6B,EACA,IAAA,CAAA,MAESxG,IAAS,OACL,MAAA,OAAAH,GACb8E,EACA6B,EACA,MAAA,CAAA,cAIS3G,GAAgB8E,EAAA,OAA4B3E,CAAI,CAAA,EAE5D,CAAA,EAAA,OAAQqG,GAAa,OAAAA,EAAM,GAAW,EACtC,KAAK;AAAA,CAAK,EACR,GAAAD,EAAA,IACCpG,IAAS,cACFoG,CAAM;AAAA,KACNpG,IAAS;EACNoG,CAAM;AAAA,MACTpG,IAAS;EACPoG,CAAM;AAAA,SAGhBpG,IAAS,KACL,GAED;AAAA,EAGRkF,GAAA,SAAA,CAECY,GADmB,MAAAD,KACG,gBAClB,IAAAY,EAAyBb,EAAU,IAAKO,GAC3CD,EAAgBC,EAAM,IAAI,CAAA,EAEvBO,EAAyBd,EAAU,IAAKO,GAC3CD,EAAgBC,EAAM,IAAI,CAAA,EAEvBQ,EAA2Bf,EAAU,IAAKO,GAC7CD,EAAgBC,EAAM,MAAM,CAAA,EAEzBS,EAAsBhB,EAAU,IAClCO,GAASX,EAAaW,EAAK,QAAQ,EAAE,UAAY,EAAA,EAEnDpB,EAAA,EAAAgB,EAAYU,EAAa,IAAK,CAAAN,EAAM3F,MACnC,KAAA2F,EACA,SAAUS,EAAUpG,CAAK,CAAA,EAAA,CAAA,EAE1BuE,EAAA,EAAAiB,EAAYU,EAAa,IAAK,CAAAP,EAAM3F,MACnC,KAAA2F,EACA,SAAUS,EAAUpG,CAAK,CAAA,EAAA,CAAA,EAE1BuE,EAAA,EAAAkB,EAAcU,EAAe,IAAK,CAAAR,EAAM3F,MACvC,KAAA2F,EACA,SAAUS,EAAUpG,CAAK,CAAA,EAAA,CAAA,EAGpB,MAAA2E,GAAA,EAENJ,EAAA,EAAAW,EAAmBnB,EAAY,SAAA,8CAYbA,EAAWO,qDAqBXN,EAAOM,qDAmBPa,EAASb,+lCC9K7B,MAAe+B,GAAA,wpCCAAC,GAAA,0yBCAAC,GAAA,+yECeC,WACL,yDAFR,IAAAC,EAAAvJ,MAAoB,SAAW,QAAU,WAAY8H,EAAA9H,KAAiB,OAAM,+BAA5B,MAAI,eAAwB;AAAA,WACrE,0DADPqD,EAAA,GAAAkG,KAAAA,EAAAvJ,MAAoB,SAAW,QAAU,SAAMsD,GAAAkG,EAAAD,CAAA,EAAMlG,EAAA,GAAAyE,KAAAA,EAAA9H,KAAiB,OAAM,KAAAsD,GAAAuE,EAAAC,CAAA,sGAWrD9H,EAAC,EAAA,CAAA,gDAAtBC,GAA8BC,EAAAuD,EAAArD,CAAA,wCAGgCJ,EAAU,CAAA,EACtEA,OACC,KAAI,8DAFuDA,EAAU,CAAA,EACtEA,OACC,KAAI,KAAAsD,GAAA,EAAAF,CAAA,kCAF8B,IAAAA,EAAApD,KAAY,KAAI,kDAAhBqD,EAAA,GAAAD,KAAAA,EAAApD,KAAY,KAAI,KAAAsD,GAAA,EAAAF,CAAA,0DAMfpD,EAAK,CAAA,EAAA,SAC3CA,EAAS,CAAA,EAAA,WAXLA,EAAgB,CAAA,EAAC,OAAS,GAACmB,GAAAnB,CAAA,kBAIzB,OAAAA,OAAqB,SAAQoB,oIAKtB,wCACwB,eAAO,IAAE,eAG/C;AAAA,eACD,2JAjBDnB,GAAgBC,EAAAyD,EAAAvD,CAAA,YAChBH,GAiBKC,EAAAC,EAAAC,CAAA,EAhBJC,GASGF,EAAAI,CAAA,yBALFF,GAIAE,EAAAkD,CAAA,sBAEDpD,GAKGF,EAAAM,CAAA,0DAdGT,EAAgB,CAAA,EAAC,OAAS,6HAUQA,EAAK,CAAA,EAAA,KAAAsD,GAAAwC,EAAAF,CAAA,cAC3C5F,EAAS,CAAA,EAAA,KAAAsD,GAAAM,EAAAC,CAAA,wGASI,EAAK,CAAA,CAAA,uEADtB5D,GAEKC,EAAAC,EAAAC,CAAA,wKAhCQJ,EAAgB,CAAA,EAAC,OAAS,EAACiB,6BAOjCjB,EAAgB,CAAA,CAAA,uBAArB,OAAIkE,GAAA,2BAsBFlE,EAAU,CAAA,GAAAsB,GAAA,kHA9BT;AAAA,UACG,6KAMQtB,EAAU,CAAA,CAAA,UAV3BC,GAQIC,EAAAiE,EAAA/D,CAAA,EAPHC,GAEK8D,EAAAvC,CAAA,gCAON3B,GAsBKC,EAAAkE,EAAAhE,CAAA,gLArBGJ,EAAgB,CAAA,CAAA,oBAArB,OAAIkE,GAAA,EAAA,mHAAJ,8BADclE,EAAU,CAAA,CAAA,EAuBtBA,EAAU,CAAA,+NAvCH,GAAA,CAAA,WAAAsE,CAAA,EAAAxD,EACA,CAAA,iBAAAyD,CAAA,EAAAzD,EACA,CAAA,WAAA0D,CAAA,EAAA1D,EACA,CAAA,iBAAAoB,CAAA,EAAApB,0tBCNZ,MAAe2I,GAAA,GAAA,IAAA,IAAA,mBAAA,YAAA,GAAA,EAAA,goBCsTT;AAAA;AAAA,OAGA,4DAAqC;AAAA,EAC1C,qDAA6B;AAAA;AAAA,EAE7B,0CAAmB,GACpB,mBAJMxJ,EAAqCC,EAAAM,EAAAJ,CAAA,WAC1CH,EAA6BC,EAAAwJ,EAAAtJ,CAAA,WAE7BH,EAAmBC,EAAAyJ,EAAAvJ,CAAA,gHAlLfJ,EAAS,CAAA,EAAC,OAAS,EAAIA,KAAU,OAASA,KAAM,QAAM,4CAlCjDA,EAAU,EAAA,CAAA,uBAAf,OAAIkE,GAAA,qBAcH,IAAA0F,EAAA5J,OAAsB,SAAO6J,GAAA7J,CAAA,IAsB5BA,EAAS,CAAA,EAAC,OAAS,GAAC8J,GAAA9J,CAAA,OAsBlBA,EAAS,CAAA,EAAC,OAAS,EAAIA,EAAS,CAAA,EAAGA,EAAK,CAAA,CAAA,uBAA7C,OAAIkE,GAAA,6DAoEF,OAAAlE,OAAsB,kBAAiB,EAiBlCA,OAAsB,MAAK,EAgB3BA,OAAsB,QAAO,uCAiBlCA,EAAiB,CAAA,GAAAoB,GAAApB,CAAA,mLA9IqC,sBAAoB,2MAwK5C,wCAElC,yOAFSA,EAAQ,CAAA,CAAA,+DA7MlBC,EAeKC,EAAA0B,EAAAxB,CAAA,EAdJC,EAaKuB,EAAAC,CAAA,EAZJxB,EAA8CwB,EAAA4B,CAAA,6FAiChDxD,EAwBKC,EAAAkE,EAAAhE,CAAA,EAvBJC,EAEA+D,EAAA2F,CAAA,+CAsBD9J,EAkEKC,EAAA8J,EAAA5J,CAAA,iEACLH,EAAYC,EAAAK,EAAAH,CAAA,yDA4EZH,EAAYC,EAAAO,EAAAL,CAAA,WACZH,EAIGC,EAAA+J,EAAA7J,CAAA,EAHFC,EAEG4J,EAAAC,CAAA,yCA5MKlK,EAAU,EAAA,CAAA,uBAAf,OAAIkE,IAAA,EAAA,+HAAJ,UAcClE,OAAsB,8IAoBvBA,EAAS,CAAA,EAAC,OAAS,EAAIA,KAAU,OAASA,KAAM,QAAM,KAAAsD,GAAAwC,EAAAF,CAAA,EAEpD5F,EAAS,CAAA,EAAC,OAAS,0EAsBjBA,EAAS,CAAA,EAAC,OAAS,EAAIA,EAAS,CAAA,EAAGA,EAAK,CAAA,CAAA,uBAA7C,OAAIkE,IAAA,EAAA,+HAAJ,+KAsHElE,EAAiB,CAAA,8FA0BZA,EAAQ,CAAA,CAAA,0NAlMbA,EAAY,EAAA,EAAA,2GALGwB,EAAAd,EAAA,QAAAyJ,EAAA,YAAAnK,EAAsB,CAAA,IAAAA,EAAA,EAAA,EACnC,eACA,iBAAe,iBAAA,UAJnBC,EAQQC,EAAAQ,EAAAN,CAAA,wDANSiD,EAAA,CAAA,EAAA,IAAA8G,KAAAA,EAAA,YAAAnK,EAAsB,CAAA,IAAAA,EAAA,EAAA,EACnC,eACA,iBAAe,6OAwBrBC,EAAYC,EAAAc,EAAAZ,CAAA,gNAXuD0H,EAAA9H,OAChE,MACG,MACA,+DAIeA,EAAW,EAAA,CAAA,CAAA,CAAA,kEAPkB,kBAAgB,eAG1C,GAAC,8OAGiCA,EAAW,EAAA,oHARpEC,EAWKC,EAAA0B,EAAAxB,CAAA,EAVJC,EAKAuB,EAAAwI,CAAA,EAJE/J,EAA8C+J,EAAA3G,CAAA,8BAKhDpD,EAGKuB,EAAAC,CAAA,EAFJxB,EAAqEwB,EAAAwI,CAAA,oCANL,CAAAhG,GAAAhB,EAAA,CAAA,EAAA,KAAAyE,KAAAA,EAAA9H,OAChE,MACG,MACA,oBAAiBsD,GAAAuE,EAAAC,CAAA,4BAGmC9H,EAAW,EAAA,aAAXA,EAAW,EAAA,iCAChDA,EAAW,EAAA,8YAY/BC,EAiBKC,EAAAC,EAAAC,CAAA,EAhBJC,EAOQF,EAAAmK,CAAA,SACRjK,EAOQF,EAAAoK,CAAA,wQAYIvK,EAAc,CAAA,EAAC,IAAIA,MAAK,IAAI,GACpCA,EAAiB,CAAA,IAAK,kBACbqK,EAAA,SAAAG,EAAAxK,OAAsB,kBACzBwB,EAAA6I,EAAA,QAAAI,EAAAzK,OAAsB,kBAC1B,qCACA,EAAE,UARNC,EAiBCC,EAAAmK,EAAAjK,CAAA,2DAdSJ,EAAc,CAAA,EAAC,IAAIA,MAAK,IAAI,GACpCA,EAAiB,CAAA,IAAK,kCACbqD,EAAA,CAAA,EAAA,IAAAmH,KAAAA,EAAAxK,OAAsB,mCACzBqD,EAAA,CAAA,EAAA,IAAAoH,KAAAA,EAAAzK,OAAsB,kBAC1B,qCACA,wHA4BC,OAAO,KAAKA,MAAK,UAAU,EAAE,OAAS,oHAD5CC,EAsBKC,EAAAC,EAAAC,CAAA,yMAFHH,EAA+BC,EAAAc,EAAAZ,CAAA,4CAjBvBsK,EAAAC,GAAA,OAAO,QAAQ3K,MAAK,UAAU,CAAA,uBAAnC,OAAIkE,GAAA,uHADPjE,EAgBKC,EAAAC,EAAAC,CAAA,sEAfGsK,EAAAC,GAAA,OAAO,QAAQ3K,MAAK,UAAU,CAAA,oBAAnC,OAAI,GAAA,EAAA,mHAAJ,yDAEOA,EAAI,EAAA,EAAA,WAER0F,EAAA1F,MAAM,KAAI,OAAEA,EAAK,EAAA,EAAC,UAAY,OACf,cAAA,KAAK,UAAUA,MAAM,OAAO,CAAA,GAC1C,WAGF+F,GAAA/F,EAAM,EAAA,EAAA,YACJA,EAAM,EAAA,EAAA,YACN,8DAA4D,yEARpC,GAC1B,uBAEK,GACP,gNANDC,EAYKC,EAAAC,EAAAC,CAAA,EAXJC,EAAkBF,EAAAsE,CAAA,gBAClBpE,EAIMF,EAAAsD,CAAA,qCACNpD,EAIGF,EAAAa,CAAA,uCAVIhB,EAAI,EAAA,EAAA,KAAAsD,GAAAkG,EAAAD,CAAA,EAERlG,EAAA,CAAA,EAAA,GAAAqC,KAAAA,EAAA1F,MAAM,KAAI,KAAAsD,GAAAqC,EAAAD,CAAA,iBAAE1F,EAAK,EAAA,EAAC,UAAY,OACf,cAAA,KAAK,UAAUA,MAAM,OAAO,CAAA,GAC1C,KAAEsD,GAAAwC,EAAAF,CAAA,EAGJvC,EAAA,CAAA,EAAA,GAAA0C,KAAAA,GAAA/F,EAAM,EAAA,EAAA,YACJA,EAAM,EAAA,EAAA,YACN,8DAA4D,KAAAsD,GAAA+C,EAAAN,CAAA,iDAzBzCxC,EAAAvD,MAAK,KAAI,SAEhC0F,GAAA1F,EAAK,EAAA,EAAA,YACJA,EAAK,EAAA,EAAA,YACL,oDAAkD,SAG7BqF,EAAArF,EAAK,EAAA,EAAA,SAAW,IAAM,gBAhC5CA,EAAS,CAAA,EAAC,OAAS,GAACoF,GAAApF,CAAA,+CAmCrB,IAAAkB,EAAAlB,MAAK,UAAQ6F,GAAA7F,CAAA,sGAV4B;AAAA,QAC3C,oVA5BJC,EA8DKC,EAAA0B,EAAAxB,CAAA,EA7DJC,EAmCKuB,EAAAC,CAAA,wBAdJxB,EAaQwB,EAAAnB,CAAA,EATPL,EAOAK,EAAAuF,CAAA,EANE5F,EAA0C4F,EAAAlE,CAAA,gBAC3C1B,EAIC4F,EAAAnE,CAAA,gBAEFzB,EAA0DK,EAAAwF,CAAA,8EAhCtDlG,EAAS,CAAA,EAAC,OAAS,yDAyBIqD,EAAA,CAAA,EAAA,GAAAE,KAAAA,EAAAvD,MAAK,KAAI,KAAAsD,GAAAE,EAAAD,CAAA,EAEhCF,EAAA,CAAA,EAAA,GAAAqC,KAAAA,GAAA1F,EAAK,EAAA,EAAA,YACJA,EAAK,EAAA,EAAA,YACL,oDAAkD,KAAAsD,GAAAqC,EAAAD,CAAA,EAG7BrC,EAAA,CAAA,EAAA,GAAAgC,KAAAA,EAAArF,EAAK,EAAA,EAAA,SAAW,IAAM,MAAGsD,GAAAkC,EAAAH,CAAA,EAGhDrF,MAAK,wQAgEoB;AAAA;AAAA,GAEhC,gDACC;AAAA,GACD,iKAJAC,EAAgCC,EAAA6J,EAAA3J,CAAA,WAEhCH,EACCC,EAAAgK,EAAA9J,CAAA,WACDH,EAAYC,EAAAc,EAAAZ,CAAA,kXApBkB;AAAA;AAAA;AAAA;AAAA,GAI9B,8DAJAH,EAA8BC,EAAA6J,EAAA3J,CAAA,WAI9BH,EAAYC,EAAAc,EAAAZ,CAAA,oXArB8B;AAAA;AAAA;AAAA,GAG1C,8DAHAH,EAA0CC,EAAA6J,EAAA3J,CAAA,WAG1CH,EAAYC,EAAAc,EAAAZ,CAAA,iPAyCHmD,EAAA,KAAK,UAAUvD,EAAwB,CAAA,EAAA,KAAM,CAAC,EAAA,+BAHlC,KAAA,KAAK,UAAUA,EAAwB,CAAA,EAAA,KAAM,CAAC,+LAFlEC,EAOMC,EAAAuE,EAAArE,CAAA,EANLC,EAEKoE,EAAA5C,CAAA,sBACLxB,EAEKoE,EAAA7C,CAAA,EADJvB,EAA2DuB,EAAAiD,CAAA,iCAHzCxB,EAAA,CAAA,EAAA,MAAAkC,EAAA,KAAA,KAAK,UAAUvF,EAAwB,CAAA,EAAA,KAAM,CAAC,cAG1D,CAAAqE,GAAAhB,EAAA,CAAA,EAAA,MAAAE,KAAAA,EAAA,KAAK,UAAUvD,EAAwB,CAAA,EAAA,KAAM,CAAC,EAAA,KAAAsD,GAAAE,EAAAD,CAAA,0HAhB9CA,EAAA,KAAK,UAAUvD,EAAsB,CAAA,EAAA,KAAM,CAAC,EAAA,+BAHhC,KAAA,KAAK,UAAUA,EAAsB,CAAA,EAAA,KAAM,CAAC,+LAFhEC,EAOMC,EAAAuE,EAAArE,CAAA,EANLC,EAEKoE,EAAA5C,CAAA,sBACLxB,EAEKoE,EAAA7C,CAAA,EADJvB,EAAyDuB,EAAAiD,CAAA,iCAHvCxB,EAAA,CAAA,EAAA,MAAAkC,EAAA,KAAA,KAAK,UAAUvF,EAAsB,CAAA,EAAA,KAAM,CAAC,cAGxD,CAAAqE,GAAAhB,EAAA,CAAA,EAAA,MAAAE,KAAAA,EAAA,KAAK,UAAUvD,EAAsB,CAAA,EAAA,KAAM,CAAC,EAAA,KAAAsD,GAAAE,EAAAD,CAAA,0HAhB5CA,EAAA,KAAK,UAAUvD,EAA0B,EAAA,EAAA,KAAM,CAAC,EAAA,+BAJ/C,KAAA,KAAK,UAAUA,EAA0B,EAAA,EAAA,KAAM,CAAC,+LAHzDC,EASMC,EAAAuE,EAAArE,CAAA,EARLC,EAIKoE,EAAA5C,CAAA,sBACLxB,EAEKoE,EAAA7C,CAAA,EADJvB,EAA6DuB,EAAAiD,CAAA,iCAJtDxB,EAAA,CAAA,EAAA,OAAAkC,EAAA,KAAA,KAAK,UAAUvF,EAA0B,EAAA,EAAA,KAAM,CAAC,cAIjD,CAAAqE,GAAAhB,EAAA,CAAA,EAAA,OAAAE,KAAAA,EAAA,KAAK,UAAUvD,EAA0B,EAAA,EAAA,KAAM,CAAC,EAAA,KAAAsD,GAAAE,EAAAD,CAAA,mLA4CtD;AAAA,oCAEF;;;;;iWARDtD,EAoBKC,EAAAC,EAAAC,CAAA,EAnBJC,EAOOF,EAAAiK,CAAA,EANN/J,EAIC+J,EAAAC,CAAA,YAFcrK,EAAmB,CAAA,gBAKnCK,EAUGF,EAAAa,CAAA,gEAfahB,EAAmB,CAAA,mGAzLjCA,EAAiB,CAAA,EAAA,4UAhGV,CAAA,kBAAA4K,CAAA,EAAA9J,EACA,CAAA,eAAA+J,CAAA,EAAA/J,EACA,CAAA,0BAAAgK,CAAA,EAAAhK,EACA,CAAA,MAAAiK,CAAA,EAAAjK,EACA,CAAA,UAAAkK,EAAA,EAAA,EAAAlK,GACA,eAAAmK,EAAkC,IAAA,GAAA,EAAAnK,EAClC,CAAA,aAAAoK,CAAA,EAAApK,EACA,CAAA,eAAAqK,CAAA,EAAArK,EACA,CAAA,kBAAAsK,CAAA,EAAAtK,EACA,CAAA,SAAAuK,CAAA,EAAAvK,EAkBPwK,EAA+B,kBAC/BC,EAAsB,GAEpB,MAAAC,EAAA,CAAA,CACJ,kBAAmB,iBAAiB,EAAA,CACpC,MAAO,KAAK,EAAA,CACZ,QAAS,OAAO,CAAA,EAOT,SAAAC,EACRC,EACAC,EAAA,KAEKD,EAAoB,OAAA,KAEnB,MAAAE,EAAS,KAAK,MAAM,KAAK,UAAUF,CAAW,CAAA,KAEhDC,GAAkBP,EAAA,CACf,MAAAS,EAAA,CACL,QAAS,MACT,KAAA,CACC,SACA,cACA,SACA,aACAP,IAAsB,MACnBT,EACAC,EACH,uBAGFc,EAAO,WAAW,uBAAyBC,OAEpC,OAAAD,EAAO,YAAY,uBAGpB,OAAAA,EAuCa,MAAA7K,EAAA+K,GAAAxE,EAAA,EAAAgE,EAAoBQ,CAAS,aAmC7Cb,EAAc,IAAO,IAAID,EAAU,IAAKe,GAAMA,EAAE,IAAI,CAAA,CAAA,UAQpDzE,EAAA,EAAA2D,MAAqB,GAAG,QAsBXe,IAAC,CACRA,EAAE,cAAc,QACnBf,EAAe,IAAIgB,EAAK,IAAI,EAE5BhB,EAAe,OAAOgB,EAAK,IAAI,8BAQZ,SAAQ,CAAIA,EAAK,SAAQjB,EAAA1D,EAAA,EAAAyD,CAAA,CAAA,eAkGlCQ,EAAmB,KAAA,+gBArPrCjE,EAAA,GAAG4E,EACFZ,IAAsB,MAAQT,EAAiBC,CAAA,uBAiChDxD,EAAA,GAAG6E,EAA2BV,EAC7BP,GAEM,GAAAA,EACH,WAAA,IACIA,EAAa,WAChB,OAAA,CACI,GAAAA,EAAa,WAAW,OAC3B,IAAKJ,KAIP,KACHS,yBAGEjE,EAAA,EAAA8E,EAAuBX,EACzBP,EACAK,CAAA,CAAA,uBAEEjE,EAAA,EAAA+E,EAAyBZ,EAC3BN,EACAI,CAAA,CAAA,stDC9FQ,CAAA,QAAA9D,GAAA,sBAAA7G,IAAsC,OAAA,mPAmS1CZ,EAAS,EAAA,EAAA,0eAIL,KAAAA,MAAYA,EAAI,CAAA,0EAcdA,EAAK,EAAA,CAAA,uBAAV,OAAIkE,GAAA,0DAcF,OAAAlE,KAAU,OAAM,0BAoHhB,IAAAkB,EAAAlB,QAAqB,OAAKmB,GAAAnB,CAAA,+eAnJjCC,EAOKC,EAAA2B,EAAAzB,CAAA,wBAELH,EAqLKC,EAAAoM,EAAAlM,CAAA,EApLJC,GAIKiM,EAAA1K,CAAA,UACLvB,GA8KKiM,EAAAtC,CAAA,EA7KJ3J,GAcK2J,EAAA5F,CAAA,4HA3BCf,EAAA,CAAA,EAAA,IAAAkJ,EAAA,KAAAvM,MAAYA,EAAI,CAAA,qEAcdA,EAAK,EAAA,CAAA,oBAAV,OAAIkE,GAAA,EAAA,mHAAJ,2IAkIElE,QAAqB,oSAxHtBA,EAAY,EAAA,EAAA,iHADHA,EAAG,EAAA,CAAA,GAAAwB,EAAAgL,EAAA,MAAAC,CAAA,8CANbjL,EAAAkL,EAAA,QAAAC,EAAA,YAAA3M,QAAqBA,EAAQ,EAAA,EAAG,eAAiB,iBAAe,iBAAA,UAFjEC,EAUIC,EAAAwM,EAAAtM,CAAA,EAFHC,GAAuBqM,EAAAF,CAAA,kEANvBnJ,EAAA,CAAA,EAAA,MAAAsJ,KAAAA,EAAA,YAAA3M,QAAqBA,EAAQ,EAAA,EAAG,eAAiB,iBAAe,mIAiD5D,OAAAA,EAAoB,EAAA,GAAA,UAAYA,OAAoB,aAAY,EAO3DA,OAAoB,MAAK,0BAkB/B,IAAAkB,EAAAlB,QAAqB,OAAK6F,GAAA7F,CAAA,iGA1B/BC,EAwBGC,EAAAc,EAAAZ,CAAA,mMAEEJ,QAAqB,uPAxDpB8H,EAAA9H,KAAU,OAAM,yHAcR,WAAAA,KAAI,WACJ,WAAAA,MAAYA,EAAI,CAAA,mDAjB7B,wBACuB,oBACpB,GAAC,eAAkB,GAAC,wBAGrB;AAAA,wBAEe,OAACA,EAAgB,EAAA,CAAA,OAAA;AAAA,eAElC;;uVAbDC,EA6BKC,EAAAC,EAAAC,CAAA,EA5BJC,GAOGF,EAAAI,CAAA,UAHoBF,GAEtBE,EAAAkD,CAAA,kCAEDpD,GAIGF,EAAAM,CAAA,uDAWHJ,GAIGF,EAAA8J,CAAA,WAEJhK,EAIGC,EAAA0M,EAAAxM,CAAA,gBA5BG,CAAAiE,GAAAhB,EAAA,CAAA,EAAA,KAAAyE,KAAAA,EAAA9H,KAAU,OAAM,KAAAsD,GAAAuE,EAAAC,CAAA,wBAKH9H,EAAgB,EAAA,CAAA,mIASrBqD,EAAA,CAAA,EAAA,IAAAwJ,EAAA,WAAA7M,KAAI,YACJqD,EAAA,CAAA,EAAA,IAAAwJ,EAAA,WAAA7M,MAAYA,EAAI,CAAA,kLAoCvB,yDAEN,sLAVS,MAAAA,KAAM,OAAMA,EAAA,EAAA,CAAA,YACRA,EAAK,CAAA,kXADTqD,EAAA,CAAA,EAAA,MAAAyJ,EAAA,MAAA9M,KAAM,OAAMA,EAAA,EAAA,CAAA,yBACRA,EAAK,CAAA,qTAbmD;AAAA,QAEpE,oBAAyCA,EAAgB,EAAA,CAAA,OAAO;AAAA,gBACxD,iBAES,MAAI,OACpB,2CACF,sCAHQwB,EAAA0I,EAAA,OAAA6C,EAAA/M,OAAoB,SAAWgN,GAAUC,EAAO,wEAFvDhN,EAAgEC,EAAAuD,EAAArD,CAAA,mBACxDH,EAGPC,EAAAgK,EAAA9J,CAAA,2CAJwCJ,EAAgB,EAAA,CAAA,EAElDqD,EAAA,CAAA,EAAA,MAAA0J,KAAAA,EAAA/M,OAAoB,SAAWgN,GAAUC,uJA4B3CjN,EAAQ,CAAA,GAAA8J,GAAA9J,CAAA,gHAsBR,IAAAkB,EAAAlB,OAAoB,QAAMkN,GAAAlN,CAAA,qDA1Bf;AAAA;AAAA;AAAA,QAIhB,gBASQ;AAAA;AAAA;AAAA,QAGR,yBAQQ;AAAA;AAAA,QAER,2EA1BDC,EA8CGC,EAAAc,EAAAZ,CAAA,6IA1CGJ,EAAQ,CAAA,qHAsBRA,OAAoB,gRAtBX;AAAA,0CACoB,iBAOhB,WAAS,OACzB,IAAE,EAPIwB,EAAA0I,EAAA,OAAA6C,EAAA/M,OAAoB,SACvBgN,GAAUG,GACVnN,OAAoB,aACnBiN,GAAUE,GACVC,EAAS,kFALmBnN,EAQhCC,EAAAgK,EAAA9J,CAAA,2BAPMiD,EAAA,CAAA,EAAA,MAAA0J,KAAAA,EAAA/M,OAAoB,SACvBgN,GAAUG,GACVnN,OAAoB,aACnBiN,GAAUE,GACVC,2OAYLnN,EAA8BC,EAAAC,EAAAC,CAAA,WAC9BH,EAAwCC,EAAAc,EAAAZ,CAAA,sGAYlB+E,EAAAnF,OAAa,MAAIoF,GAAA,6BATF,GAAM,kBAAM;AAAA;AAAA,SAEjD,iDAA2B;AAAA,SAC3B,yCAAgB;AAAA,eACV,wCAAgB,gBAAc,yCAAiB;AAAA,oBAC1C,6CAAqB;AAAA,SAChC,wCAAgB;AAAA,8BACK,wCAAgB,OAAK,yCAAiB;AAAA;AAAA,0BAE1C,gBAEQ;AAAA,SACzB,iBAAoC,WAAS,OAAI,GAClD,+NADUgI,EAAS,+DAZanN,EAAMC,EAAAmN,EAAAjN,CAAA,WAAMH,EAAMC,EAAAoN,EAAAlN,CAAA,WAEjDH,EAA2BC,EAAA6J,EAAA3J,CAAA,WAC3BH,EAAgBC,EAAAM,EAAAJ,CAAA,WACVH,EAAgBC,EAAAwJ,EAAAtJ,CAAA,WAAcH,EAAiBC,EAAAyJ,EAAAvJ,CAAA,WAC1CH,EAAqBC,EAAAqN,EAAAnN,CAAA,WAChCH,EAAgBC,EAAAsN,EAAApN,CAAA,WACKH,EAAgBC,EAAAuN,EAAArN,CAAA,WAAKH,EAAiBC,EAAAwN,EAAAtN,CAAA,gCAK3DH,EAAiDC,EAAAgK,EAAA9J,CAAA,2BAH3BJ,OAAa,6OAAI;AAAA,6BAEnB,+DAchBA,EAAY,CAAA,CAAA,uBAAjB,OAAI,GAAA,oNAACA,EAAY,CAAA,CAAA,oBAAjB,OAAIkE,GAAA,EAAA,sHAAJ,OAAIA,EAAAyJ,EAAA,OAAAzJ,GAAA,0CAAJ,OAAIA,GAAA,4LAImBlE,EAAI,EAAA,EAAC,gBACzB,IAAMA,EAAU,EAAA,EAAC,UAChB,yFAMU,WAAAA,KAAI,2BACCA,EAAI,EAAA,EAAC,gBACrB,IAAMA,EAAU,EAAA,EAAC,UAChB,iDAIgBA,EAAI,EAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,sBACUA,EAAO,EAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,qFAMgBA,EAAI,EAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,mBACUA,EAAO,EAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,qMA/BJC,EAmCKC,EAAAC,EAAAC,CAAA,2HAjCkBJ,EAAI,EAAA,EAAC,gBACzB,IAAMA,EAAU,EAAA,EAAC,UAChB,0JAMUqD,EAAA,CAAA,EAAA,IAAAuK,EAAA,WAAA5N,KAAI,0CACCA,EAAI,EAAA,EAAC,gBACrB,IAAMA,EAAU,EAAA,EAAC,UAChB,iEAIgBA,EAAI,EAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,qCACUA,EAAO,EAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,sGAMgBA,EAAI,EAAA,EAAC,gBACtB,IAAMA,EAAU,EAAA,EAAC,UAChB,kCACUA,EAAO,EAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,EAC3D,uQAhCAA,EAAU,EAAA,EAAC,UAAYA,EAAI,EAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,GAAAiB,GAAAjB,CAAA,uEAArEA,EAAU,EAAA,EAAC,UAAYA,EAAI,EAAA,EAAC,gBAAgB,IAAMA,EAAU,EAAA,EAAC,QAAQ,8MAvJ3EA,EAAI,EAAA,GAAAsB,GAAAtB,CAAA,uEAAJA,EAAI,EAAA,2LArQFiN,GACL,mEACKD,GACL,uEACKI,GACL,+DACKD,GAAqB,sCACrB9B,GACL,oEAsCG/G,GAAa,GAtBR,SAAAuJ,GAAgBhL,EAAaR,EAAA,OAC/ByL,EAAU,IAAA,IAAI,OAAO,SAAS,IAAI,EACxCA,EAAI,aAAa,IAAIjL,EAAKR,CAAK,EAC/B,QAAQ,aAAa,KAAM,GAAIyL,EAAI,SAAA,CAAA,WAG3BC,GAAgBlL,EAAA,QACR,IAAA,IAAI,OAAO,SAAS,IAAI,EAC7B,aAAa,IAAIA,CAAG,WAGvBmL,GAAkBzL,EAAA,CAClB,MAAA,CAAA,SAAU,aAAc,OAAQ,KAAK,EAAE,SAASA,GAAQ,EAAE,iCA3CxD,CAAA,aAAAwF,CAAA,EAAAjH,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,IAAAmN,CAAA,EAAAnN,EACA,CAAA,SAAA4F,CAAA,EAAA5F,EACA,CAAA,UAAAoN,CAAA,EAAApN,EACA,CAAA,SAAA8F,CAAA,EAAA9F,EAYPmB,EAAY8F,EAAa,OAC3BvB,GAAeA,EAAW,QAC1B,EAAA,OAEE3F,IAAS,KACZA,EAAO,SAAS,SAAW,KAAO,SAAS,KAAO,SAAS,UAEvDA,EAAK,SAAS,GAAG,IACrBA,GAAQ,KAGE,GAAA,CAAA,UAAAsH,EAAA,EAAA,EAAArH,EACPoB,EAA6D,SAiB3D,MAAAiM,EAAA,CACJ,CAAA,SAAU,SAAU/E,EAAM,EAC1B,CAAA,aAAc,aAAcC,EAAU,EACtC,CAAA,OAAQ,OAAQC,EAAI,EACpB,CAAA,MAAO,MAAOG,EAAG,OAIfmB,EAAoB,GAET,eAAAxC,GAAA,CAQP,OADH,MAHA,MAAiB,MACpBvH,EAAK,QAAQ,MAAO,EAAE,EAAIoN,EAAI,WAAa,OAAA,GAElB,OAGZ,eAAAG,GAAA,CAEP,OADH,MAAoBH,EAAI,WAIzB,IAAAI,EAKAC,EAEJlG,EAAA,EAAW,KAAMmG,GAAA,MAChBF,EAAOE,CAAA,IAGRH,EAAA,EAAc,KAAMI,GAAA,MACnBF,EAAUE,CAAA,UAGL7N,EAAWC,KAuCb,IAAAmK,EAAA,CAAA,EACA0D,EAAA,CAAA,EACAvD,EACAC,EACAC,EAAoB,GACpBH,EAAkC,IAAA,IAClCyD,EAAchI,EAAWA,EAAS,MAAM,GAAG,EAAE,MAAQ,IAAM,YAEtDiI,EAAmBC,EAAA,QACvBF,GAAeE,EAAS,WAAWF,CAAW,EAC1CE,EAAS,MAAMF,EAAY,MAAM,EAElCE,EAGF,MAAA/C,EAAA,CACL,QAAS,MACT,KAAA,CACC,SACA,cACA,SACA,aACAhL,EACA,oBAAA,GAIa,eAAAgO,GAAA,KAET,IAAAC,EAAA,GAAgBjO,CAAI,wBAElB,MAAAkO,GAAA,MADA,MAAiB,MAAMD,CAAU,GACT,YAC9B1D,EAAoB2D,GAClB,IAAK9C,IAAcA,GAAK,MAAM,iBAAiB,EAC/C,KAAM+C,IAAqBA,EAAO,CAAA,EAEpC1H,EAAA,EAAAyD,EAAQgE,GAAO,IAAK9C,KAAA,CACnB,KAAMA,GAAK,KACX,YAAaA,GAAK,aAAe,GACjC,WAAYA,GAAK,aAAa,YAAA,CAAA,EAC9B,SAAU,OAEX3E,EAAA,EAAA2D,EAAA,IAAqB,IAAIF,EAAM,IAAKkB,IAASA,GAAK,IAAI,CAAA,CAAA,EACtDwC,EAAUM,GAAO,IAAK9C,IAAcA,GAAK,MAAM,SAAe,CAAA,CAAA,EAAA,OAC1DwC,EAAQ,OAAS,GACpBnH,EAAA,EAAA4D,EAAA,CACC,WAAA,CACC,OAAA,CACC,IAAKL,EACL,QAAS4D,EAAQ,QACfQ,GAAqCC,MACrCD,GAAYC,EAAW,EAAI,sBACpBD,YAOZ3H,EAAA,EAAA6D,EAAA,CACC,WAAA,CACC,OAAA,CACC,QAAS,MACT,KAAA,CACC,aACAN,EACA,cACA,WACG,GAAA4D,EACD,IAAKU,IAAA,CACL,WACG,GAAAA,EAAM,uBAET,CAAA,EAAA,KAAA,SAMN7H,EAAA,EAAA4D,EAAA,CACC,WAAA,CACC,QACC,IAAKL,CAAA,CAAA,IAIRvD,EAAA,EAAA6D,EAAA,CACC,WAAA,CACC,OAAA,CACC,QAAS,MACT,KAAO,CAAA,aAAcN,EAAgB,cAAe,UAAU,MAI7DO,IACH9D,EAAA,EAAA4D,EAAa,WAAW,uBACvBW,EAAAX,CAAA,EACD5D,EAAA,EAAA6D,EAAe,WAAW,uBACzBU,EAAAV,CAAA,GAGK,OAAAiE,EAAA,CACR,QAAQ,MAAM,6BAA8BA,CAAK,EACjD9H,EAAA,EAAAyD,EAAA,CAAA,CAAA,GAIFtD,GAAA,IAAA,CACC,SAAS,KAAK,MAAM,SAAW,SAC3B,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,EAG7B,MAAA4H,EAAatB,GAAgB,MAAM,EACrC,OAAAC,GAAkBqB,CAAU,QAC/BnN,EAAmBmN,CAAA,EAIpB,MAAMxE,CAAc,EAClB,KAAMyE,IAAA,CACNhI,EAAA,GAAAsD,EAAoB0E,GAAS,EAAA,EACzB1E,GACHiE,IACKb,GAAkBqB,CAAU,QAChCnN,EAAmB,KAAA,GAGf8L,GAAkBqB,CAAU,QAChCnN,EAAmB,QAAA,CAIrB,CAAA,EAAA,MAAA,IAAA,MACA0I,EAAoB,EAAA,SAIrB,SAAS,KAAK,MAAM,SAAW,sDA6B1BtD,EAAA,GAAApF,EAAmBqN,CAAQ,EAC3B1B,GAAgB,OAAQ0B,CAAQ,GA0DXC,EAAAvD,GAAShB,EAAe,IAAIgB,EAAK,IAAI,2BAoCzD,MAAA9J,EAAA,IAAAxB,EAAS,QAAW,CAAA,qBAAsB,EAAI,CAAA,sUA3SnD2G,EAAA,GAAAmI,EAAuB,MAAM,KAAKxE,CAAc,CAAA,yBAChD3D,EAAA,GAAAoI,EACFD,EAAqB,IAAId,CAAkB,CAAA,mBAC5CrH,EAAA,GAAGuD,KAAoBhK,CAAI,oBAAA,yBAC3ByG,EAAA,GAAGwD,EACF2E,EAAqB,OAAS,GAC9BA,EAAqB,OAAS1E,EAAM,UAC9BlK,CAAI,yBAAyB6O,EAA8B,KAAK,GAAG,CAAA,MACnE7O,CAAI,iBAAA,yBAEJqK,GAAgBD,EAAe,KAAO,EAAA,CACtC,MAAA0E,EACLF,EAAqB,OAAS,GAC9BA,EAAqB,OAAS1E,EAAM,UAC9BlK,CAAI,4BAA4B6O,EAA8B,KAAK,GAAG,CAAA,MACtE7O,CAAI,yBACXqK,EAAa,WAAW,OAAO,IAAMyE,EAAAzE,CAAA,EACjCC,GACH7D,EAAA,EAAA6D,EAAe,WAAW,OAAO,KAAK,CAAC,EAAIwE,EAAAxE,CAAA"}