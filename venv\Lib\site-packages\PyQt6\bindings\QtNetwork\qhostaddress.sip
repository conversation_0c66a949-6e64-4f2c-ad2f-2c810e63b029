// qhostaddress.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHostAddress /TypeHintIn="Union[QHostAddress, QHostAddress.SpecialAddress]"/
{
%TypeHeaderCode
#include <qhostaddress.h>
%End

%ConvertToTypeCode
// SIP doesn't support automatic type convertors so we explicitly allow a
// QHostAddress::SpecialAddress to be used whenever a QHostAddress is expected.

bool is_special_address = true;
int special_address = sipConvertToEnum(sipPy, sipType_QHostAddress_SpecialAddress);

if (PyErr_Occurred())
{
    PyErr_Clear();
    is_special_address = false;
}

if (sipIsErr == NULL)
    return (is_special_address ||
            sipCanConvertToType(sipPy, sipType_QHostAddress, SIP_NO_CONVERTORS));

if (is_special_address)
{
    *sipCppPtr = new QHostAddress(static_cast<QHostAddress::SpecialAddress>(special_address));

    return sipGetState(sipTransferObj);
}


*sipCppPtr = reinterpret_cast<QHostAddress *>(sipConvertToType(sipPy, sipType_QHostAddress, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

public:
    enum SpecialAddress
    {
        Null,
        Broadcast,
        LocalHost,
        LocalHostIPv6,
        AnyIPv4,
        AnyIPv6,
        Any,
    };

    QHostAddress();
    QHostAddress(QHostAddress::SpecialAddress address /Constrained/);
    explicit QHostAddress(quint32 ip4Addr);
    explicit QHostAddress(const QString &address);
    explicit QHostAddress(const Q_IPV6ADDR &ip6Addr);
    QHostAddress(const QHostAddress &copy);
    ~QHostAddress();
    void setAddress(QHostAddress::SpecialAddress address /Constrained/);
    void setAddress(quint32 ip4Addr);
    bool setAddress(const QString &address);
    void setAddress(const Q_IPV6ADDR &ip6Addr);
    QAbstractSocket::NetworkLayerProtocol protocol() const;
    quint32 toIPv4Address(bool *ok = 0) const;
    Q_IPV6ADDR toIPv6Address() const;
    QString toString() const;
    QString scopeId() const;
    void setScopeId(const QString &id);
    bool operator==(const QHostAddress &address) const;
    bool operator==(QHostAddress::SpecialAddress address) const;
    bool operator!=(const QHostAddress &address) const;
    bool operator!=(QHostAddress::SpecialAddress address) const;
    bool isNull() const;
    void clear();
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    bool isInSubnet(const QHostAddress &subnet, int netmask) const;
    bool isInSubnet(const std::pair<QHostAddress, int> &subnet) const;
    bool isLoopback() const;
    static std::pair<QHostAddress, int> parseSubnet(const QString &subnet);
    void swap(QHostAddress &other /Constrained/);
    bool isMulticast() const;

    enum ConversionModeFlag /BaseType=Flag/
    {
        ConvertV4MappedToIPv4,
        ConvertV4CompatToIPv4,
        ConvertUnspecifiedAddress,
        ConvertLocalHost,
        TolerantConversion,
        StrictConversion,
    };

    typedef QFlags<QHostAddress::ConversionModeFlag> ConversionMode;
    bool isEqual(const QHostAddress &address, QHostAddress::ConversionMode mode = QHostAddress::TolerantConversion) const;
    bool isGlobal() const;
    bool isLinkLocal() const;
    bool isSiteLocal() const;
    bool isUniqueLocalUnicast() const;
    bool isBroadcast() const;
%If (Qt_6_6_0 -)
    bool isPrivateUse() const;
%End
};

bool operator==(QHostAddress::SpecialAddress address1, const QHostAddress &address2);
bool operator!=(QHostAddress::SpecialAddress lhs, const QHostAddress &rhs);
QDataStream &operator<<(QDataStream &, const QHostAddress &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QHostAddress &) /ReleaseGIL/;
// Q_IPV6ADDR is implemented as a Python 16-tuple of ints.
%MappedType Q_IPV6ADDR /TypeHint="Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]"/
{
%TypeHeaderCode
#include <qhostaddress.h>
%End

%ConvertFromTypeCode
    // Create the tuple.
    PyObject *t;

    if ((t = PyTuple_New(16)) == NULL)
        return NULL;

    // Set the tuple elements.
    for (int i = 0; i < 16; ++i)
    {
        PyObject *pobj;

        if ((pobj = PyLong_FromLong((*sipCpp)[i])) == NULL)
        {
            Py_DECREF(t);

            return NULL;
        }

        PyTuple_SetItem(t, i, pobj);
    }

    return t;
%End

%ConvertToTypeCode
    // Check the type if that is all that is required.
    if (sipIsErr == NULL)
        return (PySequence_Check(sipPy) && PySequence_Size(sipPy) == 16);

    Q_IPV6ADDR *qa = new Q_IPV6ADDR;
 
    for (Py_ssize_t i = 0; i < 16; ++i)
    {
        PyObject *itm = PySequence_GetItem(sipPy, i);

        if (!itm)
        {
            delete qa;
            *sipIsErr = 1;

            return 0;
        }

        (*qa)[i] = PyLong_AsLong(itm);

        Py_DECREF(itm);
    }
 
    *sipCppPtr = qa;
 
    return sipGetState(sipTransferObj);
%End
};
