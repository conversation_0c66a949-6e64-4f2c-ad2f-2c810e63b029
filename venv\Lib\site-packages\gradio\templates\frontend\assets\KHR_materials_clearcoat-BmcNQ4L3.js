import{ar as u,an as l,ao as m}from"./index-Dpxo-yl_.js";import{GLTFLoader as h}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const s="KHR_materials_clearcoat";class d{constructor(t){this.name=s,this.order=190,this._loader=t,this.enabled=this._loader.isExtensionUsed(s)}dispose(){this._loader=null}loadMaterialPropertiesAsync(t,r,e){return h.LoadExtensionAsync(t,r,this.name,(o,a)=>{const c=new Array;return c.push(this._loader.loadMaterialPropertiesAsync(t,r,e)),c.push(this._loadClearCoatPropertiesAsync(o,a,e)),Promise.all(c).then(()=>{})})}_loadClearCoatPropertiesAsync(t,r,e){if(!(e instanceof u))throw new Error(`${t}: Material type not supported`);const o=new Array;return e.clearCoat.isEnabled=!0,e.clearCoat.useRoughnessFromMainTexture=!1,e.clearCoat.remapF0OnInterfaceChange=!1,r.clearcoatFactor!=null?e.clearCoat.intensity=r.clearcoatFactor:e.clearCoat.intensity=0,r.clearcoatTexture&&o.push(this._loader.loadTextureInfoAsync(`${t}/clearcoatTexture`,r.clearcoatTexture,a=>{a.name=`${e.name} (ClearCoat)`,e.clearCoat.texture=a})),r.clearcoatRoughnessFactor!=null?e.clearCoat.roughness=r.clearcoatRoughnessFactor:e.clearCoat.roughness=0,r.clearcoatRoughnessTexture&&(r.clearcoatRoughnessTexture.nonColorData=!0,o.push(this._loader.loadTextureInfoAsync(`${t}/clearcoatRoughnessTexture`,r.clearcoatRoughnessTexture,a=>{a.name=`${e.name} (ClearCoat Roughness)`,e.clearCoat.textureRoughness=a}))),r.clearcoatNormalTexture&&(r.clearcoatNormalTexture.nonColorData=!0,o.push(this._loader.loadTextureInfoAsync(`${t}/clearcoatNormalTexture`,r.clearcoatNormalTexture,a=>{a.name=`${e.name} (ClearCoat Normal)`,e.clearCoat.bumpTexture=a})),e.invertNormalMapX=!e.getScene().useRightHandedSystem,e.invertNormalMapY=e.getScene().useRightHandedSystem,r.clearcoatNormalTexture.scale!=null&&(e.clearCoat.bumpTexture.level=r.clearcoatNormalTexture.scale)),Promise.all(o).then(()=>{})}}l(s);m(s,!0,n=>new d(n));export{d as KHR_materials_clearcoat};
//# sourceMappingURL=KHR_materials_clearcoat-BmcNQ4L3.js.map
