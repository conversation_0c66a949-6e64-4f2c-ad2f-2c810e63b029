{"version": 3, "file": "flowGraphSwitchBlock.rlj1f3Pb.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSwitchBlock.js"], "sourcesContent": ["import { FlowGraphExecutionBlock } from \"../../../flowGraphExecutionBlock.js\";\nimport { RichTypeAny } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { getNumericValue, isNumeric } from \"../../../utils.js\";\n/**\n * A block that executes a branch based on a selection.\n */\nexport class FlowGraphSwitchBlock extends FlowGraphExecutionBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        /**\n         * The default case to execute if no other case is found.\n         */\n        this.default = this._registerSignalOutput(\"default\");\n        this._caseToOutputFlow = new Map();\n        this.case = this.registerDataInput(\"case\", RichTypeAny);\n        // iterate the set not using for of\n        (this.config.cases || []).forEach((caseValue) => {\n            this._caseToOutputFlow.set(caseValue, this._registerSignalOutput(`out_${caseValue}`));\n        });\n    }\n    _execute(context, _callingSignal) {\n        const selectionValue = this.case.getValue(context);\n        let outputFlow;\n        if (isNumeric(selectionValue)) {\n            outputFlow = this._getOutputFlowForCase(getNumericValue(selectionValue));\n        }\n        else {\n            outputFlow = this._getOutputFlowForCase(selectionValue);\n        }\n        if (outputFlow) {\n            outputFlow._activateSignal(context);\n        }\n        else {\n            this.default._activateSignal(context);\n        }\n    }\n    /**\n     * Adds a new case to the switch block.\n     * @param newCase the new case to add.\n     */\n    addCase(newCase) {\n        if (this.config.cases.includes(newCase)) {\n            return;\n        }\n        this.config.cases.push(newCase);\n        this._caseToOutputFlow.set(newCase, this._registerSignalOutput(`out_${newCase}`));\n    }\n    /**\n     * Removes a case from the switch block.\n     * @param caseToRemove the case to remove.\n     */\n    removeCase(caseToRemove) {\n        if (!this.config.cases.includes(caseToRemove)) {\n            return;\n        }\n        const index = this.config.cases.indexOf(caseToRemove);\n        this.config.cases.splice(index, 1);\n        this._caseToOutputFlow.delete(caseToRemove);\n    }\n    /**\n     * @internal\n     */\n    _getOutputFlowForCase(caseValue) {\n        return this._caseToOutputFlow.get(caseValue);\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphSwitchBlock\" /* FlowGraphBlockNames.Switch */;\n    }\n    /**\n     * Serialize the block to a JSON representation.\n     * @param serializationObject the object to serialize to.\n     */\n    serialize(serializationObject) {\n        super.serialize(serializationObject);\n        serializationObject.cases = this.config.cases;\n    }\n}\nRegisterClass(\"FlowGraphSwitchBlock\" /* FlowGraphBlockNames.Switch */, FlowGraphSwitchBlock);\n//# sourceMappingURL=flowGraphSwitchBlock.js.map"], "names": ["FlowGraphSwitchBlock", "FlowGraphExecutionBlock", "config", "RichTypeAny", "caseValue", "context", "_callingSignal", "selectionValue", "outputFlow", "isNumeric", "getNumericValue", "newCase", "caseToRemove", "index", "serializationObject", "RegisterClass"], "mappings": "8JAOO,MAAMA,UAA6BC,CAAwB,CAC9D,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EAId,KAAK,QAAU,KAAK,sBAAsB,SAAS,EACnD,KAAK,kBAAoB,IAAI,IAC7B,KAAK,KAAO,KAAK,kBAAkB,OAAQC,CAAW,GAErD,KAAK,OAAO,OAAS,CAAA,GAAI,QAASC,GAAc,CAC7C,KAAK,kBAAkB,IAAIA,EAAW,KAAK,sBAAsB,OAAOA,CAAS,EAAE,CAAC,CAChG,CAAS,CACJ,CACD,SAASC,EAASC,EAAgB,CAC9B,MAAMC,EAAiB,KAAK,KAAK,SAASF,CAAO,EACjD,IAAIG,EACAC,EAAUF,CAAc,EACxBC,EAAa,KAAK,sBAAsBE,EAAgBH,CAAc,CAAC,EAGvEC,EAAa,KAAK,sBAAsBD,CAAc,EAEtDC,EACAA,EAAW,gBAAgBH,CAAO,EAGlC,KAAK,QAAQ,gBAAgBA,CAAO,CAE3C,CAKD,QAAQM,EAAS,CACT,KAAK,OAAO,MAAM,SAASA,CAAO,IAGtC,KAAK,OAAO,MAAM,KAAKA,CAAO,EAC9B,KAAK,kBAAkB,IAAIA,EAAS,KAAK,sBAAsB,OAAOA,CAAO,EAAE,CAAC,EACnF,CAKD,WAAWC,EAAc,CACrB,GAAI,CAAC,KAAK,OAAO,MAAM,SAASA,CAAY,EACxC,OAEJ,MAAMC,EAAQ,KAAK,OAAO,MAAM,QAAQD,CAAY,EACpD,KAAK,OAAO,MAAM,OAAOC,EAAO,CAAC,EACjC,KAAK,kBAAkB,OAAOD,CAAY,CAC7C,CAID,sBAAsBR,EAAW,CAC7B,OAAO,KAAK,kBAAkB,IAAIA,CAAS,CAC9C,CAID,cAAe,CACX,MAAO,sBACV,CAKD,UAAUU,EAAqB,CAC3B,MAAM,UAAUA,CAAmB,EACnCA,EAAoB,MAAQ,KAAK,OAAO,KAC3C,CACL,CACAC,EAAc,uBAAyDf,CAAoB", "x_google_ignoreList": [0]}