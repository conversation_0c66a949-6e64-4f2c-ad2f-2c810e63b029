import{ar as u,an as l,ao as h}from"./index.BoI39RQH.js";import{GLTFLoader as d}from"./glTFLoader.BetPWe9U.js";const o="KHR_materials_clearcoat";class m{constructor(s){this.name=o,this.order=190,this._loader=s,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}loadMaterialPropertiesAsync(s,a,e){return d.LoadExtensionAsync(s,a,this.name,(t,r)=>{const c=new Array;return c.push(this._loader.loadMaterialPropertiesAsync(s,a,e)),c.push(this._loadClearCoatPropertiesAsync(t,r,e)),Promise.all(c).then(()=>{})})}_loadClearCoatPropertiesAsync(s,a,e){if(!(e instanceof u))throw new Error(`${s}: Material type not supported`);const t=new Array;return e.clearCoat.isEnabled=!0,e.clearCoat.useRoughnessFromMainTexture=!1,e.clearCoat.remapF0OnInterfaceChange=!1,a.clearcoatFactor!=null?e.clearCoat.intensity=a.clearcoatFactor:e.clearCoat.intensity=0,a.clearcoatTexture&&t.push(this._loader.loadTextureInfoAsync(`${s}/clearcoatTexture`,a.clearcoatTexture,r=>{r.name=`${e.name} (ClearCoat)`,e.clearCoat.texture=r})),a.clearcoatRoughnessFactor!=null?e.clearCoat.roughness=a.clearcoatRoughnessFactor:e.clearCoat.roughness=0,a.clearcoatRoughnessTexture&&(a.clearcoatRoughnessTexture.nonColorData=!0,t.push(this._loader.loadTextureInfoAsync(`${s}/clearcoatRoughnessTexture`,a.clearcoatRoughnessTexture,r=>{r.name=`${e.name} (ClearCoat Roughness)`,e.clearCoat.textureRoughness=r}))),a.clearcoatNormalTexture&&(a.clearcoatNormalTexture.nonColorData=!0,t.push(this._loader.loadTextureInfoAsync(`${s}/clearcoatNormalTexture`,a.clearcoatNormalTexture,r=>{r.name=`${e.name} (ClearCoat Normal)`,e.clearCoat.bumpTexture=r})),e.invertNormalMapX=!e.getScene().useRightHandedSystem,e.invertNormalMapY=e.getScene().useRightHandedSystem,a.clearcoatNormalTexture.scale!=null&&(e.clearCoat.bumpTexture.level=a.clearcoatNormalTexture.scale)),Promise.all(t).then(()=>{})}}l(o);h(o,!0,n=>new m(n));export{m as KHR_materials_clearcoat};
//# sourceMappingURL=KHR_materials_clearcoat.smUn2wvX.js.map
