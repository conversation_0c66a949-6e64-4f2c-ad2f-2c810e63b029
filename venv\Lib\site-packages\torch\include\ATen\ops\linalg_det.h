#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/linalg_det_ops.h>

namespace at {


// aten::linalg_det(Tensor A) -> Tensor
inline at::Tensor linalg_det(const at::Tensor & A) {
    return at::_ops::linalg_det::call(A);
}

// aten::linalg_det.out(Tensor A, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & linalg_det_out(at::Tensor & out, const at::Tensor & A) {
    return at::_ops::linalg_det_out::call(A, out);
}
// aten::linalg_det.out(Tensor A, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & linalg_det_outf(const at::Tensor & A, at::Tensor & out) {
    return at::_ops::linalg_det_out::call(A, out);
}

}
