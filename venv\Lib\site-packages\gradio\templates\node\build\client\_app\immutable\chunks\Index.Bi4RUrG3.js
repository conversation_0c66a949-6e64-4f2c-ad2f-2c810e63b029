import{SvelteComponent as re,init as oe,safe_not_equal as fe,element as B,space as j,claim_element as N,children as I,claim_space as O,detach as h,attr as _,insert_hydration as T,append_hydration as g,noop as $,createEventDispatcher as _e,text as K,claim_text as P,toggle_class as Q,set_style as U,set_data as y,ensure_array_like as x,empty as W,destroy_each as de,listen as me,create_component as V,claim_component as C,mount_component as J,transition_in as z,transition_out as D,destroy_component as q,assign as be,get_spread_update as he,get_spread_object as ge,group_outros as ee,check_outros as le}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as ve,S as ke}from"./2.B2AoQPnG.js";import{L as ce}from"./LineChart.C8bDl53w.js";import{B as we}from"./BlockLabel.BTSz9r5s.js";import{E as Ee}from"./Empty.DwQ6nkN6.js";function te(a,l,n){const e=a.slice();return e[6]=l[n],e[8]=n,e}function ae(a){let l,n=a[0].label+"",e;return{c(){l=B("h2"),e=K(n),this.h()},l(t){l=N(t,"H2",{class:!0,"data-testid":!0});var i=I(l);e=P(i,n),i.forEach(h),this.h()},h(){_(l,"class","output-class svelte-1mutzus"),_(l,"data-testid","label-output-value"),Q(l,"no-confidence",!("confidences"in a[0])),U(l,"background-color",a[1]||"transparent")},m(t,i){T(t,l,i),g(l,e)},p(t,i){i&1&&n!==(n=t[0].label+"")&&y(e,n),i&1&&Q(l,"no-confidence",!("confidences"in t[0])),i&2&&U(l,"background-color",t[1]||"transparent")},d(t){t&&h(l)}}}function ne(a){let l,n=x(a[0].confidences),e=[];for(let t=0;t<n.length;t+=1)e[t]=ie(te(a,n,t));return{c(){for(let t=0;t<e.length;t+=1)e[t].c();l=W()},l(t){for(let i=0;i<e.length;i+=1)e[i].l(t);l=W()},m(t,i){for(let r=0;r<e.length;r+=1)e[r]&&e[r].m(t,i);T(t,l,i)},p(t,i){if(i&21){n=x(t[0].confidences);let r;for(r=0;r<n.length;r+=1){const d=te(t,n,r);e[r]?e[r].p(d,i):(e[r]=ie(d),e[r].c(),e[r].m(l.parentNode,l))}for(;r<e.length;r+=1)e[r].d(1);e.length=n.length}},d(t){t&&h(l),de(e,t)}}}function ie(a){let l,n,e,t,i,r,d,v,c,o,E=a[6].label+"",m,L,s,u,k,w=Math.round(a[6].confidence*100)+"",M,H,R,f,X,p;function ue(){return a[5](a[8],a[6])}return{c(){l=B("button"),n=B("div"),e=B("meter"),v=j(),c=B("dl"),o=B("dt"),m=K(E),L=j(),u=B("div"),k=B("dd"),M=K(w),H=K("%"),R=j(),this.h()},l(S){l=N(S,"BUTTON",{class:!0,"data-testid":!0});var b=I(l);n=N(b,"DIV",{class:!0});var A=I(n);e=N(A,"METER",{"aria-labelledby":!0,"aria-label":!0,"aria-valuenow":!0,"aria-valuemin":!0,"aria-valuemax":!0,class:!0,min:!0,max:!0,style:!0}),I(e).forEach(h),v=O(A),c=N(A,"DL",{class:!0});var F=I(c);o=N(F,"DT",{id:!0,class:!0});var Y=I(o);m=P(Y,E),L=O(Y),Y.forEach(h),u=N(F,"DIV",{class:!0}),I(u).forEach(h),k=N(F,"DD",{class:!0});var Z=I(k);M=P(Z,w),H=P(Z,"%"),Z.forEach(h),F.forEach(h),A.forEach(h),R=O(b),b.forEach(h),this.h()},h(){_(e,"aria-labelledby",t=G(`meter-text-${a[6].label}`)),_(e,"aria-label",i=a[6].label),_(e,"aria-valuenow",r=Math.round(a[6].confidence*100)),_(e,"aria-valuemin","0"),_(e,"aria-valuemax","100"),_(e,"class","bar svelte-1mutzus"),_(e,"min","0"),_(e,"max","1"),e.value=d=a[6].confidence,U(e,"width",a[6].confidence*100+"%"),U(e,"background","var(--stat-background-fill)"),_(o,"id",s=G(`meter-text-${a[6].label}`)),_(o,"class","text svelte-1mutzus"),_(u,"class","line svelte-1mutzus"),_(k,"class","confidence svelte-1mutzus"),_(c,"class","label svelte-1mutzus"),_(n,"class","inner-wrap svelte-1mutzus"),_(l,"class","confidence-set group svelte-1mutzus"),_(l,"data-testid",f=`${a[6].label}-confidence-set`),Q(l,"selectable",a[2])},m(S,b){T(S,l,b),g(l,n),g(n,e),g(n,v),g(n,c),g(c,o),g(o,m),g(o,L),g(c,u),g(c,k),g(k,M),g(k,H),g(l,R),X||(p=me(l,"click",ue),X=!0)},p(S,b){a=S,b&1&&t!==(t=G(`meter-text-${a[6].label}`))&&_(e,"aria-labelledby",t),b&1&&i!==(i=a[6].label)&&_(e,"aria-label",i),b&1&&r!==(r=Math.round(a[6].confidence*100))&&_(e,"aria-valuenow",r),b&1&&d!==(d=a[6].confidence)&&(e.value=d),b&1&&U(e,"width",a[6].confidence*100+"%"),b&1&&E!==(E=a[6].label+"")&&y(m,E),b&1&&s!==(s=G(`meter-text-${a[6].label}`))&&_(o,"id",s),b&1&&w!==(w=Math.round(a[6].confidence*100)+"")&&y(M,w),b&1&&f!==(f=`${a[6].label}-confidence-set`)&&_(l,"data-testid",f),b&4&&Q(l,"selectable",a[2])},d(S){S&&h(l),X=!1,p()}}}function ze(a){let l,n,e=(a[3]||!a[0].confidences)&&ae(a),t=typeof a[0]=="object"&&a[0].confidences&&ne(a);return{c(){l=B("div"),e&&e.c(),n=j(),t&&t.c(),this.h()},l(i){l=N(i,"DIV",{class:!0});var r=I(l);e&&e.l(r),n=O(r),t&&t.l(r),r.forEach(h),this.h()},h(){_(l,"class","container svelte-1mutzus")},m(i,r){T(i,l,r),e&&e.m(l,null),g(l,n),t&&t.m(l,null)},p(i,[r]){i[3]||!i[0].confidences?e?e.p(i,r):(e=ae(i),e.c(),e.m(l,n)):e&&(e.d(1),e=null),typeof i[0]=="object"&&i[0].confidences?t?t.p(i,r):(t=ne(i),t.c(),t.m(l,null)):t&&(t.d(1),t=null)},i:$,o:$,d(i){i&&h(l),e&&e.d(),t&&t.d()}}}function G(a){return a.replace(/\s/g,"-")}function De(a,l,n){let{value:e}=l;const t=_e();let{color:i=void 0}=l,{selectable:r=!1}=l,{show_heading:d=!0}=l;const v=(c,o)=>{t("select",{index:c,value:o.label})};return a.$$set=c=>{"value"in c&&n(0,e=c.value),"color"in c&&n(1,i=c.color),"selectable"in c&&n(2,r=c.selectable),"show_heading"in c&&n(3,d=c.show_heading)},[e,i,r,d,t,v]}class Le extends re{constructor(l){super(),oe(this,l,De,ze,fe,{value:0,color:1,selectable:2,show_heading:3})}}const Be=Le;function se(a){let l,n;return l=new we({props:{Icon:ce,label:a[6],disable:a[7]===!1,float:a[13]===!0}}),{c(){V(l.$$.fragment)},l(e){C(l.$$.fragment,e)},m(e,t){J(l,e,t),n=!0},p(e,t){const i={};t&64&&(i.label=e[6]),t&128&&(i.disable=e[7]===!1),t&8192&&(i.float=e[13]===!0),l.$set(i)},i(e){n||(z(l.$$.fragment,e),n=!0)},o(e){D(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Ne(a){let l,n;return l=new Ee({props:{unpadded_box:!0,$$slots:{default:[Me]},$$scope:{ctx:a}}}),{c(){V(l.$$.fragment)},l(e){C(l.$$.fragment,e)},m(e,t){J(l,e,t),n=!0},p(e,t){const i={};t&262144&&(i.$$scope={dirty:t,ctx:e}),l.$set(i)},i(e){n||(z(l.$$.fragment,e),n=!0)},o(e){D(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Ie(a){let l,n;return l=new Be({props:{selectable:a[12],value:a[5],color:a[4],show_heading:a[13]}}),l.$on("select",a[17]),{c(){V(l.$$.fragment)},l(e){C(l.$$.fragment,e)},m(e,t){J(l,e,t),n=!0},p(e,t){const i={};t&4096&&(i.selectable=e[12]),t&32&&(i.value=e[5]),t&16&&(i.color=e[4]),t&8192&&(i.show_heading=e[13]),l.$set(i)},i(e){n||(z(l.$$.fragment,e),n=!0)},o(e){D(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Me(a){let l,n;return l=new ce({}),{c(){V(l.$$.fragment)},l(e){C(l.$$.fragment,e)},m(e,t){J(l,e,t),n=!0},i(e){n||(z(l.$$.fragment,e),n=!0)},o(e){D(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function Se(a){let l,n,e,t,i,r,d;const v=[{autoscroll:a[0].autoscroll},{i18n:a[0].i18n},a[10]];let c={};for(let s=0;s<v.length;s+=1)c=be(c,v[s]);l=new ke({props:c}),l.$on("clear_status",a[16]);let o=a[11]&&se(a);const E=[Ie,Ne],m=[];function L(s,u){return s[14]!==void 0&&s[14]!==null?0:1}return t=L(a),i=m[t]=E[t](a),{c(){V(l.$$.fragment),n=j(),o&&o.c(),e=j(),i.c(),r=W()},l(s){C(l.$$.fragment,s),n=O(s),o&&o.l(s),e=O(s),i.l(s),r=W()},m(s,u){J(l,s,u),T(s,n,u),o&&o.m(s,u),T(s,e,u),m[t].m(s,u),T(s,r,u),d=!0},p(s,u){const k=u&1025?he(v,[u&1&&{autoscroll:s[0].autoscroll},u&1&&{i18n:s[0].i18n},u&1024&&ge(s[10])]):{};l.$set(k),s[11]?o?(o.p(s,u),u&2048&&z(o,1)):(o=se(s),o.c(),z(o,1),o.m(e.parentNode,e)):o&&(ee(),D(o,1,1,()=>{o=null}),le());let w=t;t=L(s),t===w?m[t].p(s,u):(ee(),D(m[w],1,1,()=>{m[w]=null}),le(),i=m[t],i?i.p(s,u):(i=m[t]=E[t](s),i.c()),z(i,1),i.m(r.parentNode,r))},i(s){d||(z(l.$$.fragment,s),z(o),z(i),d=!0)},o(s){D(l.$$.fragment,s),D(o),D(i),d=!1},d(s){s&&(h(n),h(e),h(r)),q(l,s),o&&o.d(s),m[t].d(s)}}}function Te(a){let l,n;return l=new ve({props:{test_id:"label",visible:a[3],elem_id:a[1],elem_classes:a[2],container:a[7],scale:a[8],min_width:a[9],padding:!1,$$slots:{default:[Se]},$$scope:{ctx:a}}}),{c(){V(l.$$.fragment)},l(e){C(l.$$.fragment,e)},m(e,t){J(l,e,t),n=!0},p(e,[t]){const i={};t&8&&(i.visible=e[3]),t&2&&(i.elem_id=e[1]),t&4&&(i.elem_classes=e[2]),t&128&&(i.container=e[7]),t&256&&(i.scale=e[8]),t&512&&(i.min_width=e[9]),t&294129&&(i.$$scope={dirty:t,ctx:e}),l.$set(i)},i(e){n||(z(l.$$.fragment,e),n=!0)},o(e){D(l.$$.fragment,e),n=!1},d(e){q(l,e)}}}function je(a,l,n){let e,{gradio:t}=l,{elem_id:i=""}=l,{elem_classes:r=[]}=l,{visible:d=!0}=l,{color:v=void 0}=l,{value:c={}}=l,o=null,{label:E=t.i18n("label.label")}=l,{container:m=!0}=l,{scale:L=null}=l,{min_width:s=void 0}=l,{loading_status:u}=l,{show_label:k=!0}=l,{_selectable:w=!1}=l,{show_heading:M=!0}=l;const H=()=>t.dispatch("clear_status",u),R=({detail:f})=>t.dispatch("select",f);return a.$$set=f=>{"gradio"in f&&n(0,t=f.gradio),"elem_id"in f&&n(1,i=f.elem_id),"elem_classes"in f&&n(2,r=f.elem_classes),"visible"in f&&n(3,d=f.visible),"color"in f&&n(4,v=f.color),"value"in f&&n(5,c=f.value),"label"in f&&n(6,E=f.label),"container"in f&&n(7,m=f.container),"scale"in f&&n(8,L=f.scale),"min_width"in f&&n(9,s=f.min_width),"loading_status"in f&&n(10,u=f.loading_status),"show_label"in f&&n(11,k=f.show_label),"_selectable"in f&&n(12,w=f._selectable),"show_heading"in f&&n(13,M=f.show_heading)},a.$$.update=()=>{a.$$.dirty&32801&&JSON.stringify(c)!==JSON.stringify(o)&&(n(15,o=c),t.dispatch("change")),a.$$.dirty&32&&n(14,e=c.label)},[t,i,r,d,v,c,E,m,L,s,u,k,w,M,e,o,H,R]}class Re extends re{constructor(l){super(),oe(this,l,je,Te,fe,{gradio:0,elem_id:1,elem_classes:2,visible:3,color:4,value:5,label:6,container:7,scale:8,min_width:9,loading_status:10,show_label:11,_selectable:12,show_heading:13})}}export{Be as BaseLabel,Re as default};
//# sourceMappingURL=Index.Bi4RUrG3.js.map
