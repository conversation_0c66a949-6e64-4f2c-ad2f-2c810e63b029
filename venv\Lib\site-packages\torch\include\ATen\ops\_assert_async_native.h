#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API void _assert_async_cpu(const at::Tensor & self);
TORCH_API void _assert_async_cuda(const at::Tensor & self);
TORCH_API void _assert_async_msg_cpu(const at::Tensor & self, c10::string_view assert_msg);
TORCH_API void _assert_async_msg_cuda(const at::Tensor & self, c10::string_view assert_msg);
} // namespace native
} // namespace at
