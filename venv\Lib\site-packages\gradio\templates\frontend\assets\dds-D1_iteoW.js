import{au as d,av as w,aj as O,h as H,aw as it}from"./index-Dpxo-yl_.js";import"./abstractEngine.cubeTexture-7uN6d2SV.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";const ut=542327876,Q=131072,V=512,Y=4,Z=64,$=131072;function E(h){return h.charCodeAt(0)+(h.charCodeAt(1)<<8)+(h.charCodeAt(2)<<16)+(h.charCodeAt(3)<<24)}function yt(h){return String.fromCharCode(h&255,h>>8&255,h>>16&255,h>>24&255)}const v=E("DXT1"),S=E("DXT3"),tt=E("DXT5"),N=E("DX10"),rt=113,et=116,at=2,ft=10,pt=88,W=31,Ft=0,Ct=1,st=2,ot=3,T=4,nt=7,j=20,ct=21,Gt=22,mt=23,bt=24,Bt=25,Rt=26,ht=28,_t=32;class A{static GetDDSInfo(a){const o=new Int32Array(a.buffer,a.byteOffset,W),s=new Int32Array(a.buffer,a.byteOffset,W+4);let n=1;o[st]&Q&&(n=Math.max(1,o[nt]));const u=o[ct],p=u===N?s[_t]:0;let r=0;switch(u){case rt:r=2;break;case et:r=1;break;case N:if(p===ft){r=2;break}if(p===at){r=1;break}}return{width:o[T],height:o[ot],mipmapCount:n,isFourCC:(o[j]&Y)===Y,isRGB:(o[j]&Z)===Z,isLuminance:(o[j]&$)===$,isCube:(o[ht]&V)===V,isCompressed:u===v||u===S||u===tt,dxgiFormat:p,textureType:r}}static _GetHalfFloatAsFloatRGBAArrayBuffer(a,o,s,n,u,p){const r=new Float32Array(n),c=new Uint16Array(u,s);let t=0;for(let e=0;e<o;e++)for(let l=0;l<a;l++){const f=(l+e*a)*4;r[t]=d(c[f]),r[t+1]=d(c[f+1]),r[t+2]=d(c[f+2]),A.StoreLODInAlphaChannel?r[t+3]=p:r[t+3]=d(c[f+3]),t+=4}return r}static _GetHalfFloatRGBAArrayBuffer(a,o,s,n,u,p){if(A.StoreLODInAlphaChannel){const r=new Uint16Array(n),c=new Uint16Array(u,s);let t=0;for(let e=0;e<o;e++)for(let l=0;l<a;l++){const f=(l+e*a)*4;r[t]=c[f],r[t+1]=c[f+1],r[t+2]=c[f+2],r[t+3]=w(p),t+=4}return r}return new Uint16Array(u,s,n)}static _GetFloatRGBAArrayBuffer(a,o,s,n,u,p){if(A.StoreLODInAlphaChannel){const r=new Float32Array(n),c=new Float32Array(u,s);let t=0;for(let e=0;e<o;e++)for(let l=0;l<a;l++){const f=(l+e*a)*4;r[t]=c[f],r[t+1]=c[f+1],r[t+2]=c[f+2],r[t+3]=p,t+=4}return r}return new Float32Array(u,s,n)}static _GetFloatAsHalfFloatRGBAArrayBuffer(a,o,s,n,u,p){const r=new Uint16Array(n),c=new Float32Array(u,s);let t=0;for(let e=0;e<o;e++)for(let l=0;l<a;l++)r[t]=w(c[t]),r[t+1]=w(c[t+1]),r[t+2]=w(c[t+2]),A.StoreLODInAlphaChannel?r[t+3]=w(p):r[t+3]=w(c[t+3]),t+=4;return r}static _GetFloatAsUIntRGBAArrayBuffer(a,o,s,n,u,p){const r=new Uint8Array(n),c=new Float32Array(u,s);let t=0;for(let e=0;e<o;e++)for(let l=0;l<a;l++){const f=(l+e*a)*4;r[t]=O(c[f])*255,r[t+1]=O(c[f+1])*255,r[t+2]=O(c[f+2])*255,A.StoreLODInAlphaChannel?r[t+3]=p:r[t+3]=O(c[f+3])*255,t+=4}return r}static _GetHalfFloatAsUIntRGBAArrayBuffer(a,o,s,n,u,p){const r=new Uint8Array(n),c=new Uint16Array(u,s);let t=0;for(let e=0;e<o;e++)for(let l=0;l<a;l++){const f=(l+e*a)*4;r[t]=O(d(c[f]))*255,r[t+1]=O(d(c[f+1]))*255,r[t+2]=O(d(c[f+2]))*255,A.StoreLODInAlphaChannel?r[t+3]=p:r[t+3]=O(d(c[f+3]))*255,t+=4}return r}static _GetRGBAArrayBuffer(a,o,s,n,u,p,r,c,t){const e=new Uint8Array(n),l=new Uint8Array(u,s);let f=0;for(let B=0;B<o;B++)for(let i=0;i<a;i++){const y=(i+B*a)*4;e[f]=l[y+p],e[f+1]=l[y+r],e[f+2]=l[y+c],e[f+3]=l[y+t],f+=4}return e}static _ExtractLongWordOrder(a){return a===0||a===255||a===-16777216?0:1+A._ExtractLongWordOrder(a>>8)}static _GetRGBArrayBuffer(a,o,s,n,u,p,r,c){const t=new Uint8Array(n),e=new Uint8Array(u,s);let l=0;for(let f=0;f<o;f++)for(let B=0;B<a;B++){const i=(B+f*a)*3;t[l]=e[i+p],t[l+1]=e[i+r],t[l+2]=e[i+c],l+=3}return t}static _GetLuminanceArrayBuffer(a,o,s,n,u){const p=new Uint8Array(n),r=new Uint8Array(u,s);let c=0;for(let t=0;t<o;t++)for(let e=0;e<a;e++){const l=e+t*a;p[c]=r[l],c++}return p}static UploadDDSLevels(a,o,s,n,u,p,r=-1,c,t=!0){let e=null;n.sphericalPolynomial&&(e=[]);const l=!!a.getCaps().s3tc;o.generateMipMaps=u;const f=new Int32Array(s.buffer,s.byteOffset,W);let B,i,y,F=0,m,_,X,I,L=0,M=1;if(f[Ft]!==ut){H.Error("Invalid magic number in DDS header");return}if(!n.isFourCC&&!n.isRGB&&!n.isLuminance){H.Error("Unsupported format, must contain a FourCC, RGB or LUMINANCE code");return}if(n.isCompressed&&!l){H.Error("Compressed textures are not supported on this platform.");return}let C=f[Gt];m=f[Ct]+4;let g=!1;if(n.isFourCC)switch(B=f[ct],B){case v:M=8,L=33777;break;case S:M=16,L=33778;break;case tt:M=16,L=33779;break;case rt:g=!0,C=64;break;case et:g=!0,C=128;break;case N:{m+=5*4;let b=!1;switch(n.dxgiFormat){case ft:g=!0,C=64,b=!0;break;case at:g=!0,C=128,b=!0;break;case pt:n.isRGB=!0,n.isFourCC=!1,C=32,b=!0;break}if(b)break}default:H.Error(["Unsupported FourCC code:",yt(B)]);return}const q=A._ExtractLongWordOrder(f[mt]),J=A._ExtractLongWordOrder(f[bt]),K=A._ExtractLongWordOrder(f[Bt]),lt=A._ExtractLongWordOrder(f[Rt]);g&&(L=a._getRGBABufferInternalSizedFormat(n.textureType)),X=1,f[st]&Q&&u!==!1&&(X=Math.max(1,f[nt]));const At=c||0,k=a.getCaps();for(let b=At;b<p;b++){for(i=f[T],y=f[ot],I=0;I<X;++I){if(r===-1||r===I){const G=r===-1?I:0;if(!n.isCompressed&&n.isFourCC){o.format=5,F=i*y*4;let R=null;if(a._badOS||a._badDesktopOS||!k.textureHalfFloat&&!k.textureFloat)C===128?(R=A._GetFloatAsUIntRGBAArrayBuffer(i,y,s.byteOffset+m,F,s.buffer,G),e&&G==0&&e.push(A._GetFloatRGBAArrayBuffer(i,y,s.byteOffset+m,F,s.buffer,G))):C===64&&(R=A._GetHalfFloatAsUIntRGBAArrayBuffer(i,y,s.byteOffset+m,F,s.buffer,G),e&&G==0&&e.push(A._GetHalfFloatAsFloatRGBAArrayBuffer(i,y,s.byteOffset+m,F,s.buffer,G))),o.type=0;else{const P=k.textureFloat&&(t&&k.textureFloatLinearFiltering||!t),D=k.textureHalfFloat&&(t&&k.textureHalfFloatLinearFiltering||!t),z=(C===128||C===64&&!D)&&P?1:(C===64||C===128&&!P)&&D?2:0;let U,x=null;switch(C){case 128:{switch(z){case 1:U=A._GetFloatRGBAArrayBuffer,x=null;break;case 2:U=A._GetFloatAsHalfFloatRGBAArrayBuffer,x=A._GetFloatRGBAArrayBuffer;break;case 0:U=A._GetFloatAsUIntRGBAArrayBuffer,x=A._GetFloatRGBAArrayBuffer;break}break}default:{switch(z){case 1:U=A._GetHalfFloatAsFloatRGBAArrayBuffer,x=null;break;case 2:U=A._GetHalfFloatRGBAArrayBuffer,x=A._GetHalfFloatAsFloatRGBAArrayBuffer;break;case 0:U=A._GetHalfFloatAsUIntRGBAArrayBuffer,x=A._GetHalfFloatAsFloatRGBAArrayBuffer;break}break}}o.type=z,R=U(i,y,s.byteOffset+m,F,s.buffer,G),e&&G==0&&e.push(x?x(i,y,s.byteOffset+m,F,s.buffer,G):R)}R&&a._uploadDataToTextureDirectly(o,R,b,G)}else if(n.isRGB)o.type=0,C===24?(o.format=4,F=i*y*3,_=A._GetRGBArrayBuffer(i,y,s.byteOffset+m,F,s.buffer,q,J,K),a._uploadDataToTextureDirectly(o,_,b,G)):(o.format=5,F=i*y*4,_=A._GetRGBAArrayBuffer(i,y,s.byteOffset+m,F,s.buffer,q,J,K,lt),a._uploadDataToTextureDirectly(o,_,b,G));else if(n.isLuminance){const R=a._getUnpackAlignement(),P=i;F=Math.floor((i+R-1)/R)*R*(y-1)+P,_=A._GetLuminanceArrayBuffer(i,y,s.byteOffset+m,F,s.buffer),o.format=1,o.type=0,a._uploadDataToTextureDirectly(o,_,b,G)}else F=Math.max(4,i)/4*Math.max(4,y)/4*M,_=new Uint8Array(s.buffer,s.byteOffset+m,F),o.type=0,a._uploadCompressedDataToTextureDirectly(o,L,i,y,_,b,G)}m+=C?i*y*(C/8):F,i*=.5,y*=.5,i=Math.max(1,i),y=Math.max(1,y)}if(c!==void 0)break}e&&e.length>0?n.sphericalPolynomial=it.ConvertCubeMapToSphericalPolynomial({size:f[T],right:e[0],left:e[1],up:e[2],down:e[3],front:e[4],back:e[5],format:5,type:1,gammaSpace:!1}):n.sphericalPolynomial=void 0}}A.StoreLODInAlphaChannel=!1;export{A as DDSTools};
//# sourceMappingURL=dds-D1_iteoW.js.map
