import g from"./katex-rPiVaalG.js";var c=function(t,a,e){for(var r=e,n=0,l=t.length;r<a.length;){var d=a[r];if(n<=0&&a.slice(r,r+l)===t)return r;d==="\\"?r++:d==="{"?n++:d==="}"&&n--,r++}return-1},o=function(t){return t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},p=/^\\begin{/,m=function(t,a){for(var e,r=[],n=new RegExp("("+a.map(f=>o(f.left)).join("|")+")");e=t.search(n),e!==-1;){e>0&&(r.push({type:"text",data:t.slice(0,e)}),t=t.slice(e));var l=a.findIndex(f=>t.startsWith(f.left));if(e=c(a[l].right,t,a[l].left.length),e===-1)break;var d=t.slice(0,e+a[l].right.length),i=p.test(d)?d:t.slice(a[l].left.length,e);r.push({type:"math",data:i,rawData:d,display:a[l].display}),t=t.slice(e+a[l].right.length)}return t!==""&&r.push({type:"text",data:t}),r},y=function(t,a){var e=m(t,a.delimiters);if(e.length===1&&e[0].type==="text")return null;for(var r=document.createDocumentFragment(),n=0;n<e.length;n++)if(e[n].type==="text")r.appendChild(document.createTextNode(e[n].data));else{var l=document.createElement("span"),d=e[n].data;a.displayMode=e[n].display;try{a.preProcess&&(d=a.preProcess(d)),g.render(d,l,a)}catch(i){if(!(i instanceof g.ParseError))throw i;a.errorCallback("KaTeX auto-render: Failed to parse `"+e[n].data+"` with ",i),r.appendChild(document.createTextNode(e[n].rawData));continue}r.appendChild(l)}return r},T=function s(t,a){for(var e=0;e<t.childNodes.length;e++){var r=t.childNodes[e];if(r.nodeType===3){for(var n=r.textContent,l=r.nextSibling,d=0;l&&l.nodeType===Node.TEXT_NODE;)n+=l.textContent,l=l.nextSibling,d++;var i=y(n,a);if(i){for(var f=0;f<d;f++)r.nextSibling.remove();e+=i.childNodes.length-1,t.replaceChild(i,r)}else e+=d}else r.nodeType===1&&function(){var h=" "+r.className+" ",v=a.ignoredTags.indexOf(r.nodeName.toLowerCase())===-1&&a.ignoredClasses.every(u=>h.indexOf(" "+u+" ")===-1);v&&s(r,a)}()}},E=function(t,a){if(!t)throw new Error("No element provided to render");var e={};for(var r in a)a.hasOwnProperty(r)&&(e[r]=a[r]);e.delimiters=e.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],e.ignoredTags=e.ignoredTags||["script","noscript","style","textarea","pre","code","option"],e.ignoredClasses=e.ignoredClasses||[],e.errorCallback=e.errorCallback||console.error,e.macros=e.macros||{},T(t,e)};export{E as default};
//# sourceMappingURL=auto-render-8xbOkcXl.js.map
