import{SvelteComponent as x,init as q,safe_not_equal as y,element as f,text as h,space as C,claim_element as d,children as v,claim_text as g,detach as m,claim_space as H,insert_hydration as _,append_hydration as E,set_data as $,noop as b,component_subscribe as P}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{p as S}from"../chunks/stores.z8sZTwoA.js";function j(p){var u;let t,a=p[0].status+"",o,n,s,i=((u=p[0].error)==null?void 0:u.message)+"",c;return{c(){t=f("h1"),o=h(a),n=C(),s=f("p"),c=h(i)},l(e){t=d(e,"H1",{});var r=v(t);o=g(r,a),r.for<PERSON>ach(m),n=H(e),s=d(e,"P",{});var l=v(s);c=g(l,i),l.forEach(m)},m(e,r){_(e,t,r),E(t,o),_(e,n,r),_(e,s,r),E(s,c)},p(e,[r]){var l;r&1&&a!==(a=e[0].status+"")&&$(o,a),r&1&&i!==(i=((l=e[0].error)==null?void 0:l.message)+"")&&$(c,i)},i:b,o:b,d(e){e&&(m(t),m(n),m(s))}}}function k(p,t,a){let o;return P(p,S,n=>a(0,o=n)),[o]}class B extends x{constructor(t){super(),q(this,t,k,j,y,{})}}export{B as component};
//# sourceMappingURL=1.Cm-0Xidz.js.map
