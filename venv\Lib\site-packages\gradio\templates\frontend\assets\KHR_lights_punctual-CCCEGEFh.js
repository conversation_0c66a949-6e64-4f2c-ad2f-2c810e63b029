import{N as T,V as r,aq as x,M as S,_ as f,s as u,R as Z,C as b,an as C,ao as E}from"./index-Dpxo-yl_.js";import{b as v,a as R}from"./objectModelMapping-BR4RdEzn.js";import{ArrayItem as M,GLTFLoader as D}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";T.AddNodeConstructor("Light_Type_1",(c,t)=>()=>new a(c,r.Zero(),t));class a extends v{get shadowFrustumSize(){return this._shadowFrustumSize}set shadowFrustumSize(t){this._shadowFrustumSize=t,this.forceProjectionMatrixCompute()}get shadowOrthoScale(){return this._shadowOrthoScale}set shadowOrthoScale(t){this._shadowOrthoScale=t,this.forceProjectionMatrixCompute()}get orthoLeft(){return this._orthoLeft}set orthoLeft(t){this._orthoLeft=t}get orthoRight(){return this._orthoRight}set orthoRight(t){this._orthoRight=t}get orthoTop(){return this._orthoTop}set orthoTop(t){this._orthoTop=t}get orthoBottom(){return this._orthoBottom}set orthoBottom(t){this._orthoBottom=t}constructor(t,e,n){super(t,n),this._shadowFrustumSize=0,this._shadowOrthoScale=.1,this.autoUpdateExtends=!0,this.autoCalcShadowZBounds=!1,this._orthoLeft=Number.MAX_VALUE,this._orthoRight=Number.MIN_VALUE,this._orthoTop=Number.MIN_VALUE,this._orthoBottom=Number.MAX_VALUE,this.position=e.scale(-1),this.direction=e}getClassName(){return"DirectionalLight"}getTypeID(){return x.LIGHTTYPEID_DIRECTIONALLIGHT}_setDefaultShadowProjectionMatrix(t,e,n){this.shadowFrustumSize>0?this._setDefaultFixedFrustumShadowProjectionMatrix(t):this._setDefaultAutoExtendShadowProjectionMatrix(t,e,n)}_setDefaultFixedFrustumShadowProjectionMatrix(t){const e=this.getScene().activeCamera;e&&S.OrthoLHToRef(this.shadowFrustumSize,this.shadowFrustumSize,this.shadowMinZ!==void 0?this.shadowMinZ:e.minZ,this.shadowMaxZ!==void 0?this.shadowMaxZ:e.maxZ,t,this.getScene().getEngine().isNDCHalfZRange)}_setDefaultAutoExtendShadowProjectionMatrix(t,e,n){const h=this.getScene().activeCamera;if(this.autoUpdateExtends||this._orthoLeft===Number.MAX_VALUE){const o=r.Zero();this._orthoLeft=Number.MAX_VALUE,this._orthoRight=-Number.MAX_VALUE,this._orthoTop=-Number.MAX_VALUE,this._orthoBottom=Number.MAX_VALUE;let p=Number.MAX_VALUE,w=-Number.MAX_VALUE;for(let L=0;L<n.length;L++){const A=n[L];if(!A)continue;const B=A.getBoundingInfo().boundingBox;for(let y=0;y<B.vectorsWorld.length;y++)r.TransformCoordinatesToRef(B.vectorsWorld[y],e,o),o.x<this._orthoLeft&&(this._orthoLeft=o.x),o.y<this._orthoBottom&&(this._orthoBottom=o.y),o.x>this._orthoRight&&(this._orthoRight=o.x),o.y>this._orthoTop&&(this._orthoTop=o.y),this.autoCalcShadowZBounds&&(o.z<p&&(p=o.z),o.z>w&&(w=o.z))}this.autoCalcShadowZBounds&&(this._shadowMinZ=p,this._shadowMaxZ=w)}const l=this._orthoRight-this._orthoLeft,d=this._orthoTop-this._orthoBottom,i=this.shadowMinZ!==void 0?this.shadowMinZ:h?.minZ||0,s=this.shadowMaxZ!==void 0?this.shadowMaxZ:h?.maxZ||1e4,_=this.getScene().getEngine().useReverseDepthBuffer;S.OrthoOffCenterLHToRef(this._orthoLeft-l*this.shadowOrthoScale,this._orthoRight+l*this.shadowOrthoScale,this._orthoBottom-d*this.shadowOrthoScale,this._orthoTop+d*this.shadowOrthoScale,_?s:i,_?i:s,t,this.getScene().getEngine().isNDCHalfZRange)}_buildUniformLayout(){this._uniformBuffer.addUniform("vLightData",4),this._uniformBuffer.addUniform("vLightDiffuse",4),this._uniformBuffer.addUniform("vLightSpecular",4),this._uniformBuffer.addUniform("shadowsInfo",3),this._uniformBuffer.addUniform("depthValues",2),this._uniformBuffer.create()}transferToEffect(t,e){return this.computeTransformedInformation()?(this._uniformBuffer.updateFloat4("vLightData",this.transformedDirection.x,this.transformedDirection.y,this.transformedDirection.z,1,e),this):(this._uniformBuffer.updateFloat4("vLightData",this.direction.x,this.direction.y,this.direction.z,1,e),this)}transferToNodeMaterialEffect(t,e){return this.computeTransformedInformation()?(t.setFloat3(e,this.transformedDirection.x,this.transformedDirection.y,this.transformedDirection.z),this):(t.setFloat3(e,this.direction.x,this.direction.y,this.direction.z),this)}getDepthMinZ(t){const e=this._scene.getEngine();return!e.useReverseDepthBuffer&&e.isNDCHalfZRange?0:1}getDepthMaxZ(t){const e=this._scene.getEngine();return e.useReverseDepthBuffer&&e.isNDCHalfZRange?0:1}prepareLightSpecificDefines(t,e){t["DIRLIGHT"+e]=!0}}f([u()],a.prototype,"shadowFrustumSize",null);f([u()],a.prototype,"shadowOrthoScale",null);f([u()],a.prototype,"autoUpdateExtends",void 0);f([u()],a.prototype,"autoCalcShadowZBounds",void 0);f([u("orthoLeft")],a.prototype,"_orthoLeft",void 0);f([u("orthoRight")],a.prototype,"_orthoRight",void 0);f([u("orthoTop")],a.prototype,"_orthoTop",void 0);f([u("orthoBottom")],a.prototype,"_orthoBottom",void 0);Z("BABYLON.DirectionalLight",a);T.AddNodeConstructor("Light_Type_0",(c,t)=>()=>new g(c,r.Zero(),t));class g extends v{get shadowAngle(){return this._shadowAngle}set shadowAngle(t){this._shadowAngle=t,this.forceProjectionMatrixCompute()}get direction(){return this._direction}set direction(t){const e=this.needCube();if(this._direction=t,this.needCube()!==e&&this._shadowGenerators){const n=this._shadowGenerators.values();for(let h=n.next();h.done!==!0;h=n.next())h.value.recreateShadowMap()}}constructor(t,e,n){super(t,n),this._shadowAngle=Math.PI/2,this.position=e}getClassName(){return"PointLight"}getTypeID(){return x.LIGHTTYPEID_POINTLIGHT}needCube(){return!this.direction}getShadowDirection(t){if(this.direction)return super.getShadowDirection(t);switch(t){case 0:return new r(1,0,0);case 1:return new r(-1,0,0);case 2:return new r(0,-1,0);case 3:return new r(0,1,0);case 4:return new r(0,0,1);case 5:return new r(0,0,-1)}return r.Zero()}_setDefaultShadowProjectionMatrix(t,e,n){const h=this.getScene().activeCamera;if(!h)return;const l=this.shadowMinZ!==void 0?this.shadowMinZ:h.minZ,d=this.shadowMaxZ!==void 0?this.shadowMaxZ:h.maxZ,i=this.getScene().getEngine().useReverseDepthBuffer;S.PerspectiveFovLHToRef(this.shadowAngle,1,i?d:l,i?l:d,t,!0,this._scene.getEngine().isNDCHalfZRange,void 0,i)}_buildUniformLayout(){this._uniformBuffer.addUniform("vLightData",4),this._uniformBuffer.addUniform("vLightDiffuse",4),this._uniformBuffer.addUniform("vLightSpecular",4),this._uniformBuffer.addUniform("vLightFalloff",4),this._uniformBuffer.addUniform("shadowsInfo",3),this._uniformBuffer.addUniform("depthValues",2),this._uniformBuffer.create()}transferToEffect(t,e){return this.computeTransformedInformation()?this._uniformBuffer.updateFloat4("vLightData",this.transformedPosition.x,this.transformedPosition.y,this.transformedPosition.z,0,e):this._uniformBuffer.updateFloat4("vLightData",this.position.x,this.position.y,this.position.z,0,e),this._uniformBuffer.updateFloat4("vLightFalloff",this.range,this._inverseSquaredRange,0,0,e),this}transferToNodeMaterialEffect(t,e){return this.computeTransformedInformation()?t.setFloat3(e,this.transformedPosition.x,this.transformedPosition.y,this.transformedPosition.z):t.setFloat3(e,this.position.x,this.position.y,this.position.z),this}prepareLightSpecificDefines(t,e){t["POINTLIGHT"+e]=!0}}f([u()],g.prototype,"shadowAngle",null);Z("BABYLON.PointLight",g);const m="KHR_lights_punctual";class N{constructor(t){this.name=m,this._loader=t,this.enabled=this._loader.isExtensionUsed(m)}dispose(){this._loader=null,delete this._lights}onLoading(){const t=this._loader.gltf.extensions;if(t&&t[this.name]){const e=t[this.name];this._lights=e.lights,M.Assign(this._lights)}}loadNodeAsync(t,e,n){return D.LoadExtensionAsync(t,e,this.name,(h,l)=>(this._loader._allMaterialsDirtyRequired=!0,this._loader.loadNodeAsync(t,e,d=>{let i;const s=M.Get(h,this._lights,l.light),_=s.name||d.name;switch(this._loader.babylonScene._blockEntityCollection=!!this._loader._assetContainer,s.type){case"directional":{const o=new a(_,r.Backward(),this._loader.babylonScene);o.position.setAll(0),i=o;break}case"point":{i=new g(_,r.Zero(),this._loader.babylonScene);break}case"spot":{const o=new R(_,r.Zero(),r.Backward(),0,1,this._loader.babylonScene);o.angle=(s.spot&&s.spot.outerConeAngle||Math.PI/4)*2,o.innerAngle=(s.spot&&s.spot.innerConeAngle||0)*2,i=o;break}default:throw this._loader.babylonScene._blockEntityCollection=!1,new Error(`${h}: Invalid light type (${s.type})`)}i._parentContainer=this._loader._assetContainer,this._loader.babylonScene._blockEntityCollection=!1,s._babylonLight=i,i.falloffType=x.FALLOFF_GLTF,i.diffuse=s.color?b.FromArray(s.color):b.White(),i.intensity=s.intensity==null?1:s.intensity,i.range=s.range==null?Number.MAX_VALUE:s.range,i.parent=d,this._loader._babylonLights.push(i),D.AddPointerMetadata(i,h),n(d)})))}}C(m);E(m,!0,c=>new N(c));export{N as KHR_lights};
//# sourceMappingURL=KHR_lights_punctual-CCCEGEFh.js.map
