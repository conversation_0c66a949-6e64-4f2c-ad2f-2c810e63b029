import{aG as a,R as u}from"./index-Dpxo-yl_.js";import{F as c}from"./KHR_interactivity-DTxiAnOo.js";import{b as h,f as r,R as g}from"./declarationMapper-BZjsjg7g.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class l extends c{constructor(t){super(t),this.config=t,this._easingFunctions={},this.mode=this.registerDataInput("mode",h,0),this.controlPoint1=this.registerDataInput("controlPoint1",r),this.controlPoint2=this.registerDataInput("controlPoint2",r),this.easingFunction=this.registerDataOutput("easingFunction",g)}_updateOutputs(t){const e=this.mode.getValue(t),i=this.controlPoint1.getValue(t),s=this.controlPoint2.getValue(t);if(e===void 0)return;const o=`${e}-${i.x}-${i.y}-${s.x}-${s.y}`;if(!this._easingFunctions[o]){const n=new a(i.x,i.y,s.x,s.y);n.setEasingMode(e),this._easingFunctions[o]=n}this.easingFunction.setValue(this._easingFunctions[o],t)}getClassName(){return"FlowGraphBezierCurveEasing"}}u("FlowGraphBezierCurveEasing",l);export{l as FlowGraphBezierCurveEasingBlock};
//# sourceMappingURL=flowGraphBezierCurveEasingBlock-C_gI8Cqw.js.map
