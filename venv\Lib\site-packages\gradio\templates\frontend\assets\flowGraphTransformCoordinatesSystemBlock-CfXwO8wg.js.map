{"version": 3, "file": "flowGraphTransformCoordinatesSystemBlock-CfXwO8wg.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphTransformCoordinatesSystemBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../flowGraphBlock.js\";\nimport { RichTypeAny, RichTypeVector3 } from \"../../flowGraphRichTypes.js\";\nimport { TmpVectors, Vector3 } from \"../../../Maths/math.vector.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\n/**\n * This blocks transforms a vector from one coordinate system to another.\n */\nexport class FlowGraphTransformCoordinatesSystemBlock extends FlowGraphBlock {\n    /**\n     * Creates a new FlowGraphCoordinateTransformBlock\n     * @param config optional configuration for this block\n     */\n    constructor(config) {\n        super(config);\n        this.sourceSystem = this.registerDataInput(\"sourceSystem\", RichTypeAny);\n        this.destinationSystem = this.registerDataInput(\"destinationSystem\", RichTypeAny);\n        this.inputCoordinates = this.registerDataInput(\"inputCoordinates\", RichTypeVector3);\n        this.outputCoordinates = this.registerDataOutput(\"outputCoordinates\", RichTypeVector3);\n    }\n    _updateOutputs(_context) {\n        const sourceSystemValue = this.sourceSystem.getValue(_context);\n        const destinationSystemValue = this.destinationSystem.getValue(_context);\n        const inputCoordinatesValue = this.inputCoordinates.getValue(_context);\n        // takes coordinates from source space to world space\n        const sourceWorld = sourceSystemValue.getWorldMatrix();\n        // takes coordinates from destination space to world space\n        const destinationWorld = destinationSystemValue.getWorldMatrix();\n        const destinationWorldInverse = TmpVectors.Matrix[0].copyFrom(destinationWorld);\n        // takes coordinates from world space to destination space\n        destinationWorldInverse.invert();\n        const sourceToDestination = TmpVectors.Matrix[1];\n        // takes coordinates from source space to world space to destination space\n        destinationWorldInverse.multiplyToRef(sourceWorld, sourceToDestination);\n        const outputCoordinatesValue = this.outputCoordinates.getValue(_context);\n        Vector3.TransformCoordinatesToRef(inputCoordinatesValue, sourceToDestination, outputCoordinatesValue);\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FlowGraphTransformCoordinatesSystemBlock\" /* FlowGraphBlockNames.TransformCoordinatesSystem */;\n    }\n}\nRegisterClass(\"FlowGraphTransformCoordinatesSystemBlock\" /* FlowGraphBlockNames.TransformCoordinatesSystem */, FlowGraphTransformCoordinatesSystemBlock);\n//# sourceMappingURL=flowGraphTransformCoordinatesSystemBlock.js.map"], "names": ["FlowGraphTransformCoordinatesSystemBlock", "FlowGraphBlock", "config", "RichTypeAny", "RichTypeVector3", "_context", "sourceSystemValue", "destinationSystemValue", "inputCoordinatesValue", "sourceWorld", "destinationWorld", "destinationWorldInverse", "TmpVectors", "sourceToDestination", "outputCoordinatesValue", "Vector3", "RegisterClass"], "mappings": "qQAOO,MAAMA,UAAiDC,CAAe,CAKzE,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,aAAe,KAAK,kBAAkB,eAAgBC,CAAW,EACtE,KAAK,kBAAoB,KAAK,kBAAkB,oBAAqBA,CAAW,EAChF,KAAK,iBAAmB,KAAK,kBAAkB,mBAAoBC,CAAe,EAClF,KAAK,kBAAoB,KAAK,mBAAmB,oBAAqBA,CAAe,CACxF,CACD,eAAeC,EAAU,CACrB,MAAMC,EAAoB,KAAK,aAAa,SAASD,CAAQ,EACvDE,EAAyB,KAAK,kBAAkB,SAASF,CAAQ,EACjEG,EAAwB,KAAK,iBAAiB,SAASH,CAAQ,EAE/DI,EAAcH,EAAkB,iBAEhCI,EAAmBH,EAAuB,iBAC1CI,EAA0BC,EAAW,OAAO,CAAC,EAAE,SAASF,CAAgB,EAE9EC,EAAwB,OAAM,EAC9B,MAAME,EAAsBD,EAAW,OAAO,CAAC,EAE/CD,EAAwB,cAAcF,EAAaI,CAAmB,EACtE,MAAMC,EAAyB,KAAK,kBAAkB,SAAST,CAAQ,EACvEU,EAAQ,0BAA0BP,EAAuBK,EAAqBC,CAAsB,CACvG,CAKD,cAAe,CACX,MAAO,0CACV,CACL,CACAE,EAAc,2CAAiGhB,CAAwC", "x_google_ignoreList": [0]}